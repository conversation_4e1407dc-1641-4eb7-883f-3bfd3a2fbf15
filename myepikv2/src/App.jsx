import { MainLayout } from '@/components';
import {
  Companies,
  EdgeDevices,
  PlaygroundComponents,
  Calls,
  LegacyFax,
  Fax,
  Numbers,
  NumberOrdering,
  Porting,
  E911Numbers,
  CarrierLookup,
  Users,
  EdgeDeviceDetails,
} from '@/pages';
import { ConfigProvider, Spin } from 'antd';
import { useEffect, useState } from 'react';
import {
  Navigate,
  Route,
  Routes,
  useLocation,
  useNavigate,
} from 'react-router-dom';
import useAuthStore from './store/authStore';
import { useStore } from '@/store';
import { useUserPermissions } from '@/graphqlHooks/auth/useUserPermissions';

const colorBgContainer = '#FFFFFF';
const colorPrimary = '#4d9cd3';
const alertColor = '#D92D20';
const colorBgContainerDisabled = '#C8E0F1';
const alertColorDisabled = '#FECDCA';
const defaultColorDisabled = '#E4E7EC';
const iconBlackColor = '#475467';
const sidebarMenuIconColor = '#667085';
const defaultColorBgContainer = '#ffffff';
const customColorBgContainer = '#E4F0F8';
const switchSecondaryVariant = '#3B5AA9';
const tableHdBg = '#ebeff6';

const colorPrimary2 = '#54ABE7';
const colorPrimary3 = '#5EB1EB';
const colorPrimary4 = '#85CAFA';
const colorPrimary5 = '#379DE5';
const colorPrimary6 = '#3784BB';
const colorPrimary7 = '#336B92';
const barChartColor1 = '#7093EC';

const token = {
  colorBgContainer,
  colorPrimary,
  alertColor,
  colorBgContainerDisabled,
  alertColorDisabled,
  defaultColorDisabled,
  iconBlackColor,
  sidebarMenuIconColor,
  defaultColorBgContainer,
  customColorBgContainer,
  switchSecondaryVariant,
  colorPrimary2,
  colorPrimary3,
  colorPrimary4,
  colorPrimary5,
  colorPrimary6,
  colorPrimary7,
  barChartColor1,
  fontFamily: 'Inter, Montserrat, Arial, sans-serif',
};

const resourceKeyToSidebarMap = {
  USER: '/users',
  PERMISSIONGROUP: '/users/permissions',
  USERAPPROVAL: '/users/approvals',
  EDGEDEVICES: '/edge-devices',
  COMPANY: '/companies',
  NUMBERORDERING: '/carrier-hub/number-ordering',
  PORTINGORDER: '/carrier-hub/porting',
  E911: '/carrier-hub/e911',
  CARRIERLOOKUP: '/carrier-hub/carrier-lookup',
  CALLS: '/usage/calls',
  LEGACY_FAX: '/usage/legacy-fax',
  FAX: '/usage/fax',
  NUMBERS: '/usage/numbers',
};

const getParentPath = (title) => {
  const map = {
    Usage: '/usage',
    'Carrier Hub': '/carrier-hub',
  };
  return map[title] || null;
};

function App() {
  const [menuKeys, setMenuKeys] = useState([]);
  const { initialize } = useAuthStore();
  const { setUiSchema } = useStore((s) => s.permission);
  const { data, isLoading } = useUserPermissions();
  const location = useLocation();
  const navigate = useNavigate();
  // const _token = useToken()
  // console.log({_token})
  // const init = () => {
  //   useAuthStore().initialize();
  // };

  const userPermissionsError = useStore(
    (s) => s.error.errorState['user-permissions'] || null,
  );

  const listEpikboxesError = useStore(
    (s) => s.error.errorState['list-epikboxes'] || null,
  );

  const homeRoute = menuKeys[0] || null;

  const defaultRedirect =
    location.pathname === '/' && homeRoute ? homeRoute : null;

  const buildSidebarMenuKeys = (menuItems, resources) => {
    const allowedResourceKeys = new Set(resources.map((r) => r.resourceKey));
    const allowedPaths = new Set();

    const recurse = (items) => {
      for (const item of items) {
        if (item.resourceKey && allowedResourceKeys.has(item.resourceKey)) {
          const mapped = resourceKeyToSidebarMap[item.resourceKey];
          if (mapped) allowedPaths.add(mapped);
        }

        if (item.childs?.length) {
          recurse(item.childs);
          const hasVisibleChild = item.childs.some(
            (child) =>
              child.resourceKey &&
              allowedResourceKeys.has(child.resourceKey) &&
              resourceKeyToSidebarMap[child.resourceKey],
          );
          if (hasVisibleChild) {
            const parentPath = getParentPath(item.title);
            if (parentPath) allowedPaths.add(parentPath);
          }
        }
      }
    };

    recurse(menuItems);
    return Array.from(allowedPaths);
  };

  useEffect(() => {
    initialize();
  }, []);

  useEffect(() => {
    if (!isLoading) {
      const permissions = data?.ListUserPermission;
      const keys = buildSidebarMenuKeys(
        permissions?.menuItems || [],
        permissions?.uiSchema?.resources || [],
      );

      if (keys.includes('/edge-devices')) {
        keys.push('/edge-devices/:deviceId');
      }

      // these are missing from the response,
      // I hardcoded to view,otherwise it is handled properly and not show in the sidebar menu.
      const detailRoutes = [
        '/usage/legacy-fax',
        '/carrier-hub/number-ordering',
      ];
      // const finalKeys = [...keys, ...detailRoutes];
      let finalKeys = [...keys];
      if (finalKeys?.length > 0) {
        finalKeys = [...finalKeys, ...detailRoutes];
      }
      setMenuKeys(finalKeys);
      if (permissions?.uiSchema) {
        setUiSchema(permissions.uiSchema);
      }
    }
  }, [isLoading, data]);

  useEffect(() => {
    if (listEpikboxesError || userPermissionsError) {
      navigate('/edge-devices', { replace: true });
    }
  }, [listEpikboxesError, userPermissionsError]);

  const allRoutes = [
    { key: '/edge-devices', element: <EdgeDevices /> },
    { key: '/edge-devices/:deviceId', element: <EdgeDeviceDetails /> },
    { key: '/companies', element: <Companies /> },
    { key: '/users', element: <Users /> },
    { key: '/usage/calls', element: <Calls /> },
    { key: '/usage/legacy-fax', element: <LegacyFax /> },
    { key: '/usage/fax', element: <Fax /> },
    { key: '/usage/numbers', element: <Numbers /> },
    { key: '/carrier-hub/number-ordering', element: <NumberOrdering /> },
    { key: '/carrier-hub/porting', element: <Porting /> },
    { key: '/carrier-hub/e911', element: <E911Numbers /> },
    { key: '/carrier-hub/carrier-lookup', element: <CarrierLookup /> },
  ];

  const filteredRoutes = allRoutes.filter((route) =>
    menuKeys.includes(route.key),
  );

  const contentStyle = {
    padding: 50,
    background: 'rgba(0, 0, 0, 0.05)',
  };

  const content = <div style={contentStyle} />;

  if (isLoading) {
    return (
      <div
        style={{
          display: 'flex',
          height: '100vh',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Spin tip="Loading" size="large" className="app-spin">
          {content}
        </Spin>
      </div>
    );
  }

  return (
    <ConfigProvider
      theme={{
        cssVar: { key: 'app', prefix: 'epik' },
        token,
        components: {
          Layout: {
            headerBg: colorBgContainer,
            headerHeight: '4rem',
            siderBg: colorBgContainer,
          },
          Table: {
            headerBg: tableHdBg,
          },
        },
      }}
      componentSize="middle"
    >
      <Routes>
        <Route
          path="/"
          element={<MainLayout menuKeys={menuKeys} loading={isLoading} />}
        >
          <Route path="/edge-devices" element={<EdgeDevices />} />
          <Route path="/playground" element={<PlaygroundComponents />} />
          {filteredRoutes.map(({ key, element }) => (
            <Route key={key} path={key} element={element} />
          ))}
          {defaultRedirect && (
            <>
              <Route index element={<Navigate to={defaultRedirect} />} />
              <Route path="*" element={<Navigate to={defaultRedirect} />} />
            </>
          )}
        </Route>
      </Routes>
    </ConfigProvider>
  );
}

export default App;
