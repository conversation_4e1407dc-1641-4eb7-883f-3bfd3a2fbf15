@import './fonts';
html,
html {
  font-size: 14px;
  /* font-size: clamp(12px, 1vw, 17px); */
}
body {
  margin: 0;
  padding: 0;
  font-size: 1rem;
}
@media (width <= 1500px) {
  html {
    font-size: 11px;
  }
}
@media (width <= 1000px) {
  html {
    font-size: 10px;
  }
}
:root {
  --primary-purple: #7f56d9;
  --bg-gray: #e4e7ec;

  --primary-green: #027a48;

  --primary-green1: #12b76a;

  --secondary-green: #ecfdf3;

  --primary-warning: #f79009;

  --secondary-warning: #fef3f2;

  --primary-red: #b42318;

  --error-600: #d92d20;

  --error-icon: #ed2b2e;

  --secondary-red: #fef3f2;

  --primary-gray: #344054;

  --gray-500: #667085;

  --gray-700: #344054;

  --secondary-gray: #f2f4f7;

  --bg-white-connection: #ffffff;

  --button-light-blue: #4d9cd3;

  --button-light-blue-1: #5eb1eb;

  --button-dark-blue: #458cbe;

  --button-dark-blue-1: #2d9cdb;

  --bg-blue: #edf5fb;

  --primary-white: white;

  --disabled-field-color: #e8e8e8;

  --primary-button-blue: #4c9cd3;

  --primary-gray1: #3e4042;

  --primary-gray2: #f4f6f8;

  --gray-600: #475467;

  --gray-900: #101828;

  --primary-gray4: #181c32;

  --primary-gray5: #a1a5b7;

  --secondary-gray: #f2f4f7;

  --black-1: #000;

  --black-3: #414245;

  --granite-blue: #4d9cd3;

  --blue-2: #edf5fb;

  --granite-blue-light: #e4f0f8;

  --granite-blue-light-active: #c8e0f1;

  --box-shadow: #1018281a;

  --border-grey: #d0d5dd;

  --error: #fee4e2;

  --error-hover: #fdb7b1;

  --success-icon: #2fd214;

  --link: #2256cf;

  --primary-blue: #355398;

  --surface-light-blue: #ebeff6;

  --primary-yellow: #dc6803;

  --secondary-yellow: #fef0c7;
}

/* color classes */
.primary-purple-clr {
  color: var(--primary-purple) !important;
}

.lb1-color {
  color: var(--gray-600);
}
/* padding classes */
.plr-10 {
  padding-left: 15px !important;
  padding-right: 15px !important;
}

/* margin classes */
.mt-15 {
  margin-top: 15px !important;
}

/* shadow */
.box-shadow-light {
  box-shadow:
    0 1px 2px 0 rgba(0, 0, 0, 0.03),
    0 1px 6px -1px rgba(0, 0, 0, 0.02),
    0 2px 4px 0 rgba(0, 0, 0, 0.02);
}

/* color classes */
.gray1-color {
  color: var(--primary-gray1);
}

.black1-color {
  color: var(--black-1);
}

/* font classes */
.font-400 {
  font-weight: 400;
}

/* width classes */
.w-100-percent {
  width: 100%;
}

.app-spin {
  color: var(--granite-blue);
}
.app-spin .ant-spin-dot-holder {
  color: var(--granite-blue);
}
