import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);

/**
 * Flexible timestamp formatter
 * @param {string|Date} timestamp
 * @param {Object} options
 * @param {string} options.format
 * @param {boolean} options.utc
 * @param {string} options.fallback
 * @returns {string}
 */
export function formatTimestamp(timestamp, options = {}) {
  const {
    format = 'MMM D, YYYY hh:mm:ss a z',
    utc: useUTC = false,
    fallback = 'N/A',
  } = options;

  if (!timestamp) return fallback;

  let date = dayjs(timestamp);
  if (useUTC) date = dayjs.utc(timestamp);

  // Detect and apply system/browser timezone automatically
  date = date.tz(dayjs.tz.guess());

  return date.isValid() ? date.format(format) : fallback;
}
