export function debounce<T extends (...args: any[]) => void>(
    func: T,
    timeout = 300
  ): (...args: Parameters<T>) => void {
    let timer: ReturnType<typeof setTimeout> | null;
  
    return function (this: ThisParameterType<T>, ...args: Parameters<T>) {
      if (timer) {
        clearTimeout(timer);
      }
      timer = setTimeout(() => {
        func.apply(this, args);
      }, timeout);
    };
  }
  