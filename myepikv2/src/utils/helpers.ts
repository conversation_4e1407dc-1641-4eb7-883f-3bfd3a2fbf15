import { generateQueryKey } from "./queryKeys";

export const generateRandomId = (): string => {
  return Math.random().toString(36).substring(2, 15);
};

export const epikBoxByIdKey = (id: string | number) =>
  generateQueryKey('epikboxbyid', id);

export const epiDetailByIdKey = (id: string | number) =>
  generateQueryKey('epidetailbyid', id);

export const isEscene = (macAddress: string) => macAddress?.startsWith('002');
