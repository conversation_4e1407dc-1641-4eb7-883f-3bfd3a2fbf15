import { StateCreator } from 'zustand';
import { StoreState } from './index';

export interface UiSchema {
  resources: {
    resourceKey: string;
    features: {
      feature: string;
      meta: {
        read: boolean;
        write: boolean;
        resourceKey?: string;
      };
    }[];
  }[];
}

export interface PermissionSlice {
  uiSchema: UiSchema | null;
  setUiSchema: (schema: UiSchema) => void;
  getFeatureAccess: (
    featureKey: string
  ) => { read: boolean; write: boolean };
  hasReadAccess: (featureKey: string) => boolean;
  hasWriteAccess: (featureKey: string) => boolean;
  getMultipleFeatureAccess: (
    featureKeys: string[]
  ) => Record<string, { read: boolean; write: boolean }>;
}

export const createPermissionSlice: StateCreator<
  StoreState & { permission: PermissionSlice },
  [['zustand/immer', never]],
  [],
  PermissionSlice
> = (set, get) => ({
  uiSchema: null,

  setUiSchema: (schema) => {
    set((state) => {
      (state as any).permission.uiSchema = schema;
    });
  },

  getFeatureAccess: (featureKey) => {
    const schema = (get() as any).permission.uiSchema;
    for (const resource of schema?.resources || []) {
      for (const feature of resource.features || []) {
        if (feature.feature === featureKey) {
          return {
            read: !!feature.meta?.read,
            write: !!feature.meta?.write,
          };
        }
      }
    }
    return { read: false, write: false };
  },

  getMultipleFeatureAccess: (featureKeys: string[]) => {
    const schema = (get() as any).permission.uiSchema;
    const result: Record<string, { read: boolean; write: boolean }> = {};
  
    for (const key of featureKeys) {
      result[key] = { read: false, write: false };
  
      for (const resource of schema?.resources || []) {
        for (const feature of resource.features || []) {
          if (feature.feature === key) {
            result[key] = {
              read: !!feature.meta?.read,
              write: !!feature.meta?.write,
            };
            break;
          }
        }
      }
    }
  
    return result;
  },
  

  hasReadAccess: (featureKey) => {
    return get().permission.getFeatureAccess(featureKey).read;
  },

  hasWriteAccess: (featureKey) => {
    return get().permission.getFeatureAccess(featureKey).write;
  },
});
