import { APIS } from '@/constants';
import { apiClientAsync, useDebounce, useGraphQLQuery } from '@/hooks';
import { ApiResponse } from '@/types/apiClient';
import { ListUserResponse, MeResponse, User } from '@/types/user';
import { debounce } from '@/utils/debounce';
import { generateRandomId } from '@/utils/helpers';
import logger from '@/utils/logger';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

// Constants
const IDLE_TIMEOUT = 15 * 60 * 1000; // 15 minutes in milliseconds
const TOKEN_REFRESH_INTERVAL = 4 * 60 * 1000; // 4 minutes in milliseconds
const LAST_ACTIVITY_KEY = 'v2:last_activity';
const TAB_ID_KEY = 'v2:tab_id';
const BROADCAST_CHANNEL = 'auth_channel';

interface BroadcastMessage {
  type: 'ACTIVITY_UPDATE' | 'LOGOUT_ALL';
  timestamp?: number;
  tabId?: string;
}

// Declare global window properties for our session management
declare global {
  interface Window {
    tokenRefreshInterval?: NodeJS.Timeout;
    idleCheckInterval?: NodeJS.Timeout;
    activityHandler?: (event: Event) => void;
    broadcastMessageHandler?: (event: MessageEvent<BroadcastMessage>) => void;
  }
}

interface AuthState {
  user: User | null;
  loading: boolean;
  authFailed: boolean;
  tabId: string;
  broadcastChannel: BroadcastChannel | null;

  // Methods
  initialize: () => Promise<void>;
  logout: (fromAllTabs?: boolean) => Promise<void>;
  refreshToken: () => Promise<void>;
  updateActivity: () => void;
  setupTokenRefresh: () => void;
  setupIdleChecking: () => void;
  setupActivityTracking: () => void;
  setupBroadcastListeners: () => void;
  cleanupAuthListeners: () => void;
}

const getTabId = (): string => {
  const existingId = sessionStorage.getItem(TAB_ID_KEY);
  if (existingId) return existingId;

  const newId = generateRandomId();
  sessionStorage.setItem(TAB_ID_KEY, newId);
  return newId;
};

const createBroadcastChannel = (): BroadcastChannel | null => {
  if (typeof BroadcastChannel !== 'undefined') {
    return new BroadcastChannel(BROADCAST_CHANNEL);
  }
  return null;
};

// Create the auth store
const useAuthStore = create<AuthState>()((set, get) => ({
  user: null,
  loading: true,
  authFailed: false,
  tabId: getTabId(),
  broadcastChannel: createBroadcastChannel(),

  initialize: async () => {
    let remoteUserRes = await apiClientAsync<MeResponse>({
      endpoint: '/user/me',
      api: APIS.AUTH,
      method: 'GET',
    });
    if (remoteUserRes.status === 'failed') {
      let res = await apiClientAsync<ApiResponse<null>>({
        endpoint: '/user/token-validation-from-myepik',
        api: APIS.AUTH_WITH_MYEPIK_TOKEN,
        method: 'GET',
      });
      if (res.status === 'failed') {
        return set({ authFailed: true, loading: false });
      }
      remoteUserRes = await apiClientAsync<MeResponse>({
        endpoint: '/user/me',
        api: APIS.AUTH,
        method: 'GET',
      });
    }
    set({ user: remoteUserRes.data || null, loading: false });
    try {
      get().setupTokenRefresh();

      get().setupIdleChecking();

      get().setupActivityTracking();

      get().setupBroadcastListeners();
    } catch (error) {
      console.error('Session validation failed:', error);
      get().logout();
    }
  },

  // Logout function
  logout: async (): Promise<void> => {
    const { broadcastChannel } = get();
    set({ loading: true });
    let res = await apiClientAsync<ApiResponse<null>>({
      endpoint: '/user/refresh-token',
      api: APIS.AUTH,
      method: 'GET',
    });
    if (res.status === 'success') {
      broadcastChannel?.postMessage({
        type: 'LOGOUT_ALL',
      } as BroadcastMessage);
      get().cleanupAuthListeners();
      set({ user: null, loading: false });
    }
  },

  refreshToken: async (): Promise<void> => {
    try {
      logger.info('refreshToken');
      let res = await apiClientAsync<ApiResponse<any>>({
        endpoint: '/user/refresh-token',
        api: APIS.AUTH,
        method: 'GET',
      });
      if (res.status === 'failed') {
        get().logout();
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
      get().logout();
    }
  },

  updateActivity: (): void => {
    const { tabId, broadcastChannel } = get();
    const now = Date.now();
    localStorage.setItem(LAST_ACTIVITY_KEY, now.toString());

    if (broadcastChannel) {
      broadcastChannel.postMessage({
        type: 'ACTIVITY_UPDATE',
        timestamp: now,
        tabId,
      } as BroadcastMessage);
    }
  },

  setupTokenRefresh: (): void => {
    if (window.tokenRefreshInterval) {
      clearInterval(window.tokenRefreshInterval);
    }
    window.tokenRefreshInterval = setInterval(() => {
      logger.info('setupTokenRefresh');
      get().refreshToken();
    }, TOKEN_REFRESH_INTERVAL);
    get().refreshToken();
  },

  setupIdleChecking: (): void => {
    if (window.idleCheckInterval) {
      clearInterval(window.idleCheckInterval);
    }

    window.idleCheckInterval = setInterval(() => {
      const lastActivity = localStorage.getItem(LAST_ACTIVITY_KEY);
      if (!lastActivity) {
        get().updateActivity();
        return;
      }

      const now = Date.now();
      const idle = now - parseInt(lastActivity, 10);

      if (idle > IDLE_TIMEOUT) {
        get().logout();
      }
    }, 60000);
  },

  setupActivityTracking: (): void => {
    const activityHandler = () => {
      get().updateActivity();
    };
    const debouncedActivityHandler = debounce(activityHandler, 1000);
    window.activityHandler = debouncedActivityHandler;
    const events = ['mousemove', 'keypress', 'scroll', 'click'];
    events.forEach((event) => {
      window.addEventListener(event, debouncedActivityHandler);
    });
  },

  // Set up broadcast channel listeners
  setupBroadcastListeners: (): void => {
    const { broadcastChannel, tabId } = get();
    if (!broadcastChannel) return;

    const messageHandler = (event: MessageEvent<BroadcastMessage>) => {
      const { type, timestamp, tabId: sourceTabId } = event.data;

      switch (type) {
        case 'ACTIVITY_UPDATE':
          // Another tab reported activity, update our last activity if it's newer
          if (sourceTabId !== tabId && timestamp) {
            const currentLastActivity = parseInt(
              localStorage.getItem(LAST_ACTIVITY_KEY) || '0',
              10,
            );
            if (timestamp > currentLastActivity) {
              localStorage.setItem(LAST_ACTIVITY_KEY, timestamp.toString());
            }
          }
          break;

        case 'LOGOUT_ALL':
          set({ user: null });
          get().cleanupAuthListeners();
          break;

        default:
          break;
      }
    };

    // Store handler reference for cleanup
    window.broadcastMessageHandler = messageHandler;

    broadcastChannel.addEventListener('message', messageHandler);
  },

  cleanupAuthListeners: (): void => {
    if (window.tokenRefreshInterval) {
      clearInterval(window.tokenRefreshInterval);
      window.tokenRefreshInterval = undefined;
    }

    if (window.idleCheckInterval) {
      clearInterval(window.idleCheckInterval);
      window.idleCheckInterval = undefined;
    }

    if (window.activityHandler) {
      const events = ['mousemove', 'keypress', 'scroll', 'click'];
      events.forEach((event) => {
        window.removeEventListener(
          event,
          window.activityHandler as EventListener,
        );
      });
      window.activityHandler = undefined;
    }

    const { broadcastChannel } = get();
    if (broadcastChannel && window.broadcastMessageHandler) {
      broadcastChannel.removeEventListener(
        'message',
        window.broadcastMessageHandler as EventListener,
      );
      window.broadcastMessageHandler = undefined;
    }
  },
}));

export default useAuthStore;
