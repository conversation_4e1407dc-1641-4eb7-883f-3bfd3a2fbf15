import { StateCreator } from 'zustand';
import React from 'react';
import { StoreState } from '@/store';

export interface DrawerSlice {
  isOpen: boolean;
  ContentComponent: React.ComponentType<any> | null;
  contentProps: Record<string, any>;
  handleOk: (() => void) | undefined;

  openDrawer: (
    Component: React.ComponentType<any>,
    props?: Record<string, any>,
    handleOk?: () => void
  ) => void;

  closeDrawer: () => void;
}

export const createDrawerSlice: StateCreator<
  StoreState,
  [['zustand/immer', never]],
  [],
  DrawerSlice
> = (set) => ({
  isOpen: false,
  ContentComponent: null,
  contentProps: {},
  handleOk: undefined,

  openDrawer: (Component, props = {}, handleOk) => {
    set((state) => {
      state.drawer.isOpen = true;
      state.drawer.ContentComponent = Component;
      state.drawer.contentProps = props;
      state.drawer.handleOk = handleOk;
    });
  },

  closeDrawer: () => {
    set((state) => {
      state.drawer.isOpen = false;
      state.drawer.ContentComponent = null;
      state.drawer.contentProps = {};
      state.drawer.handleOk = undefined;
    });
  },
});

