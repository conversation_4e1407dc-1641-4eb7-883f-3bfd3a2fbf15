import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { createDrawerSlice, DrawerSlice } from './drawerSlice';
import { createPermissionSlice, PermissionSlice } from './permissionSlice';
import { createErrorSlice, ErrorSlice } from './errorSlice'; 
import useAuthStore from './authStore';
import { StateCreator } from 'zustand/vanilla';

export interface StoreState {
  drawer: DrawerSlice;
  permission: PermissionSlice;
  error: ErrorSlice; 
}

type StoreCreator = StateCreator<
  StoreState,
  [['zustand/immer', never]],
  [],
  StoreState
>;

const store: StoreCreator = (set, get, api) => ({
  drawer: createDrawerSlice(set, get, api),
  permission: createPermissionSlice(set, get, api),
  error: createErrorSlice(set, get, api),
});

export const useStore = create<StoreState>()(immer(store));
export const authStore = useAuthStore;
