import { create } from 'zustand';

export interface EdgeDeviceStore {
  loadingState: {
    powerState: boolean;
    activeInterface: boolean;
    apiOnline: boolean;
    customerProvidedIp: boolean;
    registered: boolean;
    monitor: boolean;
    modemInfo: boolean;
    datacenter: boolean;
    sensorData: boolean;
    lanIp: boolean;
    publicIp: boolean;
    deviceOnline: boolean;
    signalStrength: boolean;
    networkInfo: boolean;
    wifiStatus: boolean;
  };
  setLoadingState: (search: string) => void;
  resetLoadingState: () => void;
}
const initialLoadingState = {
  powerState: true,
  activeInterface: true,
  apiOnline: true,
  customerProvidedIp: true,
  registered: true,
  monitor: true,
  modemInfo: true,
  datacenter: true,
  sensorData: true,
  lanIp: true,
  publicIp: true,
  deviceOnline: true,
  signalStrength: true,
  networkInfo: true,
  wifiStatus: true,
};
export const useEdgeDeviceStore = create<EdgeDeviceStore>((set, get) => ({
  loadingState: initialLoadingState,

  setLoadingState: (key: string) => {
    if (!(key in get().loadingState)) return;
    set((state) => ({
      loadingState: {
        ...state.loadingState,
        [key]: false,
      },
    }));
  },
  resetLoadingState: () => {
    set(() => ({
      loadingState: initialLoadingState,
    }));
  },
}));
