import { StateCreator } from 'zustand';
import { StoreState } from './index';

export interface ErrorSlice {
  errorState: Record<string, string | null>;
  setErrorState: (key: string, message: string | null) => void;
  resetErrorState: (keys?: string[]) => void;
}

export const createErrorSlice: StateCreator<
  StoreState & { error: ErrorSlice },
  [['zustand/immer', never]],
  [],
  ErrorSlice
> = (set, get) => ({
  errorState: {},

  setErrorState: (key, message) => {
    set((state) => {
      state.error.errorState[key] = message;
    });
  },

  resetErrorState: (keys) => {
    set((state) => {
      if (!keys) {
        state.error.errorState = {};
      } else {
        for (const key of keys) {
          delete state.error.errorState[key];
        }
      }
    });
  },
});
