import { create } from 'zustand';

export interface EpiStore {
  loadingState: {
    registrationMeta: boolean;
    portPhysicalMeta: boolean;
  };
  setLoadingState: (search: string) => void;
  resetLoadingState: () => void;
}

const initialEpiLoadingState = {
  registrationMeta: true,
  portPhysicalMeta: true,
};

export const useEpiStore = create<EpiStore>((set, get) => ({
  loadingState: initialEpiLoadingState,

  setLoadingState: (key) => {
    if (!(key in get().loadingState)) return;
    set((state) => ({
      loadingState: {
        ...state.loadingState,
        [key]: false,
      },
    }));
  },

  resetLoadingState: () => {
    set(() => ({
      loadingState: initialEpiLoadingState,
    }));
  },
}));
