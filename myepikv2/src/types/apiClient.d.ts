import { TypedDocumentNode } from '@graphql-typed-document-node/core';
import { AxiosInstance } from 'axios';

interface ApiClientOptions {
  timeout?: number;
  headers?: Record<string, string>;
  withCredentials?: boolean;
}

interface ApiRequestOptions {
  api?: string;
  endpoint: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  body?: any | FormData;
  headers?: Record<string, string>;
  timeout?: number;
  withCredentials?: boolean;
  onUploadProgress?: (progressEvent: any) => void;
  params?: Record<string, any>;
  raw?: boolean;
  signal?: AbortSignal;
  responseType?: 'json' | 'text' | 'blob' | 'arraybuffer';
}

interface ApiClientMap {
  [key: string]: AxiosInstance;
}

interface ApiResponse<T> {
  status: 'success' | 'failed';
  data?: T;
  message?: string;
}

interface GraphQLResponse<T = any> {
  data: T;
  message: string;
  errors?: Array<{
    message: string;
    [key: string]: any;
  }>;
}

interface GraphQLQueryOptions {
  query: TypedDocumentNode<TResult, TVariables>;
  variables?: Record<string, any>;
  api?: string;
  queryKey?: QueryKey;
  enabled?: boolean;
  staleTime?: number;
  cacheTime?: number; // For backward compatibility
  gcTime?: number; // v4 naming
  retry?: number | boolean;
  refetchOnWindowFocus?: boolean;
  onSuccess?: (data: any) => void;
  onError?: (error: string) => void;
  select?: (data: any) => any;
  keepPreviousData: boolean,
}
