/* eslint-disable */
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  /** The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf). */
  JSON: { input: any; output: any; }
  /** The `JSONObject` scalar type represents JSON objects as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf). */
  JSONObject: { input: any; output: any; }
  /** Mongo object id scalar type */
  OID: { input: any; output: any; }
  /** The javascript `Date` as integer. Type represents date and time as number of milliseconds from start of UNIX epoch. */
  Timestamp: { input: any; output: any; }
};

export type ActiveLteSim = {
  __typename?: 'ActiveLTESim';
  iccid?: Maybe<Scalars['String']['output']>;
  imei?: Maybe<Scalars['String']['output']>;
  ip?: Maybe<Scalars['String']['output']>;
  mtn?: Maybe<Scalars['String']['output']>;
  sim?: Maybe<Scalars['String']['output']>;
  timeStamp?: Maybe<Scalars['Timestamp']['output']>;
};

export type AlarmRelayDocument = {
  __typename?: 'AlarmRelayDocument';
  _id: Scalars['ID']['output'];
  companionPort?: Maybe<Scalars['String']['output']>;
  deleted?: Maybe<Scalars['Boolean']['output']>;
  isEnabled?: Maybe<Scalars['Boolean']['output']>;
  isTestPassed?: Maybe<Scalars['Boolean']['output']>;
  isTestStarted?: Maybe<Scalars['Boolean']['output']>;
  operationMode?: Maybe<Scalars['String']['output']>;
  protoclSelection?: Maybe<Scalars['String']['output']>;
  timerSession?: Maybe<Scalars['OID']['output']>;
};

export type ArpTableOptions = {
  __typename?: 'ArpTableOptions';
  /** List of IP addresses in the ARP table */
  keys?: Maybe<Array<Scalars['String']['output']>>;
  /** List of MAC addresses corresponding to the IPs in the ARP table */
  values?: Maybe<Array<Scalars['String']['output']>>;
};

export type BoxFirmware = {
  __typename?: 'BoxFirmware';
  version?: Maybe<Scalars['String']['output']>;
};

export enum BoxRegistrarEnum {
  Unified = 'Unified',
  MidRegistrar = 'midRegistrar'
}

export type CasSettingsDocument = {
  __typename?: 'CASSettingsDocument';
  authType?: Maybe<Scalars['String']['output']>;
  bChannel?: Maybe<Scalars['String']['output']>;
  casFailovers?: Maybe<Array<Scalars['String']['output']>>;
  channelRange?: Maybe<Scalars['String']['output']>;
  channels?: Maybe<Scalars['String']['output']>;
  e911Number?: Maybe<Scalars['String']['output']>;
  echoCancellation?: Maybe<Scalars['Boolean']['output']>;
  echoCancellationType?: Maybe<Scalars['String']['output']>;
  framing?: Maybe<Scalars['String']['output']>;
  ip?: Maybe<Scalars['String']['output']>;
  lineCode?: Maybe<Scalars['String']['output']>;
  numberSettings?: Maybe<NumberSettings>;
  password?: Maybe<Scalars['String']['output']>;
  shippedAddress?: Maybe<ShippedAddress>;
  switchType?: Maybe<Scalars['String']['output']>;
  timeAndSource?: Maybe<Scalars['String']['output']>;
  username?: Maybe<Scalars['String']['output']>;
};

export enum CarrierEnum {
  Inteliquent = 'INTELIQUENT',
  IqTollfree = 'IQ_TOLLFREE',
  NonepikLine = 'NONEPIK_LINE',
  Skyt = 'SKYT',
  Tdm = 'TDM',
  Telnyx = 'TELNYX',
  VerizonSip = 'VERIZON_SIP',
  VerizonTdm = 'VERIZON_TDM',
  Voicetel = 'VOICETEL'
}

export type CdmBoxStatus = {
  __typename?: 'CdmBoxStatus';
  Duration?: Maybe<Scalars['String']['output']>;
  LastCdmCheck?: Maybe<Scalars['String']['output']>;
  Status?: Maybe<Scalars['String']['output']>;
};

export type CompanyDocument = {
  __typename?: 'CompanyDocument';
  _id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
};

export type CpuInfo = {
  __typename?: 'CpuInfo';
  CPUCount?: Maybe<Scalars['String']['output']>;
  Guest?: Maybe<Scalars['String']['output']>;
  GuestNice?: Maybe<Scalars['String']['output']>;
  Idle?: Maybe<Scalars['String']['output']>;
  Iowait?: Maybe<Scalars['String']['output']>;
  Nice?: Maybe<Scalars['String']['output']>;
  Softirq?: Maybe<Scalars['String']['output']>;
  StatCount?: Maybe<Scalars['String']['output']>;
  Steal?: Maybe<Scalars['String']['output']>;
  System?: Maybe<Scalars['String']['output']>;
  Total?: Maybe<Scalars['String']['output']>;
  User?: Maybe<Scalars['String']['output']>;
  irq?: Maybe<Scalars['String']['output']>;
};

export type DcAvgPingDocument = {
  __typename?: 'DcAvgPingDocument';
  atPingAvg?: Maybe<Scalars['String']['output']>;
  bestDC?: Maybe<Scalars['String']['output']>;
  bestLatency?: Maybe<Scalars['String']['output']>;
  chPingAvg?: Maybe<Scalars['String']['output']>;
  dlPingAvg?: Maybe<Scalars['String']['output']>;
  error?: Maybe<Scalars['String']['output']>;
  laPingAvg?: Maybe<Scalars['String']['output']>;
  nyPingAvg?: Maybe<Scalars['String']['output']>;
  timeUpdated?: Maybe<Scalars['String']['output']>;
};

export type DcConnectionStatsResponse = {
  __typename?: 'DcConnectionStatsResponse';
  AT?: Maybe<Scalars['JSONObject']['output']>;
  CH?: Maybe<Scalars['JSONObject']['output']>;
  DL?: Maybe<Scalars['JSONObject']['output']>;
  LA?: Maybe<Scalars['JSONObject']['output']>;
  NY?: Maybe<Scalars['JSONObject']['output']>;
  Timestamp?: Maybe<Scalars['String']['output']>;
};

export type DeviceFirmwareVersion = {
  __typename?: 'DeviceFirmwareVersion';
  version?: Maybe<Scalars['String']['output']>;
};

export type DiskIo = {
  __typename?: 'DiskIo';
  Name?: Maybe<Scalars['String']['output']>;
  ReadsCompleted?: Maybe<Scalars['String']['output']>;
  WritesCompleted?: Maybe<Scalars['String']['output']>;
};

export type DiskUsage = {
  __typename?: 'DiskUsage';
  all?: Maybe<Scalars['String']['output']>;
  avail?: Maybe<Scalars['String']['output']>;
  error?: Maybe<Scalars['String']['output']>;
  free?: Maybe<Scalars['String']['output']>;
  size?: Maybe<Scalars['String']['output']>;
  used?: Maybe<Scalars['String']['output']>;
};

export enum E911CarrierEnum {
  Bandwidth = 'BANDWIDTH',
  Voicetel = 'VOICETEL'
}

export type E911Info = {
  __typename?: 'E911Info';
  address1?: Maybe<Scalars['String']['output']>;
  address2?: Maybe<Scalars['String']['output']>;
  callername?: Maybe<Scalars['String']['output']>;
  city?: Maybe<Scalars['String']['output']>;
  country?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['Timestamp']['output']>;
  locationid?: Maybe<Scalars['String']['output']>;
  state?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['Timestamp']['output']>;
  vendor?: Maybe<Scalars['String']['output']>;
  zip?: Maybe<Scalars['String']['output']>;
};

export type EdgeDeviceDetails = {
  __typename?: 'EdgeDeviceDetails';
  OBEnable?: Maybe<Scalars['Boolean']['output']>;
  Obis?: Maybe<Array<Scalars['OID']['output']>>;
  _id: Scalars['ID']['output'];
  activeCarrier?: Maybe<Scalars['String']['output']>;
  activeInterface?: Maybe<Scalars['String']['output']>;
  activeLTE?: Maybe<Array<ActiveLteSim>>;
  advancedRouting?: Maybe<Scalars['Boolean']['output']>;
  apuType?: Maybe<Scalars['String']['output']>;
  assignedTo?: Maybe<Scalars['OID']['output']>;
  boxOfflineCallForward?: Maybe<Scalars['Boolean']['output']>;
  boxRegistrar?: Maybe<BoxRegistrarEnum>;
  companyDoc?: Maybe<CompanyDocument>;
  creationDate?: Maybe<Scalars['Timestamp']['output']>;
  currentApn?: Maybe<Scalars['String']['output']>;
  customerProvidedIp?: Maybe<Scalars['String']['output']>;
  datacenter?: Maybe<Scalars['String']['output']>;
  dcAvgPing?: Maybe<DcAvgPingDocument>;
  dcConnectionStats?: Maybe<DcConnectionStatsResponse>;
  deleted?: Maybe<Scalars['Boolean']['output']>;
  deviceOnline?: Maybe<Scalars['Boolean']['output']>;
  displayName?: Maybe<Scalars['String']['output']>;
  dnsCheck?: Maybe<Scalars['Boolean']['output']>;
  e911Number?: Maybe<Scalars['String']['output']>;
  epiTimezone?: Maybe<Scalars['String']['output']>;
  epikUpdateStatus?: Maybe<Scalars['Boolean']['output']>;
  eth3Disable?: Maybe<Scalars['Boolean']['output']>;
  ext_9override?: Maybe<Scalars['Boolean']['output']>;
  features?: Maybe<Features>;
  fwVersion?: Maybe<Scalars['String']['output']>;
  isDemoBox?: Maybe<Scalars['Boolean']['output']>;
  lanIp?: Maybe<Scalars['String']['output']>;
  liveActiveInterface?: Maybe<Scalars['String']['output']>;
  liveCurrentApn?: Maybe<Scalars['String']['output']>;
  liveDcAvgPing?: Maybe<DcAvgPingDocument>;
  liveDcConnectionStats?: Maybe<DcConnectionStatsResponse>;
  liveDeviceOnline?: Maybe<Scalars['Boolean']['output']>;
  liveDnsCheck?: Maybe<Scalars['Boolean']['output']>;
  liveEpikUpdateStatus?: Maybe<Scalars['Boolean']['output']>;
  liveEpis?: Maybe<EpiEntry>;
  liveLanIp?: Maybe<Scalars['String']['output']>;
  liveLteAnalyzer?: Maybe<Scalars['JSONObject']['output']>;
  liveModemInfo?: Maybe<ModemInfoDocument>;
  liveNetworkInfo?: Maybe<NetworkInfo>;
  liveNightlyUpdateTime?: Maybe<Scalars['Timestamp']['output']>;
  livePortForwardList?: Maybe<Array<PortForwardObj>>;
  livePowerState?: Maybe<Scalars['String']['output']>;
  livePreferredProviderTest?: Maybe<FetchLtePerf>;
  livePrimarySim?: Maybe<Scalars['String']['output']>;
  livePriorityInterface?: Maybe<Scalars['String']['output']>;
  livePublicIp?: Maybe<Scalars['String']['output']>;
  liveRegistered?: Maybe<Scalars['Boolean']['output']>;
  liveSensorData?: Maybe<SensorData>;
  liveSignalStrength?: Maybe<Scalars['String']['output']>;
  liveSimStatus?: Maybe<Scalars['String']['output']>;
  liveSpeedTestData?: Maybe<SpeedTest>;
  liveSpeedTestVoice?: Maybe<SpeedTest>;
  liveSysInfo?: Maybe<SystemInfoDocument>;
  liveVSwitchTab?: Maybe<VSwitchTab>;
  liveWifiStatus?: Maybe<WifiStatus>;
  locRef?: Maybe<Scalars['OID']['output']>;
  locationDoc?: Maybe<LocationDocument>;
  lteAnalyzer?: Maybe<Scalars['JSONObject']['output']>;
  lteIp?: Maybe<Scalars['String']['output']>;
  lteIp2?: Maybe<Scalars['String']['output']>;
  model?: Maybe<Scalars['String']['output']>;
  modemInfo?: Maybe<ModemInfoDocument>;
  modems?: Maybe<Array<Modem>>;
  monitor?: Maybe<Scalars['Boolean']['output']>;
  myEpik?: Maybe<Scalars['Boolean']['output']>;
  networkInfo?: Maybe<NetworkInfo>;
  nightlyUpdateTime?: Maybe<Scalars['Timestamp']['output']>;
  numPorts?: Maybe<Scalars['Float']['output']>;
  numberDoc?: Maybe<NumberDocument>;
  obiDocs?: Maybe<Array<EpiDocumentPopulated>>;
  phoneDocs?: Maybe<Array<PhoneDocument>>;
  phones?: Maybe<Array<Scalars['OID']['output']>>;
  portAlerts?: Maybe<Scalars['Boolean']['output']>;
  portForwardList?: Maybe<Array<PortForwardObj>>;
  powerSaveOption?: Maybe<Scalars['Boolean']['output']>;
  powerState?: Maybe<Scalars['String']['output']>;
  preferredProviderTest?: Maybe<FetchLtePerf>;
  priSettings?: Maybe<PriSettingsDocument>;
  primarySim?: Maybe<Scalars['String']['output']>;
  priorityInterface?: Maybe<Scalars['String']['output']>;
  publicIp?: Maybe<Scalars['String']['output']>;
  recording?: Maybe<Scalars['Boolean']['output']>;
  registered?: Maybe<Scalars['Boolean']['output']>;
  removedObis?: Maybe<Array<Scalars['OID']['output']>>;
  sensorData?: Maybe<SensorData>;
  serialNumber?: Maybe<Scalars['String']['output']>;
  signalStrength?: Maybe<Scalars['String']['output']>;
  simStatus?: Maybe<Scalars['String']['output']>;
  sipSettings?: Maybe<CasSettingsDocument>;
  speedTestData?: Maybe<SpeedTest>;
  speedTestVoice?: Maybe<SpeedTest>;
  starCodes?: Maybe<Scalars['Boolean']['output']>;
  sysInfo?: Maybe<SystemInfoDocument>;
  vSwitchTab?: Maybe<VSwitchTab>;
  vpnAddress?: Maybe<Scalars['String']['output']>;
  wifiStatus?: Maybe<WifiStatus>;
};

export type EpiDocumentPopulated = {
  __typename?: 'EpiDocumentPopulated';
  _id: Scalars['ID']['output'];
  assignedTo?: Maybe<Scalars['OID']['output']>;
  deviceId: Scalars['String']['output'];
  livePortPhysicalMeta?: Maybe<Array<PortPhysicalMeta>>;
  liveRegistrationMeta?: Maybe<EpiRegistrationMeta>;
  macAddress?: Maybe<Scalars['String']['output']>;
  obiNumber?: Maybe<Scalars['String']['output']>;
  port1?: Maybe<EpiPortPopulated>;
  port2?: Maybe<EpiPortPopulated>;
  portPhysicalMeta?: Maybe<Array<PortPhysicalMeta>>;
  registrationMeta?: Maybe<EpiRegistrationMeta>;
};

export type EpiDocumentPopulatedPaginationResult = {
  __typename?: 'EpiDocumentPopulatedPaginationResult';
  /** Array of documents */
  docs: Array<EpiDocumentPopulated>;
  pagination: PaginationResponseOptions;
};

export type EpiEntry = {
  __typename?: 'EpiEntry';
  data: Scalars['JSON']['output'];
};

export type EpiFilterInput = {
  macAddress?: InputMaybe<Scalars['String']['input']>;
  obiNumber?: InputMaybe<Scalars['String']['input']>;
};

export type EpiInfo = {
  __typename?: 'EpiInfo';
  fwVersion?: Maybe<Scalars['String']['output']>;
  ip?: Maybe<Scalars['String']['output']>;
  mac?: Maybe<Scalars['String']['output']>;
  provUrl?: Maybe<Scalars['String']['output']>;
  uptime?: Maybe<Scalars['String']['output']>;
};

export type EpiPortPopulated = {
  __typename?: 'EpiPortPopulated';
  CPCDelayTime?: Maybe<Scalars['String']['output']>;
  CPCDuration?: Maybe<Scalars['String']['output']>;
  ChannelRxGain?: Maybe<Scalars['String']['output']>;
  ChannelTxGain?: Maybe<Scalars['String']['output']>;
  DTMFDetectMinGap?: Maybe<Scalars['String']['output']>;
  DTMFDetectMinLength?: Maybe<Scalars['String']['output']>;
  DTMFMethod?: Maybe<Scalars['String']['output']>;
  DTMFPlaybackLevel?: Maybe<Scalars['String']['output']>;
  DigitMapShortTimer?: Maybe<Scalars['String']['output']>;
  JitterMaxDelay?: Maybe<Scalars['String']['output']>;
  JitterMinDelay?: Maybe<Scalars['String']['output']>;
  OffHookCurrentMax?: Maybe<Scalars['String']['output']>;
  OnHookTipRingVoltage?: Maybe<Scalars['String']['output']>;
  RingVoltage?: Maybe<Scalars['String']['output']>;
  SilenceDetectSensitivity?: Maybe<SilenceDetectSensitivityEnum>;
  advancedModemConfiguration?: Maybe<Scalars['Boolean']['output']>;
  appliedConfigTemplate?: Maybe<Scalars['OID']['output']>;
  appliedConfigTemplateDoc?: Maybe<PortConfigTemplateDocument>;
  assignedNumber?: Maybe<Scalars['String']['output']>;
  assignedNumberDoc?: Maybe<NumberDocumentPopulated>;
  assignedNumberRef?: Maybe<Scalars['OID']['output']>;
  authName?: Maybe<Scalars['String']['output']>;
  blockCallerId?: Maybe<Scalars['Boolean']['output']>;
  boxPortNumber?: Maybe<Scalars['String']['output']>;
  callWaiting?: Maybe<Scalars['Boolean']['output']>;
  callerIdMasking?: Maybe<Scalars['Boolean']['output']>;
  callerIdMaskingNumber?: Maybe<Scalars['String']['output']>;
  callerIdOverride?: Maybe<Scalars['String']['output']>;
  configOverride?: Maybe<Scalars['Boolean']['output']>;
  configPassword?: Maybe<Scalars['String']['output']>;
  connected?: Maybe<Scalars['Boolean']['output']>;
  disabled?: Maybe<Scalars['Boolean']['output']>;
  e911Enabled?: Maybe<Scalars['Boolean']['output']>;
  e911Number?: Maybe<Scalars['String']['output']>;
  enableDisableLastUpdatedTime?: Maybe<Scalars['Timestamp']['output']>;
  faxEnabled?: Maybe<Scalars['Boolean']['output']>;
  forceForward?: Maybe<Scalars['Boolean']['output']>;
  forceForwardNumber?: Maybe<Scalars['String']['output']>;
  ipAddress?: Maybe<Scalars['String']['output']>;
  modemMode?: Maybe<Scalars['Boolean']['output']>;
  modemOnly?: Maybe<Scalars['Boolean']['output']>;
  monitored?: Maybe<Scalars['Boolean']['output']>;
  portActivated?: Maybe<Scalars['Boolean']['output']>;
  portActivatedDate?: Maybe<Scalars['Timestamp']['output']>;
  provisionDate?: Maybe<Scalars['Timestamp']['output']>;
  provisionUrl?: Maybe<Scalars['String']['output']>;
  registered?: Maybe<Scalars['Boolean']['output']>;
  remoteHost?: Maybe<Scalars['String']['output']>;
  remotePort?: Maybe<Scalars['String']['output']>;
  serviceName?: Maybe<Scalars['String']['output']>;
  showFaxEnabledToggle?: Maybe<Scalars['Boolean']['output']>;
  showJitterDelay?: Maybe<Scalars['Boolean']['output']>;
  showModemModeToggle?: Maybe<Scalars['Boolean']['output']>;
  showModemOnlyToggle?: Maybe<Scalars['Boolean']['output']>;
  showT38EnableToggle?: Maybe<Scalars['Boolean']['output']>;
  showVadToggle?: Maybe<Scalars['Boolean']['output']>;
  startedBilling?: Maybe<Scalars['Boolean']['output']>;
  startedBillingDate?: Maybe<Scalars['Timestamp']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  t38Enabled?: Maybe<Scalars['Boolean']['output']>;
  updatedOn?: Maybe<Scalars['Timestamp']['output']>;
  vmBox?: Maybe<Scalars['OID']['output']>;
  vmNumber?: Maybe<Scalars['String']['output']>;
  vmailBoxDoc?: Maybe<VmailBoxDocument>;
  voip?: Maybe<Scalars['Boolean']['output']>;
};

export type EpiRegistrationMeta = {
  __typename?: 'EpiRegistrationMeta';
  obiTalkServiceStatus?: Maybe<ObiTalkServiceStatus>;
  sp1ServiceStatus?: Maybe<Sp1erviceStatus>;
  sp2ServiceStatus?: Maybe<Sp2erviceStatus>;
  wanInfo?: Maybe<WanInfo>;
};

export type EpikBoxDocumentPopulated = {
  __typename?: 'EpikBoxDocumentPopulated';
  OBEnable?: Maybe<Scalars['Boolean']['output']>;
  Obis?: Maybe<Array<Scalars['OID']['output']>>;
  _id: Scalars['ID']['output'];
  activeCarrier?: Maybe<Scalars['String']['output']>;
  activeInterface?: Maybe<Scalars['String']['output']>;
  activeLTE?: Maybe<Array<ActiveLteSim>>;
  advancedRouting?: Maybe<Scalars['Boolean']['output']>;
  apuType?: Maybe<Scalars['String']['output']>;
  assignedTo?: Maybe<Scalars['OID']['output']>;
  boxOfflineCallForward?: Maybe<Scalars['Boolean']['output']>;
  boxRegistrar?: Maybe<BoxRegistrarEnum>;
  companyDoc?: Maybe<CompanyDocument>;
  creationDate?: Maybe<Scalars['Timestamp']['output']>;
  currentApn?: Maybe<Scalars['String']['output']>;
  customerProvidedIp?: Maybe<Scalars['String']['output']>;
  datacenter?: Maybe<Scalars['String']['output']>;
  dcAvgPing?: Maybe<DcAvgPingDocument>;
  dcConnectionStats?: Maybe<DcConnectionStatsResponse>;
  deleted?: Maybe<Scalars['Boolean']['output']>;
  deviceOnline?: Maybe<Scalars['Boolean']['output']>;
  displayName?: Maybe<Scalars['String']['output']>;
  dnsCheck?: Maybe<Scalars['Boolean']['output']>;
  e911Number?: Maybe<Scalars['String']['output']>;
  epiTimezone?: Maybe<Scalars['String']['output']>;
  epikUpdateStatus?: Maybe<Scalars['Boolean']['output']>;
  eth3Disable?: Maybe<Scalars['Boolean']['output']>;
  ext_9override?: Maybe<Scalars['Boolean']['output']>;
  features?: Maybe<Features>;
  fwVersion?: Maybe<Scalars['String']['output']>;
  isDemoBox?: Maybe<Scalars['Boolean']['output']>;
  lanIp?: Maybe<Scalars['String']['output']>;
  locRef?: Maybe<Scalars['OID']['output']>;
  locationDoc?: Maybe<LocationDocument>;
  lteAnalyzer?: Maybe<Scalars['JSONObject']['output']>;
  lteIp?: Maybe<Scalars['String']['output']>;
  lteIp2?: Maybe<Scalars['String']['output']>;
  model?: Maybe<Scalars['String']['output']>;
  modemInfo?: Maybe<ModemInfoDocument>;
  modems?: Maybe<Array<Modem>>;
  monitor?: Maybe<Scalars['Boolean']['output']>;
  myEpik?: Maybe<Scalars['Boolean']['output']>;
  networkInfo?: Maybe<NetworkInfo>;
  nightlyUpdateTime?: Maybe<Scalars['Timestamp']['output']>;
  numPorts?: Maybe<Scalars['Float']['output']>;
  numberDoc?: Maybe<NumberDocument>;
  obiDocs?: Maybe<Array<EpiDocumentPopulated>>;
  phoneDocs?: Maybe<Array<PhoneDocument>>;
  phones?: Maybe<Array<Scalars['OID']['output']>>;
  portAlerts?: Maybe<Scalars['Boolean']['output']>;
  portForwardList?: Maybe<Array<PortForwardObj>>;
  powerSaveOption?: Maybe<Scalars['Boolean']['output']>;
  powerState?: Maybe<Scalars['String']['output']>;
  preferredProviderTest?: Maybe<FetchLtePerf>;
  priSettings?: Maybe<PriSettingsDocument>;
  primarySim?: Maybe<Scalars['String']['output']>;
  priorityInterface?: Maybe<Scalars['String']['output']>;
  publicIp?: Maybe<Scalars['String']['output']>;
  recording?: Maybe<Scalars['Boolean']['output']>;
  registered?: Maybe<Scalars['Boolean']['output']>;
  removedObis?: Maybe<Array<Scalars['OID']['output']>>;
  sensorData?: Maybe<SensorData>;
  serialNumber?: Maybe<Scalars['String']['output']>;
  signalStrength?: Maybe<Scalars['String']['output']>;
  simStatus?: Maybe<Scalars['String']['output']>;
  sipSettings?: Maybe<CasSettingsDocument>;
  speedTestData?: Maybe<SpeedTest>;
  speedTestVoice?: Maybe<SpeedTest>;
  starCodes?: Maybe<Scalars['Boolean']['output']>;
  sysInfo?: Maybe<SystemInfoDocument>;
  vSwitchTab?: Maybe<VSwitchTab>;
  vpnAddress?: Maybe<Scalars['String']['output']>;
  wifiStatus?: Maybe<WifiStatus>;
};

export type EpikBoxDocumentPopulatedPaginationResult = {
  __typename?: 'EpikBoxDocumentPopulatedPaginationResult';
  /** Array of documents */
  docs: Array<EpikBoxDocumentPopulated>;
  pagination: PaginationResponseOptions;
};

export type EpikBoxFilterInput = {
  assignedNumber?: InputMaybe<Array<Scalars['String']['input']>>;
  company?: InputMaybe<Scalars['String']['input']>;
  companyName?: InputMaybe<Scalars['String']['input']>;
  createdon?: InputMaybe<Scalars['String']['input']>;
  displayName?: InputMaybe<Scalars['String']['input']>;
  epiNumber?: InputMaybe<Scalars['String']['input']>;
  ids?: InputMaybe<Array<Scalars['String']['input']>>;
  imei?: InputMaybe<Scalars['String']['input']>;
  isAll?: InputMaybe<Scalars['Boolean']['input']>;
  macAddress?: InputMaybe<Scalars['String']['input']>;
  number?: InputMaybe<Scalars['String']['input']>;
  query?: InputMaybe<Scalars['String']['input']>;
  serialNumber?: InputMaybe<Scalars['String']['input']>;
  shipingNumber?: InputMaybe<Scalars['String']['input']>;
  sim?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
  vpnAddress?: InputMaybe<Scalars['String']['input']>;
};

export type Eth0Config = {
  __typename?: 'Eth0Config';
  nameserver1?: Maybe<Scalars['String']['output']>;
  nameserver2?: Maybe<Scalars['String']['output']>;
  nameserver3?: Maybe<Scalars['String']['output']>;
  nameserver4?: Maybe<Scalars['String']['output']>;
};

export type Eth0Stats = {
  __typename?: 'Eth0Stats';
  eth0IP?: Maybe<Scalars['String']['output']>;
  eth0MAC?: Maybe<Scalars['String']['output']>;
  rxErrors?: Maybe<Scalars['String']['output']>;
  rxPackets?: Maybe<Scalars['String']['output']>;
  txErrors?: Maybe<Scalars['String']['output']>;
  txPackets?: Maybe<Scalars['String']['output']>;
};

export type Features = {
  __typename?: 'Features';
  daisyChain?: Maybe<Scalars['Boolean']['output']>;
  dcAutoUpdate?: Maybe<Scalars['Boolean']['output']>;
  pri?: Maybe<Scalars['Boolean']['output']>;
  showPriTab?: Maybe<Scalars['Boolean']['output']>;
  sip?: Maybe<Scalars['Boolean']['output']>;
  twoModems?: Maybe<Scalars['Boolean']['output']>;
};

export type FetchLtePerf = {
  __typename?: 'FetchLtePerf';
  SimsInfo?: Maybe<Array<SimPingInfo>>;
  TimeStamp?: Maybe<Scalars['String']['output']>;
  initiatedTimeStamp?: Maybe<Scalars['Timestamp']['output']>;
};

export type Icmp = {
  __typename?: 'Icmp';
  eth0?: Maybe<Scalars['String']['output']>;
  wwan0?: Maybe<Scalars['String']['output']>;
};

export type LinkStatus = {
  __typename?: 'LinkStatus';
  epiLink?: Maybe<Scalars['String']['output']>;
  eth02?: Maybe<Scalars['String']['output']>;
};

export type ListNumberInput = {
  query?: InputMaybe<Scalars['String']['input']>;
};

export type LiveFieldUpdate = {
  __typename?: 'LiveFieldUpdate';
  error?: Maybe<Scalars['String']['output']>;
  fieldName: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  value?: Maybe<Scalars['JSON']['output']>;
};

export type LoadAvg = {
  __typename?: 'LoadAvg';
  Loadavg1?: Maybe<Scalars['String']['output']>;
  Loadavg5?: Maybe<Scalars['String']['output']>;
  Loadavg15?: Maybe<Scalars['String']['output']>;
  error?: Maybe<Scalars['String']['output']>;
};

export type LocationDocument = {
  __typename?: 'LocationDocument';
  _id: Scalars['ID']['output'];
  locationName: Scalars['String']['output'];
};

export type LteSignalStrength = {
  __typename?: 'LteSignalStrength';
  signalStrength?: Maybe<Scalars['String']['output']>;
};

export type MemoryInfo = {
  __typename?: 'MemoryInfo';
  Active?: Maybe<Scalars['String']['output']>;
  Available?: Maybe<Scalars['String']['output']>;
  Buffers?: Maybe<Scalars['String']['output']>;
  Cached?: Maybe<Scalars['String']['output']>;
  Free?: Maybe<Scalars['String']['output']>;
  Inactive?: Maybe<Scalars['String']['output']>;
  MemAvailableEnabled?: Maybe<Scalars['String']['output']>;
  SwapCached?: Maybe<Scalars['String']['output']>;
  SwapFree?: Maybe<Scalars['String']['output']>;
  SwapTotal?: Maybe<Scalars['String']['output']>;
  SwapUsed?: Maybe<Scalars['String']['output']>;
  Total?: Maybe<Scalars['String']['output']>;
  Used?: Maybe<Scalars['String']['output']>;
};

export type Modem = {
  __typename?: 'Modem';
  imeis?: Maybe<Scalars['String']['output']>;
  label?: Maybe<Scalars['String']['output']>;
  phones?: Maybe<Array<Scalars['String']['output']>>;
  sims?: Maybe<Array<Scalars['String']['output']>>;
  type?: Maybe<Scalars['String']['output']>;
};

export type ModemInfoDocument = {
  __typename?: 'ModemInfoDocument';
  carrier?: Maybe<Scalars['String']['output']>;
  carrier_placeholder?: Maybe<Scalars['String']['output']>;
  imei?: Maybe<Scalars['String']['output']>;
  imei_placeholder?: Maybe<Scalars['String']['output']>;
  ipAddress?: Maybe<Scalars['String']['output']>;
  ipAddress_placeholder?: Maybe<Scalars['String']['output']>;
  manufacturer?: Maybe<Scalars['String']['output']>;
  manufacturer_placeholder?: Maybe<Scalars['String']['output']>;
  model?: Maybe<Scalars['String']['output']>;
  model_placeholder?: Maybe<Scalars['String']['output']>;
  sim?: Maybe<Scalars['String']['output']>;
  sim_placeholder?: Maybe<Scalars['String']['output']>;
};

export type NetworkInfaceObj = {
  __typename?: 'NetworkInfaceObj';
  icmp?: Maybe<Scalars['String']['output']>;
  interface?: Maybe<Scalars['String']['output']>;
  internet?: Maybe<Scalars['String']['output']>;
  wg?: Maybe<Scalars['String']['output']>;
};

export type NetworkInfo = {
  __typename?: 'NetworkInfo';
  dns?: Maybe<Scalars['String']['output']>;
  error?: Maybe<Scalars['String']['output']>;
  interfaces?: Maybe<Array<NetworkInfaceObj>>;
  timestamp?: Maybe<Scalars['String']['output']>;
};

export type NetworkInfoDetail = {
  __typename?: 'NetworkInfoDetail';
  activeInterface?: Maybe<Scalars['String']['output']>;
  arp?: Maybe<ArpTableOptions>;
  cdmBoxStatus?: Maybe<CdmBoxStatus>;
  dcAvgPing?: Maybe<DcAvgPingDocument>;
  deviceFirmwareVersion?: Maybe<DeviceFirmwareVersion>;
  edgeIP?: Maybe<Scalars['String']['output']>;
  epiInfo?: Maybe<Array<EpiInfo>>;
  eth0Config?: Maybe<Eth0Config>;
  eth0Stats?: Maybe<Eth0Stats>;
  fwVersion?: Maybe<Scalars['String']['output']>;
  icmp?: Maybe<Icmp>;
  ip4G?: Maybe<Scalars['String']['output']>;
  linkStatus?: Maybe<LinkStatus>;
  lteSgnalStrength?: Maybe<Scalars['String']['output']>;
  modemCount?: Maybe<Scalars['String']['output']>;
  powerSource?: Maybe<Scalars['String']['output']>;
  priCardStatus?: Maybe<PriCardStatus>;
  registrationStatus?: Maybe<Scalars['String']['output']>;
  tunnelAccess?: Maybe<TunnelAccess>;
  tunnelIP?: Maybe<Scalars['String']['output']>;
};

export type NetworkInterface = {
  __typename?: 'NetworkInterface';
  Name?: Maybe<Scalars['String']['output']>;
  RxBytes?: Maybe<Scalars['String']['output']>;
  TxBytes?: Maybe<Scalars['String']['output']>;
};

export type NumberDocument = {
  __typename?: 'NumberDocument';
  _id: Scalars['ID']['output'];
  agreementFilePath?: Maybe<Scalars['String']['output']>;
  alarmRelayInfo?: Maybe<Scalars['OID']['output']>;
  allowedNumbers?: Maybe<Array<Scalars['String']['output']>>;
  allowedUsers?: Maybe<Array<Scalars['String']['output']>>;
  assignedTo?: Maybe<Scalars['OID']['output']>;
  callerIdName?: Maybe<Scalars['String']['output']>;
  carrier?: Maybe<CarrierEnum>;
  cid?: Maybe<Scalars['String']['output']>;
  cidOverride?: Maybe<Scalars['String']['output']>;
  company?: Maybe<Scalars['OID']['output']>;
  confirmationEmail?: Maybe<Scalars['String']['output']>;
  dateCreated?: Maybe<Scalars['Timestamp']['output']>;
  deleted?: Maybe<Scalars['Boolean']['output']>;
  destination?: Maybe<Scalars['String']['output']>;
  e911Carrier?: Maybe<E911CarrierEnum>;
  e911Info?: Maybe<E911Info>;
  emails?: Maybe<Array<Scalars['String']['output']>>;
  enabled?: Maybe<Scalars['Boolean']['output']>;
  fallbackEmails?: Maybe<Array<Scalars['String']['output']>>;
  faxDeliveryConfirmation?: Maybe<Scalars['Boolean']['output']>;
  forwardNumbers?: Maybe<Array<Scalars['String']['output']>>;
  forwardUris?: Maybe<Array<Scalars['String']['output']>>;
  greetingFilePath?: Maybe<Scalars['String']['output']>;
  groupMembers?: Maybe<Array<Scalars['String']['output']>>;
  huntPorts?: Maybe<Array<Scalars['String']['output']>>;
  huntType?: Maybe<Scalars['String']['output']>;
  inBoundCallBlocked?: Maybe<Scalars['Boolean']['output']>;
  inbandRouting?: Maybe<Scalars['Boolean']['output']>;
  isForwarded?: Maybe<Scalars['Boolean']['output']>;
  isNumberLocked?: Maybe<Scalars['Boolean']['output']>;
  isTollFree?: Maybe<Scalars['Boolean']['output']>;
  linkedBox?: Maybe<Scalars['OID']['output']>;
  linkedToExternalAta?: Maybe<Scalars['Boolean']['output']>;
  linkedToObi?: Maybe<Scalars['Boolean']['output']>;
  numDigits?: Maybe<Scalars['Float']['output']>;
  number?: Maybe<Scalars['String']['output']>;
  onlyEfax?: Maybe<Scalars['Boolean']['output']>;
  port?: Maybe<Scalars['String']['output']>;
  portLabel?: Maybe<Scalars['String']['output']>;
  previousRouteInfo?: Maybe<RouteInfo>;
  previousTrunk?: Maybe<Scalars['String']['output']>;
  provisionDate?: Maybe<Scalars['Timestamp']['output']>;
  routeInfo?: Maybe<RouteInfo>;
  savefaxes?: Maybe<Scalars['Boolean']['output']>;
  spoofIncomingCid?: Maybe<Scalars['Boolean']['output']>;
  spoofType?: Maybe<Scalars['String']['output']>;
  tdmRoute?: Maybe<Scalars['String']['output']>;
  temporary?: Maybe<Scalars['String']['output']>;
  tollFreeNumbers?: Maybe<Array<Scalars['String']['output']>>;
  trunk?: Maybe<Scalars['String']['output']>;
  trunkSelection?: Maybe<Scalars['String']['output']>;
  type: Scalars['String']['output'];
  usbPort?: Maybe<Scalars['OID']['output']>;
};

export type NumberDocumentPaginationResult = {
  __typename?: 'NumberDocumentPaginationResult';
  /** Array of documents */
  docs: Array<NumberDocument>;
  pagination: PaginationResponseOptions;
};

export type NumberDocumentPopulated = {
  __typename?: 'NumberDocumentPopulated';
  _id: Scalars['ID']['output'];
  agreementFilePath?: Maybe<Scalars['String']['output']>;
  alarmRelayInfo?: Maybe<Scalars['OID']['output']>;
  alarmRelayInfoDoc?: Maybe<AlarmRelayDocument>;
  allowedNumbers?: Maybe<Array<Scalars['String']['output']>>;
  allowedUsers?: Maybe<Array<Scalars['String']['output']>>;
  assignedTo?: Maybe<Scalars['OID']['output']>;
  callerIdName?: Maybe<Scalars['String']['output']>;
  carrier?: Maybe<CarrierEnum>;
  cid?: Maybe<Scalars['String']['output']>;
  cidOverride?: Maybe<Scalars['String']['output']>;
  company?: Maybe<Scalars['OID']['output']>;
  confirmationEmail?: Maybe<Scalars['String']['output']>;
  dateCreated?: Maybe<Scalars['Timestamp']['output']>;
  deleted?: Maybe<Scalars['Boolean']['output']>;
  destination?: Maybe<Scalars['String']['output']>;
  e911Carrier?: Maybe<E911CarrierEnum>;
  e911Info?: Maybe<E911Info>;
  emails?: Maybe<Array<Scalars['String']['output']>>;
  enabled?: Maybe<Scalars['Boolean']['output']>;
  fallbackEmails?: Maybe<Array<Scalars['String']['output']>>;
  faxDeliveryConfirmation?: Maybe<Scalars['Boolean']['output']>;
  forwardNumbers?: Maybe<Array<Scalars['String']['output']>>;
  forwardUris?: Maybe<Array<Scalars['String']['output']>>;
  greetingFilePath?: Maybe<Scalars['String']['output']>;
  groupMembers?: Maybe<Array<Scalars['String']['output']>>;
  huntPorts?: Maybe<Array<Scalars['String']['output']>>;
  huntType?: Maybe<Scalars['String']['output']>;
  inBoundCallBlocked?: Maybe<Scalars['Boolean']['output']>;
  inbandRouting?: Maybe<Scalars['Boolean']['output']>;
  isForwarded?: Maybe<Scalars['Boolean']['output']>;
  isNumberLocked?: Maybe<Scalars['Boolean']['output']>;
  isTollFree?: Maybe<Scalars['Boolean']['output']>;
  linkedBox?: Maybe<Scalars['OID']['output']>;
  linkedToExternalAta?: Maybe<Scalars['Boolean']['output']>;
  linkedToObi?: Maybe<Scalars['Boolean']['output']>;
  numDigits?: Maybe<Scalars['Float']['output']>;
  number?: Maybe<Scalars['String']['output']>;
  onlyEfax?: Maybe<Scalars['Boolean']['output']>;
  port?: Maybe<Scalars['String']['output']>;
  portLabel?: Maybe<Scalars['String']['output']>;
  previousRouteInfo?: Maybe<RouteInfo>;
  previousTrunk?: Maybe<Scalars['String']['output']>;
  provisionDate?: Maybe<Scalars['Timestamp']['output']>;
  routeInfo?: Maybe<RouteInfo>;
  savefaxes?: Maybe<Scalars['Boolean']['output']>;
  spoofIncomingCid?: Maybe<Scalars['Boolean']['output']>;
  spoofType?: Maybe<Scalars['String']['output']>;
  tdmRoute?: Maybe<Scalars['String']['output']>;
  temporary?: Maybe<Scalars['String']['output']>;
  tollFreeNumbers?: Maybe<Array<Scalars['String']['output']>>;
  trunk?: Maybe<Scalars['String']['output']>;
  trunkSelection?: Maybe<Scalars['String']['output']>;
  type: Scalars['String']['output'];
  usbPort?: Maybe<Scalars['OID']['output']>;
};

export type NumberSettings = {
  __typename?: 'NumberSettings';
  callerIdName?: Maybe<Scalars['String']['output']>;
  callerIdNumber?: Maybe<Scalars['String']['output']>;
  dnis?: Maybe<Scalars['String']['output']>;
  dtmfType?: Maybe<Scalars['String']['output']>;
  numbers?: Maybe<Array<NumberDocument>>;
};

export type ObiTalkServiceStatus = {
  __typename?: 'ObiTalkServiceStatus';
  placeHolder?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type PriCardStatus = {
  __typename?: 'PRICardStatus';
  priCardStatus?: Maybe<Scalars['String']['output']>;
};

export type PriSettingsDocument = {
  __typename?: 'PRISettingsDocument';
  bChannels?: Maybe<Scalars['String']['output']>;
  channels?: Maybe<Scalars['String']['output']>;
  dChannel?: Maybe<Scalars['String']['output']>;
  e911Number?: Maybe<Scalars['String']['output']>;
  echoCancellation?: Maybe<Scalars['Boolean']['output']>;
  echoCancellationType?: Maybe<Scalars['String']['output']>;
  echoChannels?: Maybe<Scalars['String']['output']>;
  framing?: Maybe<Scalars['String']['output']>;
  isSynced?: Maybe<Scalars['Boolean']['output']>;
  lineCode?: Maybe<Scalars['String']['output']>;
  numberSettings?: Maybe<NumberSettings>;
  pbx?: Maybe<Scalars['Boolean']['output']>;
  priFailovers?: Maybe<Array<Scalars['String']['output']>>;
  shippedAddress?: Maybe<ShippedAddress>;
  switchType?: Maybe<Scalars['String']['output']>;
  timeAndSource?: Maybe<Scalars['String']['output']>;
};

export type PaginationInput = {
  page?: Scalars['Int']['input'];
  pageSize?: Scalars['Int']['input'];
};

export type PaginationResponseOptions = {
  __typename?: 'PaginationResponseOptions';
  count: Scalars['Int']['output'];
  currentPage: Scalars['Int']['output'];
  totalPages: Scalars['Int']['output'];
};

export type PhoneDocument = {
  __typename?: 'PhoneDocument';
  _id: Scalars['ID']['output'];
  displayName?: Maybe<Scalars['String']['output']>;
  extension?: Maybe<Scalars['String']['output']>;
  model?: Maybe<Scalars['String']['output']>;
  number?: Maybe<Scalars['OID']['output']>;
  numberDoc?: Maybe<NumberDocument>;
};

export type PingLine = {
  __typename?: 'PingLine';
  completed?: Maybe<Scalars['Boolean']['output']>;
  line?: Maybe<Scalars['String']['output']>;
};

export type PortConfigTemplateDocument = {
  __typename?: 'PortConfigTemplateDocument';
  CPCDelayTime: Scalars['String']['output'];
  CPCDuration: Scalars['String']['output'];
  ChannelRxGain: Scalars['String']['output'];
  ChannelTxGain: Scalars['String']['output'];
  DTMFDetectMinGap: Scalars['String']['output'];
  DTMFDetectMinLength: Scalars['String']['output'];
  DTMFMethod: Scalars['String']['output'];
  DTMFPlaybackLevel: Scalars['String']['output'];
  DigitMapShortTimer: Scalars['String']['output'];
  JitterMaxDelay?: Maybe<Scalars['String']['output']>;
  JitterMinDelay?: Maybe<Scalars['String']['output']>;
  OffHookCurrentMax: Scalars['String']['output'];
  OnHookTipRingVoltage: Scalars['String']['output'];
  RingVoltage: Scalars['String']['output'];
  SilenceDetectSensitivity: Scalars['String']['output'];
  _id: Scalars['ID']['output'];
  ataType?: Maybe<Scalars['String']['output']>;
  default: Scalars['Boolean']['output'];
  faxEnabled: Scalars['Boolean']['output'];
  inbandRoute?: Maybe<Scalars['Boolean']['output']>;
  modemMode: Scalars['Boolean']['output'];
  modemOnly?: Maybe<Scalars['Boolean']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  showFaxEnabledToggle: Scalars['Boolean']['output'];
  showInbandRouteToggle?: Maybe<Scalars['Boolean']['output']>;
  showJitterDelay?: Maybe<Scalars['Boolean']['output']>;
  showModemModeToggle: Scalars['Boolean']['output'];
  showModemOnlyToggle?: Maybe<Scalars['Boolean']['output']>;
  showT38EnableToggle: Scalars['Boolean']['output'];
  showTrunkSelectionToggle?: Maybe<Scalars['Boolean']['output']>;
  showVadToggle?: Maybe<Scalars['Boolean']['output']>;
  t38Enabled: Scalars['Boolean']['output'];
  title?: Maybe<Scalars['String']['output']>;
  trunkSelection?: Maybe<Scalars['String']['output']>;
  vadEnable?: Maybe<Scalars['Boolean']['output']>;
};

export type PortForwardObj = {
  __typename?: 'PortForwardObj';
  dstEndPort?: Maybe<Scalars['Float']['output']>;
  dstIP?: Maybe<Scalars['String']['output']>;
  dstStartPort?: Maybe<Scalars['Float']['output']>;
  proto?: Maybe<Scalars['String']['output']>;
  srcEndPort?: Maybe<Scalars['Float']['output']>;
  srcIP?: Maybe<Scalars['String']['output']>;
  srcStartPort?: Maybe<Scalars['Float']['output']>;
};

export type PortInfo = {
  __typename?: 'PortInfo';
  calledId?: Maybe<Scalars['String']['output']>;
  port?: Maybe<Scalars['String']['output']>;
  recording?: Maybe<Scalars['String']['output']>;
  trunkType?: Maybe<Scalars['String']['output']>;
};

export type PortPhysicalMeta = {
  __typename?: 'PortPhysicalMeta';
  Vbat?: Maybe<Scalars['String']['output']>;
  lastCallerInfo?: Maybe<Scalars['String']['output']>;
  loopCurrent?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  state?: Maybe<Scalars['String']['output']>;
  tipRingVoltage?: Maybe<Scalars['String']['output']>;
};

export type PortStatuses = {
  __typename?: 'PortStatuses';
  eth0?: Maybe<Scalars['String']['output']>;
  eth1?: Maybe<Scalars['String']['output']>;
  eth2?: Maybe<Scalars['String']['output']>;
  wg0?: Maybe<Scalars['String']['output']>;
  wg7?: Maybe<Scalars['String']['output']>;
  wlan0?: Maybe<Scalars['String']['output']>;
  wwan0?: Maybe<Scalars['String']['output']>;
};

export type Query = {
  __typename?: 'Query';
  EpiDetailById: EpiDocumentPopulated;
  EpikBoxById: EdgeDeviceDetails;
  ListEpi: EpiDocumentPopulatedPaginationResult;
  ListEpikBoxes: EpikBoxDocumentPopulatedPaginationResult;
  ListNumbers: NumberDocumentPaginationResult;
};


export type QueryEpiDetailByIdArgs = {
  id?: InputMaybe<Scalars['String']['input']>;
};


export type QueryEpikBoxByIdArgs = {
  id?: InputMaybe<Scalars['String']['input']>;
};


export type QueryListEpiArgs = {
  filter?: InputMaybe<EpiFilterInput>;
  pagination?: InputMaybe<PaginationInput>;
};


export type QueryListEpikBoxesArgs = {
  filter?: InputMaybe<EpikBoxFilterInput>;
  pagination?: InputMaybe<PaginationInput>;
};


export type QueryListNumbersArgs = {
  filter?: InputMaybe<ListNumberInput>;
  pagination?: InputMaybe<PaginationInput>;
};

export type RouteInfo = {
  __typename?: 'RouteInfo';
  gateway?: Maybe<Scalars['String']['output']>;
  route?: Maybe<Scalars['Int']['output']>;
};

export type SensorData = {
  __typename?: 'SensorData';
  power?: Maybe<Scalars['String']['output']>;
  temp?: Maybe<Scalars['String']['output']>;
};

export type ShippedAddress = {
  __typename?: 'ShippedAddress';
  attn?: Maybe<Scalars['String']['output']>;
  city?: Maybe<Scalars['String']['output']>;
  state?: Maybe<Scalars['String']['output']>;
  street1?: Maybe<Scalars['String']['output']>;
  street2?: Maybe<Scalars['String']['output']>;
  zip?: Maybe<Scalars['String']['output']>;
};

export enum SilenceDetectSensitivityEnum {
  Low = 'Low',
  Medium = 'Medium'
}

export type SimPingInfo = {
  __typename?: 'SimPingInfo';
  Error?: Maybe<Scalars['String']['output']>;
  Jitter?: Maybe<Scalars['Float']['output']>;
  PacketLoss?: Maybe<Scalars['Float']['output']>;
  PingAvg?: Maybe<Scalars['Float']['output']>;
  SIM?: Maybe<Scalars['Float']['output']>;
};

export type Sp1erviceStatus = {
  __typename?: 'Sp1erviceStatus';
  callState?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type Sp2erviceStatus = {
  __typename?: 'Sp2erviceStatus';
  callState?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type SpeedTest = {
  __typename?: 'SpeedTest';
  downloadSpeed?: Maybe<Scalars['String']['output']>;
  jitter?: Maybe<Scalars['String']['output']>;
  latency?: Maybe<Scalars['String']['output']>;
  uploadSpeed?: Maybe<Scalars['String']['output']>;
};

export type Subscription = {
  __typename?: 'Subscription';
  EpiUpdateHook: LiveFieldUpdate;
  EpikBoxUpdateHook: LiveFieldUpdate;
  PingBoxHook: PingLine;
};


export type SubscriptionEpiUpdateHookArgs = {
  fields?: InputMaybe<Array<Scalars['String']['input']>>;
  id: Scalars['ID']['input'];
};


export type SubscriptionEpikBoxUpdateHookArgs = {
  fields?: InputMaybe<Array<Scalars['String']['input']>>;
  id: Scalars['String']['input'];
};


export type SubscriptionPingBoxHookArgs = {
  serialNumber: Scalars['String']['input'];
};

export type SystemInfoDocument = {
  __typename?: 'SystemInfoDocument';
  _id: Scalars['ID']['output'];
  boxFirmware?: Maybe<BoxFirmware>;
  boxId?: Maybe<Scalars['OID']['output']>;
  cdmStatus?: Maybe<Scalars['String']['output']>;
  cpu?: Maybe<CpuInfo>;
  deviceFw?: Maybe<Scalars['String']['output']>;
  diskIo?: Maybe<Array<DiskIo>>;
  diskUsage?: Maybe<DiskUsage>;
  error?: Maybe<Scalars['String']['output']>;
  failoverStatus?: Maybe<Scalars['String']['output']>;
  failoverUpdateStatus?: Maybe<Scalars['String']['output']>;
  lastUpdated?: Maybe<Scalars['String']['output']>;
  loadavg?: Maybe<LoadAvg>;
  lteSignalStrength?: Maybe<LteSignalStrength>;
  memory?: Maybe<MemoryInfo>;
  network?: Maybe<Array<NetworkInterface>>;
  networkInfoDetail?: Maybe<NetworkInfoDetail>;
  portStatuses?: Maybe<PortStatuses>;
  restbinStatus?: Maybe<Scalars['String']['output']>;
  restbinVersion?: Maybe<Scalars['String']['output']>;
  time?: Maybe<Scalars['String']['output']>;
  uptime?: Maybe<Uptime>;
  watchGuardStatus?: Maybe<Scalars['String']['output']>;
  wifiInfo?: Maybe<WifiInfo>;
};

export type TunnelAccess = {
  __typename?: 'TunnelAccess';
  eth0?: Maybe<Scalars['String']['output']>;
  wwan0?: Maybe<Scalars['String']['output']>;
};

export type Uptime = {
  __typename?: 'Uptime';
  uptime?: Maybe<Scalars['String']['output']>;
};

export type VSwitchTab = {
  __typename?: 'VSwitchTab';
  portsConfigCreated?: Maybe<Array<Scalars['String']['output']>>;
  portsInfo?: Maybe<Array<PortInfo>>;
  registerationConfigCreated?: Maybe<Scalars['Boolean']['output']>;
  registered?: Maybe<Scalars['Boolean']['output']>;
};

export type VmailBoxDocument = {
  __typename?: 'VmailBoxDocument';
  _id: Scalars['ID']['output'];
  createdAt?: Maybe<Scalars['Timestamp']['output']>;
  deleted?: Maybe<Scalars['Boolean']['output']>;
  greeting?: Maybe<Scalars['String']['output']>;
  limit?: Maybe<Scalars['Int']['output']>;
  notificationEmails?: Maybe<Array<Scalars['String']['output']>>;
  notificationNumbers?: Maybe<Array<Scalars['String']['output']>>;
  number?: Maybe<Scalars['ID']['output']>;
  numberDoc?: Maybe<NumberDocumentPopulated>;
  pin?: Maybe<Scalars['String']['output']>;
  totalMessages?: Maybe<Scalars['Int']['output']>;
  updatedAt?: Maybe<Scalars['Timestamp']['output']>;
};

export type WanInfo = {
  __typename?: 'WanInfo';
  dns?: Maybe<Scalars['String']['output']>;
  gateway?: Maybe<Scalars['String']['output']>;
  ip?: Maybe<Scalars['String']['output']>;
  placeHolder?: Maybe<Scalars['String']['output']>;
  subnet?: Maybe<Scalars['String']['output']>;
};

export type WifiInfo = {
  __typename?: 'WifiInfo';
  ap?: Maybe<Scalars['String']['output']>;
  error?: Maybe<Scalars['String']['output']>;
  mode?: Maybe<Scalars['String']['output']>;
  ssid?: Maybe<Scalars['String']['output']>;
};

export type WifiStatus = {
  __typename?: 'WifiStatus';
  Error_Msg?: Maybe<Scalars['String']['output']>;
  Gateway?: Maybe<Scalars['String']['output']>;
  IP?: Maybe<Scalars['String']['output']>;
  Mode?: Maybe<Scalars['String']['output']>;
  Password?: Maybe<Scalars['String']['output']>;
  SSID?: Maybe<Scalars['String']['output']>;
  Sec_Mode?: Maybe<Scalars['String']['output']>;
  Status?: Maybe<Scalars['String']['output']>;
  Subnet?: Maybe<Scalars['String']['output']>;
};

export type EpiUpdateHookSubscriptionVariables = Exact<{
  fields: Array<Scalars['String']['input']> | Scalars['String']['input'];
  id: Scalars['ID']['input'];
}>;


export type EpiUpdateHookSubscription = { __typename?: 'Subscription', EpiUpdateHook: { __typename?: 'LiveFieldUpdate', fieldName: string, value?: any | null, id: string, error?: string | null } };

export type EpikBoxUpdateHookSubscriptionVariables = Exact<{
  fields: Array<Scalars['String']['input']> | Scalars['String']['input'];
  id: Scalars['String']['input'];
}>;


export type EpikBoxUpdateHookSubscription = { __typename?: 'Subscription', EpikBoxUpdateHook: { __typename?: 'LiveFieldUpdate', fieldName: string, value?: any | null, id: string, error?: string | null } };

export type EpiDetailByIdQueryVariables = Exact<{
  id: Scalars['String']['input'];
}>;


export type EpiDetailByIdQuery = { __typename?: 'Query', EpiDetailById: { __typename?: 'EpiDocumentPopulated', _id: string, deviceId: string, obiNumber?: string | null, macAddress?: string | null, assignedTo?: any | null, registrationMeta?: { __typename?: 'EpiRegistrationMeta', wanInfo?: { __typename?: 'WanInfo', placeHolder?: string | null, ip?: string | null, subnet?: string | null, gateway?: string | null, dns?: string | null } | null, sp1ServiceStatus?: { __typename?: 'Sp1erviceStatus', status?: string | null, callState?: string | null } | null, sp2ServiceStatus?: { __typename?: 'Sp2erviceStatus', status?: string | null, callState?: string | null } | null, obiTalkServiceStatus?: { __typename?: 'ObiTalkServiceStatus', placeHolder?: string | null, status?: string | null } | null } | null, liveRegistrationMeta?: { __typename?: 'EpiRegistrationMeta', wanInfo?: { __typename?: 'WanInfo', placeHolder?: string | null, ip?: string | null, subnet?: string | null, gateway?: string | null, dns?: string | null } | null, sp1ServiceStatus?: { __typename?: 'Sp1erviceStatus', status?: string | null, callState?: string | null } | null, sp2ServiceStatus?: { __typename?: 'Sp2erviceStatus', status?: string | null, callState?: string | null } | null, obiTalkServiceStatus?: { __typename?: 'ObiTalkServiceStatus', placeHolder?: string | null, status?: string | null } | null } | null, portPhysicalMeta?: Array<{ __typename?: 'PortPhysicalMeta', name?: string | null, state?: string | null, loopCurrent?: string | null, Vbat?: string | null, tipRingVoltage?: string | null, lastCallerInfo?: string | null }> | null, livePortPhysicalMeta?: Array<{ __typename?: 'PortPhysicalMeta', name?: string | null, state?: string | null, loopCurrent?: string | null, Vbat?: string | null, tipRingVoltage?: string | null, lastCallerInfo?: string | null }> | null } };

export type EpikBoxByIdQueryVariables = Exact<{
  id: Scalars['String']['input'];
}>;


export type EpikBoxByIdQuery = { __typename?: 'Query', EpikBoxById: { __typename?: 'EdgeDeviceDetails', _id: string, displayName?: string | null, serialNumber?: string | null, vpnAddress?: string | null, powerState?: string | null, livePowerState?: string | null, activeInterface?: string | null, signalStrength?: string | null, liveSignalStrength?: string | null, liveActiveInterface?: string | null, monitor?: boolean | null, publicIp?: string | null, livePublicIp?: string | null, simStatus?: string | null, liveSimStatus?: string | null, deviceOnline?: boolean | null, liveDeviceOnline?: boolean | null, lanIp?: string | null, liveLanIp?: string | null, lteIp?: string | null, lteIp2?: string | null, registered?: boolean | null, activeCarrier?: string | null, datacenter?: string | null, lteAnalyzer?: any | null, myEpik?: boolean | null, model?: string | null, apuType?: string | null, powerSaveOption?: boolean | null, starCodes?: boolean | null, boxOfflineCallForward?: boolean | null, recording?: boolean | null, OBEnable?: boolean | null, eth3Disable?: boolean | null, nightlyUpdateTime?: any | null, ext_9override?: boolean | null, boxRegistrar?: BoxRegistrarEnum | null, e911Number?: string | null, primarySim?: string | null, priorityInterface?: string | null, portAlerts?: boolean | null, currentApn?: string | null, epiTimezone?: string | null, speedTestData?: { __typename?: 'SpeedTest', latency?: string | null, jitter?: string | null, uploadSpeed?: string | null, downloadSpeed?: string | null } | null, speedTestVoice?: { __typename?: 'SpeedTest', latency?: string | null, jitter?: string | null, uploadSpeed?: string | null, downloadSpeed?: string | null } | null, preferredProviderTest?: { __typename?: 'FetchLtePerf', TimeStamp?: string | null, initiatedTimeStamp?: any | null, SimsInfo?: Array<{ __typename?: 'SimPingInfo', Error?: string | null, Jitter?: number | null, PacketLoss?: number | null, PingAvg?: number | null, SIM?: number | null }> | null } | null, liveSpeedTestData?: { __typename?: 'SpeedTest', latency?: string | null, jitter?: string | null, uploadSpeed?: string | null, downloadSpeed?: string | null } | null, liveSpeedTestVoice?: { __typename?: 'SpeedTest', latency?: string | null, jitter?: string | null, uploadSpeed?: string | null, downloadSpeed?: string | null } | null, livePreferredProviderTest?: { __typename?: 'FetchLtePerf', TimeStamp?: string | null, initiatedTimeStamp?: any | null, SimsInfo?: Array<{ __typename?: 'SimPingInfo', Error?: string | null, Jitter?: number | null, PacketLoss?: number | null, PingAvg?: number | null, SIM?: number | null }> | null } | null, sensorData?: { __typename?: 'SensorData', power?: string | null, temp?: string | null } | null, liveSensorData?: { __typename?: 'SensorData', power?: string | null, temp?: string | null } | null, features?: { __typename?: 'Features', showPriTab?: boolean | null, pri?: boolean | null, sip?: boolean | null, twoModems?: boolean | null, daisyChain?: boolean | null, dcAutoUpdate?: boolean | null } | null, wifiStatus?: { __typename?: 'WifiStatus', Error_Msg?: string | null, Gateway?: string | null, IP?: string | null, Mode?: string | null, Password?: string | null, SSID?: string | null, Sec_Mode?: string | null, Status?: string | null, Subnet?: string | null } | null, liveWifiStatus?: { __typename?: 'WifiStatus', Error_Msg?: string | null, Gateway?: string | null, IP?: string | null, Mode?: string | null, Password?: string | null, SSID?: string | null, Sec_Mode?: string | null, Status?: string | null, Subnet?: string | null } | null, networkInfo?: { __typename?: 'NetworkInfo', dns?: string | null, timestamp?: string | null, error?: string | null, interfaces?: Array<{ __typename?: 'NetworkInfaceObj', interface?: string | null, internet?: string | null, icmp?: string | null, wg?: string | null }> | null } | null, liveNetworkInfo?: { __typename?: 'NetworkInfo', dns?: string | null, timestamp?: string | null, error?: string | null, interfaces?: Array<{ __typename?: 'NetworkInfaceObj', interface?: string | null, internet?: string | null, icmp?: string | null, wg?: string | null }> | null } | null, priSettings?: { __typename?: 'PRISettingsDocument', isSynced?: boolean | null, pbx?: boolean | null, priFailovers?: Array<string> | null, echoChannels?: string | null, channels?: string | null, framing?: string | null, lineCode?: string | null, timeAndSource?: string | null, dChannel?: string | null, bChannels?: string | null, switchType?: string | null, echoCancellation?: boolean | null, echoCancellationType?: string | null, e911Number?: string | null, numberSettings?: { __typename?: 'NumberSettings', dtmfType?: string | null, dnis?: string | null, callerIdName?: string | null, callerIdNumber?: string | null, numbers?: Array<{ __typename?: 'NumberDocument', _id: string }> | null } | null, shippedAddress?: { __typename?: 'ShippedAddress', attn?: string | null, street1?: string | null, street2?: string | null, city?: string | null, state?: string | null, zip?: string | null } | null } | null, sipSettings?: { __typename?: 'CASSettingsDocument', casFailovers?: Array<string> | null, channels?: string | null, framing?: string | null, lineCode?: string | null, timeAndSource?: string | null, bChannel?: string | null, switchType?: string | null, echoCancellation?: boolean | null, echoCancellationType?: string | null, channelRange?: string | null, authType?: string | null, ip?: string | null, username?: string | null, password?: string | null, e911Number?: string | null, numberSettings?: { __typename?: 'NumberSettings', dtmfType?: string | null, dnis?: string | null, callerIdName?: string | null, callerIdNumber?: string | null, numbers?: Array<{ __typename?: 'NumberDocument', _id: string }> | null } | null, shippedAddress?: { __typename?: 'ShippedAddress', attn?: string | null, street1?: string | null, street2?: string | null, city?: string | null, state?: string | null, zip?: string | null } | null } | null, dcAvgPing?: { __typename?: 'DcAvgPingDocument', chPingAvg?: string | null, laPingAvg?: string | null, nyPingAvg?: string | null, atPingAvg?: string | null, dlPingAvg?: string | null, bestDC?: string | null, bestLatency?: string | null, timeUpdated?: string | null, error?: string | null } | null, liveDcAvgPing?: { __typename?: 'DcAvgPingDocument', chPingAvg?: string | null, laPingAvg?: string | null, nyPingAvg?: string | null, atPingAvg?: string | null, dlPingAvg?: string | null, bestDC?: string | null, bestLatency?: string | null, timeUpdated?: string | null, error?: string | null } | null, modemInfo?: { __typename?: 'ModemInfoDocument', carrier?: string | null, carrier_placeholder?: string | null, imei?: string | null, imei_placeholder?: string | null, ipAddress?: string | null, ipAddress_placeholder?: string | null, manufacturer?: string | null, manufacturer_placeholder?: string | null, model?: string | null, model_placeholder?: string | null, sim?: string | null, sim_placeholder?: string | null } | null, liveModemInfo?: { __typename?: 'ModemInfoDocument', carrier?: string | null, carrier_placeholder?: string | null, imei?: string | null, imei_placeholder?: string | null, ipAddress?: string | null, ipAddress_placeholder?: string | null, manufacturer?: string | null, manufacturer_placeholder?: string | null, model?: string | null, model_placeholder?: string | null, sim?: string | null, sim_placeholder?: string | null } | null, companyDoc?: { __typename?: 'CompanyDocument', _id: string, name: string } | null, obiDocs?: Array<{ __typename?: 'EpiDocumentPopulated', _id: string, macAddress?: string | null, obiNumber?: string | null, deviceId: string, port1?: { __typename?: 'EpiPortPopulated', boxPortNumber?: string | null, registered?: boolean | null, disabled?: boolean | null, provisionDate?: any | null, enableDisableLastUpdatedTime?: any | null, updatedOn?: any | null, serviceName?: string | null, monitored?: boolean | null, callerIdMasking?: boolean | null, callerIdMaskingNumber?: string | null, SilenceDetectSensitivity?: SilenceDetectSensitivityEnum | null, OnHookTipRingVoltage?: string | null, OffHookCurrentMax?: string | null, DTMFDetectMinLength?: string | null, DTMFDetectMinGap?: string | null, ChannelTxGain?: string | null, ChannelRxGain?: string | null, DTMFMethod?: string | null, DTMFPlaybackLevel?: string | null, RingVoltage?: string | null, DigitMapShortTimer?: string | null, CPCDelayTime?: string | null, CPCDuration?: string | null, showVadToggle?: boolean | null, showModemModeToggle?: boolean | null, modemMode?: boolean | null, showT38EnableToggle?: boolean | null, t38Enabled?: boolean | null, showFaxEnabledToggle?: boolean | null, faxEnabled?: boolean | null, showJitterDelay?: boolean | null, JitterMinDelay?: string | null, JitterMaxDelay?: string | null, showModemOnlyToggle?: boolean | null, modemOnly?: boolean | null, forceForward?: boolean | null, forceForwardNumber?: string | null, configOverride?: boolean | null, e911Number?: string | null, e911Enabled?: boolean | null, voip?: boolean | null, status?: string | null, connected?: boolean | null, remoteHost?: string | null, ipAddress?: string | null, remotePort?: string | null, authName?: string | null, assignedNumber?: string | null, vmNumber?: string | null, advancedModemConfiguration?: boolean | null, portActivated?: boolean | null, portActivatedDate?: any | null, startedBilling?: boolean | null, startedBillingDate?: any | null, callerIdOverride?: string | null, provisionUrl?: string | null, blockCallerId?: boolean | null, callWaiting?: boolean | null, configPassword?: string | null, assignedNumberRef?: any | null, appliedConfigTemplate?: any | null, vmBox?: any | null, vmailBoxDoc?: { __typename?: 'VmailBoxDocument', _id: string, pin?: string | null, totalMessages?: number | null, limit?: number | null, greeting?: string | null, notificationEmails?: Array<string> | null, notificationNumbers?: Array<string> | null, number?: string | null, deleted?: boolean | null, createdAt?: any | null, updatedAt?: any | null, numberDoc?: { __typename?: 'NumberDocumentPopulated', _id: string, number?: string | null, linkedBox?: any | null, assignedTo?: any | null, company?: any | null, type: string, callerIdName?: string | null, isForwarded?: boolean | null, huntPorts?: Array<string> | null, huntType?: string | null, portLabel?: string | null, forwardUris?: Array<string> | null, linkedToObi?: boolean | null, faxDeliveryConfirmation?: boolean | null, savefaxes?: boolean | null, confirmationEmail?: string | null, dateCreated?: any | null, deleted?: boolean | null, enabled?: boolean | null, cid?: string | null, emails?: Array<string> | null, allowedUsers?: Array<string> | null, allowedNumbers?: Array<string> | null, groupMembers?: Array<string> | null, forwardNumbers?: Array<string> | null, fallbackEmails?: Array<string> | null, destination?: string | null, cidOverride?: string | null, port?: string | null, temporary?: string | null, greetingFilePath?: string | null, agreementFilePath?: string | null, carrier?: CarrierEnum | null, e911Carrier?: E911CarrierEnum | null, tdmRoute?: string | null, trunk?: string | null, previousTrunk?: string | null, linkedToExternalAta?: boolean | null, alarmRelayInfo?: any | null, inBoundCallBlocked?: boolean | null, inbandRouting?: boolean | null, isNumberLocked?: boolean | null, usbPort?: any | null, onlyEfax?: boolean | null, isTollFree?: boolean | null, tollFreeNumbers?: Array<string> | null, provisionDate?: any | null, spoofType?: string | null, spoofIncomingCid?: boolean | null, trunkSelection?: string | null, numDigits?: number | null } | null } | null, assignedNumberDoc?: { __typename?: 'NumberDocumentPopulated', _id: string, number?: string | null, linkedBox?: any | null, assignedTo?: any | null, company?: any | null, type: string, callerIdName?: string | null, isForwarded?: boolean | null, huntPorts?: Array<string> | null, huntType?: string | null, portLabel?: string | null, forwardUris?: Array<string> | null, linkedToObi?: boolean | null, faxDeliveryConfirmation?: boolean | null, savefaxes?: boolean | null, confirmationEmail?: string | null, dateCreated?: any | null, deleted?: boolean | null, enabled?: boolean | null, cid?: string | null, emails?: Array<string> | null, allowedUsers?: Array<string> | null, allowedNumbers?: Array<string> | null, groupMembers?: Array<string> | null, forwardNumbers?: Array<string> | null, fallbackEmails?: Array<string> | null, destination?: string | null, cidOverride?: string | null, port?: string | null, temporary?: string | null, greetingFilePath?: string | null, agreementFilePath?: string | null, carrier?: CarrierEnum | null, e911Carrier?: E911CarrierEnum | null, tdmRoute?: string | null, trunk?: string | null, previousTrunk?: string | null, linkedToExternalAta?: boolean | null, alarmRelayInfo?: any | null, inBoundCallBlocked?: boolean | null, inbandRouting?: boolean | null, isNumberLocked?: boolean | null, usbPort?: any | null, onlyEfax?: boolean | null, isTollFree?: boolean | null, tollFreeNumbers?: Array<string> | null, provisionDate?: any | null, spoofType?: string | null, spoofIncomingCid?: boolean | null, trunkSelection?: string | null, numDigits?: number | null, routeInfo?: { __typename?: 'RouteInfo', route?: number | null, gateway?: string | null } | null, previousRouteInfo?: { __typename?: 'RouteInfo', route?: number | null, gateway?: string | null } | null, e911Info?: { __typename?: 'E911Info', address1?: string | null, address2?: string | null, city?: string | null, state?: string | null, zip?: string | null, country?: string | null, locationid?: string | null, callername?: string | null, vendor?: string | null, createdAt?: any | null, updatedAt?: any | null } | null } | null, appliedConfigTemplateDoc?: { __typename?: 'PortConfigTemplateDocument', _id: string, title?: string | null, name?: string | null, default: boolean, SilenceDetectSensitivity: string, OnHookTipRingVoltage: string, OffHookCurrentMax: string, DTMFDetectMinLength: string, DTMFDetectMinGap: string, ChannelTxGain: string, ChannelRxGain: string, DTMFMethod: string, DTMFPlaybackLevel: string, RingVoltage: string, DigitMapShortTimer: string, CPCDelayTime: string, CPCDuration: string, showModemModeToggle: boolean, modemMode: boolean, showT38EnableToggle: boolean, t38Enabled: boolean, showFaxEnabledToggle: boolean, faxEnabled: boolean, showJitterDelay?: boolean | null, JitterMinDelay?: string | null, JitterMaxDelay?: string | null, inbandRoute?: boolean | null, showInbandRouteToggle?: boolean | null, trunkSelection?: string | null, showTrunkSelectionToggle?: boolean | null, vadEnable?: boolean | null, showVadToggle?: boolean | null, showModemOnlyToggle?: boolean | null, modemOnly?: boolean | null, ataType?: string | null } | null } | null, port2?: { __typename?: 'EpiPortPopulated', boxPortNumber?: string | null, registered?: boolean | null, disabled?: boolean | null, provisionDate?: any | null, enableDisableLastUpdatedTime?: any | null, updatedOn?: any | null, serviceName?: string | null, monitored?: boolean | null, callerIdMasking?: boolean | null, callerIdMaskingNumber?: string | null, SilenceDetectSensitivity?: SilenceDetectSensitivityEnum | null, OnHookTipRingVoltage?: string | null, OffHookCurrentMax?: string | null, DTMFDetectMinLength?: string | null, DTMFDetectMinGap?: string | null, ChannelTxGain?: string | null, ChannelRxGain?: string | null, DTMFMethod?: string | null, DTMFPlaybackLevel?: string | null, RingVoltage?: string | null, DigitMapShortTimer?: string | null, CPCDelayTime?: string | null, CPCDuration?: string | null, showVadToggle?: boolean | null, showModemModeToggle?: boolean | null, modemMode?: boolean | null, showT38EnableToggle?: boolean | null, t38Enabled?: boolean | null, showFaxEnabledToggle?: boolean | null, faxEnabled?: boolean | null, showJitterDelay?: boolean | null, JitterMinDelay?: string | null, JitterMaxDelay?: string | null, showModemOnlyToggle?: boolean | null, modemOnly?: boolean | null, forceForward?: boolean | null, forceForwardNumber?: string | null, configOverride?: boolean | null, e911Number?: string | null, e911Enabled?: boolean | null, voip?: boolean | null, status?: string | null, connected?: boolean | null, remoteHost?: string | null, ipAddress?: string | null, remotePort?: string | null, authName?: string | null, assignedNumber?: string | null, vmNumber?: string | null, advancedModemConfiguration?: boolean | null, portActivated?: boolean | null, portActivatedDate?: any | null, startedBilling?: boolean | null, startedBillingDate?: any | null, callerIdOverride?: string | null, provisionUrl?: string | null, blockCallerId?: boolean | null, callWaiting?: boolean | null, configPassword?: string | null, assignedNumberRef?: any | null, vmBox?: any | null, appliedConfigTemplate?: any | null, vmailBoxDoc?: { __typename?: 'VmailBoxDocument', _id: string, pin?: string | null, totalMessages?: number | null, limit?: number | null, greeting?: string | null, notificationEmails?: Array<string> | null, notificationNumbers?: Array<string> | null, number?: string | null, deleted?: boolean | null, createdAt?: any | null, updatedAt?: any | null, numberDoc?: { __typename?: 'NumberDocumentPopulated', _id: string, number?: string | null, linkedBox?: any | null, assignedTo?: any | null, company?: any | null, type: string, callerIdName?: string | null, isForwarded?: boolean | null, huntPorts?: Array<string> | null, huntType?: string | null, portLabel?: string | null, forwardUris?: Array<string> | null, linkedToObi?: boolean | null, faxDeliveryConfirmation?: boolean | null, savefaxes?: boolean | null, confirmationEmail?: string | null, dateCreated?: any | null, deleted?: boolean | null, enabled?: boolean | null, cid?: string | null, emails?: Array<string> | null, allowedUsers?: Array<string> | null, allowedNumbers?: Array<string> | null, groupMembers?: Array<string> | null, forwardNumbers?: Array<string> | null, fallbackEmails?: Array<string> | null, destination?: string | null, cidOverride?: string | null, port?: string | null, temporary?: string | null, greetingFilePath?: string | null, agreementFilePath?: string | null, carrier?: CarrierEnum | null, e911Carrier?: E911CarrierEnum | null, tdmRoute?: string | null, trunk?: string | null, previousTrunk?: string | null, linkedToExternalAta?: boolean | null, alarmRelayInfo?: any | null, inBoundCallBlocked?: boolean | null, inbandRouting?: boolean | null, isNumberLocked?: boolean | null, usbPort?: any | null, onlyEfax?: boolean | null, isTollFree?: boolean | null, tollFreeNumbers?: Array<string> | null, provisionDate?: any | null, spoofType?: string | null, spoofIncomingCid?: boolean | null, trunkSelection?: string | null, numDigits?: number | null } | null } | null, assignedNumberDoc?: { __typename?: 'NumberDocumentPopulated', _id: string, number?: string | null, linkedBox?: any | null, assignedTo?: any | null, company?: any | null, type: string, callerIdName?: string | null, isForwarded?: boolean | null, huntPorts?: Array<string> | null, huntType?: string | null, portLabel?: string | null, forwardUris?: Array<string> | null, linkedToObi?: boolean | null, faxDeliveryConfirmation?: boolean | null, savefaxes?: boolean | null, confirmationEmail?: string | null, dateCreated?: any | null, deleted?: boolean | null, enabled?: boolean | null, cid?: string | null, emails?: Array<string> | null, allowedUsers?: Array<string> | null, allowedNumbers?: Array<string> | null, groupMembers?: Array<string> | null, forwardNumbers?: Array<string> | null, fallbackEmails?: Array<string> | null, destination?: string | null, cidOverride?: string | null, port?: string | null, temporary?: string | null, greetingFilePath?: string | null, agreementFilePath?: string | null, carrier?: CarrierEnum | null, e911Carrier?: E911CarrierEnum | null, tdmRoute?: string | null, trunk?: string | null, previousTrunk?: string | null, linkedToExternalAta?: boolean | null, alarmRelayInfo?: any | null, inBoundCallBlocked?: boolean | null, inbandRouting?: boolean | null, isNumberLocked?: boolean | null, usbPort?: any | null, onlyEfax?: boolean | null, isTollFree?: boolean | null, tollFreeNumbers?: Array<string> | null, provisionDate?: any | null, spoofType?: string | null, spoofIncomingCid?: boolean | null, trunkSelection?: string | null, numDigits?: number | null, routeInfo?: { __typename?: 'RouteInfo', route?: number | null, gateway?: string | null } | null, previousRouteInfo?: { __typename?: 'RouteInfo', route?: number | null, gateway?: string | null } | null, e911Info?: { __typename?: 'E911Info', address1?: string | null, address2?: string | null, city?: string | null, state?: string | null, zip?: string | null, country?: string | null, locationid?: string | null, callername?: string | null, vendor?: string | null, createdAt?: any | null, updatedAt?: any | null } | null } | null, appliedConfigTemplateDoc?: { __typename?: 'PortConfigTemplateDocument', _id: string, title?: string | null, name?: string | null, default: boolean, SilenceDetectSensitivity: string, OnHookTipRingVoltage: string, OffHookCurrentMax: string, DTMFDetectMinLength: string, DTMFDetectMinGap: string, ChannelTxGain: string, ChannelRxGain: string, DTMFMethod: string, DTMFPlaybackLevel: string, RingVoltage: string, DigitMapShortTimer: string, CPCDelayTime: string, CPCDuration: string, showModemModeToggle: boolean, modemMode: boolean, showT38EnableToggle: boolean, t38Enabled: boolean, showFaxEnabledToggle: boolean, faxEnabled: boolean, showJitterDelay?: boolean | null, JitterMinDelay?: string | null, JitterMaxDelay?: string | null, inbandRoute?: boolean | null, showInbandRouteToggle?: boolean | null, trunkSelection?: string | null, showTrunkSelectionToggle?: boolean | null, vadEnable?: boolean | null, showVadToggle?: boolean | null, showModemOnlyToggle?: boolean | null, modemOnly?: boolean | null, ataType?: string | null } | null } | null }> | null, phoneDocs?: Array<{ __typename?: 'PhoneDocument', _id: string, displayName?: string | null, extension?: string | null, model?: string | null, number?: any | null, numberDoc?: { __typename?: 'NumberDocument', _id: string, number?: string | null } | null }> | null, locationDoc?: { __typename?: 'LocationDocument', locationName: string, _id: string } | null, sysInfo?: { __typename?: 'SystemInfoDocument', _id: string, boxId?: any | null, cdmStatus?: string | null, deviceFw?: string | null, error?: string | null, failoverStatus?: string | null, failoverUpdateStatus?: string | null, restbinStatus?: string | null, restbinVersion?: string | null, time?: string | null, watchGuardStatus?: string | null, lastUpdated?: string | null, diskIo?: Array<{ __typename?: 'DiskIo', Name?: string | null, ReadsCompleted?: string | null, WritesCompleted?: string | null }> | null, diskUsage?: { __typename?: 'DiskUsage', all?: string | null, avail?: string | null, error?: string | null, free?: string | null, size?: string | null, used?: string | null } | null, loadavg?: { __typename?: 'LoadAvg', Loadavg1?: string | null, Loadavg5?: string | null, Loadavg15?: string | null, error?: string | null } | null, uptime?: { __typename?: 'Uptime', uptime?: string | null } | null, wifiInfo?: { __typename?: 'WifiInfo', ap?: string | null, error?: string | null, mode?: string | null, ssid?: string | null } | null, boxFirmware?: { __typename?: 'BoxFirmware', version?: string | null } | null, lteSignalStrength?: { __typename?: 'LteSignalStrength', signalStrength?: string | null } | null, networkInfoDetail?: { __typename?: 'NetworkInfoDetail', activeInterface?: string | null, fwVersion?: string | null, modemCount?: string | null, edgeIP?: string | null, ip4G?: string | null, registrationStatus?: string | null, tunnelIP?: string | null, powerSource?: string | null, lteSgnalStrength?: string | null, arp?: { __typename?: 'ArpTableOptions', keys?: Array<string> | null, values?: Array<string> | null } | null, eth0Config?: { __typename?: 'Eth0Config', nameserver1?: string | null, nameserver2?: string | null, nameserver3?: string | null, nameserver4?: string | null } | null, eth0Stats?: { __typename?: 'Eth0Stats', eth0IP?: string | null, eth0MAC?: string | null, rxErrors?: string | null, rxPackets?: string | null, txErrors?: string | null, txPackets?: string | null } | null, icmp?: { __typename?: 'Icmp', eth0?: string | null, wwan0?: string | null } | null, linkStatus?: { __typename?: 'LinkStatus', epiLink?: string | null, eth02?: string | null } | null, tunnelAccess?: { __typename?: 'TunnelAccess', eth0?: string | null, wwan0?: string | null } | null, priCardStatus?: { __typename?: 'PRICardStatus', priCardStatus?: string | null } | null, deviceFirmwareVersion?: { __typename?: 'DeviceFirmwareVersion', version?: string | null } | null, epiInfo?: Array<{ __typename?: 'EpiInfo', fwVersion?: string | null, ip?: string | null, mac?: string | null, provUrl?: string | null, uptime?: string | null }> | null, dcAvgPing?: { __typename?: 'DcAvgPingDocument', chPingAvg?: string | null, laPingAvg?: string | null, nyPingAvg?: string | null, atPingAvg?: string | null, dlPingAvg?: string | null, bestDC?: string | null, bestLatency?: string | null, timeUpdated?: string | null, error?: string | null } | null, cdmBoxStatus?: { __typename?: 'CdmBoxStatus', Status?: string | null, LastCdmCheck?: string | null, Duration?: string | null } | null } | null, cpu?: { __typename?: 'CpuInfo', CPUCount?: string | null, Guest?: string | null, GuestNice?: string | null, Idle?: string | null, Iowait?: string | null, irq?: string | null, Nice?: string | null, Softirq?: string | null, StatCount?: string | null, Steal?: string | null, System?: string | null, Total?: string | null, User?: string | null } | null, memory?: { __typename?: 'MemoryInfo', Active?: string | null, Available?: string | null, Buffers?: string | null, Cached?: string | null, Free?: string | null, Inactive?: string | null, MemAvailableEnabled?: string | null, SwapCached?: string | null, SwapFree?: string | null, SwapTotal?: string | null, SwapUsed?: string | null, Total?: string | null, Used?: string | null } | null, network?: Array<{ __typename?: 'NetworkInterface', Name?: string | null, RxBytes?: string | null, TxBytes?: string | null }> | null, portStatuses?: { __typename?: 'PortStatuses', eth0?: string | null, eth1?: string | null, eth2?: string | null, wg0?: string | null, wg7?: string | null, wlan0?: string | null, wwan0?: string | null } | null } | null, liveSysInfo?: { __typename?: 'SystemInfoDocument', _id: string, boxId?: any | null, cdmStatus?: string | null, deviceFw?: string | null, error?: string | null, failoverStatus?: string | null, failoverUpdateStatus?: string | null, restbinStatus?: string | null, restbinVersion?: string | null, time?: string | null, watchGuardStatus?: string | null, lastUpdated?: string | null, diskIo?: Array<{ __typename?: 'DiskIo', Name?: string | null, ReadsCompleted?: string | null, WritesCompleted?: string | null }> | null, diskUsage?: { __typename?: 'DiskUsage', all?: string | null, avail?: string | null, error?: string | null, free?: string | null, size?: string | null, used?: string | null } | null, loadavg?: { __typename?: 'LoadAvg', Loadavg1?: string | null, Loadavg5?: string | null, Loadavg15?: string | null, error?: string | null } | null, uptime?: { __typename?: 'Uptime', uptime?: string | null } | null, wifiInfo?: { __typename?: 'WifiInfo', ap?: string | null, error?: string | null, mode?: string | null, ssid?: string | null } | null, boxFirmware?: { __typename?: 'BoxFirmware', version?: string | null } | null, lteSignalStrength?: { __typename?: 'LteSignalStrength', signalStrength?: string | null } | null, networkInfoDetail?: { __typename?: 'NetworkInfoDetail', activeInterface?: string | null, fwVersion?: string | null, modemCount?: string | null, edgeIP?: string | null, ip4G?: string | null, registrationStatus?: string | null, tunnelIP?: string | null, powerSource?: string | null, lteSgnalStrength?: string | null, arp?: { __typename?: 'ArpTableOptions', keys?: Array<string> | null, values?: Array<string> | null } | null, eth0Config?: { __typename?: 'Eth0Config', nameserver1?: string | null, nameserver2?: string | null, nameserver3?: string | null, nameserver4?: string | null } | null, eth0Stats?: { __typename?: 'Eth0Stats', eth0IP?: string | null, eth0MAC?: string | null, rxErrors?: string | null, rxPackets?: string | null, txErrors?: string | null, txPackets?: string | null } | null, icmp?: { __typename?: 'Icmp', eth0?: string | null, wwan0?: string | null } | null, linkStatus?: { __typename?: 'LinkStatus', epiLink?: string | null, eth02?: string | null } | null, tunnelAccess?: { __typename?: 'TunnelAccess', eth0?: string | null, wwan0?: string | null } | null, priCardStatus?: { __typename?: 'PRICardStatus', priCardStatus?: string | null } | null, deviceFirmwareVersion?: { __typename?: 'DeviceFirmwareVersion', version?: string | null } | null, epiInfo?: Array<{ __typename?: 'EpiInfo', fwVersion?: string | null, ip?: string | null, mac?: string | null, provUrl?: string | null, uptime?: string | null }> | null, dcAvgPing?: { __typename?: 'DcAvgPingDocument', chPingAvg?: string | null, laPingAvg?: string | null, nyPingAvg?: string | null, atPingAvg?: string | null, dlPingAvg?: string | null, bestDC?: string | null, bestLatency?: string | null, timeUpdated?: string | null, error?: string | null } | null, cdmBoxStatus?: { __typename?: 'CdmBoxStatus', Status?: string | null, LastCdmCheck?: string | null, Duration?: string | null } | null } | null, cpu?: { __typename?: 'CpuInfo', CPUCount?: string | null, Guest?: string | null, GuestNice?: string | null, Idle?: string | null, Iowait?: string | null, irq?: string | null, Nice?: string | null, Softirq?: string | null, StatCount?: string | null, Steal?: string | null, System?: string | null, Total?: string | null, User?: string | null } | null, memory?: { __typename?: 'MemoryInfo', Active?: string | null, Available?: string | null, Buffers?: string | null, Cached?: string | null, Free?: string | null, Inactive?: string | null, MemAvailableEnabled?: string | null, SwapCached?: string | null, SwapFree?: string | null, SwapTotal?: string | null, SwapUsed?: string | null, Total?: string | null, Used?: string | null } | null, network?: Array<{ __typename?: 'NetworkInterface', Name?: string | null, RxBytes?: string | null, TxBytes?: string | null }> | null, portStatuses?: { __typename?: 'PortStatuses', eth0?: string | null, eth1?: string | null, eth2?: string | null, wg0?: string | null, wg7?: string | null, wlan0?: string | null, wwan0?: string | null } | null } | null, vSwitchTab?: { __typename?: 'VSwitchTab', registered?: boolean | null, registerationConfigCreated?: boolean | null, portsConfigCreated?: Array<string> | null, portsInfo?: Array<{ __typename?: 'PortInfo', port?: string | null, calledId?: string | null, recording?: string | null, trunkType?: string | null }> | null } | null, liveVSwitchTab?: { __typename?: 'VSwitchTab', registered?: boolean | null, registerationConfigCreated?: boolean | null, portsConfigCreated?: Array<string> | null, portsInfo?: Array<{ __typename?: 'PortInfo', port?: string | null, calledId?: string | null, recording?: string | null, trunkType?: string | null }> | null } | null } };

export type EpikBoxDeviceViewQueryVariables = Exact<{
  id: Scalars['String']['input'];
}>;


export type EpikBoxDeviceViewQuery = { __typename?: 'Query', EpikBoxById: { __typename?: 'EdgeDeviceDetails', _id: string, displayName?: string | null, serialNumber?: string | null, fwVersion?: string | null, creationDate?: any | null, vpnAddress?: string | null, apuType?: string | null, model?: string | null, lteIp?: string | null, lteIp2?: string | null, primarySim?: string | null, priorityInterface?: string | null, simStatus?: string | null, liveSimStatus?: string | null, advancedRouting?: boolean | null, myEpik?: boolean | null, boxRegistrar?: BoxRegistrarEnum | null, customerProvidedIp?: string | null, activeLTE?: Array<{ __typename?: 'ActiveLTESim', sim?: string | null, imei?: string | null, mtn?: string | null, ip?: string | null, iccid?: string | null, timeStamp?: any | null }> | null, modems?: Array<{ __typename?: 'Modem', imeis?: string | null, label?: string | null, phones?: Array<string> | null, sims?: Array<string> | null, type?: string | null }> | null, modemInfo?: { __typename?: 'ModemInfoDocument', model?: string | null, imei?: string | null, sim?: string | null, ipAddress?: string | null } | null } };

export type ListEpikBoxesQueryVariables = Exact<{
  pagination?: InputMaybe<PaginationInput>;
  filter: EpikBoxFilterInput;
}>;


export type ListEpikBoxesQuery = { __typename?: 'Query', ListEpikBoxes: { __typename?: 'EpikBoxDocumentPopulatedPaginationResult', docs: Array<{ __typename?: 'EpikBoxDocumentPopulated', _id: string, serialNumber?: string | null, vpnAddress?: string | null, creationDate?: any | null, deleted?: boolean | null, monitor?: boolean | null, displayName?: string | null, numPorts?: number | null, companyDoc?: { __typename?: 'CompanyDocument', _id: string, name: string } | null, locationDoc?: { __typename?: 'LocationDocument', _id: string, locationName: string } | null }>, pagination: { __typename?: 'PaginationResponseOptions', currentPage: number, totalPages: number, count: number } } };

export type ListNumbersQueryVariables = Exact<{
  filter: ListNumberInput;
  pagination: PaginationInput;
}>;


export type ListNumbersQuery = { __typename?: 'Query', ListNumbers: { __typename?: 'NumberDocumentPaginationResult', docs: Array<{ __typename?: 'NumberDocument', _id: string, number?: string | null, linkedBox?: any | null, company?: any | null, callerIdName?: string | null }>, pagination: { __typename?: 'PaginationResponseOptions', currentPage: number, totalPages: number, count: number } } };

export type ListEpiQueryVariables = Exact<{
  input: EpiFilterInput;
  pagination: PaginationInput;
}>;


export type ListEpiQuery = { __typename?: 'Query', ListEpi: { __typename?: 'EpiDocumentPopulatedPaginationResult', pagination: { __typename?: 'PaginationResponseOptions', totalPages: number, count: number }, docs: Array<{ __typename?: 'EpiDocumentPopulated', _id: string, macAddress?: string | null, obiNumber?: string | null }> } };


export const EpiUpdateHookDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"subscription","name":{"kind":"Name","value":"EpiUpdateHook"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"fields"}},"type":{"kind":"NonNullType","type":{"kind":"ListType","type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"EpiUpdateHook"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"fields"},"value":{"kind":"Variable","name":{"kind":"Name","value":"fields"}}},{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"fieldName"}},{"kind":"Field","name":{"kind":"Name","value":"value"}},{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"error"}}]}}]}}]} as unknown as DocumentNode<EpiUpdateHookSubscription, EpiUpdateHookSubscriptionVariables>;
export const EpikBoxUpdateHookDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"subscription","name":{"kind":"Name","value":"EpikBoxUpdateHook"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"fields"}},"type":{"kind":"NonNullType","type":{"kind":"ListType","type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"EpikBoxUpdateHook"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"fields"},"value":{"kind":"Variable","name":{"kind":"Name","value":"fields"}}},{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"fieldName"}},{"kind":"Field","name":{"kind":"Name","value":"value"}},{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"error"}}]}}]}}]} as unknown as DocumentNode<EpikBoxUpdateHookSubscription, EpikBoxUpdateHookSubscriptionVariables>;
export const EpiDetailByIdDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"EpiDetailById"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"EpiDetailById"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"deviceId"}},{"kind":"Field","name":{"kind":"Name","value":"obiNumber"}},{"kind":"Field","name":{"kind":"Name","value":"macAddress"}},{"kind":"Field","name":{"kind":"Name","value":"assignedTo"}},{"kind":"Field","name":{"kind":"Name","value":"registrationMeta"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"wanInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"placeHolder"}},{"kind":"Field","name":{"kind":"Name","value":"ip"}},{"kind":"Field","name":{"kind":"Name","value":"subnet"}},{"kind":"Field","name":{"kind":"Name","value":"gateway"}},{"kind":"Field","name":{"kind":"Name","value":"dns"}}]}},{"kind":"Field","name":{"kind":"Name","value":"sp1ServiceStatus"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"callState"}}]}},{"kind":"Field","name":{"kind":"Name","value":"sp2ServiceStatus"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"callState"}}]}},{"kind":"Field","name":{"kind":"Name","value":"obiTalkServiceStatus"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"placeHolder"}},{"kind":"Field","name":{"kind":"Name","value":"status"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"liveRegistrationMeta"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"wanInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"placeHolder"}},{"kind":"Field","name":{"kind":"Name","value":"ip"}},{"kind":"Field","name":{"kind":"Name","value":"subnet"}},{"kind":"Field","name":{"kind":"Name","value":"gateway"}},{"kind":"Field","name":{"kind":"Name","value":"dns"}}]}},{"kind":"Field","name":{"kind":"Name","value":"sp1ServiceStatus"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"callState"}}]}},{"kind":"Field","name":{"kind":"Name","value":"sp2ServiceStatus"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"callState"}}]}},{"kind":"Field","name":{"kind":"Name","value":"obiTalkServiceStatus"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"placeHolder"}},{"kind":"Field","name":{"kind":"Name","value":"status"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"portPhysicalMeta"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"state"}},{"kind":"Field","name":{"kind":"Name","value":"loopCurrent"}},{"kind":"Field","name":{"kind":"Name","value":"Vbat"}},{"kind":"Field","name":{"kind":"Name","value":"tipRingVoltage"}},{"kind":"Field","name":{"kind":"Name","value":"lastCallerInfo"}}]}},{"kind":"Field","name":{"kind":"Name","value":"livePortPhysicalMeta"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"state"}},{"kind":"Field","name":{"kind":"Name","value":"loopCurrent"}},{"kind":"Field","name":{"kind":"Name","value":"Vbat"}},{"kind":"Field","name":{"kind":"Name","value":"tipRingVoltage"}},{"kind":"Field","name":{"kind":"Name","value":"lastCallerInfo"}}]}}]}}]}}]} as unknown as DocumentNode<EpiDetailByIdQuery, EpiDetailByIdQueryVariables>;
export const EpikBoxByIdDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"EpikBoxById"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"EpikBoxById"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"displayName"}},{"kind":"Field","name":{"kind":"Name","value":"serialNumber"}},{"kind":"Field","name":{"kind":"Name","value":"vpnAddress"}},{"kind":"Field","name":{"kind":"Name","value":"powerState"}},{"kind":"Field","name":{"kind":"Name","value":"livePowerState"}},{"kind":"Field","name":{"kind":"Name","value":"activeInterface"}},{"kind":"Field","name":{"kind":"Name","value":"signalStrength"}},{"kind":"Field","name":{"kind":"Name","value":"liveSignalStrength"}},{"kind":"Field","name":{"kind":"Name","value":"liveSignalStrength"}},{"kind":"Field","name":{"kind":"Name","value":"liveActiveInterface"}},{"kind":"Field","name":{"kind":"Name","value":"monitor"}},{"kind":"Field","name":{"kind":"Name","value":"publicIp"}},{"kind":"Field","name":{"kind":"Name","value":"livePublicIp"}},{"kind":"Field","name":{"kind":"Name","value":"simStatus"}},{"kind":"Field","name":{"kind":"Name","value":"liveSimStatus"}},{"kind":"Field","name":{"kind":"Name","value":"deviceOnline"}},{"kind":"Field","name":{"kind":"Name","value":"liveDeviceOnline"}},{"kind":"Field","name":{"kind":"Name","value":"lanIp"}},{"kind":"Field","name":{"kind":"Name","value":"liveLanIp"}},{"kind":"Field","name":{"kind":"Name","value":"lteIp"}},{"kind":"Field","name":{"kind":"Name","value":"lteIp2"}},{"kind":"Field","name":{"kind":"Name","value":"registered"}},{"kind":"Field","name":{"kind":"Name","value":"activeCarrier"}},{"kind":"Field","name":{"kind":"Name","value":"datacenter"}},{"kind":"Field","name":{"kind":"Name","value":"lteAnalyzer"}},{"kind":"Field","name":{"kind":"Name","value":"speedTestData"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"latency"}},{"kind":"Field","name":{"kind":"Name","value":"jitter"}},{"kind":"Field","name":{"kind":"Name","value":"uploadSpeed"}},{"kind":"Field","name":{"kind":"Name","value":"downloadSpeed"}}]}},{"kind":"Field","name":{"kind":"Name","value":"speedTestVoice"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"latency"}},{"kind":"Field","name":{"kind":"Name","value":"jitter"}},{"kind":"Field","name":{"kind":"Name","value":"uploadSpeed"}},{"kind":"Field","name":{"kind":"Name","value":"downloadSpeed"}}]}},{"kind":"Field","name":{"kind":"Name","value":"preferredProviderTest"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"SimsInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"Error"}},{"kind":"Field","name":{"kind":"Name","value":"Jitter"}},{"kind":"Field","name":{"kind":"Name","value":"PacketLoss"}},{"kind":"Field","name":{"kind":"Name","value":"PingAvg"}},{"kind":"Field","name":{"kind":"Name","value":"SIM"}}]}},{"kind":"Field","name":{"kind":"Name","value":"TimeStamp"}},{"kind":"Field","name":{"kind":"Name","value":"initiatedTimeStamp"}}]}},{"kind":"Field","name":{"kind":"Name","value":"liveSpeedTestData"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"latency"}},{"kind":"Field","name":{"kind":"Name","value":"jitter"}},{"kind":"Field","name":{"kind":"Name","value":"uploadSpeed"}},{"kind":"Field","name":{"kind":"Name","value":"downloadSpeed"}}]}},{"kind":"Field","name":{"kind":"Name","value":"liveSpeedTestVoice"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"latency"}},{"kind":"Field","name":{"kind":"Name","value":"jitter"}},{"kind":"Field","name":{"kind":"Name","value":"uploadSpeed"}},{"kind":"Field","name":{"kind":"Name","value":"downloadSpeed"}}]}},{"kind":"Field","name":{"kind":"Name","value":"livePreferredProviderTest"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"SimsInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"Error"}},{"kind":"Field","name":{"kind":"Name","value":"Jitter"}},{"kind":"Field","name":{"kind":"Name","value":"PacketLoss"}},{"kind":"Field","name":{"kind":"Name","value":"PingAvg"}},{"kind":"Field","name":{"kind":"Name","value":"SIM"}}]}},{"kind":"Field","name":{"kind":"Name","value":"TimeStamp"}},{"kind":"Field","name":{"kind":"Name","value":"initiatedTimeStamp"}}]}},{"kind":"Field","name":{"kind":"Name","value":"sensorData"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"power"}},{"kind":"Field","name":{"kind":"Name","value":"temp"}}]}},{"kind":"Field","name":{"kind":"Name","value":"liveSensorData"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"power"}},{"kind":"Field","name":{"kind":"Name","value":"temp"}}]}},{"kind":"Field","name":{"kind":"Name","value":"myEpik"}},{"kind":"Field","name":{"kind":"Name","value":"model"}},{"kind":"Field","name":{"kind":"Name","value":"apuType"}},{"kind":"Field","name":{"kind":"Name","value":"powerSaveOption"}},{"kind":"Field","name":{"kind":"Name","value":"starCodes"}},{"kind":"Field","name":{"kind":"Name","value":"boxOfflineCallForward"}},{"kind":"Field","name":{"kind":"Name","value":"recording"}},{"kind":"Field","name":{"kind":"Name","value":"OBEnable"}},{"kind":"Field","name":{"kind":"Name","value":"eth3Disable"}},{"kind":"Field","name":{"kind":"Name","value":"nightlyUpdateTime"}},{"kind":"Field","name":{"kind":"Name","value":"ext_9override"}},{"kind":"Field","name":{"kind":"Name","value":"boxRegistrar"}},{"kind":"Field","name":{"kind":"Name","value":"e911Number"}},{"kind":"Field","name":{"kind":"Name","value":"primarySim"}},{"kind":"Field","name":{"kind":"Name","value":"priorityInterface"}},{"kind":"Field","name":{"kind":"Name","value":"portAlerts"}},{"kind":"Field","name":{"kind":"Name","value":"currentApn"}},{"kind":"Field","name":{"kind":"Name","value":"epiTimezone"}},{"kind":"Field","name":{"kind":"Name","value":"features"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"showPriTab"}},{"kind":"Field","name":{"kind":"Name","value":"pri"}},{"kind":"Field","name":{"kind":"Name","value":"sip"}},{"kind":"Field","name":{"kind":"Name","value":"twoModems"}},{"kind":"Field","name":{"kind":"Name","value":"daisyChain"}},{"kind":"Field","name":{"kind":"Name","value":"dcAutoUpdate"}}]}},{"kind":"Field","name":{"kind":"Name","value":"wifiStatus"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"Error_Msg"}},{"kind":"Field","name":{"kind":"Name","value":"Gateway"}},{"kind":"Field","name":{"kind":"Name","value":"IP"}},{"kind":"Field","name":{"kind":"Name","value":"Mode"}},{"kind":"Field","name":{"kind":"Name","value":"Password"}},{"kind":"Field","name":{"kind":"Name","value":"SSID"}},{"kind":"Field","name":{"kind":"Name","value":"Sec_Mode"}},{"kind":"Field","name":{"kind":"Name","value":"Status"}},{"kind":"Field","name":{"kind":"Name","value":"Subnet"}}]}},{"kind":"Field","name":{"kind":"Name","value":"liveWifiStatus"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"Error_Msg"}},{"kind":"Field","name":{"kind":"Name","value":"Gateway"}},{"kind":"Field","name":{"kind":"Name","value":"IP"}},{"kind":"Field","name":{"kind":"Name","value":"Mode"}},{"kind":"Field","name":{"kind":"Name","value":"Password"}},{"kind":"Field","name":{"kind":"Name","value":"SSID"}},{"kind":"Field","name":{"kind":"Name","value":"Sec_Mode"}},{"kind":"Field","name":{"kind":"Name","value":"Status"}},{"kind":"Field","name":{"kind":"Name","value":"Subnet"}}]}},{"kind":"Field","name":{"kind":"Name","value":"networkInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"dns"}},{"kind":"Field","name":{"kind":"Name","value":"interfaces"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"interface"}},{"kind":"Field","name":{"kind":"Name","value":"internet"}},{"kind":"Field","name":{"kind":"Name","value":"icmp"}},{"kind":"Field","name":{"kind":"Name","value":"wg"}}]}},{"kind":"Field","name":{"kind":"Name","value":"timestamp"}},{"kind":"Field","name":{"kind":"Name","value":"error"}}]}},{"kind":"Field","name":{"kind":"Name","value":"liveNetworkInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"dns"}},{"kind":"Field","name":{"kind":"Name","value":"interfaces"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"interface"}},{"kind":"Field","name":{"kind":"Name","value":"internet"}},{"kind":"Field","name":{"kind":"Name","value":"icmp"}},{"kind":"Field","name":{"kind":"Name","value":"wg"}}]}},{"kind":"Field","name":{"kind":"Name","value":"timestamp"}},{"kind":"Field","name":{"kind":"Name","value":"error"}}]}},{"kind":"Field","name":{"kind":"Name","value":"priSettings"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"isSynced"}},{"kind":"Field","name":{"kind":"Name","value":"pbx"}},{"kind":"Field","name":{"kind":"Name","value":"priFailovers"}},{"kind":"Field","name":{"kind":"Name","value":"echoChannels"}},{"kind":"Field","name":{"kind":"Name","value":"channels"}},{"kind":"Field","name":{"kind":"Name","value":"framing"}},{"kind":"Field","name":{"kind":"Name","value":"lineCode"}},{"kind":"Field","name":{"kind":"Name","value":"timeAndSource"}},{"kind":"Field","name":{"kind":"Name","value":"dChannel"}},{"kind":"Field","name":{"kind":"Name","value":"bChannels"}},{"kind":"Field","name":{"kind":"Name","value":"switchType"}},{"kind":"Field","name":{"kind":"Name","value":"echoCancellation"}},{"kind":"Field","name":{"kind":"Name","value":"echoCancellationType"}},{"kind":"Field","name":{"kind":"Name","value":"numberSettings"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"dtmfType"}},{"kind":"Field","name":{"kind":"Name","value":"dnis"}},{"kind":"Field","name":{"kind":"Name","value":"callerIdName"}},{"kind":"Field","name":{"kind":"Name","value":"callerIdNumber"}},{"kind":"Field","name":{"kind":"Name","value":"numbers"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"e911Number"}},{"kind":"Field","name":{"kind":"Name","value":"shippedAddress"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"attn"}},{"kind":"Field","name":{"kind":"Name","value":"street1"}},{"kind":"Field","name":{"kind":"Name","value":"street2"}},{"kind":"Field","name":{"kind":"Name","value":"city"}},{"kind":"Field","name":{"kind":"Name","value":"state"}},{"kind":"Field","name":{"kind":"Name","value":"zip"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"sipSettings"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"casFailovers"}},{"kind":"Field","name":{"kind":"Name","value":"channels"}},{"kind":"Field","name":{"kind":"Name","value":"framing"}},{"kind":"Field","name":{"kind":"Name","value":"lineCode"}},{"kind":"Field","name":{"kind":"Name","value":"timeAndSource"}},{"kind":"Field","name":{"kind":"Name","value":"bChannel"}},{"kind":"Field","name":{"kind":"Name","value":"switchType"}},{"kind":"Field","name":{"kind":"Name","value":"echoCancellation"}},{"kind":"Field","name":{"kind":"Name","value":"echoCancellationType"}},{"kind":"Field","name":{"kind":"Name","value":"channelRange"}},{"kind":"Field","name":{"kind":"Name","value":"authType"}},{"kind":"Field","name":{"kind":"Name","value":"ip"}},{"kind":"Field","name":{"kind":"Name","value":"username"}},{"kind":"Field","name":{"kind":"Name","value":"password"}},{"kind":"Field","name":{"kind":"Name","value":"numberSettings"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"dtmfType"}},{"kind":"Field","name":{"kind":"Name","value":"dnis"}},{"kind":"Field","name":{"kind":"Name","value":"callerIdName"}},{"kind":"Field","name":{"kind":"Name","value":"callerIdNumber"}},{"kind":"Field","name":{"kind":"Name","value":"numbers"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"e911Number"}},{"kind":"Field","name":{"kind":"Name","value":"shippedAddress"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"attn"}},{"kind":"Field","name":{"kind":"Name","value":"street1"}},{"kind":"Field","name":{"kind":"Name","value":"street2"}},{"kind":"Field","name":{"kind":"Name","value":"city"}},{"kind":"Field","name":{"kind":"Name","value":"state"}},{"kind":"Field","name":{"kind":"Name","value":"zip"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"dcAvgPing"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"chPingAvg"}},{"kind":"Field","name":{"kind":"Name","value":"laPingAvg"}},{"kind":"Field","name":{"kind":"Name","value":"nyPingAvg"}},{"kind":"Field","name":{"kind":"Name","value":"atPingAvg"}},{"kind":"Field","name":{"kind":"Name","value":"dlPingAvg"}},{"kind":"Field","name":{"kind":"Name","value":"bestDC"}},{"kind":"Field","name":{"kind":"Name","value":"bestLatency"}},{"kind":"Field","name":{"kind":"Name","value":"timeUpdated"}},{"kind":"Field","name":{"kind":"Name","value":"error"}}]}},{"kind":"Field","name":{"kind":"Name","value":"liveDcAvgPing"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"chPingAvg"}},{"kind":"Field","name":{"kind":"Name","value":"laPingAvg"}},{"kind":"Field","name":{"kind":"Name","value":"nyPingAvg"}},{"kind":"Field","name":{"kind":"Name","value":"atPingAvg"}},{"kind":"Field","name":{"kind":"Name","value":"dlPingAvg"}},{"kind":"Field","name":{"kind":"Name","value":"bestDC"}},{"kind":"Field","name":{"kind":"Name","value":"bestLatency"}},{"kind":"Field","name":{"kind":"Name","value":"timeUpdated"}},{"kind":"Field","name":{"kind":"Name","value":"error"}}]}},{"kind":"Field","name":{"kind":"Name","value":"modemInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"carrier"}},{"kind":"Field","name":{"kind":"Name","value":"carrier_placeholder"}},{"kind":"Field","name":{"kind":"Name","value":"imei"}},{"kind":"Field","name":{"kind":"Name","value":"imei_placeholder"}},{"kind":"Field","name":{"kind":"Name","value":"ipAddress"}},{"kind":"Field","name":{"kind":"Name","value":"ipAddress_placeholder"}},{"kind":"Field","name":{"kind":"Name","value":"manufacturer"}},{"kind":"Field","name":{"kind":"Name","value":"manufacturer_placeholder"}},{"kind":"Field","name":{"kind":"Name","value":"model"}},{"kind":"Field","name":{"kind":"Name","value":"model_placeholder"}},{"kind":"Field","name":{"kind":"Name","value":"sim"}},{"kind":"Field","name":{"kind":"Name","value":"sim_placeholder"}}]}},{"kind":"Field","name":{"kind":"Name","value":"liveModemInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"carrier"}},{"kind":"Field","name":{"kind":"Name","value":"carrier_placeholder"}},{"kind":"Field","name":{"kind":"Name","value":"imei"}},{"kind":"Field","name":{"kind":"Name","value":"imei_placeholder"}},{"kind":"Field","name":{"kind":"Name","value":"ipAddress"}},{"kind":"Field","name":{"kind":"Name","value":"ipAddress_placeholder"}},{"kind":"Field","name":{"kind":"Name","value":"manufacturer"}},{"kind":"Field","name":{"kind":"Name","value":"manufacturer_placeholder"}},{"kind":"Field","name":{"kind":"Name","value":"model"}},{"kind":"Field","name":{"kind":"Name","value":"model_placeholder"}},{"kind":"Field","name":{"kind":"Name","value":"sim"}},{"kind":"Field","name":{"kind":"Name","value":"sim_placeholder"}}]}},{"kind":"Field","name":{"kind":"Name","value":"companyDoc"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}}]}},{"kind":"Field","name":{"kind":"Name","value":"obiDocs"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"macAddress"}},{"kind":"Field","name":{"kind":"Name","value":"obiNumber"}},{"kind":"Field","name":{"kind":"Name","value":"deviceId"}},{"kind":"Field","name":{"kind":"Name","value":"port1"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"boxPortNumber"}},{"kind":"Field","name":{"kind":"Name","value":"registered"}},{"kind":"Field","name":{"kind":"Name","value":"disabled"}},{"kind":"Field","name":{"kind":"Name","value":"provisionDate"}},{"kind":"Field","name":{"kind":"Name","value":"enableDisableLastUpdatedTime"}},{"kind":"Field","name":{"kind":"Name","value":"updatedOn"}},{"kind":"Field","name":{"kind":"Name","value":"serviceName"}},{"kind":"Field","name":{"kind":"Name","value":"monitored"}},{"kind":"Field","name":{"kind":"Name","value":"callerIdMasking"}},{"kind":"Field","name":{"kind":"Name","value":"callerIdMaskingNumber"}},{"kind":"Field","name":{"kind":"Name","value":"SilenceDetectSensitivity"}},{"kind":"Field","name":{"kind":"Name","value":"OnHookTipRingVoltage"}},{"kind":"Field","name":{"kind":"Name","value":"OffHookCurrentMax"}},{"kind":"Field","name":{"kind":"Name","value":"DTMFDetectMinLength"}},{"kind":"Field","name":{"kind":"Name","value":"DTMFDetectMinGap"}},{"kind":"Field","name":{"kind":"Name","value":"ChannelTxGain"}},{"kind":"Field","name":{"kind":"Name","value":"ChannelRxGain"}},{"kind":"Field","name":{"kind":"Name","value":"DTMFMethod"}},{"kind":"Field","name":{"kind":"Name","value":"DTMFPlaybackLevel"}},{"kind":"Field","name":{"kind":"Name","value":"RingVoltage"}},{"kind":"Field","name":{"kind":"Name","value":"DigitMapShortTimer"}},{"kind":"Field","name":{"kind":"Name","value":"CPCDelayTime"}},{"kind":"Field","name":{"kind":"Name","value":"CPCDuration"}},{"kind":"Field","name":{"kind":"Name","value":"showVadToggle"}},{"kind":"Field","name":{"kind":"Name","value":"showModemModeToggle"}},{"kind":"Field","name":{"kind":"Name","value":"modemMode"}},{"kind":"Field","name":{"kind":"Name","value":"showT38EnableToggle"}},{"kind":"Field","name":{"kind":"Name","value":"t38Enabled"}},{"kind":"Field","name":{"kind":"Name","value":"showFaxEnabledToggle"}},{"kind":"Field","name":{"kind":"Name","value":"faxEnabled"}},{"kind":"Field","name":{"kind":"Name","value":"showJitterDelay"}},{"kind":"Field","name":{"kind":"Name","value":"JitterMinDelay"}},{"kind":"Field","name":{"kind":"Name","value":"JitterMaxDelay"}},{"kind":"Field","name":{"kind":"Name","value":"showModemOnlyToggle"}},{"kind":"Field","name":{"kind":"Name","value":"modemOnly"}},{"kind":"Field","name":{"kind":"Name","value":"forceForward"}},{"kind":"Field","name":{"kind":"Name","value":"forceForwardNumber"}},{"kind":"Field","name":{"kind":"Name","value":"configOverride"}},{"kind":"Field","name":{"kind":"Name","value":"e911Number"}},{"kind":"Field","name":{"kind":"Name","value":"e911Enabled"}},{"kind":"Field","name":{"kind":"Name","value":"voip"}},{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"connected"}},{"kind":"Field","name":{"kind":"Name","value":"remoteHost"}},{"kind":"Field","name":{"kind":"Name","value":"ipAddress"}},{"kind":"Field","name":{"kind":"Name","value":"remotePort"}},{"kind":"Field","name":{"kind":"Name","value":"authName"}},{"kind":"Field","name":{"kind":"Name","value":"assignedNumber"}},{"kind":"Field","name":{"kind":"Name","value":"vmNumber"}},{"kind":"Field","name":{"kind":"Name","value":"advancedModemConfiguration"}},{"kind":"Field","name":{"kind":"Name","value":"portActivated"}},{"kind":"Field","name":{"kind":"Name","value":"portActivatedDate"}},{"kind":"Field","name":{"kind":"Name","value":"startedBilling"}},{"kind":"Field","name":{"kind":"Name","value":"startedBillingDate"}},{"kind":"Field","name":{"kind":"Name","value":"callerIdOverride"}},{"kind":"Field","name":{"kind":"Name","value":"provisionUrl"}},{"kind":"Field","name":{"kind":"Name","value":"blockCallerId"}},{"kind":"Field","name":{"kind":"Name","value":"callWaiting"}},{"kind":"Field","name":{"kind":"Name","value":"configPassword"}},{"kind":"Field","name":{"kind":"Name","value":"assignedNumberRef"}},{"kind":"Field","name":{"kind":"Name","value":"appliedConfigTemplate"}},{"kind":"Field","name":{"kind":"Name","value":"vmBox"}},{"kind":"Field","name":{"kind":"Name","value":"vmailBoxDoc"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"pin"}},{"kind":"Field","name":{"kind":"Name","value":"totalMessages"}},{"kind":"Field","name":{"kind":"Name","value":"limit"}},{"kind":"Field","name":{"kind":"Name","value":"greeting"}},{"kind":"Field","name":{"kind":"Name","value":"notificationEmails"}},{"kind":"Field","name":{"kind":"Name","value":"notificationNumbers"}},{"kind":"Field","name":{"kind":"Name","value":"number"}},{"kind":"Field","name":{"kind":"Name","value":"numberDoc"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"number"}},{"kind":"Field","name":{"kind":"Name","value":"linkedBox"}},{"kind":"Field","name":{"kind":"Name","value":"assignedTo"}},{"kind":"Field","name":{"kind":"Name","value":"company"}},{"kind":"Field","name":{"kind":"Name","value":"type"}},{"kind":"Field","name":{"kind":"Name","value":"callerIdName"}},{"kind":"Field","name":{"kind":"Name","value":"isForwarded"}},{"kind":"Field","name":{"kind":"Name","value":"huntPorts"}},{"kind":"Field","name":{"kind":"Name","value":"huntType"}},{"kind":"Field","name":{"kind":"Name","value":"portLabel"}},{"kind":"Field","name":{"kind":"Name","value":"forwardUris"}},{"kind":"Field","name":{"kind":"Name","value":"linkedToObi"}},{"kind":"Field","name":{"kind":"Name","value":"faxDeliveryConfirmation"}},{"kind":"Field","name":{"kind":"Name","value":"savefaxes"}},{"kind":"Field","name":{"kind":"Name","value":"confirmationEmail"}},{"kind":"Field","name":{"kind":"Name","value":"dateCreated"}},{"kind":"Field","name":{"kind":"Name","value":"deleted"}},{"kind":"Field","name":{"kind":"Name","value":"enabled"}},{"kind":"Field","name":{"kind":"Name","value":"cid"}},{"kind":"Field","name":{"kind":"Name","value":"emails"}},{"kind":"Field","name":{"kind":"Name","value":"allowedUsers"}},{"kind":"Field","name":{"kind":"Name","value":"allowedNumbers"}},{"kind":"Field","name":{"kind":"Name","value":"groupMembers"}},{"kind":"Field","name":{"kind":"Name","value":"forwardNumbers"}},{"kind":"Field","name":{"kind":"Name","value":"fallbackEmails"}},{"kind":"Field","name":{"kind":"Name","value":"destination"}},{"kind":"Field","name":{"kind":"Name","value":"cidOverride"}},{"kind":"Field","name":{"kind":"Name","value":"port"}},{"kind":"Field","name":{"kind":"Name","value":"temporary"}},{"kind":"Field","name":{"kind":"Name","value":"greetingFilePath"}},{"kind":"Field","name":{"kind":"Name","value":"agreementFilePath"}},{"kind":"Field","name":{"kind":"Name","value":"carrier"}},{"kind":"Field","name":{"kind":"Name","value":"e911Carrier"}},{"kind":"Field","name":{"kind":"Name","value":"tdmRoute"}},{"kind":"Field","name":{"kind":"Name","value":"trunk"}},{"kind":"Field","name":{"kind":"Name","value":"previousTrunk"}},{"kind":"Field","name":{"kind":"Name","value":"linkedToExternalAta"}},{"kind":"Field","name":{"kind":"Name","value":"alarmRelayInfo"}},{"kind":"Field","name":{"kind":"Name","value":"inBoundCallBlocked"}},{"kind":"Field","name":{"kind":"Name","value":"inbandRouting"}},{"kind":"Field","name":{"kind":"Name","value":"isNumberLocked"}},{"kind":"Field","name":{"kind":"Name","value":"usbPort"}},{"kind":"Field","name":{"kind":"Name","value":"onlyEfax"}},{"kind":"Field","name":{"kind":"Name","value":"isTollFree"}},{"kind":"Field","name":{"kind":"Name","value":"tollFreeNumbers"}},{"kind":"Field","name":{"kind":"Name","value":"provisionDate"}},{"kind":"Field","name":{"kind":"Name","value":"spoofType"}},{"kind":"Field","name":{"kind":"Name","value":"spoofIncomingCid"}},{"kind":"Field","name":{"kind":"Name","value":"trunkSelection"}},{"kind":"Field","name":{"kind":"Name","value":"numDigits"}}]}},{"kind":"Field","name":{"kind":"Name","value":"deleted"}},{"kind":"Field","name":{"kind":"Name","value":"createdAt"}},{"kind":"Field","name":{"kind":"Name","value":"updatedAt"}}]}},{"kind":"Field","name":{"kind":"Name","value":"assignedNumberDoc"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"number"}},{"kind":"Field","name":{"kind":"Name","value":"linkedBox"}},{"kind":"Field","name":{"kind":"Name","value":"assignedTo"}},{"kind":"Field","name":{"kind":"Name","value":"company"}},{"kind":"Field","name":{"kind":"Name","value":"type"}},{"kind":"Field","name":{"kind":"Name","value":"callerIdName"}},{"kind":"Field","name":{"kind":"Name","value":"isForwarded"}},{"kind":"Field","name":{"kind":"Name","value":"huntPorts"}},{"kind":"Field","name":{"kind":"Name","value":"huntType"}},{"kind":"Field","name":{"kind":"Name","value":"portLabel"}},{"kind":"Field","name":{"kind":"Name","value":"forwardUris"}},{"kind":"Field","name":{"kind":"Name","value":"linkedToObi"}},{"kind":"Field","name":{"kind":"Name","value":"faxDeliveryConfirmation"}},{"kind":"Field","name":{"kind":"Name","value":"savefaxes"}},{"kind":"Field","name":{"kind":"Name","value":"confirmationEmail"}},{"kind":"Field","name":{"kind":"Name","value":"dateCreated"}},{"kind":"Field","name":{"kind":"Name","value":"deleted"}},{"kind":"Field","name":{"kind":"Name","value":"enabled"}},{"kind":"Field","name":{"kind":"Name","value":"cid"}},{"kind":"Field","name":{"kind":"Name","value":"emails"}},{"kind":"Field","name":{"kind":"Name","value":"allowedUsers"}},{"kind":"Field","name":{"kind":"Name","value":"allowedNumbers"}},{"kind":"Field","name":{"kind":"Name","value":"groupMembers"}},{"kind":"Field","name":{"kind":"Name","value":"forwardNumbers"}},{"kind":"Field","name":{"kind":"Name","value":"fallbackEmails"}},{"kind":"Field","name":{"kind":"Name","value":"routeInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"route"}},{"kind":"Field","name":{"kind":"Name","value":"gateway"}}]}},{"kind":"Field","name":{"kind":"Name","value":"previousRouteInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"route"}},{"kind":"Field","name":{"kind":"Name","value":"gateway"}}]}},{"kind":"Field","name":{"kind":"Name","value":"destination"}},{"kind":"Field","name":{"kind":"Name","value":"cidOverride"}},{"kind":"Field","name":{"kind":"Name","value":"port"}},{"kind":"Field","name":{"kind":"Name","value":"temporary"}},{"kind":"Field","name":{"kind":"Name","value":"greetingFilePath"}},{"kind":"Field","name":{"kind":"Name","value":"agreementFilePath"}},{"kind":"Field","name":{"kind":"Name","value":"carrier"}},{"kind":"Field","name":{"kind":"Name","value":"e911Carrier"}},{"kind":"Field","name":{"kind":"Name","value":"tdmRoute"}},{"kind":"Field","name":{"kind":"Name","value":"trunk"}},{"kind":"Field","name":{"kind":"Name","value":"previousTrunk"}},{"kind":"Field","name":{"kind":"Name","value":"linkedToExternalAta"}},{"kind":"Field","name":{"kind":"Name","value":"alarmRelayInfo"}},{"kind":"Field","name":{"kind":"Name","value":"inBoundCallBlocked"}},{"kind":"Field","name":{"kind":"Name","value":"inbandRouting"}},{"kind":"Field","name":{"kind":"Name","value":"isNumberLocked"}},{"kind":"Field","name":{"kind":"Name","value":"usbPort"}},{"kind":"Field","name":{"kind":"Name","value":"onlyEfax"}},{"kind":"Field","name":{"kind":"Name","value":"isTollFree"}},{"kind":"Field","name":{"kind":"Name","value":"tollFreeNumbers"}},{"kind":"Field","name":{"kind":"Name","value":"provisionDate"}},{"kind":"Field","name":{"kind":"Name","value":"spoofType"}},{"kind":"Field","name":{"kind":"Name","value":"spoofIncomingCid"}},{"kind":"Field","name":{"kind":"Name","value":"trunkSelection"}},{"kind":"Field","name":{"kind":"Name","value":"numDigits"}},{"kind":"Field","name":{"kind":"Name","value":"e911Info"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"address1"}},{"kind":"Field","name":{"kind":"Name","value":"address2"}},{"kind":"Field","name":{"kind":"Name","value":"city"}},{"kind":"Field","name":{"kind":"Name","value":"state"}},{"kind":"Field","name":{"kind":"Name","value":"zip"}},{"kind":"Field","name":{"kind":"Name","value":"country"}},{"kind":"Field","name":{"kind":"Name","value":"locationid"}},{"kind":"Field","name":{"kind":"Name","value":"callername"}},{"kind":"Field","name":{"kind":"Name","value":"vendor"}},{"kind":"Field","name":{"kind":"Name","value":"createdAt"}},{"kind":"Field","name":{"kind":"Name","value":"updatedAt"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"appliedConfigTemplateDoc"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"default"}},{"kind":"Field","name":{"kind":"Name","value":"SilenceDetectSensitivity"}},{"kind":"Field","name":{"kind":"Name","value":"OnHookTipRingVoltage"}},{"kind":"Field","name":{"kind":"Name","value":"OffHookCurrentMax"}},{"kind":"Field","name":{"kind":"Name","value":"DTMFDetectMinLength"}},{"kind":"Field","name":{"kind":"Name","value":"DTMFDetectMinGap"}},{"kind":"Field","name":{"kind":"Name","value":"ChannelTxGain"}},{"kind":"Field","name":{"kind":"Name","value":"ChannelRxGain"}},{"kind":"Field","name":{"kind":"Name","value":"DTMFMethod"}},{"kind":"Field","name":{"kind":"Name","value":"DTMFPlaybackLevel"}},{"kind":"Field","name":{"kind":"Name","value":"RingVoltage"}},{"kind":"Field","name":{"kind":"Name","value":"DigitMapShortTimer"}},{"kind":"Field","name":{"kind":"Name","value":"CPCDelayTime"}},{"kind":"Field","name":{"kind":"Name","value":"CPCDuration"}},{"kind":"Field","name":{"kind":"Name","value":"showModemModeToggle"}},{"kind":"Field","name":{"kind":"Name","value":"modemMode"}},{"kind":"Field","name":{"kind":"Name","value":"showT38EnableToggle"}},{"kind":"Field","name":{"kind":"Name","value":"t38Enabled"}},{"kind":"Field","name":{"kind":"Name","value":"showFaxEnabledToggle"}},{"kind":"Field","name":{"kind":"Name","value":"faxEnabled"}},{"kind":"Field","name":{"kind":"Name","value":"showJitterDelay"}},{"kind":"Field","name":{"kind":"Name","value":"JitterMinDelay"}},{"kind":"Field","name":{"kind":"Name","value":"JitterMaxDelay"}},{"kind":"Field","name":{"kind":"Name","value":"inbandRoute"}},{"kind":"Field","name":{"kind":"Name","value":"showInbandRouteToggle"}},{"kind":"Field","name":{"kind":"Name","value":"trunkSelection"}},{"kind":"Field","name":{"kind":"Name","value":"showTrunkSelectionToggle"}},{"kind":"Field","name":{"kind":"Name","value":"vadEnable"}},{"kind":"Field","name":{"kind":"Name","value":"showVadToggle"}},{"kind":"Field","name":{"kind":"Name","value":"showModemOnlyToggle"}},{"kind":"Field","name":{"kind":"Name","value":"modemOnly"}},{"kind":"Field","name":{"kind":"Name","value":"ataType"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"port2"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"boxPortNumber"}},{"kind":"Field","name":{"kind":"Name","value":"registered"}},{"kind":"Field","name":{"kind":"Name","value":"disabled"}},{"kind":"Field","name":{"kind":"Name","value":"provisionDate"}},{"kind":"Field","name":{"kind":"Name","value":"enableDisableLastUpdatedTime"}},{"kind":"Field","name":{"kind":"Name","value":"updatedOn"}},{"kind":"Field","name":{"kind":"Name","value":"serviceName"}},{"kind":"Field","name":{"kind":"Name","value":"monitored"}},{"kind":"Field","name":{"kind":"Name","value":"callerIdMasking"}},{"kind":"Field","name":{"kind":"Name","value":"callerIdMaskingNumber"}},{"kind":"Field","name":{"kind":"Name","value":"SilenceDetectSensitivity"}},{"kind":"Field","name":{"kind":"Name","value":"OnHookTipRingVoltage"}},{"kind":"Field","name":{"kind":"Name","value":"OffHookCurrentMax"}},{"kind":"Field","name":{"kind":"Name","value":"DTMFDetectMinLength"}},{"kind":"Field","name":{"kind":"Name","value":"DTMFDetectMinGap"}},{"kind":"Field","name":{"kind":"Name","value":"ChannelTxGain"}},{"kind":"Field","name":{"kind":"Name","value":"ChannelRxGain"}},{"kind":"Field","name":{"kind":"Name","value":"DTMFMethod"}},{"kind":"Field","name":{"kind":"Name","value":"DTMFPlaybackLevel"}},{"kind":"Field","name":{"kind":"Name","value":"RingVoltage"}},{"kind":"Field","name":{"kind":"Name","value":"DigitMapShortTimer"}},{"kind":"Field","name":{"kind":"Name","value":"CPCDelayTime"}},{"kind":"Field","name":{"kind":"Name","value":"CPCDuration"}},{"kind":"Field","name":{"kind":"Name","value":"showVadToggle"}},{"kind":"Field","name":{"kind":"Name","value":"showModemModeToggle"}},{"kind":"Field","name":{"kind":"Name","value":"modemMode"}},{"kind":"Field","name":{"kind":"Name","value":"showT38EnableToggle"}},{"kind":"Field","name":{"kind":"Name","value":"t38Enabled"}},{"kind":"Field","name":{"kind":"Name","value":"showFaxEnabledToggle"}},{"kind":"Field","name":{"kind":"Name","value":"faxEnabled"}},{"kind":"Field","name":{"kind":"Name","value":"showJitterDelay"}},{"kind":"Field","name":{"kind":"Name","value":"JitterMinDelay"}},{"kind":"Field","name":{"kind":"Name","value":"JitterMaxDelay"}},{"kind":"Field","name":{"kind":"Name","value":"showModemOnlyToggle"}},{"kind":"Field","name":{"kind":"Name","value":"modemOnly"}},{"kind":"Field","name":{"kind":"Name","value":"forceForward"}},{"kind":"Field","name":{"kind":"Name","value":"forceForwardNumber"}},{"kind":"Field","name":{"kind":"Name","value":"configOverride"}},{"kind":"Field","name":{"kind":"Name","value":"e911Number"}},{"kind":"Field","name":{"kind":"Name","value":"e911Enabled"}},{"kind":"Field","name":{"kind":"Name","value":"voip"}},{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"connected"}},{"kind":"Field","name":{"kind":"Name","value":"remoteHost"}},{"kind":"Field","name":{"kind":"Name","value":"ipAddress"}},{"kind":"Field","name":{"kind":"Name","value":"remotePort"}},{"kind":"Field","name":{"kind":"Name","value":"authName"}},{"kind":"Field","name":{"kind":"Name","value":"assignedNumber"}},{"kind":"Field","name":{"kind":"Name","value":"vmNumber"}},{"kind":"Field","name":{"kind":"Name","value":"advancedModemConfiguration"}},{"kind":"Field","name":{"kind":"Name","value":"portActivated"}},{"kind":"Field","name":{"kind":"Name","value":"portActivatedDate"}},{"kind":"Field","name":{"kind":"Name","value":"startedBilling"}},{"kind":"Field","name":{"kind":"Name","value":"startedBillingDate"}},{"kind":"Field","name":{"kind":"Name","value":"callerIdOverride"}},{"kind":"Field","name":{"kind":"Name","value":"provisionUrl"}},{"kind":"Field","name":{"kind":"Name","value":"blockCallerId"}},{"kind":"Field","name":{"kind":"Name","value":"callWaiting"}},{"kind":"Field","name":{"kind":"Name","value":"configPassword"}},{"kind":"Field","name":{"kind":"Name","value":"assignedNumberRef"}},{"kind":"Field","name":{"kind":"Name","value":"vmBox"}},{"kind":"Field","name":{"kind":"Name","value":"vmailBoxDoc"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"pin"}},{"kind":"Field","name":{"kind":"Name","value":"totalMessages"}},{"kind":"Field","name":{"kind":"Name","value":"limit"}},{"kind":"Field","name":{"kind":"Name","value":"greeting"}},{"kind":"Field","name":{"kind":"Name","value":"notificationEmails"}},{"kind":"Field","name":{"kind":"Name","value":"notificationNumbers"}},{"kind":"Field","name":{"kind":"Name","value":"number"}},{"kind":"Field","name":{"kind":"Name","value":"numberDoc"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"number"}},{"kind":"Field","name":{"kind":"Name","value":"linkedBox"}},{"kind":"Field","name":{"kind":"Name","value":"assignedTo"}},{"kind":"Field","name":{"kind":"Name","value":"company"}},{"kind":"Field","name":{"kind":"Name","value":"type"}},{"kind":"Field","name":{"kind":"Name","value":"callerIdName"}},{"kind":"Field","name":{"kind":"Name","value":"isForwarded"}},{"kind":"Field","name":{"kind":"Name","value":"huntPorts"}},{"kind":"Field","name":{"kind":"Name","value":"huntType"}},{"kind":"Field","name":{"kind":"Name","value":"portLabel"}},{"kind":"Field","name":{"kind":"Name","value":"forwardUris"}},{"kind":"Field","name":{"kind":"Name","value":"linkedToObi"}},{"kind":"Field","name":{"kind":"Name","value":"faxDeliveryConfirmation"}},{"kind":"Field","name":{"kind":"Name","value":"savefaxes"}},{"kind":"Field","name":{"kind":"Name","value":"confirmationEmail"}},{"kind":"Field","name":{"kind":"Name","value":"dateCreated"}},{"kind":"Field","name":{"kind":"Name","value":"deleted"}},{"kind":"Field","name":{"kind":"Name","value":"enabled"}},{"kind":"Field","name":{"kind":"Name","value":"cid"}},{"kind":"Field","name":{"kind":"Name","value":"emails"}},{"kind":"Field","name":{"kind":"Name","value":"allowedUsers"}},{"kind":"Field","name":{"kind":"Name","value":"allowedNumbers"}},{"kind":"Field","name":{"kind":"Name","value":"groupMembers"}},{"kind":"Field","name":{"kind":"Name","value":"forwardNumbers"}},{"kind":"Field","name":{"kind":"Name","value":"fallbackEmails"}},{"kind":"Field","name":{"kind":"Name","value":"destination"}},{"kind":"Field","name":{"kind":"Name","value":"cidOverride"}},{"kind":"Field","name":{"kind":"Name","value":"port"}},{"kind":"Field","name":{"kind":"Name","value":"temporary"}},{"kind":"Field","name":{"kind":"Name","value":"greetingFilePath"}},{"kind":"Field","name":{"kind":"Name","value":"agreementFilePath"}},{"kind":"Field","name":{"kind":"Name","value":"carrier"}},{"kind":"Field","name":{"kind":"Name","value":"e911Carrier"}},{"kind":"Field","name":{"kind":"Name","value":"tdmRoute"}},{"kind":"Field","name":{"kind":"Name","value":"trunk"}},{"kind":"Field","name":{"kind":"Name","value":"previousTrunk"}},{"kind":"Field","name":{"kind":"Name","value":"linkedToExternalAta"}},{"kind":"Field","name":{"kind":"Name","value":"alarmRelayInfo"}},{"kind":"Field","name":{"kind":"Name","value":"inBoundCallBlocked"}},{"kind":"Field","name":{"kind":"Name","value":"inbandRouting"}},{"kind":"Field","name":{"kind":"Name","value":"isNumberLocked"}},{"kind":"Field","name":{"kind":"Name","value":"usbPort"}},{"kind":"Field","name":{"kind":"Name","value":"onlyEfax"}},{"kind":"Field","name":{"kind":"Name","value":"isTollFree"}},{"kind":"Field","name":{"kind":"Name","value":"tollFreeNumbers"}},{"kind":"Field","name":{"kind":"Name","value":"provisionDate"}},{"kind":"Field","name":{"kind":"Name","value":"spoofType"}},{"kind":"Field","name":{"kind":"Name","value":"spoofIncomingCid"}},{"kind":"Field","name":{"kind":"Name","value":"trunkSelection"}},{"kind":"Field","name":{"kind":"Name","value":"numDigits"}}]}},{"kind":"Field","name":{"kind":"Name","value":"deleted"}},{"kind":"Field","name":{"kind":"Name","value":"createdAt"}},{"kind":"Field","name":{"kind":"Name","value":"updatedAt"}}]}},{"kind":"Field","name":{"kind":"Name","value":"assignedNumberDoc"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"number"}},{"kind":"Field","name":{"kind":"Name","value":"linkedBox"}},{"kind":"Field","name":{"kind":"Name","value":"assignedTo"}},{"kind":"Field","name":{"kind":"Name","value":"company"}},{"kind":"Field","name":{"kind":"Name","value":"type"}},{"kind":"Field","name":{"kind":"Name","value":"callerIdName"}},{"kind":"Field","name":{"kind":"Name","value":"isForwarded"}},{"kind":"Field","name":{"kind":"Name","value":"huntPorts"}},{"kind":"Field","name":{"kind":"Name","value":"huntType"}},{"kind":"Field","name":{"kind":"Name","value":"portLabel"}},{"kind":"Field","name":{"kind":"Name","value":"forwardUris"}},{"kind":"Field","name":{"kind":"Name","value":"linkedToObi"}},{"kind":"Field","name":{"kind":"Name","value":"faxDeliveryConfirmation"}},{"kind":"Field","name":{"kind":"Name","value":"savefaxes"}},{"kind":"Field","name":{"kind":"Name","value":"confirmationEmail"}},{"kind":"Field","name":{"kind":"Name","value":"dateCreated"}},{"kind":"Field","name":{"kind":"Name","value":"deleted"}},{"kind":"Field","name":{"kind":"Name","value":"enabled"}},{"kind":"Field","name":{"kind":"Name","value":"cid"}},{"kind":"Field","name":{"kind":"Name","value":"emails"}},{"kind":"Field","name":{"kind":"Name","value":"allowedUsers"}},{"kind":"Field","name":{"kind":"Name","value":"allowedNumbers"}},{"kind":"Field","name":{"kind":"Name","value":"groupMembers"}},{"kind":"Field","name":{"kind":"Name","value":"forwardNumbers"}},{"kind":"Field","name":{"kind":"Name","value":"fallbackEmails"}},{"kind":"Field","name":{"kind":"Name","value":"routeInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"route"}},{"kind":"Field","name":{"kind":"Name","value":"gateway"}}]}},{"kind":"Field","name":{"kind":"Name","value":"previousRouteInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"route"}},{"kind":"Field","name":{"kind":"Name","value":"gateway"}}]}},{"kind":"Field","name":{"kind":"Name","value":"destination"}},{"kind":"Field","name":{"kind":"Name","value":"cidOverride"}},{"kind":"Field","name":{"kind":"Name","value":"port"}},{"kind":"Field","name":{"kind":"Name","value":"temporary"}},{"kind":"Field","name":{"kind":"Name","value":"greetingFilePath"}},{"kind":"Field","name":{"kind":"Name","value":"agreementFilePath"}},{"kind":"Field","name":{"kind":"Name","value":"carrier"}},{"kind":"Field","name":{"kind":"Name","value":"e911Carrier"}},{"kind":"Field","name":{"kind":"Name","value":"tdmRoute"}},{"kind":"Field","name":{"kind":"Name","value":"trunk"}},{"kind":"Field","name":{"kind":"Name","value":"previousTrunk"}},{"kind":"Field","name":{"kind":"Name","value":"linkedToExternalAta"}},{"kind":"Field","name":{"kind":"Name","value":"alarmRelayInfo"}},{"kind":"Field","name":{"kind":"Name","value":"inBoundCallBlocked"}},{"kind":"Field","name":{"kind":"Name","value":"inbandRouting"}},{"kind":"Field","name":{"kind":"Name","value":"isNumberLocked"}},{"kind":"Field","name":{"kind":"Name","value":"usbPort"}},{"kind":"Field","name":{"kind":"Name","value":"onlyEfax"}},{"kind":"Field","name":{"kind":"Name","value":"isTollFree"}},{"kind":"Field","name":{"kind":"Name","value":"tollFreeNumbers"}},{"kind":"Field","name":{"kind":"Name","value":"provisionDate"}},{"kind":"Field","name":{"kind":"Name","value":"spoofType"}},{"kind":"Field","name":{"kind":"Name","value":"spoofIncomingCid"}},{"kind":"Field","name":{"kind":"Name","value":"trunkSelection"}},{"kind":"Field","name":{"kind":"Name","value":"numDigits"}},{"kind":"Field","name":{"kind":"Name","value":"e911Info"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"address1"}},{"kind":"Field","name":{"kind":"Name","value":"address2"}},{"kind":"Field","name":{"kind":"Name","value":"city"}},{"kind":"Field","name":{"kind":"Name","value":"state"}},{"kind":"Field","name":{"kind":"Name","value":"zip"}},{"kind":"Field","name":{"kind":"Name","value":"country"}},{"kind":"Field","name":{"kind":"Name","value":"locationid"}},{"kind":"Field","name":{"kind":"Name","value":"callername"}},{"kind":"Field","name":{"kind":"Name","value":"vendor"}},{"kind":"Field","name":{"kind":"Name","value":"createdAt"}},{"kind":"Field","name":{"kind":"Name","value":"updatedAt"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"appliedConfigTemplate"}},{"kind":"Field","name":{"kind":"Name","value":"appliedConfigTemplateDoc"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"default"}},{"kind":"Field","name":{"kind":"Name","value":"SilenceDetectSensitivity"}},{"kind":"Field","name":{"kind":"Name","value":"OnHookTipRingVoltage"}},{"kind":"Field","name":{"kind":"Name","value":"OffHookCurrentMax"}},{"kind":"Field","name":{"kind":"Name","value":"DTMFDetectMinLength"}},{"kind":"Field","name":{"kind":"Name","value":"DTMFDetectMinGap"}},{"kind":"Field","name":{"kind":"Name","value":"ChannelTxGain"}},{"kind":"Field","name":{"kind":"Name","value":"ChannelRxGain"}},{"kind":"Field","name":{"kind":"Name","value":"DTMFMethod"}},{"kind":"Field","name":{"kind":"Name","value":"DTMFPlaybackLevel"}},{"kind":"Field","name":{"kind":"Name","value":"RingVoltage"}},{"kind":"Field","name":{"kind":"Name","value":"DigitMapShortTimer"}},{"kind":"Field","name":{"kind":"Name","value":"CPCDelayTime"}},{"kind":"Field","name":{"kind":"Name","value":"CPCDuration"}},{"kind":"Field","name":{"kind":"Name","value":"showModemModeToggle"}},{"kind":"Field","name":{"kind":"Name","value":"modemMode"}},{"kind":"Field","name":{"kind":"Name","value":"showT38EnableToggle"}},{"kind":"Field","name":{"kind":"Name","value":"t38Enabled"}},{"kind":"Field","name":{"kind":"Name","value":"showFaxEnabledToggle"}},{"kind":"Field","name":{"kind":"Name","value":"faxEnabled"}},{"kind":"Field","name":{"kind":"Name","value":"showJitterDelay"}},{"kind":"Field","name":{"kind":"Name","value":"JitterMinDelay"}},{"kind":"Field","name":{"kind":"Name","value":"JitterMaxDelay"}},{"kind":"Field","name":{"kind":"Name","value":"inbandRoute"}},{"kind":"Field","name":{"kind":"Name","value":"showInbandRouteToggle"}},{"kind":"Field","name":{"kind":"Name","value":"trunkSelection"}},{"kind":"Field","name":{"kind":"Name","value":"showTrunkSelectionToggle"}},{"kind":"Field","name":{"kind":"Name","value":"vadEnable"}},{"kind":"Field","name":{"kind":"Name","value":"showVadToggle"}},{"kind":"Field","name":{"kind":"Name","value":"showModemOnlyToggle"}},{"kind":"Field","name":{"kind":"Name","value":"modemOnly"}},{"kind":"Field","name":{"kind":"Name","value":"ataType"}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"phoneDocs"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"displayName"}},{"kind":"Field","name":{"kind":"Name","value":"extension"}},{"kind":"Field","name":{"kind":"Name","value":"model"}},{"kind":"Field","name":{"kind":"Name","value":"number"}},{"kind":"Field","name":{"kind":"Name","value":"numberDoc"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"number"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"locationDoc"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"locationName"}},{"kind":"Field","name":{"kind":"Name","value":"_id"}}]}},{"kind":"Field","name":{"kind":"Name","value":"sysInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"boxId"}},{"kind":"Field","name":{"kind":"Name","value":"cdmStatus"}},{"kind":"Field","name":{"kind":"Name","value":"deviceFw"}},{"kind":"Field","name":{"kind":"Name","value":"error"}},{"kind":"Field","name":{"kind":"Name","value":"failoverStatus"}},{"kind":"Field","name":{"kind":"Name","value":"failoverUpdateStatus"}},{"kind":"Field","name":{"kind":"Name","value":"restbinStatus"}},{"kind":"Field","name":{"kind":"Name","value":"diskIo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"Name"}},{"kind":"Field","name":{"kind":"Name","value":"ReadsCompleted"}},{"kind":"Field","name":{"kind":"Name","value":"WritesCompleted"}}]}},{"kind":"Field","name":{"kind":"Name","value":"diskUsage"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"all"}},{"kind":"Field","name":{"kind":"Name","value":"avail"}},{"kind":"Field","name":{"kind":"Name","value":"error"}},{"kind":"Field","name":{"kind":"Name","value":"free"}},{"kind":"Field","name":{"kind":"Name","value":"size"}},{"kind":"Field","name":{"kind":"Name","value":"used"}}]}},{"kind":"Field","name":{"kind":"Name","value":"loadavg"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"Loadavg1"}},{"kind":"Field","name":{"kind":"Name","value":"Loadavg5"}},{"kind":"Field","name":{"kind":"Name","value":"Loadavg15"}},{"kind":"Field","name":{"kind":"Name","value":"error"}}]}},{"kind":"Field","name":{"kind":"Name","value":"restbinVersion"}},{"kind":"Field","name":{"kind":"Name","value":"time"}},{"kind":"Field","name":{"kind":"Name","value":"uptime"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"uptime"}}]}},{"kind":"Field","name":{"kind":"Name","value":"watchGuardStatus"}},{"kind":"Field","name":{"kind":"Name","value":"wifiInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ap"}},{"kind":"Field","name":{"kind":"Name","value":"error"}},{"kind":"Field","name":{"kind":"Name","value":"mode"}},{"kind":"Field","name":{"kind":"Name","value":"ssid"}}]}},{"kind":"Field","name":{"kind":"Name","value":"boxFirmware"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"version"}}]}},{"kind":"Field","name":{"kind":"Name","value":"lteSignalStrength"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"signalStrength"}}]}},{"kind":"Field","name":{"kind":"Name","value":"networkInfoDetail"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"activeInterface"}},{"kind":"Field","name":{"kind":"Name","value":"arp"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"keys"}},{"kind":"Field","name":{"kind":"Name","value":"values"}}]}},{"kind":"Field","name":{"kind":"Name","value":"eth0Config"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"nameserver1"}},{"kind":"Field","name":{"kind":"Name","value":"nameserver2"}},{"kind":"Field","name":{"kind":"Name","value":"nameserver3"}},{"kind":"Field","name":{"kind":"Name","value":"nameserver4"}}]}},{"kind":"Field","name":{"kind":"Name","value":"eth0Stats"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"eth0IP"}},{"kind":"Field","name":{"kind":"Name","value":"eth0MAC"}},{"kind":"Field","name":{"kind":"Name","value":"rxErrors"}},{"kind":"Field","name":{"kind":"Name","value":"rxPackets"}},{"kind":"Field","name":{"kind":"Name","value":"txErrors"}},{"kind":"Field","name":{"kind":"Name","value":"txPackets"}}]}},{"kind":"Field","name":{"kind":"Name","value":"fwVersion"}},{"kind":"Field","name":{"kind":"Name","value":"icmp"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"eth0"}},{"kind":"Field","name":{"kind":"Name","value":"wwan0"}}]}},{"kind":"Field","name":{"kind":"Name","value":"linkStatus"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"epiLink"}},{"kind":"Field","name":{"kind":"Name","value":"eth02"}}]}},{"kind":"Field","name":{"kind":"Name","value":"modemCount"}},{"kind":"Field","name":{"kind":"Name","value":"edgeIP"}},{"kind":"Field","name":{"kind":"Name","value":"ip4G"}},{"kind":"Field","name":{"kind":"Name","value":"tunnelAccess"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"eth0"}},{"kind":"Field","name":{"kind":"Name","value":"wwan0"}}]}},{"kind":"Field","name":{"kind":"Name","value":"registrationStatus"}},{"kind":"Field","name":{"kind":"Name","value":"tunnelIP"}},{"kind":"Field","name":{"kind":"Name","value":"powerSource"}},{"kind":"Field","name":{"kind":"Name","value":"lteSgnalStrength"}},{"kind":"Field","name":{"kind":"Name","value":"priCardStatus"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"priCardStatus"}}]}},{"kind":"Field","name":{"kind":"Name","value":"deviceFirmwareVersion"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"version"}}]}},{"kind":"Field","name":{"kind":"Name","value":"epiInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"fwVersion"}},{"kind":"Field","name":{"kind":"Name","value":"ip"}},{"kind":"Field","name":{"kind":"Name","value":"mac"}},{"kind":"Field","name":{"kind":"Name","value":"provUrl"}},{"kind":"Field","name":{"kind":"Name","value":"uptime"}}]}},{"kind":"Field","name":{"kind":"Name","value":"dcAvgPing"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"chPingAvg"}},{"kind":"Field","name":{"kind":"Name","value":"laPingAvg"}},{"kind":"Field","name":{"kind":"Name","value":"nyPingAvg"}},{"kind":"Field","name":{"kind":"Name","value":"atPingAvg"}},{"kind":"Field","name":{"kind":"Name","value":"dlPingAvg"}},{"kind":"Field","name":{"kind":"Name","value":"bestDC"}},{"kind":"Field","name":{"kind":"Name","value":"bestLatency"}},{"kind":"Field","name":{"kind":"Name","value":"timeUpdated"}},{"kind":"Field","name":{"kind":"Name","value":"error"}}]}},{"kind":"Field","name":{"kind":"Name","value":"cdmBoxStatus"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"Status"}},{"kind":"Field","name":{"kind":"Name","value":"LastCdmCheck"}},{"kind":"Field","name":{"kind":"Name","value":"Duration"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"cpu"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"CPUCount"}},{"kind":"Field","name":{"kind":"Name","value":"Guest"}},{"kind":"Field","name":{"kind":"Name","value":"GuestNice"}},{"kind":"Field","name":{"kind":"Name","value":"Idle"}},{"kind":"Field","name":{"kind":"Name","value":"Iowait"}},{"kind":"Field","name":{"kind":"Name","value":"irq"}},{"kind":"Field","name":{"kind":"Name","value":"Nice"}},{"kind":"Field","name":{"kind":"Name","value":"Softirq"}},{"kind":"Field","name":{"kind":"Name","value":"StatCount"}},{"kind":"Field","name":{"kind":"Name","value":"Steal"}},{"kind":"Field","name":{"kind":"Name","value":"System"}},{"kind":"Field","name":{"kind":"Name","value":"Total"}},{"kind":"Field","name":{"kind":"Name","value":"User"}}]}},{"kind":"Field","name":{"kind":"Name","value":"memory"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"Active"}},{"kind":"Field","name":{"kind":"Name","value":"Available"}},{"kind":"Field","name":{"kind":"Name","value":"Buffers"}},{"kind":"Field","name":{"kind":"Name","value":"Cached"}},{"kind":"Field","name":{"kind":"Name","value":"Free"}},{"kind":"Field","name":{"kind":"Name","value":"Inactive"}},{"kind":"Field","name":{"kind":"Name","value":"MemAvailableEnabled"}},{"kind":"Field","name":{"kind":"Name","value":"SwapCached"}},{"kind":"Field","name":{"kind":"Name","value":"SwapFree"}},{"kind":"Field","name":{"kind":"Name","value":"SwapTotal"}},{"kind":"Field","name":{"kind":"Name","value":"SwapUsed"}},{"kind":"Field","name":{"kind":"Name","value":"Total"}},{"kind":"Field","name":{"kind":"Name","value":"Used"}}]}},{"kind":"Field","name":{"kind":"Name","value":"network"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"Name"}},{"kind":"Field","name":{"kind":"Name","value":"RxBytes"}},{"kind":"Field","name":{"kind":"Name","value":"TxBytes"}}]}},{"kind":"Field","name":{"kind":"Name","value":"portStatuses"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"eth0"}},{"kind":"Field","name":{"kind":"Name","value":"eth1"}},{"kind":"Field","name":{"kind":"Name","value":"eth2"}},{"kind":"Field","name":{"kind":"Name","value":"wg0"}},{"kind":"Field","name":{"kind":"Name","value":"wg7"}},{"kind":"Field","name":{"kind":"Name","value":"wlan0"}},{"kind":"Field","name":{"kind":"Name","value":"wwan0"}}]}},{"kind":"Field","name":{"kind":"Name","value":"lastUpdated"}}]}},{"kind":"Field","name":{"kind":"Name","value":"liveSysInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"boxId"}},{"kind":"Field","name":{"kind":"Name","value":"cdmStatus"}},{"kind":"Field","name":{"kind":"Name","value":"deviceFw"}},{"kind":"Field","name":{"kind":"Name","value":"error"}},{"kind":"Field","name":{"kind":"Name","value":"failoverStatus"}},{"kind":"Field","name":{"kind":"Name","value":"failoverUpdateStatus"}},{"kind":"Field","name":{"kind":"Name","value":"restbinStatus"}},{"kind":"Field","name":{"kind":"Name","value":"diskIo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"Name"}},{"kind":"Field","name":{"kind":"Name","value":"ReadsCompleted"}},{"kind":"Field","name":{"kind":"Name","value":"WritesCompleted"}}]}},{"kind":"Field","name":{"kind":"Name","value":"diskUsage"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"all"}},{"kind":"Field","name":{"kind":"Name","value":"avail"}},{"kind":"Field","name":{"kind":"Name","value":"error"}},{"kind":"Field","name":{"kind":"Name","value":"free"}},{"kind":"Field","name":{"kind":"Name","value":"size"}},{"kind":"Field","name":{"kind":"Name","value":"used"}}]}},{"kind":"Field","name":{"kind":"Name","value":"loadavg"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"Loadavg1"}},{"kind":"Field","name":{"kind":"Name","value":"Loadavg5"}},{"kind":"Field","name":{"kind":"Name","value":"Loadavg15"}},{"kind":"Field","name":{"kind":"Name","value":"error"}}]}},{"kind":"Field","name":{"kind":"Name","value":"restbinVersion"}},{"kind":"Field","name":{"kind":"Name","value":"time"}},{"kind":"Field","name":{"kind":"Name","value":"uptime"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"uptime"}}]}},{"kind":"Field","name":{"kind":"Name","value":"watchGuardStatus"}},{"kind":"Field","name":{"kind":"Name","value":"wifiInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ap"}},{"kind":"Field","name":{"kind":"Name","value":"error"}},{"kind":"Field","name":{"kind":"Name","value":"mode"}},{"kind":"Field","name":{"kind":"Name","value":"ssid"}}]}},{"kind":"Field","name":{"kind":"Name","value":"boxFirmware"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"version"}}]}},{"kind":"Field","name":{"kind":"Name","value":"lteSignalStrength"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"signalStrength"}}]}},{"kind":"Field","name":{"kind":"Name","value":"networkInfoDetail"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"activeInterface"}},{"kind":"Field","name":{"kind":"Name","value":"arp"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"keys"}},{"kind":"Field","name":{"kind":"Name","value":"values"}}]}},{"kind":"Field","name":{"kind":"Name","value":"eth0Config"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"nameserver1"}},{"kind":"Field","name":{"kind":"Name","value":"nameserver2"}},{"kind":"Field","name":{"kind":"Name","value":"nameserver3"}},{"kind":"Field","name":{"kind":"Name","value":"nameserver4"}}]}},{"kind":"Field","name":{"kind":"Name","value":"eth0Stats"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"eth0IP"}},{"kind":"Field","name":{"kind":"Name","value":"eth0MAC"}},{"kind":"Field","name":{"kind":"Name","value":"rxErrors"}},{"kind":"Field","name":{"kind":"Name","value":"rxPackets"}},{"kind":"Field","name":{"kind":"Name","value":"txErrors"}},{"kind":"Field","name":{"kind":"Name","value":"txPackets"}}]}},{"kind":"Field","name":{"kind":"Name","value":"fwVersion"}},{"kind":"Field","name":{"kind":"Name","value":"icmp"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"eth0"}},{"kind":"Field","name":{"kind":"Name","value":"wwan0"}}]}},{"kind":"Field","name":{"kind":"Name","value":"linkStatus"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"epiLink"}},{"kind":"Field","name":{"kind":"Name","value":"eth02"}}]}},{"kind":"Field","name":{"kind":"Name","value":"modemCount"}},{"kind":"Field","name":{"kind":"Name","value":"edgeIP"}},{"kind":"Field","name":{"kind":"Name","value":"ip4G"}},{"kind":"Field","name":{"kind":"Name","value":"tunnelAccess"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"eth0"}},{"kind":"Field","name":{"kind":"Name","value":"wwan0"}}]}},{"kind":"Field","name":{"kind":"Name","value":"registrationStatus"}},{"kind":"Field","name":{"kind":"Name","value":"tunnelIP"}},{"kind":"Field","name":{"kind":"Name","value":"powerSource"}},{"kind":"Field","name":{"kind":"Name","value":"lteSgnalStrength"}},{"kind":"Field","name":{"kind":"Name","value":"priCardStatus"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"priCardStatus"}}]}},{"kind":"Field","name":{"kind":"Name","value":"deviceFirmwareVersion"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"version"}}]}},{"kind":"Field","name":{"kind":"Name","value":"epiInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"fwVersion"}},{"kind":"Field","name":{"kind":"Name","value":"ip"}},{"kind":"Field","name":{"kind":"Name","value":"mac"}},{"kind":"Field","name":{"kind":"Name","value":"provUrl"}},{"kind":"Field","name":{"kind":"Name","value":"uptime"}}]}},{"kind":"Field","name":{"kind":"Name","value":"dcAvgPing"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"chPingAvg"}},{"kind":"Field","name":{"kind":"Name","value":"laPingAvg"}},{"kind":"Field","name":{"kind":"Name","value":"nyPingAvg"}},{"kind":"Field","name":{"kind":"Name","value":"atPingAvg"}},{"kind":"Field","name":{"kind":"Name","value":"dlPingAvg"}},{"kind":"Field","name":{"kind":"Name","value":"bestDC"}},{"kind":"Field","name":{"kind":"Name","value":"bestLatency"}},{"kind":"Field","name":{"kind":"Name","value":"timeUpdated"}},{"kind":"Field","name":{"kind":"Name","value":"error"}}]}},{"kind":"Field","name":{"kind":"Name","value":"cdmBoxStatus"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"Status"}},{"kind":"Field","name":{"kind":"Name","value":"LastCdmCheck"}},{"kind":"Field","name":{"kind":"Name","value":"Duration"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"cpu"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"CPUCount"}},{"kind":"Field","name":{"kind":"Name","value":"Guest"}},{"kind":"Field","name":{"kind":"Name","value":"GuestNice"}},{"kind":"Field","name":{"kind":"Name","value":"Idle"}},{"kind":"Field","name":{"kind":"Name","value":"Iowait"}},{"kind":"Field","name":{"kind":"Name","value":"irq"}},{"kind":"Field","name":{"kind":"Name","value":"Nice"}},{"kind":"Field","name":{"kind":"Name","value":"Softirq"}},{"kind":"Field","name":{"kind":"Name","value":"StatCount"}},{"kind":"Field","name":{"kind":"Name","value":"Steal"}},{"kind":"Field","name":{"kind":"Name","value":"System"}},{"kind":"Field","name":{"kind":"Name","value":"Total"}},{"kind":"Field","name":{"kind":"Name","value":"User"}}]}},{"kind":"Field","name":{"kind":"Name","value":"memory"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"Active"}},{"kind":"Field","name":{"kind":"Name","value":"Available"}},{"kind":"Field","name":{"kind":"Name","value":"Buffers"}},{"kind":"Field","name":{"kind":"Name","value":"Cached"}},{"kind":"Field","name":{"kind":"Name","value":"Free"}},{"kind":"Field","name":{"kind":"Name","value":"Inactive"}},{"kind":"Field","name":{"kind":"Name","value":"MemAvailableEnabled"}},{"kind":"Field","name":{"kind":"Name","value":"SwapCached"}},{"kind":"Field","name":{"kind":"Name","value":"SwapFree"}},{"kind":"Field","name":{"kind":"Name","value":"SwapTotal"}},{"kind":"Field","name":{"kind":"Name","value":"SwapUsed"}},{"kind":"Field","name":{"kind":"Name","value":"Total"}},{"kind":"Field","name":{"kind":"Name","value":"Used"}}]}},{"kind":"Field","name":{"kind":"Name","value":"network"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"Name"}},{"kind":"Field","name":{"kind":"Name","value":"RxBytes"}},{"kind":"Field","name":{"kind":"Name","value":"TxBytes"}}]}},{"kind":"Field","name":{"kind":"Name","value":"portStatuses"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"eth0"}},{"kind":"Field","name":{"kind":"Name","value":"eth1"}},{"kind":"Field","name":{"kind":"Name","value":"eth2"}},{"kind":"Field","name":{"kind":"Name","value":"wg0"}},{"kind":"Field","name":{"kind":"Name","value":"wg7"}},{"kind":"Field","name":{"kind":"Name","value":"wlan0"}},{"kind":"Field","name":{"kind":"Name","value":"wwan0"}}]}},{"kind":"Field","name":{"kind":"Name","value":"lastUpdated"}}]}},{"kind":"Field","name":{"kind":"Name","value":"vSwitchTab"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"registered"}},{"kind":"Field","name":{"kind":"Name","value":"registerationConfigCreated"}},{"kind":"Field","name":{"kind":"Name","value":"portsConfigCreated"}},{"kind":"Field","name":{"kind":"Name","value":"portsInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"port"}},{"kind":"Field","name":{"kind":"Name","value":"calledId"}},{"kind":"Field","name":{"kind":"Name","value":"recording"}},{"kind":"Field","name":{"kind":"Name","value":"trunkType"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"liveVSwitchTab"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"registered"}},{"kind":"Field","name":{"kind":"Name","value":"registerationConfigCreated"}},{"kind":"Field","name":{"kind":"Name","value":"portsConfigCreated"}},{"kind":"Field","name":{"kind":"Name","value":"portsInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"port"}},{"kind":"Field","name":{"kind":"Name","value":"calledId"}},{"kind":"Field","name":{"kind":"Name","value":"recording"}},{"kind":"Field","name":{"kind":"Name","value":"trunkType"}}]}}]}}]}}]}}]} as unknown as DocumentNode<EpikBoxByIdQuery, EpikBoxByIdQueryVariables>;
export const EpikBoxDeviceViewDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"EpikBoxDeviceView"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"EpikBoxById"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"displayName"}},{"kind":"Field","name":{"kind":"Name","value":"serialNumber"}},{"kind":"Field","name":{"kind":"Name","value":"fwVersion"}},{"kind":"Field","name":{"kind":"Name","value":"creationDate"}},{"kind":"Field","name":{"kind":"Name","value":"vpnAddress"}},{"kind":"Field","name":{"kind":"Name","value":"apuType"}},{"kind":"Field","name":{"kind":"Name","value":"model"}},{"kind":"Field","name":{"kind":"Name","value":"lteIp"}},{"kind":"Field","name":{"kind":"Name","value":"lteIp2"}},{"kind":"Field","name":{"kind":"Name","value":"primarySim"}},{"kind":"Field","name":{"kind":"Name","value":"priorityInterface"}},{"kind":"Field","name":{"kind":"Name","value":"simStatus"}},{"kind":"Field","name":{"kind":"Name","value":"liveSimStatus"}},{"kind":"Field","name":{"kind":"Name","value":"advancedRouting"}},{"kind":"Field","name":{"kind":"Name","value":"myEpik"}},{"kind":"Field","name":{"kind":"Name","value":"boxRegistrar"}},{"kind":"Field","name":{"kind":"Name","value":"customerProvidedIp"}},{"kind":"Field","name":{"kind":"Name","value":"activeLTE"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"sim"}},{"kind":"Field","name":{"kind":"Name","value":"imei"}},{"kind":"Field","name":{"kind":"Name","value":"mtn"}},{"kind":"Field","name":{"kind":"Name","value":"ip"}},{"kind":"Field","name":{"kind":"Name","value":"iccid"}},{"kind":"Field","name":{"kind":"Name","value":"timeStamp"}}]}},{"kind":"Field","name":{"kind":"Name","value":"modems"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"imeis"}},{"kind":"Field","name":{"kind":"Name","value":"label"}},{"kind":"Field","name":{"kind":"Name","value":"phones"}},{"kind":"Field","name":{"kind":"Name","value":"sims"}},{"kind":"Field","name":{"kind":"Name","value":"type"}}]}},{"kind":"Field","name":{"kind":"Name","value":"modemInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"model"}},{"kind":"Field","name":{"kind":"Name","value":"imei"}},{"kind":"Field","name":{"kind":"Name","value":"sim"}},{"kind":"Field","name":{"kind":"Name","value":"ipAddress"}}]}}]}}]}}]} as unknown as DocumentNode<EpikBoxDeviceViewQuery, EpikBoxDeviceViewQueryVariables>;
export const ListEpikBoxesDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"ListEpikBoxes"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"pagination"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"PaginationInput"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"filter"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"EpikBoxFilterInput"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ListEpikBoxes"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"pagination"},"value":{"kind":"Variable","name":{"kind":"Name","value":"pagination"}}},{"kind":"Argument","name":{"kind":"Name","value":"filter"},"value":{"kind":"Variable","name":{"kind":"Name","value":"filter"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"docs"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"serialNumber"}},{"kind":"Field","name":{"kind":"Name","value":"vpnAddress"}},{"kind":"Field","name":{"kind":"Name","value":"creationDate"}},{"kind":"Field","name":{"kind":"Name","value":"deleted"}},{"kind":"Field","name":{"kind":"Name","value":"monitor"}},{"kind":"Field","name":{"kind":"Name","value":"displayName"}},{"kind":"Field","name":{"kind":"Name","value":"numPorts"}},{"kind":"Field","name":{"kind":"Name","value":"companyDoc"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}}]}},{"kind":"Field","name":{"kind":"Name","value":"locationDoc"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"locationName"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"pagination"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"currentPage"}},{"kind":"Field","name":{"kind":"Name","value":"totalPages"}},{"kind":"Field","name":{"kind":"Name","value":"count"}}]}}]}}]}}]} as unknown as DocumentNode<ListEpikBoxesQuery, ListEpikBoxesQueryVariables>;
export const ListNumbersDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"ListNumbers"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"filter"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ListNumberInput"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"pagination"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"PaginationInput"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ListNumbers"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"pagination"},"value":{"kind":"Variable","name":{"kind":"Name","value":"pagination"}}},{"kind":"Argument","name":{"kind":"Name","value":"filter"},"value":{"kind":"Variable","name":{"kind":"Name","value":"filter"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"docs"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"number"}},{"kind":"Field","name":{"kind":"Name","value":"linkedBox"}},{"kind":"Field","name":{"kind":"Name","value":"company"}},{"kind":"Field","name":{"kind":"Name","value":"callerIdName"}}]}},{"kind":"Field","name":{"kind":"Name","value":"pagination"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"currentPage"}},{"kind":"Field","name":{"kind":"Name","value":"totalPages"}},{"kind":"Field","name":{"kind":"Name","value":"count"}}]}}]}}]}}]} as unknown as DocumentNode<ListNumbersQuery, ListNumbersQueryVariables>;
export const ListEpiDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"ListEpi"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"input"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"EpiFilterInput"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"pagination"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"PaginationInput"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ListEpi"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"filter"},"value":{"kind":"Variable","name":{"kind":"Name","value":"input"}}},{"kind":"Argument","name":{"kind":"Name","value":"pagination"},"value":{"kind":"Variable","name":{"kind":"Name","value":"pagination"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"pagination"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"totalPages"}},{"kind":"Field","name":{"kind":"Name","value":"count"}}]}},{"kind":"Field","name":{"kind":"Name","value":"docs"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"macAddress"}},{"kind":"Field","name":{"kind":"Name","value":"obiNumber"}}]}}]}}]}}]} as unknown as DocumentNode<ListEpiQuery, ListEpiQueryVariables>;