/* eslint-disable */
import * as types from './graphql';
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';

/**
 * Map of all GraphQL operations in the project.
 *
 * This map has several performance disadvantages:
 * 1. It is not tree-shakeable, so it will include all operations in the project.
 * 2. It is not minifiable, so the string of a GraphQL query will be multiple times inside the bundle.
 * 3. It does not support dead code elimination, so it will add unused operations.
 *
 * Therefore it is highly recommended to use the babel or swc plugin for production.
 * Learn more about it here: https://the-guild.dev/graphql/codegen/plugins/presets/preset-client#reducing-bundle-size
 */
type Documents = {
    "\n  subscription EpiUpdateHook($fields: [String!]!, $id: ID!) {\n    EpiUpdateHook(fields: $fields, id: $id) {\n      fieldName\n      value\n      id\n      error\n    }\n  }\n": typeof types.EpiUpdateHookDocument,
    "\n  subscription EpikBoxUpdateHook($fields: [String!]!, $id: String!) {\n    EpikBoxUpdateHook(fields: $fields, id: $id) {\n      fieldName\n      value\n      id\n      error\n    }\n  }\n": typeof types.EpikBoxUpdateHookDocument,
    "\n query EpiDetailById($id: String!) {\n  EpiDetailById(id: $id) {\n    _id\n    deviceId\n    obiNumber\n    macAddress\n    assignedTo\n    registrationMeta {\n      wanInfo {\n        placeHolder\n        ip\n        subnet\n        gateway\n        dns\n      }\n      sp1ServiceStatus {\n        status\n        callState\n      }\n      sp2ServiceStatus {\n        status\n        callState\n      }\n      obiTalkServiceStatus {\n        placeHolder\n        status\n      }\n    }\n    liveRegistrationMeta {\n      wanInfo {\n        placeHolder\n        ip\n        subnet\n        gateway\n        dns\n      }\n      sp1ServiceStatus {\n        status\n        callState\n      }\n      sp2ServiceStatus {\n        status\n        callState\n      }\n      obiTalkServiceStatus {\n        placeHolder\n        status\n      }\n    }\n    portPhysicalMeta {\n      name\n      state\n      loopCurrent\n      Vbat\n      tipRingVoltage\n      lastCallerInfo\n    }\n    livePortPhysicalMeta {\n      name\n      state\n      loopCurrent\n      Vbat\n      tipRingVoltage\n      lastCallerInfo\n    }\n  }\n}\n": typeof types.EpiDetailByIdDocument,
    "\n  query EpikBoxById($id: String!) {\n    EpikBoxById(id: $id) {\n      _id\n      displayName\n      serialNumber\n      vpnAddress\n      powerState\n      livePowerState\n      activeInterface\n      signalStrength\n      liveSignalStrength\n      liveSignalStrength\n      liveActiveInterface\n      monitor\n      publicIp\n      livePublicIp\n      simStatus\n      liveSimStatus\n      deviceOnline\n      liveDeviceOnline\n      lanIp\n      liveLanIp\n      lteIp\n      lteIp2\n      registered\n      activeCarrier\n      datacenter\n      lteAnalyzer,\n      speedTestData {\n        latency\n        jitter\n        uploadSpeed\n        downloadSpeed\n      }\n      speedTestVoice {\n        latency\n        jitter\n        uploadSpeed\n        downloadSpeed\n      }\n      preferredProviderTest {\n        SimsInfo {\n          Error\n          Jitter\n          PacketLoss\n          PingAvg\n          SIM\n        }\n        TimeStamp\n        initiatedTimeStamp\n      }\n\n      liveSpeedTestData {\n        latency\n        jitter\n        uploadSpeed\n        downloadSpeed\n      }\n      liveSpeedTestVoice {\n        latency\n        jitter\n        uploadSpeed\n        downloadSpeed\n      }\n      livePreferredProviderTest{\n        SimsInfo {\n          Error\n          Jitter\n          PacketLoss\n          PingAvg\n          SIM\n        }\n        TimeStamp\n        initiatedTimeStamp\n      }\n      sensorData {\n        power\n        temp\n      } \n      liveSensorData {\n        power\n        temp\n      } \n      myEpik\n      model\n      apuType\n      powerSaveOption\n      starCodes\n      boxOfflineCallForward\n      recording\n      OBEnable\n      eth3Disable\n      nightlyUpdateTime\n      ext_9override\n      boxRegistrar\n      e911Number\n      primarySim\n      priorityInterface\n      portAlerts\n      currentApn\n      epiTimezone\n      features {\n        showPriTab\n        pri\n        sip\n        twoModems\n        daisyChain\n        dcAutoUpdate\n      }\n      wifiStatus {\n        Error_Msg\n        Gateway\n        IP\n        Mode\n        Password\n        SSID\n        Sec_Mode\n        Status\n        Subnet\n      }\n      liveWifiStatus {\n        Error_Msg\n        Gateway\n        IP\n        Mode\n        Password\n        SSID\n        Sec_Mode\n        Status\n        Subnet\n      }\n      networkInfo {\n        dns\n        interfaces {\n          interface\n          internet\n          icmp\n          wg\n        }\n        timestamp\n        error\n      }\n      liveNetworkInfo {\n        dns\n        interfaces {\n          interface\n          internet\n          icmp\n          wg\n        }\n        timestamp\n        error\n      }\n      \n      priSettings {\n        isSynced\n        pbx\n        priFailovers\n        echoChannels\n        channels\n        framing\n        lineCode\n        timeAndSource\n        dChannel\n        bChannels\n        switchType\n        echoCancellation\n        echoCancellationType\n        numberSettings {\n          dtmfType\n          dnis\n          callerIdName\n          callerIdNumber\n          numbers {\n            _id\n          }\n        }\n        e911Number\n        shippedAddress {\n          attn\n          street1\n          street2\n          city\n          state\n          zip\n        }\n      }\n      sipSettings {\n        casFailovers\n        channels\n        framing\n        lineCode\n        timeAndSource\n        bChannel\n        switchType\n        echoCancellation\n        echoCancellationType\n        channelRange\n        authType\n        ip\n        username\n        password\n        numberSettings {\n          dtmfType\n          dnis\n          callerIdName\n          callerIdNumber\n          numbers {\n            _id\n          }\n        }\n        e911Number\n        shippedAddress {\n          attn\n          street1\n          street2\n          city\n          state\n          zip\n        }\n      }\n      dcAvgPing {\n          chPingAvg\n          laPingAvg\n          nyPingAvg\n          atPingAvg\n          dlPingAvg\n          bestDC\n          bestLatency\n          timeUpdated\n          error\n       }\n      liveDcAvgPing {\n        chPingAvg\n          laPingAvg\n          nyPingAvg\n          atPingAvg\n          dlPingAvg\n          bestDC\n          bestLatency\n          timeUpdated\n          error\n      }\n      modemInfo {\n        carrier\n        carrier_placeholder\n        imei\n        imei_placeholder\n        ipAddress\n        ipAddress_placeholder\n        manufacturer\n        manufacturer_placeholder\n        model\n        model_placeholder\n        sim\n        sim_placeholder\n      }\n\n      liveModemInfo {\n        carrier\n        carrier_placeholder\n        imei\n        imei_placeholder\n        ipAddress\n        ipAddress_placeholder\n        manufacturer\n        manufacturer_placeholder\n        model\n        model_placeholder\n        sim\n        sim_placeholder\n      }\n      companyDoc {\n        _id\n        name\n      }\n      obiDocs {\n        _id\n        macAddress\n        obiNumber\n        deviceId        \n        port1 {\n          boxPortNumber\n          registered\n          disabled\n          provisionDate\n          enableDisableLastUpdatedTime\n          updatedOn\n          serviceName\n          monitored\n          callerIdMasking\n          callerIdMaskingNumber\n          SilenceDetectSensitivity\n          OnHookTipRingVoltage\n          OffHookCurrentMax\n          DTMFDetectMinLength\n          DTMFDetectMinGap\n          ChannelTxGain\n          ChannelRxGain\n          DTMFMethod\n          DTMFPlaybackLevel\n          RingVoltage\n          DigitMapShortTimer\n          CPCDelayTime\n          CPCDuration\n          showVadToggle\n          showModemModeToggle\n          modemMode\n          showT38EnableToggle\n          t38Enabled\n          showFaxEnabledToggle\n          faxEnabled\n          showJitterDelay\n          JitterMinDelay\n          JitterMaxDelay\n          showModemOnlyToggle\n          modemOnly\n          forceForward\n          forceForwardNumber\n          configOverride\n          e911Number\n          e911Enabled\n          voip\n          status\n          connected\n          remoteHost\n          ipAddress\n          remotePort\n          authName\n          assignedNumber\n          vmNumber\n          advancedModemConfiguration\n          portActivated\n          portActivatedDate\n          startedBilling\n          startedBillingDate\n          callerIdOverride\n          provisionUrl\n          blockCallerId\n          callWaiting\n          configPassword\n          assignedNumberRef\n          appliedConfigTemplate\n          vmBox\n          vmailBoxDoc {\n            _id\n            pin\n            totalMessages\n            limit\n            greeting\n            notificationEmails\n            notificationNumbers\n            number\n            numberDoc {\n              _id\n              number\n              linkedBox\n              assignedTo\n              company\n              type\n              callerIdName\n              isForwarded\n              huntPorts\n              huntType\n              portLabel\n              forwardUris\n              linkedToObi\n              faxDeliveryConfirmation\n              savefaxes\n              confirmationEmail\n              dateCreated\n              deleted\n              enabled\n              cid\n              emails\n              allowedUsers\n              allowedNumbers\n              groupMembers\n              forwardNumbers\n              fallbackEmails\n              destination\n              cidOverride\n              port\n              temporary\n              greetingFilePath\n              agreementFilePath\n              carrier\n              e911Carrier\n              tdmRoute\n              trunk\n              previousTrunk\n              linkedToExternalAta\n              alarmRelayInfo\n              inBoundCallBlocked\n              inbandRouting\n              isNumberLocked\n              usbPort\n              onlyEfax\n              isTollFree\n              tollFreeNumbers\n              provisionDate\n              spoofType\n              spoofIncomingCid\n              trunkSelection\n              numDigits\n            }\n            deleted\n            createdAt\n            updatedAt\n          }\n          assignedNumberDoc {\n            _id\n            number\n            linkedBox\n            assignedTo\n            company\n            type\n            callerIdName\n            isForwarded\n            huntPorts\n            huntType\n            portLabel\n            forwardUris\n            linkedToObi\n            faxDeliveryConfirmation\n            savefaxes\n            confirmationEmail\n            dateCreated\n            deleted\n            enabled\n            cid\n            emails\n            allowedUsers\n            allowedNumbers\n            groupMembers\n            forwardNumbers\n            fallbackEmails\n            routeInfo {\n              route\n              gateway\n            }\n            previousRouteInfo {\n              route\n              gateway\n            }\n            destination\n            cidOverride\n            port\n            temporary\n            greetingFilePath\n            agreementFilePath\n            carrier\n            e911Carrier\n            tdmRoute\n            trunk\n            previousTrunk\n            linkedToExternalAta\n            alarmRelayInfo\n            inBoundCallBlocked\n            inbandRouting\n            isNumberLocked\n            usbPort\n            onlyEfax\n            isTollFree\n            tollFreeNumbers\n            provisionDate\n            spoofType\n            spoofIncomingCid\n            trunkSelection\n            numDigits\n            e911Info {\n              address1\n              address2\n              city\n              state\n              zip\n              country\n              locationid\n              callername\n              vendor\n              createdAt\n              updatedAt\n            }\n          }\n          appliedConfigTemplateDoc {\n            _id\n            title\n            name\n            default\n            SilenceDetectSensitivity\n            OnHookTipRingVoltage\n            OffHookCurrentMax\n            DTMFDetectMinLength\n            DTMFDetectMinGap\n            ChannelTxGain\n            ChannelRxGain\n            DTMFMethod\n            DTMFPlaybackLevel\n            RingVoltage\n            DigitMapShortTimer\n            CPCDelayTime\n            CPCDuration\n            showModemModeToggle\n            modemMode\n            showT38EnableToggle\n            t38Enabled\n            showFaxEnabledToggle\n            faxEnabled\n            showJitterDelay\n            JitterMinDelay\n            JitterMaxDelay\n            inbandRoute\n            showInbandRouteToggle\n            trunkSelection\n            showTrunkSelectionToggle\n            vadEnable\n            showVadToggle\n            showModemOnlyToggle\n            modemOnly\n            ataType\n          }\n        }\n        port2 {\n          boxPortNumber\n          registered\n          disabled\n          provisionDate\n          enableDisableLastUpdatedTime\n          updatedOn\n          serviceName\n          monitored\n          callerIdMasking\n          callerIdMaskingNumber\n          SilenceDetectSensitivity\n          OnHookTipRingVoltage\n          OffHookCurrentMax\n          DTMFDetectMinLength\n          DTMFDetectMinGap\n          ChannelTxGain\n          ChannelRxGain\n          DTMFMethod\n          DTMFPlaybackLevel\n          RingVoltage\n          DigitMapShortTimer\n          CPCDelayTime\n          CPCDuration\n          showVadToggle\n          showModemModeToggle\n          modemMode\n          showT38EnableToggle\n          t38Enabled\n          showFaxEnabledToggle\n          faxEnabled\n          showJitterDelay\n          JitterMinDelay\n          JitterMaxDelay\n          showModemOnlyToggle\n          modemOnly\n          forceForward\n          forceForwardNumber\n          configOverride\n          e911Number\n          e911Enabled\n          voip\n          status\n          connected\n          remoteHost\n          ipAddress\n          remotePort\n          authName\n          assignedNumber\n          vmNumber\n          advancedModemConfiguration\n          portActivated\n          portActivatedDate\n          startedBilling\n          startedBillingDate\n          callerIdOverride\n          provisionUrl\n          blockCallerId\n          callWaiting\n          configPassword\n          assignedNumberRef\n           vmBox\n          vmailBoxDoc {\n            _id\n            pin\n            totalMessages\n            limit\n            greeting\n            notificationEmails\n            notificationNumbers\n            number\n            numberDoc {\n              _id\n              number\n              linkedBox\n              assignedTo\n              company\n              type\n              callerIdName\n              isForwarded\n              huntPorts\n              huntType\n              portLabel\n              forwardUris\n              linkedToObi\n              faxDeliveryConfirmation\n              savefaxes\n              confirmationEmail\n              dateCreated\n              deleted\n              enabled\n              cid\n              emails\n              allowedUsers\n              allowedNumbers\n              groupMembers\n              forwardNumbers\n              fallbackEmails\n              destination\n              cidOverride\n              port\n              temporary\n              greetingFilePath\n              agreementFilePath\n              carrier\n              e911Carrier\n              tdmRoute\n              trunk\n              previousTrunk\n              linkedToExternalAta\n              alarmRelayInfo\n              inBoundCallBlocked\n              inbandRouting\n              isNumberLocked\n              usbPort\n              onlyEfax\n              isTollFree\n              tollFreeNumbers\n              provisionDate\n              spoofType\n              spoofIncomingCid\n              trunkSelection\n              numDigits\n            }\n            deleted\n            createdAt\n            updatedAt\n          }\n          assignedNumberDoc {\n            _id\n            number\n            linkedBox\n            assignedTo\n            company\n            type\n            callerIdName\n            isForwarded\n            huntPorts\n            huntType\n            portLabel\n            forwardUris\n            linkedToObi\n            faxDeliveryConfirmation\n            savefaxes\n            confirmationEmail\n            dateCreated\n            deleted\n            enabled\n            cid\n            emails\n            allowedUsers\n            allowedNumbers\n            groupMembers\n            forwardNumbers\n            fallbackEmails\n            routeInfo {\n              route\n              gateway\n            }\n            previousRouteInfo {\n              route\n              gateway\n            }\n            destination\n            cidOverride\n            port\n            temporary\n            greetingFilePath\n            agreementFilePath\n            carrier\n            e911Carrier\n            tdmRoute\n            trunk\n            previousTrunk\n            linkedToExternalAta\n            alarmRelayInfo\n            inBoundCallBlocked\n            inbandRouting\n            isNumberLocked\n            usbPort\n            onlyEfax\n            isTollFree\n            tollFreeNumbers\n            provisionDate\n            spoofType\n            spoofIncomingCid\n            trunkSelection\n            numDigits\n            e911Info {\n                address1\n                address2\n                city\n                state\n                zip\n                country\n                locationid\n                callername\n                vendor\n                createdAt\n                updatedAt\n            }\n          }\n          appliedConfigTemplate\n          appliedConfigTemplateDoc {\n            _id\n            title\n            name\n            default\n            SilenceDetectSensitivity\n            OnHookTipRingVoltage\n            OffHookCurrentMax\n            DTMFDetectMinLength\n            DTMFDetectMinGap\n            ChannelTxGain\n            ChannelRxGain\n            DTMFMethod\n            DTMFPlaybackLevel\n            RingVoltage\n            DigitMapShortTimer\n            CPCDelayTime\n            CPCDuration\n            showModemModeToggle\n            modemMode\n            showT38EnableToggle\n            t38Enabled\n            showFaxEnabledToggle\n            faxEnabled\n            showJitterDelay\n            JitterMinDelay\n            JitterMaxDelay\n            inbandRoute\n            showInbandRouteToggle\n            trunkSelection\n            showTrunkSelectionToggle\n            vadEnable\n            showVadToggle\n            showModemOnlyToggle\n            modemOnly\n            ataType\n          }\n        }\n      }\n      phoneDocs {\n        _id\n        displayName\n        extension\n        model\n        number\n        numberDoc {\n          _id\n          number\n        }\n      }\n      locationDoc {\n        locationName\n        _id\n      }\n      \n      sysInfo {\n    _id\n    boxId\n    cdmStatus\n    deviceFw\n    error\n    failoverStatus\n    failoverUpdateStatus\n    restbinStatus\n    diskIo {\n      Name\n      ReadsCompleted\n      WritesCompleted\n    }\n    diskUsage {\n      all\n      avail\n      error\n      free\n      size\n      used\n    }\n    loadavg {\n      Loadavg1\n      Loadavg5\n      Loadavg15\n      error\n    }\n    restbinVersion\n    time\n    uptime {\n      uptime\n    }\n    watchGuardStatus\n    wifiInfo {\n      ap\n      error\n      mode\n      ssid\n    }\n    boxFirmware {\n      version\n    }\n    lteSignalStrength {\n      signalStrength\n    }\n    networkInfoDetail {\n    activeInterface\n    arp {\n      keys\n      values\n    }\n    eth0Config {\n      nameserver1\n      nameserver2\n      nameserver3\n      nameserver4\n    }\n    eth0Stats {\n      eth0IP\n      eth0MAC\n      rxErrors\n      rxPackets\n      txErrors\n      txPackets\n    }\n    fwVersion\n    icmp {\n      eth0\n      wwan0\n    }\n    linkStatus {\n      epiLink\n      eth02\n    }\n    modemCount\n    edgeIP\n    ip4G\n    tunnelAccess {\n      eth0\n      wwan0\n    }\n    registrationStatus\n    tunnelIP\n    powerSource\n    lteSgnalStrength\n    priCardStatus {\n      priCardStatus\n    }\n    deviceFirmwareVersion {\n      version\n    }\n    epiInfo {\n      fwVersion\n      ip\n      mac\n      provUrl\n      uptime\n    }\n    dcAvgPing {\n      chPingAvg\n      laPingAvg\n      nyPingAvg\n      atPingAvg\n      dlPingAvg\n      bestDC\n      bestLatency\n      timeUpdated\n      error\n    }\n    cdmBoxStatus {\n      Status\n      LastCdmCheck\n      Duration\n    }\n  }\n    cpu {\n      CPUCount\n      Guest\n      GuestNice\n      Idle\n      Iowait\n      irq\n      Nice\n      Softirq\n      StatCount\n      Steal\n      System\n      Total\n      User\n    }\n    memory {\n      Active\n      Available\n      Buffers\n      Cached\n      Free\n      Inactive\n      MemAvailableEnabled\n      SwapCached\n      SwapFree\n      SwapTotal\n      SwapUsed\n      Total\n      Used\n    }\n    network {\n      Name\n      RxBytes\n      TxBytes\n    }\n    portStatuses {\n      eth0\n      eth1\n      eth2\n      wg0\n      wg7\n      wlan0\n      wwan0\n    }\n    lastUpdated\n  }\n\n  liveSysInfo {\n    _id\n    boxId\n    cdmStatus\n    deviceFw\n    error\n    failoverStatus\n    failoverUpdateStatus\n    restbinStatus\n    diskIo {\n      Name\n      ReadsCompleted\n      WritesCompleted\n    }\n    diskUsage {\n      all\n      avail\n      error\n      free\n      size\n      used\n    }\n    loadavg {\n      Loadavg1\n      Loadavg5\n      Loadavg15\n      error\n    }\n    restbinVersion\n    time\n    uptime {\n      uptime\n    }\n    watchGuardStatus\n    wifiInfo {\n      ap\n      error\n      mode\n      ssid\n    }\n    boxFirmware {\n      version\n    }\n    lteSignalStrength {\n      signalStrength\n    }\n    networkInfoDetail {\n    activeInterface\n    arp {\n      keys\n      values\n    }\n    eth0Config {\n      nameserver1\n      nameserver2\n      nameserver3\n      nameserver4\n    }\n    eth0Stats {\n      eth0IP\n      eth0MAC\n      rxErrors\n      rxPackets\n      txErrors\n      txPackets\n    }\n    fwVersion\n    icmp {\n      eth0\n      wwan0\n    }\n    linkStatus {\n      epiLink\n      eth02\n    }\n    modemCount\n    edgeIP\n    ip4G\n    tunnelAccess {\n      eth0\n      wwan0\n    }\n    registrationStatus\n    tunnelIP\n    powerSource\n    lteSgnalStrength\n    priCardStatus {\n      priCardStatus\n    }\n    deviceFirmwareVersion {\n      version\n    }\n    epiInfo {\n      fwVersion\n      ip\n      mac\n      provUrl\n      uptime\n    }\n    dcAvgPing {\n      chPingAvg\n      laPingAvg\n      nyPingAvg\n      atPingAvg\n      dlPingAvg\n      bestDC\n      bestLatency\n      timeUpdated\n      error\n    }\n    cdmBoxStatus {\n      Status\n      LastCdmCheck\n      Duration\n    }\n  }\n    cpu {\n      CPUCount\n      Guest\n      GuestNice\n      Idle\n      Iowait\n      irq\n      Nice\n      Softirq\n      StatCount\n      Steal\n      System\n      Total\n      User\n    }\n    memory {\n      Active\n      Available\n      Buffers\n      Cached\n      Free\n      Inactive\n      MemAvailableEnabled\n      SwapCached\n      SwapFree\n      SwapTotal\n      SwapUsed\n      Total\n      Used\n    }\n    network {\n      Name\n      RxBytes\n      TxBytes\n    }\n    portStatuses {\n      eth0\n      eth1\n      eth2\n      wg0\n      wg7\n      wlan0\n      wwan0\n    }\n    lastUpdated\n  }\n\n   vSwitchTab {\n      registered\n      registerationConfigCreated\n      portsConfigCreated\n      portsInfo {\n        port\n        calledId\n        recording\n        trunkType\n      }\n    }\n    liveVSwitchTab {\n    registered\n    registerationConfigCreated\n    portsConfigCreated\n    portsInfo {\n      port\n      calledId\n      recording\n      trunkType\n    }\n  }\n\n    }\n  }\n": typeof types.EpikBoxByIdDocument,
    "\n  query EpikBoxDeviceView($id: String!) {\n    EpikBoxById(id: $id) {\n      _id\n      displayName\n      serialNumber\n      fwVersion\n      creationDate\n      vpnAddress\n      apuType\n      model\n      lteIp,\n      lteIp2,\n      primarySim,\n      priorityInterface,\n      simStatus,\n      liveSimStatus\n      advancedRouting\n      myEpik\n      boxRegistrar\n      customerProvidedIp\n      activeLTE {\n        sim\n        imei\n        mtn\n        ip\n        iccid\n        timeStamp\n      }\n      modems {\n        imeis\n        label\n        phones\n        sims\n        type\n      }\n      modemInfo {\n        model\n        imei\n        sim\n        ipAddress\n      }\n    }\n  }\n": typeof types.EpikBoxDeviceViewDocument,
    "\n  query ListEpikBoxes(\n    $pagination: PaginationInput\n    $filter: EpikBoxFilterInput!\n  ) {\n    ListEpikBoxes(pagination: $pagination, filter: $filter) {\n      docs {\n        _id\n        serialNumber\n        vpnAddress\n        creationDate\n        deleted\n        monitor\n        displayName\n        numPorts\n        companyDoc {\n          _id\n          name\n        }\n        locationDoc {\n          _id\n          locationName\n        }\n      }\n      pagination {\n        currentPage\n        totalPages\n        count\n      }\n    }\n  }\n": typeof types.ListEpikBoxesDocument,
    "\n  query ListNumbers($filter: ListNumberInput!, $pagination: PaginationInput!) {\n    ListNumbers(pagination: $pagination, filter: $filter) {\n      docs {\n        _id\n        number\n        linkedBox\n        company\n        callerIdName\n      }\n      pagination {\n        currentPage\n        totalPages\n        count\n      }\n    }\n  }\n": typeof types.ListNumbersDocument,
    "\n  query ListEpi($input: EpiFilterInput!, $pagination: PaginationInput!) {\n    ListEpi(filter: $input, pagination: $pagination) {\n      pagination {\n        totalPages\n        count\n      }\n      docs {\n        _id\n        macAddress\n        obiNumber\n      }\n    }\n  }\n": typeof types.ListEpiDocument,
};
const documents: Documents = {
    "\n  subscription EpiUpdateHook($fields: [String!]!, $id: ID!) {\n    EpiUpdateHook(fields: $fields, id: $id) {\n      fieldName\n      value\n      id\n      error\n    }\n  }\n": types.EpiUpdateHookDocument,
    "\n  subscription EpikBoxUpdateHook($fields: [String!]!, $id: String!) {\n    EpikBoxUpdateHook(fields: $fields, id: $id) {\n      fieldName\n      value\n      id\n      error\n    }\n  }\n": types.EpikBoxUpdateHookDocument,
    "\n query EpiDetailById($id: String!) {\n  EpiDetailById(id: $id) {\n    _id\n    deviceId\n    obiNumber\n    macAddress\n    assignedTo\n    registrationMeta {\n      wanInfo {\n        placeHolder\n        ip\n        subnet\n        gateway\n        dns\n      }\n      sp1ServiceStatus {\n        status\n        callState\n      }\n      sp2ServiceStatus {\n        status\n        callState\n      }\n      obiTalkServiceStatus {\n        placeHolder\n        status\n      }\n    }\n    liveRegistrationMeta {\n      wanInfo {\n        placeHolder\n        ip\n        subnet\n        gateway\n        dns\n      }\n      sp1ServiceStatus {\n        status\n        callState\n      }\n      sp2ServiceStatus {\n        status\n        callState\n      }\n      obiTalkServiceStatus {\n        placeHolder\n        status\n      }\n    }\n    portPhysicalMeta {\n      name\n      state\n      loopCurrent\n      Vbat\n      tipRingVoltage\n      lastCallerInfo\n    }\n    livePortPhysicalMeta {\n      name\n      state\n      loopCurrent\n      Vbat\n      tipRingVoltage\n      lastCallerInfo\n    }\n  }\n}\n": types.EpiDetailByIdDocument,
    "\n  query EpikBoxById($id: String!) {\n    EpikBoxById(id: $id) {\n      _id\n      displayName\n      serialNumber\n      vpnAddress\n      powerState\n      livePowerState\n      activeInterface\n      signalStrength\n      liveSignalStrength\n      liveSignalStrength\n      liveActiveInterface\n      monitor\n      publicIp\n      livePublicIp\n      simStatus\n      liveSimStatus\n      deviceOnline\n      liveDeviceOnline\n      lanIp\n      liveLanIp\n      lteIp\n      lteIp2\n      registered\n      activeCarrier\n      datacenter\n      lteAnalyzer,\n      speedTestData {\n        latency\n        jitter\n        uploadSpeed\n        downloadSpeed\n      }\n      speedTestVoice {\n        latency\n        jitter\n        uploadSpeed\n        downloadSpeed\n      }\n      preferredProviderTest {\n        SimsInfo {\n          Error\n          Jitter\n          PacketLoss\n          PingAvg\n          SIM\n        }\n        TimeStamp\n        initiatedTimeStamp\n      }\n\n      liveSpeedTestData {\n        latency\n        jitter\n        uploadSpeed\n        downloadSpeed\n      }\n      liveSpeedTestVoice {\n        latency\n        jitter\n        uploadSpeed\n        downloadSpeed\n      }\n      livePreferredProviderTest{\n        SimsInfo {\n          Error\n          Jitter\n          PacketLoss\n          PingAvg\n          SIM\n        }\n        TimeStamp\n        initiatedTimeStamp\n      }\n      sensorData {\n        power\n        temp\n      } \n      liveSensorData {\n        power\n        temp\n      } \n      myEpik\n      model\n      apuType\n      powerSaveOption\n      starCodes\n      boxOfflineCallForward\n      recording\n      OBEnable\n      eth3Disable\n      nightlyUpdateTime\n      ext_9override\n      boxRegistrar\n      e911Number\n      primarySim\n      priorityInterface\n      portAlerts\n      currentApn\n      epiTimezone\n      features {\n        showPriTab\n        pri\n        sip\n        twoModems\n        daisyChain\n        dcAutoUpdate\n      }\n      wifiStatus {\n        Error_Msg\n        Gateway\n        IP\n        Mode\n        Password\n        SSID\n        Sec_Mode\n        Status\n        Subnet\n      }\n      liveWifiStatus {\n        Error_Msg\n        Gateway\n        IP\n        Mode\n        Password\n        SSID\n        Sec_Mode\n        Status\n        Subnet\n      }\n      networkInfo {\n        dns\n        interfaces {\n          interface\n          internet\n          icmp\n          wg\n        }\n        timestamp\n        error\n      }\n      liveNetworkInfo {\n        dns\n        interfaces {\n          interface\n          internet\n          icmp\n          wg\n        }\n        timestamp\n        error\n      }\n      \n      priSettings {\n        isSynced\n        pbx\n        priFailovers\n        echoChannels\n        channels\n        framing\n        lineCode\n        timeAndSource\n        dChannel\n        bChannels\n        switchType\n        echoCancellation\n        echoCancellationType\n        numberSettings {\n          dtmfType\n          dnis\n          callerIdName\n          callerIdNumber\n          numbers {\n            _id\n          }\n        }\n        e911Number\n        shippedAddress {\n          attn\n          street1\n          street2\n          city\n          state\n          zip\n        }\n      }\n      sipSettings {\n        casFailovers\n        channels\n        framing\n        lineCode\n        timeAndSource\n        bChannel\n        switchType\n        echoCancellation\n        echoCancellationType\n        channelRange\n        authType\n        ip\n        username\n        password\n        numberSettings {\n          dtmfType\n          dnis\n          callerIdName\n          callerIdNumber\n          numbers {\n            _id\n          }\n        }\n        e911Number\n        shippedAddress {\n          attn\n          street1\n          street2\n          city\n          state\n          zip\n        }\n      }\n      dcAvgPing {\n          chPingAvg\n          laPingAvg\n          nyPingAvg\n          atPingAvg\n          dlPingAvg\n          bestDC\n          bestLatency\n          timeUpdated\n          error\n       }\n      liveDcAvgPing {\n        chPingAvg\n          laPingAvg\n          nyPingAvg\n          atPingAvg\n          dlPingAvg\n          bestDC\n          bestLatency\n          timeUpdated\n          error\n      }\n      modemInfo {\n        carrier\n        carrier_placeholder\n        imei\n        imei_placeholder\n        ipAddress\n        ipAddress_placeholder\n        manufacturer\n        manufacturer_placeholder\n        model\n        model_placeholder\n        sim\n        sim_placeholder\n      }\n\n      liveModemInfo {\n        carrier\n        carrier_placeholder\n        imei\n        imei_placeholder\n        ipAddress\n        ipAddress_placeholder\n        manufacturer\n        manufacturer_placeholder\n        model\n        model_placeholder\n        sim\n        sim_placeholder\n      }\n      companyDoc {\n        _id\n        name\n      }\n      obiDocs {\n        _id\n        macAddress\n        obiNumber\n        deviceId        \n        port1 {\n          boxPortNumber\n          registered\n          disabled\n          provisionDate\n          enableDisableLastUpdatedTime\n          updatedOn\n          serviceName\n          monitored\n          callerIdMasking\n          callerIdMaskingNumber\n          SilenceDetectSensitivity\n          OnHookTipRingVoltage\n          OffHookCurrentMax\n          DTMFDetectMinLength\n          DTMFDetectMinGap\n          ChannelTxGain\n          ChannelRxGain\n          DTMFMethod\n          DTMFPlaybackLevel\n          RingVoltage\n          DigitMapShortTimer\n          CPCDelayTime\n          CPCDuration\n          showVadToggle\n          showModemModeToggle\n          modemMode\n          showT38EnableToggle\n          t38Enabled\n          showFaxEnabledToggle\n          faxEnabled\n          showJitterDelay\n          JitterMinDelay\n          JitterMaxDelay\n          showModemOnlyToggle\n          modemOnly\n          forceForward\n          forceForwardNumber\n          configOverride\n          e911Number\n          e911Enabled\n          voip\n          status\n          connected\n          remoteHost\n          ipAddress\n          remotePort\n          authName\n          assignedNumber\n          vmNumber\n          advancedModemConfiguration\n          portActivated\n          portActivatedDate\n          startedBilling\n          startedBillingDate\n          callerIdOverride\n          provisionUrl\n          blockCallerId\n          callWaiting\n          configPassword\n          assignedNumberRef\n          appliedConfigTemplate\n          vmBox\n          vmailBoxDoc {\n            _id\n            pin\n            totalMessages\n            limit\n            greeting\n            notificationEmails\n            notificationNumbers\n            number\n            numberDoc {\n              _id\n              number\n              linkedBox\n              assignedTo\n              company\n              type\n              callerIdName\n              isForwarded\n              huntPorts\n              huntType\n              portLabel\n              forwardUris\n              linkedToObi\n              faxDeliveryConfirmation\n              savefaxes\n              confirmationEmail\n              dateCreated\n              deleted\n              enabled\n              cid\n              emails\n              allowedUsers\n              allowedNumbers\n              groupMembers\n              forwardNumbers\n              fallbackEmails\n              destination\n              cidOverride\n              port\n              temporary\n              greetingFilePath\n              agreementFilePath\n              carrier\n              e911Carrier\n              tdmRoute\n              trunk\n              previousTrunk\n              linkedToExternalAta\n              alarmRelayInfo\n              inBoundCallBlocked\n              inbandRouting\n              isNumberLocked\n              usbPort\n              onlyEfax\n              isTollFree\n              tollFreeNumbers\n              provisionDate\n              spoofType\n              spoofIncomingCid\n              trunkSelection\n              numDigits\n            }\n            deleted\n            createdAt\n            updatedAt\n          }\n          assignedNumberDoc {\n            _id\n            number\n            linkedBox\n            assignedTo\n            company\n            type\n            callerIdName\n            isForwarded\n            huntPorts\n            huntType\n            portLabel\n            forwardUris\n            linkedToObi\n            faxDeliveryConfirmation\n            savefaxes\n            confirmationEmail\n            dateCreated\n            deleted\n            enabled\n            cid\n            emails\n            allowedUsers\n            allowedNumbers\n            groupMembers\n            forwardNumbers\n            fallbackEmails\n            routeInfo {\n              route\n              gateway\n            }\n            previousRouteInfo {\n              route\n              gateway\n            }\n            destination\n            cidOverride\n            port\n            temporary\n            greetingFilePath\n            agreementFilePath\n            carrier\n            e911Carrier\n            tdmRoute\n            trunk\n            previousTrunk\n            linkedToExternalAta\n            alarmRelayInfo\n            inBoundCallBlocked\n            inbandRouting\n            isNumberLocked\n            usbPort\n            onlyEfax\n            isTollFree\n            tollFreeNumbers\n            provisionDate\n            spoofType\n            spoofIncomingCid\n            trunkSelection\n            numDigits\n            e911Info {\n              address1\n              address2\n              city\n              state\n              zip\n              country\n              locationid\n              callername\n              vendor\n              createdAt\n              updatedAt\n            }\n          }\n          appliedConfigTemplateDoc {\n            _id\n            title\n            name\n            default\n            SilenceDetectSensitivity\n            OnHookTipRingVoltage\n            OffHookCurrentMax\n            DTMFDetectMinLength\n            DTMFDetectMinGap\n            ChannelTxGain\n            ChannelRxGain\n            DTMFMethod\n            DTMFPlaybackLevel\n            RingVoltage\n            DigitMapShortTimer\n            CPCDelayTime\n            CPCDuration\n            showModemModeToggle\n            modemMode\n            showT38EnableToggle\n            t38Enabled\n            showFaxEnabledToggle\n            faxEnabled\n            showJitterDelay\n            JitterMinDelay\n            JitterMaxDelay\n            inbandRoute\n            showInbandRouteToggle\n            trunkSelection\n            showTrunkSelectionToggle\n            vadEnable\n            showVadToggle\n            showModemOnlyToggle\n            modemOnly\n            ataType\n          }\n        }\n        port2 {\n          boxPortNumber\n          registered\n          disabled\n          provisionDate\n          enableDisableLastUpdatedTime\n          updatedOn\n          serviceName\n          monitored\n          callerIdMasking\n          callerIdMaskingNumber\n          SilenceDetectSensitivity\n          OnHookTipRingVoltage\n          OffHookCurrentMax\n          DTMFDetectMinLength\n          DTMFDetectMinGap\n          ChannelTxGain\n          ChannelRxGain\n          DTMFMethod\n          DTMFPlaybackLevel\n          RingVoltage\n          DigitMapShortTimer\n          CPCDelayTime\n          CPCDuration\n          showVadToggle\n          showModemModeToggle\n          modemMode\n          showT38EnableToggle\n          t38Enabled\n          showFaxEnabledToggle\n          faxEnabled\n          showJitterDelay\n          JitterMinDelay\n          JitterMaxDelay\n          showModemOnlyToggle\n          modemOnly\n          forceForward\n          forceForwardNumber\n          configOverride\n          e911Number\n          e911Enabled\n          voip\n          status\n          connected\n          remoteHost\n          ipAddress\n          remotePort\n          authName\n          assignedNumber\n          vmNumber\n          advancedModemConfiguration\n          portActivated\n          portActivatedDate\n          startedBilling\n          startedBillingDate\n          callerIdOverride\n          provisionUrl\n          blockCallerId\n          callWaiting\n          configPassword\n          assignedNumberRef\n           vmBox\n          vmailBoxDoc {\n            _id\n            pin\n            totalMessages\n            limit\n            greeting\n            notificationEmails\n            notificationNumbers\n            number\n            numberDoc {\n              _id\n              number\n              linkedBox\n              assignedTo\n              company\n              type\n              callerIdName\n              isForwarded\n              huntPorts\n              huntType\n              portLabel\n              forwardUris\n              linkedToObi\n              faxDeliveryConfirmation\n              savefaxes\n              confirmationEmail\n              dateCreated\n              deleted\n              enabled\n              cid\n              emails\n              allowedUsers\n              allowedNumbers\n              groupMembers\n              forwardNumbers\n              fallbackEmails\n              destination\n              cidOverride\n              port\n              temporary\n              greetingFilePath\n              agreementFilePath\n              carrier\n              e911Carrier\n              tdmRoute\n              trunk\n              previousTrunk\n              linkedToExternalAta\n              alarmRelayInfo\n              inBoundCallBlocked\n              inbandRouting\n              isNumberLocked\n              usbPort\n              onlyEfax\n              isTollFree\n              tollFreeNumbers\n              provisionDate\n              spoofType\n              spoofIncomingCid\n              trunkSelection\n              numDigits\n            }\n            deleted\n            createdAt\n            updatedAt\n          }\n          assignedNumberDoc {\n            _id\n            number\n            linkedBox\n            assignedTo\n            company\n            type\n            callerIdName\n            isForwarded\n            huntPorts\n            huntType\n            portLabel\n            forwardUris\n            linkedToObi\n            faxDeliveryConfirmation\n            savefaxes\n            confirmationEmail\n            dateCreated\n            deleted\n            enabled\n            cid\n            emails\n            allowedUsers\n            allowedNumbers\n            groupMembers\n            forwardNumbers\n            fallbackEmails\n            routeInfo {\n              route\n              gateway\n            }\n            previousRouteInfo {\n              route\n              gateway\n            }\n            destination\n            cidOverride\n            port\n            temporary\n            greetingFilePath\n            agreementFilePath\n            carrier\n            e911Carrier\n            tdmRoute\n            trunk\n            previousTrunk\n            linkedToExternalAta\n            alarmRelayInfo\n            inBoundCallBlocked\n            inbandRouting\n            isNumberLocked\n            usbPort\n            onlyEfax\n            isTollFree\n            tollFreeNumbers\n            provisionDate\n            spoofType\n            spoofIncomingCid\n            trunkSelection\n            numDigits\n            e911Info {\n                address1\n                address2\n                city\n                state\n                zip\n                country\n                locationid\n                callername\n                vendor\n                createdAt\n                updatedAt\n            }\n          }\n          appliedConfigTemplate\n          appliedConfigTemplateDoc {\n            _id\n            title\n            name\n            default\n            SilenceDetectSensitivity\n            OnHookTipRingVoltage\n            OffHookCurrentMax\n            DTMFDetectMinLength\n            DTMFDetectMinGap\n            ChannelTxGain\n            ChannelRxGain\n            DTMFMethod\n            DTMFPlaybackLevel\n            RingVoltage\n            DigitMapShortTimer\n            CPCDelayTime\n            CPCDuration\n            showModemModeToggle\n            modemMode\n            showT38EnableToggle\n            t38Enabled\n            showFaxEnabledToggle\n            faxEnabled\n            showJitterDelay\n            JitterMinDelay\n            JitterMaxDelay\n            inbandRoute\n            showInbandRouteToggle\n            trunkSelection\n            showTrunkSelectionToggle\n            vadEnable\n            showVadToggle\n            showModemOnlyToggle\n            modemOnly\n            ataType\n          }\n        }\n      }\n      phoneDocs {\n        _id\n        displayName\n        extension\n        model\n        number\n        numberDoc {\n          _id\n          number\n        }\n      }\n      locationDoc {\n        locationName\n        _id\n      }\n      \n      sysInfo {\n    _id\n    boxId\n    cdmStatus\n    deviceFw\n    error\n    failoverStatus\n    failoverUpdateStatus\n    restbinStatus\n    diskIo {\n      Name\n      ReadsCompleted\n      WritesCompleted\n    }\n    diskUsage {\n      all\n      avail\n      error\n      free\n      size\n      used\n    }\n    loadavg {\n      Loadavg1\n      Loadavg5\n      Loadavg15\n      error\n    }\n    restbinVersion\n    time\n    uptime {\n      uptime\n    }\n    watchGuardStatus\n    wifiInfo {\n      ap\n      error\n      mode\n      ssid\n    }\n    boxFirmware {\n      version\n    }\n    lteSignalStrength {\n      signalStrength\n    }\n    networkInfoDetail {\n    activeInterface\n    arp {\n      keys\n      values\n    }\n    eth0Config {\n      nameserver1\n      nameserver2\n      nameserver3\n      nameserver4\n    }\n    eth0Stats {\n      eth0IP\n      eth0MAC\n      rxErrors\n      rxPackets\n      txErrors\n      txPackets\n    }\n    fwVersion\n    icmp {\n      eth0\n      wwan0\n    }\n    linkStatus {\n      epiLink\n      eth02\n    }\n    modemCount\n    edgeIP\n    ip4G\n    tunnelAccess {\n      eth0\n      wwan0\n    }\n    registrationStatus\n    tunnelIP\n    powerSource\n    lteSgnalStrength\n    priCardStatus {\n      priCardStatus\n    }\n    deviceFirmwareVersion {\n      version\n    }\n    epiInfo {\n      fwVersion\n      ip\n      mac\n      provUrl\n      uptime\n    }\n    dcAvgPing {\n      chPingAvg\n      laPingAvg\n      nyPingAvg\n      atPingAvg\n      dlPingAvg\n      bestDC\n      bestLatency\n      timeUpdated\n      error\n    }\n    cdmBoxStatus {\n      Status\n      LastCdmCheck\n      Duration\n    }\n  }\n    cpu {\n      CPUCount\n      Guest\n      GuestNice\n      Idle\n      Iowait\n      irq\n      Nice\n      Softirq\n      StatCount\n      Steal\n      System\n      Total\n      User\n    }\n    memory {\n      Active\n      Available\n      Buffers\n      Cached\n      Free\n      Inactive\n      MemAvailableEnabled\n      SwapCached\n      SwapFree\n      SwapTotal\n      SwapUsed\n      Total\n      Used\n    }\n    network {\n      Name\n      RxBytes\n      TxBytes\n    }\n    portStatuses {\n      eth0\n      eth1\n      eth2\n      wg0\n      wg7\n      wlan0\n      wwan0\n    }\n    lastUpdated\n  }\n\n  liveSysInfo {\n    _id\n    boxId\n    cdmStatus\n    deviceFw\n    error\n    failoverStatus\n    failoverUpdateStatus\n    restbinStatus\n    diskIo {\n      Name\n      ReadsCompleted\n      WritesCompleted\n    }\n    diskUsage {\n      all\n      avail\n      error\n      free\n      size\n      used\n    }\n    loadavg {\n      Loadavg1\n      Loadavg5\n      Loadavg15\n      error\n    }\n    restbinVersion\n    time\n    uptime {\n      uptime\n    }\n    watchGuardStatus\n    wifiInfo {\n      ap\n      error\n      mode\n      ssid\n    }\n    boxFirmware {\n      version\n    }\n    lteSignalStrength {\n      signalStrength\n    }\n    networkInfoDetail {\n    activeInterface\n    arp {\n      keys\n      values\n    }\n    eth0Config {\n      nameserver1\n      nameserver2\n      nameserver3\n      nameserver4\n    }\n    eth0Stats {\n      eth0IP\n      eth0MAC\n      rxErrors\n      rxPackets\n      txErrors\n      txPackets\n    }\n    fwVersion\n    icmp {\n      eth0\n      wwan0\n    }\n    linkStatus {\n      epiLink\n      eth02\n    }\n    modemCount\n    edgeIP\n    ip4G\n    tunnelAccess {\n      eth0\n      wwan0\n    }\n    registrationStatus\n    tunnelIP\n    powerSource\n    lteSgnalStrength\n    priCardStatus {\n      priCardStatus\n    }\n    deviceFirmwareVersion {\n      version\n    }\n    epiInfo {\n      fwVersion\n      ip\n      mac\n      provUrl\n      uptime\n    }\n    dcAvgPing {\n      chPingAvg\n      laPingAvg\n      nyPingAvg\n      atPingAvg\n      dlPingAvg\n      bestDC\n      bestLatency\n      timeUpdated\n      error\n    }\n    cdmBoxStatus {\n      Status\n      LastCdmCheck\n      Duration\n    }\n  }\n    cpu {\n      CPUCount\n      Guest\n      GuestNice\n      Idle\n      Iowait\n      irq\n      Nice\n      Softirq\n      StatCount\n      Steal\n      System\n      Total\n      User\n    }\n    memory {\n      Active\n      Available\n      Buffers\n      Cached\n      Free\n      Inactive\n      MemAvailableEnabled\n      SwapCached\n      SwapFree\n      SwapTotal\n      SwapUsed\n      Total\n      Used\n    }\n    network {\n      Name\n      RxBytes\n      TxBytes\n    }\n    portStatuses {\n      eth0\n      eth1\n      eth2\n      wg0\n      wg7\n      wlan0\n      wwan0\n    }\n    lastUpdated\n  }\n\n   vSwitchTab {\n      registered\n      registerationConfigCreated\n      portsConfigCreated\n      portsInfo {\n        port\n        calledId\n        recording\n        trunkType\n      }\n    }\n    liveVSwitchTab {\n    registered\n    registerationConfigCreated\n    portsConfigCreated\n    portsInfo {\n      port\n      calledId\n      recording\n      trunkType\n    }\n  }\n\n    }\n  }\n": types.EpikBoxByIdDocument,
    "\n  query EpikBoxDeviceView($id: String!) {\n    EpikBoxById(id: $id) {\n      _id\n      displayName\n      serialNumber\n      fwVersion\n      creationDate\n      vpnAddress\n      apuType\n      model\n      lteIp,\n      lteIp2,\n      primarySim,\n      priorityInterface,\n      simStatus,\n      liveSimStatus\n      advancedRouting\n      myEpik\n      boxRegistrar\n      customerProvidedIp\n      activeLTE {\n        sim\n        imei\n        mtn\n        ip\n        iccid\n        timeStamp\n      }\n      modems {\n        imeis\n        label\n        phones\n        sims\n        type\n      }\n      modemInfo {\n        model\n        imei\n        sim\n        ipAddress\n      }\n    }\n  }\n": types.EpikBoxDeviceViewDocument,
    "\n  query ListEpikBoxes(\n    $pagination: PaginationInput\n    $filter: EpikBoxFilterInput!\n  ) {\n    ListEpikBoxes(pagination: $pagination, filter: $filter) {\n      docs {\n        _id\n        serialNumber\n        vpnAddress\n        creationDate\n        deleted\n        monitor\n        displayName\n        numPorts\n        companyDoc {\n          _id\n          name\n        }\n        locationDoc {\n          _id\n          locationName\n        }\n      }\n      pagination {\n        currentPage\n        totalPages\n        count\n      }\n    }\n  }\n": types.ListEpikBoxesDocument,
    "\n  query ListNumbers($filter: ListNumberInput!, $pagination: PaginationInput!) {\n    ListNumbers(pagination: $pagination, filter: $filter) {\n      docs {\n        _id\n        number\n        linkedBox\n        company\n        callerIdName\n      }\n      pagination {\n        currentPage\n        totalPages\n        count\n      }\n    }\n  }\n": types.ListNumbersDocument,
    "\n  query ListEpi($input: EpiFilterInput!, $pagination: PaginationInput!) {\n    ListEpi(filter: $input, pagination: $pagination) {\n      pagination {\n        totalPages\n        count\n      }\n      docs {\n        _id\n        macAddress\n        obiNumber\n      }\n    }\n  }\n": types.ListEpiDocument,
};

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 *
 *
 * @example
 * ```ts
 * const query = graphql(`query GetUser($id: ID!) { user(id: $id) { name } }`);
 * ```
 *
 * The query argument is unknown!
 * Please regenerate the types.
 */
export function graphql(source: string): unknown;

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  subscription EpiUpdateHook($fields: [String!]!, $id: ID!) {\n    EpiUpdateHook(fields: $fields, id: $id) {\n      fieldName\n      value\n      id\n      error\n    }\n  }\n"): (typeof documents)["\n  subscription EpiUpdateHook($fields: [String!]!, $id: ID!) {\n    EpiUpdateHook(fields: $fields, id: $id) {\n      fieldName\n      value\n      id\n      error\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  subscription EpikBoxUpdateHook($fields: [String!]!, $id: String!) {\n    EpikBoxUpdateHook(fields: $fields, id: $id) {\n      fieldName\n      value\n      id\n      error\n    }\n  }\n"): (typeof documents)["\n  subscription EpikBoxUpdateHook($fields: [String!]!, $id: String!) {\n    EpikBoxUpdateHook(fields: $fields, id: $id) {\n      fieldName\n      value\n      id\n      error\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n query EpiDetailById($id: String!) {\n  EpiDetailById(id: $id) {\n    _id\n    deviceId\n    obiNumber\n    macAddress\n    assignedTo\n    registrationMeta {\n      wanInfo {\n        placeHolder\n        ip\n        subnet\n        gateway\n        dns\n      }\n      sp1ServiceStatus {\n        status\n        callState\n      }\n      sp2ServiceStatus {\n        status\n        callState\n      }\n      obiTalkServiceStatus {\n        placeHolder\n        status\n      }\n    }\n    liveRegistrationMeta {\n      wanInfo {\n        placeHolder\n        ip\n        subnet\n        gateway\n        dns\n      }\n      sp1ServiceStatus {\n        status\n        callState\n      }\n      sp2ServiceStatus {\n        status\n        callState\n      }\n      obiTalkServiceStatus {\n        placeHolder\n        status\n      }\n    }\n    portPhysicalMeta {\n      name\n      state\n      loopCurrent\n      Vbat\n      tipRingVoltage\n      lastCallerInfo\n    }\n    livePortPhysicalMeta {\n      name\n      state\n      loopCurrent\n      Vbat\n      tipRingVoltage\n      lastCallerInfo\n    }\n  }\n}\n"): (typeof documents)["\n query EpiDetailById($id: String!) {\n  EpiDetailById(id: $id) {\n    _id\n    deviceId\n    obiNumber\n    macAddress\n    assignedTo\n    registrationMeta {\n      wanInfo {\n        placeHolder\n        ip\n        subnet\n        gateway\n        dns\n      }\n      sp1ServiceStatus {\n        status\n        callState\n      }\n      sp2ServiceStatus {\n        status\n        callState\n      }\n      obiTalkServiceStatus {\n        placeHolder\n        status\n      }\n    }\n    liveRegistrationMeta {\n      wanInfo {\n        placeHolder\n        ip\n        subnet\n        gateway\n        dns\n      }\n      sp1ServiceStatus {\n        status\n        callState\n      }\n      sp2ServiceStatus {\n        status\n        callState\n      }\n      obiTalkServiceStatus {\n        placeHolder\n        status\n      }\n    }\n    portPhysicalMeta {\n      name\n      state\n      loopCurrent\n      Vbat\n      tipRingVoltage\n      lastCallerInfo\n    }\n    livePortPhysicalMeta {\n      name\n      state\n      loopCurrent\n      Vbat\n      tipRingVoltage\n      lastCallerInfo\n    }\n  }\n}\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query EpikBoxById($id: String!) {\n    EpikBoxById(id: $id) {\n      _id\n      displayName\n      serialNumber\n      vpnAddress\n      powerState\n      livePowerState\n      activeInterface\n      signalStrength\n      liveSignalStrength\n      liveSignalStrength\n      liveActiveInterface\n      monitor\n      publicIp\n      livePublicIp\n      simStatus\n      liveSimStatus\n      deviceOnline\n      liveDeviceOnline\n      lanIp\n      liveLanIp\n      lteIp\n      lteIp2\n      registered\n      activeCarrier\n      datacenter\n      lteAnalyzer,\n      speedTestData {\n        latency\n        jitter\n        uploadSpeed\n        downloadSpeed\n      }\n      speedTestVoice {\n        latency\n        jitter\n        uploadSpeed\n        downloadSpeed\n      }\n      preferredProviderTest {\n        SimsInfo {\n          Error\n          Jitter\n          PacketLoss\n          PingAvg\n          SIM\n        }\n        TimeStamp\n        initiatedTimeStamp\n      }\n\n      liveSpeedTestData {\n        latency\n        jitter\n        uploadSpeed\n        downloadSpeed\n      }\n      liveSpeedTestVoice {\n        latency\n        jitter\n        uploadSpeed\n        downloadSpeed\n      }\n      livePreferredProviderTest{\n        SimsInfo {\n          Error\n          Jitter\n          PacketLoss\n          PingAvg\n          SIM\n        }\n        TimeStamp\n        initiatedTimeStamp\n      }\n      sensorData {\n        power\n        temp\n      } \n      liveSensorData {\n        power\n        temp\n      } \n      myEpik\n      model\n      apuType\n      powerSaveOption\n      starCodes\n      boxOfflineCallForward\n      recording\n      OBEnable\n      eth3Disable\n      nightlyUpdateTime\n      ext_9override\n      boxRegistrar\n      e911Number\n      primarySim\n      priorityInterface\n      portAlerts\n      currentApn\n      epiTimezone\n      features {\n        showPriTab\n        pri\n        sip\n        twoModems\n        daisyChain\n        dcAutoUpdate\n      }\n      wifiStatus {\n        Error_Msg\n        Gateway\n        IP\n        Mode\n        Password\n        SSID\n        Sec_Mode\n        Status\n        Subnet\n      }\n      liveWifiStatus {\n        Error_Msg\n        Gateway\n        IP\n        Mode\n        Password\n        SSID\n        Sec_Mode\n        Status\n        Subnet\n      }\n      networkInfo {\n        dns\n        interfaces {\n          interface\n          internet\n          icmp\n          wg\n        }\n        timestamp\n        error\n      }\n      liveNetworkInfo {\n        dns\n        interfaces {\n          interface\n          internet\n          icmp\n          wg\n        }\n        timestamp\n        error\n      }\n      \n      priSettings {\n        isSynced\n        pbx\n        priFailovers\n        echoChannels\n        channels\n        framing\n        lineCode\n        timeAndSource\n        dChannel\n        bChannels\n        switchType\n        echoCancellation\n        echoCancellationType\n        numberSettings {\n          dtmfType\n          dnis\n          callerIdName\n          callerIdNumber\n          numbers {\n            _id\n          }\n        }\n        e911Number\n        shippedAddress {\n          attn\n          street1\n          street2\n          city\n          state\n          zip\n        }\n      }\n      sipSettings {\n        casFailovers\n        channels\n        framing\n        lineCode\n        timeAndSource\n        bChannel\n        switchType\n        echoCancellation\n        echoCancellationType\n        channelRange\n        authType\n        ip\n        username\n        password\n        numberSettings {\n          dtmfType\n          dnis\n          callerIdName\n          callerIdNumber\n          numbers {\n            _id\n          }\n        }\n        e911Number\n        shippedAddress {\n          attn\n          street1\n          street2\n          city\n          state\n          zip\n        }\n      }\n      dcAvgPing {\n          chPingAvg\n          laPingAvg\n          nyPingAvg\n          atPingAvg\n          dlPingAvg\n          bestDC\n          bestLatency\n          timeUpdated\n          error\n       }\n      liveDcAvgPing {\n        chPingAvg\n          laPingAvg\n          nyPingAvg\n          atPingAvg\n          dlPingAvg\n          bestDC\n          bestLatency\n          timeUpdated\n          error\n      }\n      modemInfo {\n        carrier\n        carrier_placeholder\n        imei\n        imei_placeholder\n        ipAddress\n        ipAddress_placeholder\n        manufacturer\n        manufacturer_placeholder\n        model\n        model_placeholder\n        sim\n        sim_placeholder\n      }\n\n      liveModemInfo {\n        carrier\n        carrier_placeholder\n        imei\n        imei_placeholder\n        ipAddress\n        ipAddress_placeholder\n        manufacturer\n        manufacturer_placeholder\n        model\n        model_placeholder\n        sim\n        sim_placeholder\n      }\n      companyDoc {\n        _id\n        name\n      }\n      obiDocs {\n        _id\n        macAddress\n        obiNumber\n        deviceId        \n        port1 {\n          boxPortNumber\n          registered\n          disabled\n          provisionDate\n          enableDisableLastUpdatedTime\n          updatedOn\n          serviceName\n          monitored\n          callerIdMasking\n          callerIdMaskingNumber\n          SilenceDetectSensitivity\n          OnHookTipRingVoltage\n          OffHookCurrentMax\n          DTMFDetectMinLength\n          DTMFDetectMinGap\n          ChannelTxGain\n          ChannelRxGain\n          DTMFMethod\n          DTMFPlaybackLevel\n          RingVoltage\n          DigitMapShortTimer\n          CPCDelayTime\n          CPCDuration\n          showVadToggle\n          showModemModeToggle\n          modemMode\n          showT38EnableToggle\n          t38Enabled\n          showFaxEnabledToggle\n          faxEnabled\n          showJitterDelay\n          JitterMinDelay\n          JitterMaxDelay\n          showModemOnlyToggle\n          modemOnly\n          forceForward\n          forceForwardNumber\n          configOverride\n          e911Number\n          e911Enabled\n          voip\n          status\n          connected\n          remoteHost\n          ipAddress\n          remotePort\n          authName\n          assignedNumber\n          vmNumber\n          advancedModemConfiguration\n          portActivated\n          portActivatedDate\n          startedBilling\n          startedBillingDate\n          callerIdOverride\n          provisionUrl\n          blockCallerId\n          callWaiting\n          configPassword\n          assignedNumberRef\n          appliedConfigTemplate\n          vmBox\n          vmailBoxDoc {\n            _id\n            pin\n            totalMessages\n            limit\n            greeting\n            notificationEmails\n            notificationNumbers\n            number\n            numberDoc {\n              _id\n              number\n              linkedBox\n              assignedTo\n              company\n              type\n              callerIdName\n              isForwarded\n              huntPorts\n              huntType\n              portLabel\n              forwardUris\n              linkedToObi\n              faxDeliveryConfirmation\n              savefaxes\n              confirmationEmail\n              dateCreated\n              deleted\n              enabled\n              cid\n              emails\n              allowedUsers\n              allowedNumbers\n              groupMembers\n              forwardNumbers\n              fallbackEmails\n              destination\n              cidOverride\n              port\n              temporary\n              greetingFilePath\n              agreementFilePath\n              carrier\n              e911Carrier\n              tdmRoute\n              trunk\n              previousTrunk\n              linkedToExternalAta\n              alarmRelayInfo\n              inBoundCallBlocked\n              inbandRouting\n              isNumberLocked\n              usbPort\n              onlyEfax\n              isTollFree\n              tollFreeNumbers\n              provisionDate\n              spoofType\n              spoofIncomingCid\n              trunkSelection\n              numDigits\n            }\n            deleted\n            createdAt\n            updatedAt\n          }\n          assignedNumberDoc {\n            _id\n            number\n            linkedBox\n            assignedTo\n            company\n            type\n            callerIdName\n            isForwarded\n            huntPorts\n            huntType\n            portLabel\n            forwardUris\n            linkedToObi\n            faxDeliveryConfirmation\n            savefaxes\n            confirmationEmail\n            dateCreated\n            deleted\n            enabled\n            cid\n            emails\n            allowedUsers\n            allowedNumbers\n            groupMembers\n            forwardNumbers\n            fallbackEmails\n            routeInfo {\n              route\n              gateway\n            }\n            previousRouteInfo {\n              route\n              gateway\n            }\n            destination\n            cidOverride\n            port\n            temporary\n            greetingFilePath\n            agreementFilePath\n            carrier\n            e911Carrier\n            tdmRoute\n            trunk\n            previousTrunk\n            linkedToExternalAta\n            alarmRelayInfo\n            inBoundCallBlocked\n            inbandRouting\n            isNumberLocked\n            usbPort\n            onlyEfax\n            isTollFree\n            tollFreeNumbers\n            provisionDate\n            spoofType\n            spoofIncomingCid\n            trunkSelection\n            numDigits\n            e911Info {\n              address1\n              address2\n              city\n              state\n              zip\n              country\n              locationid\n              callername\n              vendor\n              createdAt\n              updatedAt\n            }\n          }\n          appliedConfigTemplateDoc {\n            _id\n            title\n            name\n            default\n            SilenceDetectSensitivity\n            OnHookTipRingVoltage\n            OffHookCurrentMax\n            DTMFDetectMinLength\n            DTMFDetectMinGap\n            ChannelTxGain\n            ChannelRxGain\n            DTMFMethod\n            DTMFPlaybackLevel\n            RingVoltage\n            DigitMapShortTimer\n            CPCDelayTime\n            CPCDuration\n            showModemModeToggle\n            modemMode\n            showT38EnableToggle\n            t38Enabled\n            showFaxEnabledToggle\n            faxEnabled\n            showJitterDelay\n            JitterMinDelay\n            JitterMaxDelay\n            inbandRoute\n            showInbandRouteToggle\n            trunkSelection\n            showTrunkSelectionToggle\n            vadEnable\n            showVadToggle\n            showModemOnlyToggle\n            modemOnly\n            ataType\n          }\n        }\n        port2 {\n          boxPortNumber\n          registered\n          disabled\n          provisionDate\n          enableDisableLastUpdatedTime\n          updatedOn\n          serviceName\n          monitored\n          callerIdMasking\n          callerIdMaskingNumber\n          SilenceDetectSensitivity\n          OnHookTipRingVoltage\n          OffHookCurrentMax\n          DTMFDetectMinLength\n          DTMFDetectMinGap\n          ChannelTxGain\n          ChannelRxGain\n          DTMFMethod\n          DTMFPlaybackLevel\n          RingVoltage\n          DigitMapShortTimer\n          CPCDelayTime\n          CPCDuration\n          showVadToggle\n          showModemModeToggle\n          modemMode\n          showT38EnableToggle\n          t38Enabled\n          showFaxEnabledToggle\n          faxEnabled\n          showJitterDelay\n          JitterMinDelay\n          JitterMaxDelay\n          showModemOnlyToggle\n          modemOnly\n          forceForward\n          forceForwardNumber\n          configOverride\n          e911Number\n          e911Enabled\n          voip\n          status\n          connected\n          remoteHost\n          ipAddress\n          remotePort\n          authName\n          assignedNumber\n          vmNumber\n          advancedModemConfiguration\n          portActivated\n          portActivatedDate\n          startedBilling\n          startedBillingDate\n          callerIdOverride\n          provisionUrl\n          blockCallerId\n          callWaiting\n          configPassword\n          assignedNumberRef\n           vmBox\n          vmailBoxDoc {\n            _id\n            pin\n            totalMessages\n            limit\n            greeting\n            notificationEmails\n            notificationNumbers\n            number\n            numberDoc {\n              _id\n              number\n              linkedBox\n              assignedTo\n              company\n              type\n              callerIdName\n              isForwarded\n              huntPorts\n              huntType\n              portLabel\n              forwardUris\n              linkedToObi\n              faxDeliveryConfirmation\n              savefaxes\n              confirmationEmail\n              dateCreated\n              deleted\n              enabled\n              cid\n              emails\n              allowedUsers\n              allowedNumbers\n              groupMembers\n              forwardNumbers\n              fallbackEmails\n              destination\n              cidOverride\n              port\n              temporary\n              greetingFilePath\n              agreementFilePath\n              carrier\n              e911Carrier\n              tdmRoute\n              trunk\n              previousTrunk\n              linkedToExternalAta\n              alarmRelayInfo\n              inBoundCallBlocked\n              inbandRouting\n              isNumberLocked\n              usbPort\n              onlyEfax\n              isTollFree\n              tollFreeNumbers\n              provisionDate\n              spoofType\n              spoofIncomingCid\n              trunkSelection\n              numDigits\n            }\n            deleted\n            createdAt\n            updatedAt\n          }\n          assignedNumberDoc {\n            _id\n            number\n            linkedBox\n            assignedTo\n            company\n            type\n            callerIdName\n            isForwarded\n            huntPorts\n            huntType\n            portLabel\n            forwardUris\n            linkedToObi\n            faxDeliveryConfirmation\n            savefaxes\n            confirmationEmail\n            dateCreated\n            deleted\n            enabled\n            cid\n            emails\n            allowedUsers\n            allowedNumbers\n            groupMembers\n            forwardNumbers\n            fallbackEmails\n            routeInfo {\n              route\n              gateway\n            }\n            previousRouteInfo {\n              route\n              gateway\n            }\n            destination\n            cidOverride\n            port\n            temporary\n            greetingFilePath\n            agreementFilePath\n            carrier\n            e911Carrier\n            tdmRoute\n            trunk\n            previousTrunk\n            linkedToExternalAta\n            alarmRelayInfo\n            inBoundCallBlocked\n            inbandRouting\n            isNumberLocked\n            usbPort\n            onlyEfax\n            isTollFree\n            tollFreeNumbers\n            provisionDate\n            spoofType\n            spoofIncomingCid\n            trunkSelection\n            numDigits\n            e911Info {\n                address1\n                address2\n                city\n                state\n                zip\n                country\n                locationid\n                callername\n                vendor\n                createdAt\n                updatedAt\n            }\n          }\n          appliedConfigTemplate\n          appliedConfigTemplateDoc {\n            _id\n            title\n            name\n            default\n            SilenceDetectSensitivity\n            OnHookTipRingVoltage\n            OffHookCurrentMax\n            DTMFDetectMinLength\n            DTMFDetectMinGap\n            ChannelTxGain\n            ChannelRxGain\n            DTMFMethod\n            DTMFPlaybackLevel\n            RingVoltage\n            DigitMapShortTimer\n            CPCDelayTime\n            CPCDuration\n            showModemModeToggle\n            modemMode\n            showT38EnableToggle\n            t38Enabled\n            showFaxEnabledToggle\n            faxEnabled\n            showJitterDelay\n            JitterMinDelay\n            JitterMaxDelay\n            inbandRoute\n            showInbandRouteToggle\n            trunkSelection\n            showTrunkSelectionToggle\n            vadEnable\n            showVadToggle\n            showModemOnlyToggle\n            modemOnly\n            ataType\n          }\n        }\n      }\n      phoneDocs {\n        _id\n        displayName\n        extension\n        model\n        number\n        numberDoc {\n          _id\n          number\n        }\n      }\n      locationDoc {\n        locationName\n        _id\n      }\n      \n      sysInfo {\n    _id\n    boxId\n    cdmStatus\n    deviceFw\n    error\n    failoverStatus\n    failoverUpdateStatus\n    restbinStatus\n    diskIo {\n      Name\n      ReadsCompleted\n      WritesCompleted\n    }\n    diskUsage {\n      all\n      avail\n      error\n      free\n      size\n      used\n    }\n    loadavg {\n      Loadavg1\n      Loadavg5\n      Loadavg15\n      error\n    }\n    restbinVersion\n    time\n    uptime {\n      uptime\n    }\n    watchGuardStatus\n    wifiInfo {\n      ap\n      error\n      mode\n      ssid\n    }\n    boxFirmware {\n      version\n    }\n    lteSignalStrength {\n      signalStrength\n    }\n    networkInfoDetail {\n    activeInterface\n    arp {\n      keys\n      values\n    }\n    eth0Config {\n      nameserver1\n      nameserver2\n      nameserver3\n      nameserver4\n    }\n    eth0Stats {\n      eth0IP\n      eth0MAC\n      rxErrors\n      rxPackets\n      txErrors\n      txPackets\n    }\n    fwVersion\n    icmp {\n      eth0\n      wwan0\n    }\n    linkStatus {\n      epiLink\n      eth02\n    }\n    modemCount\n    edgeIP\n    ip4G\n    tunnelAccess {\n      eth0\n      wwan0\n    }\n    registrationStatus\n    tunnelIP\n    powerSource\n    lteSgnalStrength\n    priCardStatus {\n      priCardStatus\n    }\n    deviceFirmwareVersion {\n      version\n    }\n    epiInfo {\n      fwVersion\n      ip\n      mac\n      provUrl\n      uptime\n    }\n    dcAvgPing {\n      chPingAvg\n      laPingAvg\n      nyPingAvg\n      atPingAvg\n      dlPingAvg\n      bestDC\n      bestLatency\n      timeUpdated\n      error\n    }\n    cdmBoxStatus {\n      Status\n      LastCdmCheck\n      Duration\n    }\n  }\n    cpu {\n      CPUCount\n      Guest\n      GuestNice\n      Idle\n      Iowait\n      irq\n      Nice\n      Softirq\n      StatCount\n      Steal\n      System\n      Total\n      User\n    }\n    memory {\n      Active\n      Available\n      Buffers\n      Cached\n      Free\n      Inactive\n      MemAvailableEnabled\n      SwapCached\n      SwapFree\n      SwapTotal\n      SwapUsed\n      Total\n      Used\n    }\n    network {\n      Name\n      RxBytes\n      TxBytes\n    }\n    portStatuses {\n      eth0\n      eth1\n      eth2\n      wg0\n      wg7\n      wlan0\n      wwan0\n    }\n    lastUpdated\n  }\n\n  liveSysInfo {\n    _id\n    boxId\n    cdmStatus\n    deviceFw\n    error\n    failoverStatus\n    failoverUpdateStatus\n    restbinStatus\n    diskIo {\n      Name\n      ReadsCompleted\n      WritesCompleted\n    }\n    diskUsage {\n      all\n      avail\n      error\n      free\n      size\n      used\n    }\n    loadavg {\n      Loadavg1\n      Loadavg5\n      Loadavg15\n      error\n    }\n    restbinVersion\n    time\n    uptime {\n      uptime\n    }\n    watchGuardStatus\n    wifiInfo {\n      ap\n      error\n      mode\n      ssid\n    }\n    boxFirmware {\n      version\n    }\n    lteSignalStrength {\n      signalStrength\n    }\n    networkInfoDetail {\n    activeInterface\n    arp {\n      keys\n      values\n    }\n    eth0Config {\n      nameserver1\n      nameserver2\n      nameserver3\n      nameserver4\n    }\n    eth0Stats {\n      eth0IP\n      eth0MAC\n      rxErrors\n      rxPackets\n      txErrors\n      txPackets\n    }\n    fwVersion\n    icmp {\n      eth0\n      wwan0\n    }\n    linkStatus {\n      epiLink\n      eth02\n    }\n    modemCount\n    edgeIP\n    ip4G\n    tunnelAccess {\n      eth0\n      wwan0\n    }\n    registrationStatus\n    tunnelIP\n    powerSource\n    lteSgnalStrength\n    priCardStatus {\n      priCardStatus\n    }\n    deviceFirmwareVersion {\n      version\n    }\n    epiInfo {\n      fwVersion\n      ip\n      mac\n      provUrl\n      uptime\n    }\n    dcAvgPing {\n      chPingAvg\n      laPingAvg\n      nyPingAvg\n      atPingAvg\n      dlPingAvg\n      bestDC\n      bestLatency\n      timeUpdated\n      error\n    }\n    cdmBoxStatus {\n      Status\n      LastCdmCheck\n      Duration\n    }\n  }\n    cpu {\n      CPUCount\n      Guest\n      GuestNice\n      Idle\n      Iowait\n      irq\n      Nice\n      Softirq\n      StatCount\n      Steal\n      System\n      Total\n      User\n    }\n    memory {\n      Active\n      Available\n      Buffers\n      Cached\n      Free\n      Inactive\n      MemAvailableEnabled\n      SwapCached\n      SwapFree\n      SwapTotal\n      SwapUsed\n      Total\n      Used\n    }\n    network {\n      Name\n      RxBytes\n      TxBytes\n    }\n    portStatuses {\n      eth0\n      eth1\n      eth2\n      wg0\n      wg7\n      wlan0\n      wwan0\n    }\n    lastUpdated\n  }\n\n   vSwitchTab {\n      registered\n      registerationConfigCreated\n      portsConfigCreated\n      portsInfo {\n        port\n        calledId\n        recording\n        trunkType\n      }\n    }\n    liveVSwitchTab {\n    registered\n    registerationConfigCreated\n    portsConfigCreated\n    portsInfo {\n      port\n      calledId\n      recording\n      trunkType\n    }\n  }\n\n    }\n  }\n"): (typeof documents)["\n  query EpikBoxById($id: String!) {\n    EpikBoxById(id: $id) {\n      _id\n      displayName\n      serialNumber\n      vpnAddress\n      powerState\n      livePowerState\n      activeInterface\n      signalStrength\n      liveSignalStrength\n      liveSignalStrength\n      liveActiveInterface\n      monitor\n      publicIp\n      livePublicIp\n      simStatus\n      liveSimStatus\n      deviceOnline\n      liveDeviceOnline\n      lanIp\n      liveLanIp\n      lteIp\n      lteIp2\n      registered\n      activeCarrier\n      datacenter\n      lteAnalyzer,\n      speedTestData {\n        latency\n        jitter\n        uploadSpeed\n        downloadSpeed\n      }\n      speedTestVoice {\n        latency\n        jitter\n        uploadSpeed\n        downloadSpeed\n      }\n      preferredProviderTest {\n        SimsInfo {\n          Error\n          Jitter\n          PacketLoss\n          PingAvg\n          SIM\n        }\n        TimeStamp\n        initiatedTimeStamp\n      }\n\n      liveSpeedTestData {\n        latency\n        jitter\n        uploadSpeed\n        downloadSpeed\n      }\n      liveSpeedTestVoice {\n        latency\n        jitter\n        uploadSpeed\n        downloadSpeed\n      }\n      livePreferredProviderTest{\n        SimsInfo {\n          Error\n          Jitter\n          PacketLoss\n          PingAvg\n          SIM\n        }\n        TimeStamp\n        initiatedTimeStamp\n      }\n      sensorData {\n        power\n        temp\n      } \n      liveSensorData {\n        power\n        temp\n      } \n      myEpik\n      model\n      apuType\n      powerSaveOption\n      starCodes\n      boxOfflineCallForward\n      recording\n      OBEnable\n      eth3Disable\n      nightlyUpdateTime\n      ext_9override\n      boxRegistrar\n      e911Number\n      primarySim\n      priorityInterface\n      portAlerts\n      currentApn\n      epiTimezone\n      features {\n        showPriTab\n        pri\n        sip\n        twoModems\n        daisyChain\n        dcAutoUpdate\n      }\n      wifiStatus {\n        Error_Msg\n        Gateway\n        IP\n        Mode\n        Password\n        SSID\n        Sec_Mode\n        Status\n        Subnet\n      }\n      liveWifiStatus {\n        Error_Msg\n        Gateway\n        IP\n        Mode\n        Password\n        SSID\n        Sec_Mode\n        Status\n        Subnet\n      }\n      networkInfo {\n        dns\n        interfaces {\n          interface\n          internet\n          icmp\n          wg\n        }\n        timestamp\n        error\n      }\n      liveNetworkInfo {\n        dns\n        interfaces {\n          interface\n          internet\n          icmp\n          wg\n        }\n        timestamp\n        error\n      }\n      \n      priSettings {\n        isSynced\n        pbx\n        priFailovers\n        echoChannels\n        channels\n        framing\n        lineCode\n        timeAndSource\n        dChannel\n        bChannels\n        switchType\n        echoCancellation\n        echoCancellationType\n        numberSettings {\n          dtmfType\n          dnis\n          callerIdName\n          callerIdNumber\n          numbers {\n            _id\n          }\n        }\n        e911Number\n        shippedAddress {\n          attn\n          street1\n          street2\n          city\n          state\n          zip\n        }\n      }\n      sipSettings {\n        casFailovers\n        channels\n        framing\n        lineCode\n        timeAndSource\n        bChannel\n        switchType\n        echoCancellation\n        echoCancellationType\n        channelRange\n        authType\n        ip\n        username\n        password\n        numberSettings {\n          dtmfType\n          dnis\n          callerIdName\n          callerIdNumber\n          numbers {\n            _id\n          }\n        }\n        e911Number\n        shippedAddress {\n          attn\n          street1\n          street2\n          city\n          state\n          zip\n        }\n      }\n      dcAvgPing {\n          chPingAvg\n          laPingAvg\n          nyPingAvg\n          atPingAvg\n          dlPingAvg\n          bestDC\n          bestLatency\n          timeUpdated\n          error\n       }\n      liveDcAvgPing {\n        chPingAvg\n          laPingAvg\n          nyPingAvg\n          atPingAvg\n          dlPingAvg\n          bestDC\n          bestLatency\n          timeUpdated\n          error\n      }\n      modemInfo {\n        carrier\n        carrier_placeholder\n        imei\n        imei_placeholder\n        ipAddress\n        ipAddress_placeholder\n        manufacturer\n        manufacturer_placeholder\n        model\n        model_placeholder\n        sim\n        sim_placeholder\n      }\n\n      liveModemInfo {\n        carrier\n        carrier_placeholder\n        imei\n        imei_placeholder\n        ipAddress\n        ipAddress_placeholder\n        manufacturer\n        manufacturer_placeholder\n        model\n        model_placeholder\n        sim\n        sim_placeholder\n      }\n      companyDoc {\n        _id\n        name\n      }\n      obiDocs {\n        _id\n        macAddress\n        obiNumber\n        deviceId        \n        port1 {\n          boxPortNumber\n          registered\n          disabled\n          provisionDate\n          enableDisableLastUpdatedTime\n          updatedOn\n          serviceName\n          monitored\n          callerIdMasking\n          callerIdMaskingNumber\n          SilenceDetectSensitivity\n          OnHookTipRingVoltage\n          OffHookCurrentMax\n          DTMFDetectMinLength\n          DTMFDetectMinGap\n          ChannelTxGain\n          ChannelRxGain\n          DTMFMethod\n          DTMFPlaybackLevel\n          RingVoltage\n          DigitMapShortTimer\n          CPCDelayTime\n          CPCDuration\n          showVadToggle\n          showModemModeToggle\n          modemMode\n          showT38EnableToggle\n          t38Enabled\n          showFaxEnabledToggle\n          faxEnabled\n          showJitterDelay\n          JitterMinDelay\n          JitterMaxDelay\n          showModemOnlyToggle\n          modemOnly\n          forceForward\n          forceForwardNumber\n          configOverride\n          e911Number\n          e911Enabled\n          voip\n          status\n          connected\n          remoteHost\n          ipAddress\n          remotePort\n          authName\n          assignedNumber\n          vmNumber\n          advancedModemConfiguration\n          portActivated\n          portActivatedDate\n          startedBilling\n          startedBillingDate\n          callerIdOverride\n          provisionUrl\n          blockCallerId\n          callWaiting\n          configPassword\n          assignedNumberRef\n          appliedConfigTemplate\n          vmBox\n          vmailBoxDoc {\n            _id\n            pin\n            totalMessages\n            limit\n            greeting\n            notificationEmails\n            notificationNumbers\n            number\n            numberDoc {\n              _id\n              number\n              linkedBox\n              assignedTo\n              company\n              type\n              callerIdName\n              isForwarded\n              huntPorts\n              huntType\n              portLabel\n              forwardUris\n              linkedToObi\n              faxDeliveryConfirmation\n              savefaxes\n              confirmationEmail\n              dateCreated\n              deleted\n              enabled\n              cid\n              emails\n              allowedUsers\n              allowedNumbers\n              groupMembers\n              forwardNumbers\n              fallbackEmails\n              destination\n              cidOverride\n              port\n              temporary\n              greetingFilePath\n              agreementFilePath\n              carrier\n              e911Carrier\n              tdmRoute\n              trunk\n              previousTrunk\n              linkedToExternalAta\n              alarmRelayInfo\n              inBoundCallBlocked\n              inbandRouting\n              isNumberLocked\n              usbPort\n              onlyEfax\n              isTollFree\n              tollFreeNumbers\n              provisionDate\n              spoofType\n              spoofIncomingCid\n              trunkSelection\n              numDigits\n            }\n            deleted\n            createdAt\n            updatedAt\n          }\n          assignedNumberDoc {\n            _id\n            number\n            linkedBox\n            assignedTo\n            company\n            type\n            callerIdName\n            isForwarded\n            huntPorts\n            huntType\n            portLabel\n            forwardUris\n            linkedToObi\n            faxDeliveryConfirmation\n            savefaxes\n            confirmationEmail\n            dateCreated\n            deleted\n            enabled\n            cid\n            emails\n            allowedUsers\n            allowedNumbers\n            groupMembers\n            forwardNumbers\n            fallbackEmails\n            routeInfo {\n              route\n              gateway\n            }\n            previousRouteInfo {\n              route\n              gateway\n            }\n            destination\n            cidOverride\n            port\n            temporary\n            greetingFilePath\n            agreementFilePath\n            carrier\n            e911Carrier\n            tdmRoute\n            trunk\n            previousTrunk\n            linkedToExternalAta\n            alarmRelayInfo\n            inBoundCallBlocked\n            inbandRouting\n            isNumberLocked\n            usbPort\n            onlyEfax\n            isTollFree\n            tollFreeNumbers\n            provisionDate\n            spoofType\n            spoofIncomingCid\n            trunkSelection\n            numDigits\n            e911Info {\n              address1\n              address2\n              city\n              state\n              zip\n              country\n              locationid\n              callername\n              vendor\n              createdAt\n              updatedAt\n            }\n          }\n          appliedConfigTemplateDoc {\n            _id\n            title\n            name\n            default\n            SilenceDetectSensitivity\n            OnHookTipRingVoltage\n            OffHookCurrentMax\n            DTMFDetectMinLength\n            DTMFDetectMinGap\n            ChannelTxGain\n            ChannelRxGain\n            DTMFMethod\n            DTMFPlaybackLevel\n            RingVoltage\n            DigitMapShortTimer\n            CPCDelayTime\n            CPCDuration\n            showModemModeToggle\n            modemMode\n            showT38EnableToggle\n            t38Enabled\n            showFaxEnabledToggle\n            faxEnabled\n            showJitterDelay\n            JitterMinDelay\n            JitterMaxDelay\n            inbandRoute\n            showInbandRouteToggle\n            trunkSelection\n            showTrunkSelectionToggle\n            vadEnable\n            showVadToggle\n            showModemOnlyToggle\n            modemOnly\n            ataType\n          }\n        }\n        port2 {\n          boxPortNumber\n          registered\n          disabled\n          provisionDate\n          enableDisableLastUpdatedTime\n          updatedOn\n          serviceName\n          monitored\n          callerIdMasking\n          callerIdMaskingNumber\n          SilenceDetectSensitivity\n          OnHookTipRingVoltage\n          OffHookCurrentMax\n          DTMFDetectMinLength\n          DTMFDetectMinGap\n          ChannelTxGain\n          ChannelRxGain\n          DTMFMethod\n          DTMFPlaybackLevel\n          RingVoltage\n          DigitMapShortTimer\n          CPCDelayTime\n          CPCDuration\n          showVadToggle\n          showModemModeToggle\n          modemMode\n          showT38EnableToggle\n          t38Enabled\n          showFaxEnabledToggle\n          faxEnabled\n          showJitterDelay\n          JitterMinDelay\n          JitterMaxDelay\n          showModemOnlyToggle\n          modemOnly\n          forceForward\n          forceForwardNumber\n          configOverride\n          e911Number\n          e911Enabled\n          voip\n          status\n          connected\n          remoteHost\n          ipAddress\n          remotePort\n          authName\n          assignedNumber\n          vmNumber\n          advancedModemConfiguration\n          portActivated\n          portActivatedDate\n          startedBilling\n          startedBillingDate\n          callerIdOverride\n          provisionUrl\n          blockCallerId\n          callWaiting\n          configPassword\n          assignedNumberRef\n           vmBox\n          vmailBoxDoc {\n            _id\n            pin\n            totalMessages\n            limit\n            greeting\n            notificationEmails\n            notificationNumbers\n            number\n            numberDoc {\n              _id\n              number\n              linkedBox\n              assignedTo\n              company\n              type\n              callerIdName\n              isForwarded\n              huntPorts\n              huntType\n              portLabel\n              forwardUris\n              linkedToObi\n              faxDeliveryConfirmation\n              savefaxes\n              confirmationEmail\n              dateCreated\n              deleted\n              enabled\n              cid\n              emails\n              allowedUsers\n              allowedNumbers\n              groupMembers\n              forwardNumbers\n              fallbackEmails\n              destination\n              cidOverride\n              port\n              temporary\n              greetingFilePath\n              agreementFilePath\n              carrier\n              e911Carrier\n              tdmRoute\n              trunk\n              previousTrunk\n              linkedToExternalAta\n              alarmRelayInfo\n              inBoundCallBlocked\n              inbandRouting\n              isNumberLocked\n              usbPort\n              onlyEfax\n              isTollFree\n              tollFreeNumbers\n              provisionDate\n              spoofType\n              spoofIncomingCid\n              trunkSelection\n              numDigits\n            }\n            deleted\n            createdAt\n            updatedAt\n          }\n          assignedNumberDoc {\n            _id\n            number\n            linkedBox\n            assignedTo\n            company\n            type\n            callerIdName\n            isForwarded\n            huntPorts\n            huntType\n            portLabel\n            forwardUris\n            linkedToObi\n            faxDeliveryConfirmation\n            savefaxes\n            confirmationEmail\n            dateCreated\n            deleted\n            enabled\n            cid\n            emails\n            allowedUsers\n            allowedNumbers\n            groupMembers\n            forwardNumbers\n            fallbackEmails\n            routeInfo {\n              route\n              gateway\n            }\n            previousRouteInfo {\n              route\n              gateway\n            }\n            destination\n            cidOverride\n            port\n            temporary\n            greetingFilePath\n            agreementFilePath\n            carrier\n            e911Carrier\n            tdmRoute\n            trunk\n            previousTrunk\n            linkedToExternalAta\n            alarmRelayInfo\n            inBoundCallBlocked\n            inbandRouting\n            isNumberLocked\n            usbPort\n            onlyEfax\n            isTollFree\n            tollFreeNumbers\n            provisionDate\n            spoofType\n            spoofIncomingCid\n            trunkSelection\n            numDigits\n            e911Info {\n                address1\n                address2\n                city\n                state\n                zip\n                country\n                locationid\n                callername\n                vendor\n                createdAt\n                updatedAt\n            }\n          }\n          appliedConfigTemplate\n          appliedConfigTemplateDoc {\n            _id\n            title\n            name\n            default\n            SilenceDetectSensitivity\n            OnHookTipRingVoltage\n            OffHookCurrentMax\n            DTMFDetectMinLength\n            DTMFDetectMinGap\n            ChannelTxGain\n            ChannelRxGain\n            DTMFMethod\n            DTMFPlaybackLevel\n            RingVoltage\n            DigitMapShortTimer\n            CPCDelayTime\n            CPCDuration\n            showModemModeToggle\n            modemMode\n            showT38EnableToggle\n            t38Enabled\n            showFaxEnabledToggle\n            faxEnabled\n            showJitterDelay\n            JitterMinDelay\n            JitterMaxDelay\n            inbandRoute\n            showInbandRouteToggle\n            trunkSelection\n            showTrunkSelectionToggle\n            vadEnable\n            showVadToggle\n            showModemOnlyToggle\n            modemOnly\n            ataType\n          }\n        }\n      }\n      phoneDocs {\n        _id\n        displayName\n        extension\n        model\n        number\n        numberDoc {\n          _id\n          number\n        }\n      }\n      locationDoc {\n        locationName\n        _id\n      }\n      \n      sysInfo {\n    _id\n    boxId\n    cdmStatus\n    deviceFw\n    error\n    failoverStatus\n    failoverUpdateStatus\n    restbinStatus\n    diskIo {\n      Name\n      ReadsCompleted\n      WritesCompleted\n    }\n    diskUsage {\n      all\n      avail\n      error\n      free\n      size\n      used\n    }\n    loadavg {\n      Loadavg1\n      Loadavg5\n      Loadavg15\n      error\n    }\n    restbinVersion\n    time\n    uptime {\n      uptime\n    }\n    watchGuardStatus\n    wifiInfo {\n      ap\n      error\n      mode\n      ssid\n    }\n    boxFirmware {\n      version\n    }\n    lteSignalStrength {\n      signalStrength\n    }\n    networkInfoDetail {\n    activeInterface\n    arp {\n      keys\n      values\n    }\n    eth0Config {\n      nameserver1\n      nameserver2\n      nameserver3\n      nameserver4\n    }\n    eth0Stats {\n      eth0IP\n      eth0MAC\n      rxErrors\n      rxPackets\n      txErrors\n      txPackets\n    }\n    fwVersion\n    icmp {\n      eth0\n      wwan0\n    }\n    linkStatus {\n      epiLink\n      eth02\n    }\n    modemCount\n    edgeIP\n    ip4G\n    tunnelAccess {\n      eth0\n      wwan0\n    }\n    registrationStatus\n    tunnelIP\n    powerSource\n    lteSgnalStrength\n    priCardStatus {\n      priCardStatus\n    }\n    deviceFirmwareVersion {\n      version\n    }\n    epiInfo {\n      fwVersion\n      ip\n      mac\n      provUrl\n      uptime\n    }\n    dcAvgPing {\n      chPingAvg\n      laPingAvg\n      nyPingAvg\n      atPingAvg\n      dlPingAvg\n      bestDC\n      bestLatency\n      timeUpdated\n      error\n    }\n    cdmBoxStatus {\n      Status\n      LastCdmCheck\n      Duration\n    }\n  }\n    cpu {\n      CPUCount\n      Guest\n      GuestNice\n      Idle\n      Iowait\n      irq\n      Nice\n      Softirq\n      StatCount\n      Steal\n      System\n      Total\n      User\n    }\n    memory {\n      Active\n      Available\n      Buffers\n      Cached\n      Free\n      Inactive\n      MemAvailableEnabled\n      SwapCached\n      SwapFree\n      SwapTotal\n      SwapUsed\n      Total\n      Used\n    }\n    network {\n      Name\n      RxBytes\n      TxBytes\n    }\n    portStatuses {\n      eth0\n      eth1\n      eth2\n      wg0\n      wg7\n      wlan0\n      wwan0\n    }\n    lastUpdated\n  }\n\n  liveSysInfo {\n    _id\n    boxId\n    cdmStatus\n    deviceFw\n    error\n    failoverStatus\n    failoverUpdateStatus\n    restbinStatus\n    diskIo {\n      Name\n      ReadsCompleted\n      WritesCompleted\n    }\n    diskUsage {\n      all\n      avail\n      error\n      free\n      size\n      used\n    }\n    loadavg {\n      Loadavg1\n      Loadavg5\n      Loadavg15\n      error\n    }\n    restbinVersion\n    time\n    uptime {\n      uptime\n    }\n    watchGuardStatus\n    wifiInfo {\n      ap\n      error\n      mode\n      ssid\n    }\n    boxFirmware {\n      version\n    }\n    lteSignalStrength {\n      signalStrength\n    }\n    networkInfoDetail {\n    activeInterface\n    arp {\n      keys\n      values\n    }\n    eth0Config {\n      nameserver1\n      nameserver2\n      nameserver3\n      nameserver4\n    }\n    eth0Stats {\n      eth0IP\n      eth0MAC\n      rxErrors\n      rxPackets\n      txErrors\n      txPackets\n    }\n    fwVersion\n    icmp {\n      eth0\n      wwan0\n    }\n    linkStatus {\n      epiLink\n      eth02\n    }\n    modemCount\n    edgeIP\n    ip4G\n    tunnelAccess {\n      eth0\n      wwan0\n    }\n    registrationStatus\n    tunnelIP\n    powerSource\n    lteSgnalStrength\n    priCardStatus {\n      priCardStatus\n    }\n    deviceFirmwareVersion {\n      version\n    }\n    epiInfo {\n      fwVersion\n      ip\n      mac\n      provUrl\n      uptime\n    }\n    dcAvgPing {\n      chPingAvg\n      laPingAvg\n      nyPingAvg\n      atPingAvg\n      dlPingAvg\n      bestDC\n      bestLatency\n      timeUpdated\n      error\n    }\n    cdmBoxStatus {\n      Status\n      LastCdmCheck\n      Duration\n    }\n  }\n    cpu {\n      CPUCount\n      Guest\n      GuestNice\n      Idle\n      Iowait\n      irq\n      Nice\n      Softirq\n      StatCount\n      Steal\n      System\n      Total\n      User\n    }\n    memory {\n      Active\n      Available\n      Buffers\n      Cached\n      Free\n      Inactive\n      MemAvailableEnabled\n      SwapCached\n      SwapFree\n      SwapTotal\n      SwapUsed\n      Total\n      Used\n    }\n    network {\n      Name\n      RxBytes\n      TxBytes\n    }\n    portStatuses {\n      eth0\n      eth1\n      eth2\n      wg0\n      wg7\n      wlan0\n      wwan0\n    }\n    lastUpdated\n  }\n\n   vSwitchTab {\n      registered\n      registerationConfigCreated\n      portsConfigCreated\n      portsInfo {\n        port\n        calledId\n        recording\n        trunkType\n      }\n    }\n    liveVSwitchTab {\n    registered\n    registerationConfigCreated\n    portsConfigCreated\n    portsInfo {\n      port\n      calledId\n      recording\n      trunkType\n    }\n  }\n\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query EpikBoxDeviceView($id: String!) {\n    EpikBoxById(id: $id) {\n      _id\n      displayName\n      serialNumber\n      fwVersion\n      creationDate\n      vpnAddress\n      apuType\n      model\n      lteIp,\n      lteIp2,\n      primarySim,\n      priorityInterface,\n      simStatus,\n      liveSimStatus\n      advancedRouting\n      myEpik\n      boxRegistrar\n      customerProvidedIp\n      activeLTE {\n        sim\n        imei\n        mtn\n        ip\n        iccid\n        timeStamp\n      }\n      modems {\n        imeis\n        label\n        phones\n        sims\n        type\n      }\n      modemInfo {\n        model\n        imei\n        sim\n        ipAddress\n      }\n    }\n  }\n"): (typeof documents)["\n  query EpikBoxDeviceView($id: String!) {\n    EpikBoxById(id: $id) {\n      _id\n      displayName\n      serialNumber\n      fwVersion\n      creationDate\n      vpnAddress\n      apuType\n      model\n      lteIp,\n      lteIp2,\n      primarySim,\n      priorityInterface,\n      simStatus,\n      liveSimStatus\n      advancedRouting\n      myEpik\n      boxRegistrar\n      customerProvidedIp\n      activeLTE {\n        sim\n        imei\n        mtn\n        ip\n        iccid\n        timeStamp\n      }\n      modems {\n        imeis\n        label\n        phones\n        sims\n        type\n      }\n      modemInfo {\n        model\n        imei\n        sim\n        ipAddress\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query ListEpikBoxes(\n    $pagination: PaginationInput\n    $filter: EpikBoxFilterInput!\n  ) {\n    ListEpikBoxes(pagination: $pagination, filter: $filter) {\n      docs {\n        _id\n        serialNumber\n        vpnAddress\n        creationDate\n        deleted\n        monitor\n        displayName\n        numPorts\n        companyDoc {\n          _id\n          name\n        }\n        locationDoc {\n          _id\n          locationName\n        }\n      }\n      pagination {\n        currentPage\n        totalPages\n        count\n      }\n    }\n  }\n"): (typeof documents)["\n  query ListEpikBoxes(\n    $pagination: PaginationInput\n    $filter: EpikBoxFilterInput!\n  ) {\n    ListEpikBoxes(pagination: $pagination, filter: $filter) {\n      docs {\n        _id\n        serialNumber\n        vpnAddress\n        creationDate\n        deleted\n        monitor\n        displayName\n        numPorts\n        companyDoc {\n          _id\n          name\n        }\n        locationDoc {\n          _id\n          locationName\n        }\n      }\n      pagination {\n        currentPage\n        totalPages\n        count\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query ListNumbers($filter: ListNumberInput!, $pagination: PaginationInput!) {\n    ListNumbers(pagination: $pagination, filter: $filter) {\n      docs {\n        _id\n        number\n        linkedBox\n        company\n        callerIdName\n      }\n      pagination {\n        currentPage\n        totalPages\n        count\n      }\n    }\n  }\n"): (typeof documents)["\n  query ListNumbers($filter: ListNumberInput!, $pagination: PaginationInput!) {\n    ListNumbers(pagination: $pagination, filter: $filter) {\n      docs {\n        _id\n        number\n        linkedBox\n        company\n        callerIdName\n      }\n      pagination {\n        currentPage\n        totalPages\n        count\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query ListEpi($input: EpiFilterInput!, $pagination: PaginationInput!) {\n    ListEpi(filter: $input, pagination: $pagination) {\n      pagination {\n        totalPages\n        count\n      }\n      docs {\n        _id\n        macAddress\n        obiNumber\n      }\n    }\n  }\n"): (typeof documents)["\n  query ListEpi($input: EpiFilterInput!, $pagination: PaginationInput!) {\n    ListEpi(filter: $input, pagination: $pagination) {\n      pagination {\n        totalPages\n        count\n      }\n      docs {\n        _id\n        macAddress\n        obiNumber\n      }\n    }\n  }\n"];

export function graphql(source: string) {
  return (documents as any)[source] ?? {};
}

export type DocumentType<TDocumentNode extends DocumentNode<any, any>> = TDocumentNode extends DocumentNode<  infer TType,  any>  ? TType  : never;