/* eslint-disable */
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  DateTime: { input: any; output: any; }
  Int64: { input: any; output: any; }
  OID: { input: any; output: any; }
};

export type Company = {
  __typename?: 'Company';
  _id: Scalars['OID']['output'];
  companyState: CompanyState;
  companyType?: Maybe<CompanyType>;
  contactEmails: Array<Scalars['String']['output']>;
  contactPerson: CompanyContactPerson;
  createdAt: Scalars['DateTime']['output'];
  enterpriseNames?: Maybe<Scalars['String']['output']>;
  enterprises: Array<Scalars['OID']['output']>;
  epikCustomerId: Scalars['String']['output'];
  location: CompanyLocation;
  logo?: Maybe<Scalars['String']['output']>;
  managedDevices: Scalars['Int']['output'];
  monitoredDevices: Scalars['Int']['output'];
  name?: Maybe<Scalars['String']['output']>;
  updatedAt: Scalars['DateTime']['output'];
};

export type CompanyContactPerson = {
  __typename?: 'CompanyContactPerson';
  email: Scalars['String']['output'];
  name: Scalars['String']['output'];
  phone: Scalars['String']['output'];
};

export type CompanyLocation = {
  __typename?: 'CompanyLocation';
  address: Scalars['String']['output'];
  city: Scalars['String']['output'];
  state: Scalars['String']['output'];
  zip: Scalars['String']['output'];
};

export enum CompanyState {
  Deleted = 'deleted',
  Disabled = 'disabled',
  Enabled = 'enabled'
}

export enum CompanyType {
  Agency = 'agency',
  Manufacturer = 'manufacturer',
  Partner = 'partner',
  Reseller = 'reseller',
  Retail = 'retail',
  Special = 'special'
}

export type FeatureMeta = {
  __typename?: 'FeatureMeta';
  read: Scalars['Boolean']['output'];
  resourceKey: Resources;
  write: Scalars['Boolean']['output'];
};

export enum Features {
  PermissionGroupList = 'PERMISSION_GROUP_LIST',
  UserAdd = 'USER_ADD',
  UserApprovalList = 'USER_APPROVAL_LIST',
  UserListUser = 'USER_LIST_USER',
  UserPermissionGroup = 'USER_PERMISSION_GROUP',
  UserSoftDelete = 'USER_SOFT_DELETE',
  UserUpdate = 'USER_UPDATE',
  UserUserApproval = 'USER_USER_APPROVAL'
}

export type ListCompanyInput = {
  epikCustomerId?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  pagination: PaginationInput;
  query?: InputMaybe<Scalars['String']['input']>;
};

export type ListCompanyResponse = {
  __typename?: 'ListCompanyResponse';
  docs: Array<Company>;
  pagination?: Maybe<PaginationResponse>;
};

export type ListPermissionGroupsResponse = {
  __typename?: 'ListPermissionGroupsResponse';
  docs: Array<PermissionGroup>;
  options: Array<PermissionOptions>;
};

export type ListUserInput = {
  company?: InputMaybe<Scalars['String']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  pagination: PaginationInput;
  query?: InputMaybe<Scalars['String']['input']>;
  role?: InputMaybe<Scalars['String']['input']>;
};

export type ListUserResponse = {
  __typename?: 'ListUserResponse';
  docs: Array<User>;
  pagination?: Maybe<PaginationResponse>;
};

export type MenuItem = {
  __typename?: 'MenuItem';
  childs?: Maybe<Array<Maybe<MenuItem>>>;
  resourceKey: Resources;
  title: Scalars['String']['output'];
};

export type PaginationInput = {
  page: Scalars['Int64']['input'];
  pageSize: Scalars['Int64']['input'];
};

export type PaginationResponse = {
  __typename?: 'PaginationResponse';
  count: Scalars['Int64']['output'];
  currentPage: Scalars['Int64']['output'];
  totalPages: Scalars['Int64']['output'];
};

export type PermissionEntry = {
  __typename?: 'PermissionEntry';
  permission: Scalars['String']['output'];
  schema: PermissionsSchema;
};

export type PermissionGroup = {
  __typename?: 'PermissionGroup';
  _id?: Maybe<Scalars['OID']['output']>;
  color: Scalars['String']['output'];
  createdAt: Scalars['DateTime']['output'];
  permissions: Array<PermissionEntry>;
  title: Scalars['String']['output'];
  updatedAt: Scalars['DateTime']['output'];
};

export type PermissionOptions = {
  __typename?: 'PermissionOptions';
  label: Scalars['String']['output'];
  value: Scalars['String']['output'];
};

export type PermissionsSchema = {
  __typename?: 'PermissionsSchema';
  read: Scalars['Boolean']['output'];
  write: Scalars['Boolean']['output'];
};

export type Query = {
  __typename?: 'Query';
  ListCompanies: ListCompanyResponse;
  ListPermissionGroups?: Maybe<ListPermissionGroupsResponse>;
  ListUserPermission?: Maybe<UserPermissionData>;
  ListUsers: ListUserResponse;
  _empty?: Maybe<Scalars['String']['output']>;
};


export type QueryListCompaniesArgs = {
  input: ListCompanyInput;
};


export type QueryListUsersArgs = {
  input: ListUserInput;
};

export type ResourceEntry = {
  __typename?: 'ResourceEntry';
  feature: Features;
  meta: FeatureMeta;
};

export enum Resources {
  PermissionGroup = 'PERMISSION_GROUP',
  User = 'USER',
  UserApproval = 'USER_APPROVAL'
}

export type UiSchema = {
  __typename?: 'UISchema';
  resources: Array<UiSchemaResource>;
};

export type UiSchemaFeature = {
  __typename?: 'UISchemaFeature';
  read: Scalars['Boolean']['output'];
  resourceKey: Resources;
  write: Scalars['Boolean']['output'];
};

export type UiSchemaResource = {
  __typename?: 'UISchemaResource';
  features: Array<ResourceEntry>;
  resourceKey: Resources;
};

export type User = {
  __typename?: 'User';
  _id?: Maybe<Scalars['OID']['output']>;
  company?: Maybe<Scalars['OID']['output']>;
  companyDoc?: Maybe<Company>;
  default2fa?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  enabled?: Maybe<Scalars['Boolean']['output']>;
  lockReminder?: Maybe<Scalars['Boolean']['output']>;
  maintenanceNoteSeen?: Maybe<Scalars['Boolean']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  phoneNumber?: Maybe<Scalars['String']['output']>;
  profilePic?: Maybe<Scalars['String']['output']>;
  registerDate?: Maybe<Scalars['DateTime']['output']>;
  resetPassword?: Maybe<Scalars['Boolean']['output']>;
  timeZone?: Maybe<Scalars['String']['output']>;
  twoFactor?: Maybe<Scalars['Boolean']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type UserPermissionData = {
  __typename?: 'UserPermissionData';
  menuItems: Array<MenuItem>;
  uiSchema: UiSchema;
};

export type ListCompaniesQueryVariables = Exact<{
  input: ListCompanyInput;
}>;


export type ListCompaniesQuery = { __typename?: 'Query', ListCompanies: { __typename?: 'ListCompanyResponse', pagination?: { __typename?: 'PaginationResponse', totalPages: any, count: any } | null, docs: Array<{ __typename?: 'Company', _id: any, name?: string | null, epikCustomerId: string, companyState: CompanyState, companyType?: CompanyType | null, managedDevices: number, monitoredDevices: number, enterpriseNames?: string | null, createdAt: any }> } };

export type ListCompaniesWithIdNameQueryVariables = Exact<{
  input: ListCompanyInput;
}>;


export type ListCompaniesWithIdNameQuery = { __typename?: 'Query', ListCompanies: { __typename?: 'ListCompanyResponse', pagination?: { __typename?: 'PaginationResponse', totalPages: any, count: any } | null, docs: Array<{ __typename?: 'Company', _id: any, name?: string | null }> } };

export type ListPermissionGroupsQueryVariables = Exact<{ [key: string]: never; }>;


export type ListPermissionGroupsQuery = { __typename?: 'Query', ListPermissionGroups?: { __typename?: 'ListPermissionGroupsResponse', options: Array<{ __typename?: 'PermissionOptions', label: string, value: string }>, docs: Array<{ __typename?: 'PermissionGroup', title: string, color: string, permissions: Array<{ __typename?: 'PermissionEntry', permission: string, schema: { __typename?: 'PermissionsSchema', read: boolean, write: boolean } }> }> } | null };

export type ListUsersQueryVariables = Exact<{
  input: ListUserInput;
}>;


export type ListUsersQuery = { __typename?: 'Query', ListUsers: { __typename?: 'ListUserResponse', pagination?: { __typename?: 'PaginationResponse', totalPages: any, count: any } | null, docs: Array<{ __typename?: 'User', _id?: any | null, name?: string | null, email?: string | null, twoFactor?: boolean | null, registerDate?: any | null, enabled?: boolean | null, companyDoc?: { __typename?: 'Company', _id: any, name?: string | null } | null }> } };

export type ListUserPermissionQueryVariables = Exact<{ [key: string]: never; }>;


export type ListUserPermissionQuery = { __typename?: 'Query', ListUserPermission?: { __typename?: 'UserPermissionData', menuItems: Array<{ __typename?: 'MenuItem', title: string, resourceKey: Resources, childs?: Array<{ __typename?: 'MenuItem', title: string, resourceKey: Resources } | null> | null }>, uiSchema: { __typename?: 'UISchema', resources: Array<{ __typename?: 'UISchemaResource', resourceKey: Resources, features: Array<{ __typename?: 'ResourceEntry', feature: Features, meta: { __typename?: 'FeatureMeta', read: boolean, write: boolean, resourceKey: Resources } }> }> } } | null };


export const ListCompaniesDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"ListCompanies"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"input"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ListCompanyInput"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ListCompanies"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"input"},"value":{"kind":"Variable","name":{"kind":"Name","value":"input"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"pagination"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"totalPages"}},{"kind":"Field","name":{"kind":"Name","value":"count"}}]}},{"kind":"Field","name":{"kind":"Name","value":"docs"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"epikCustomerId"}},{"kind":"Field","name":{"kind":"Name","value":"companyState"}},{"kind":"Field","name":{"kind":"Name","value":"companyType"}},{"kind":"Field","name":{"kind":"Name","value":"managedDevices"}},{"kind":"Field","name":{"kind":"Name","value":"monitoredDevices"}},{"kind":"Field","name":{"kind":"Name","value":"enterpriseNames"}},{"kind":"Field","name":{"kind":"Name","value":"createdAt"}}]}}]}}]}}]} as unknown as DocumentNode<ListCompaniesQuery, ListCompaniesQueryVariables>;
export const ListCompaniesWithIdNameDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"ListCompaniesWithIdName"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"input"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ListCompanyInput"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ListCompanies"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"input"},"value":{"kind":"Variable","name":{"kind":"Name","value":"input"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"pagination"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"totalPages"}},{"kind":"Field","name":{"kind":"Name","value":"count"}}]}},{"kind":"Field","name":{"kind":"Name","value":"docs"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}}]}}]}}]}}]} as unknown as DocumentNode<ListCompaniesWithIdNameQuery, ListCompaniesWithIdNameQueryVariables>;
export const ListPermissionGroupsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"ListPermissionGroups"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ListPermissionGroups"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"options"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"label"}},{"kind":"Field","name":{"kind":"Name","value":"value"}}]}},{"kind":"Field","name":{"kind":"Name","value":"docs"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"color"}},{"kind":"Field","name":{"kind":"Name","value":"permissions"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"permission"}},{"kind":"Field","name":{"kind":"Name","value":"schema"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"read"}},{"kind":"Field","name":{"kind":"Name","value":"write"}}]}}]}}]}}]}}]}}]} as unknown as DocumentNode<ListPermissionGroupsQuery, ListPermissionGroupsQueryVariables>;
export const ListUsersDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"ListUsers"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"input"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ListUserInput"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ListUsers"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"input"},"value":{"kind":"Variable","name":{"kind":"Name","value":"input"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"pagination"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"totalPages"}},{"kind":"Field","name":{"kind":"Name","value":"count"}}]}},{"kind":"Field","name":{"kind":"Name","value":"docs"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"email"}},{"kind":"Field","name":{"kind":"Name","value":"twoFactor"}},{"kind":"Field","name":{"kind":"Name","value":"registerDate"}},{"kind":"Field","name":{"kind":"Name","value":"enabled"}},{"kind":"Field","name":{"kind":"Name","value":"companyDoc"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"_id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}}]}}]}}]}}]}}]} as unknown as DocumentNode<ListUsersQuery, ListUsersQueryVariables>;
export const ListUserPermissionDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"ListUserPermission"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ListUserPermission"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"menuItems"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"resourceKey"}},{"kind":"Field","name":{"kind":"Name","value":"childs"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"resourceKey"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"uiSchema"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"resources"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"resourceKey"}},{"kind":"Field","name":{"kind":"Name","value":"features"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"feature"}},{"kind":"Field","name":{"kind":"Name","value":"meta"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"read"}},{"kind":"Field","name":{"kind":"Name","value":"write"}},{"kind":"Field","name":{"kind":"Name","value":"resourceKey"}}]}}]}}]}}]}}]}}]}}]} as unknown as DocumentNode<ListUserPermissionQuery, ListUserPermissionQueryVariables>;