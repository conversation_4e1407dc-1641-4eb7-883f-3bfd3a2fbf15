/* eslint-disable */
import * as types from './graphql';
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';

/**
 * Map of all GraphQL operations in the project.
 *
 * This map has several performance disadvantages:
 * 1. It is not tree-shakeable, so it will include all operations in the project.
 * 2. It is not minifiable, so the string of a GraphQL query will be multiple times inside the bundle.
 * 3. It does not support dead code elimination, so it will add unused operations.
 *
 * Therefore it is highly recommended to use the babel or swc plugin for production.
 * Learn more about it here: https://the-guild.dev/graphql/codegen/plugins/presets/preset-client#reducing-bundle-size
 */
type Documents = {
    "\n  query ListCompanies($input: ListCompanyInput!) {\n    ListCompanies(input: $input) {\n      pagination {\n        totalPages\n        count\n      }\n      docs {\n        _id\n        name\n        epikCustomerId\n        companyState \n        companyType\n        managedDevices\n        monitoredDevices\n        enterpriseNames\n        createdAt\n      }\n    }\n  }\n": typeof types.ListCompaniesDocument,
    "\n    query ListCompaniesWithIdName($input: ListCompanyInput!) {\n      ListCompanies(input: $input) {\n        pagination {\n          totalPages\n          count\n        }\n        docs {\n          _id\n          name\n        }\n      }\n    }\n  ": typeof types.ListCompaniesWithIdNameDocument,
    "\n  query ListPermissionGroups {\n    ListPermissionGroups {\n      options{\n        label\n        value\n    }\n    docs{\n      title\n      color\n      permissions{\n        permission\n        schema{\n          read\n          write\n        }\n      }\n    }\n    \n    }\n  }\n": typeof types.ListPermissionGroupsDocument,
    "\n  query ListUsers($input: ListUserInput!) {\n    ListUsers(input: $input) {\n      pagination {\n        totalPages\n        count\n      }\n      docs {\n        _id\n        name\n        email\n        twoFactor\n        registerDate\n        enabled\n        companyDoc {\n            _id\n            name\n          }\n      }\n    }\n  }\n": typeof types.ListUsersDocument,
    "\n  query ListUserPermission {\n    ListUserPermission {\n      menuItems {\n        title\n        resourceKey\n        childs {\n          title\n          resourceKey\n        }\n      }\n      uiSchema {\n        resources {\n          resourceKey\n          features {\n            feature\n            meta {\n              read\n              write\n              resourceKey\n            }\n          }\n        }\n      }\n    }\n  }\n": typeof types.ListUserPermissionDocument,
};
const documents: Documents = {
    "\n  query ListCompanies($input: ListCompanyInput!) {\n    ListCompanies(input: $input) {\n      pagination {\n        totalPages\n        count\n      }\n      docs {\n        _id\n        name\n        epikCustomerId\n        companyState \n        companyType\n        managedDevices\n        monitoredDevices\n        enterpriseNames\n        createdAt\n      }\n    }\n  }\n": types.ListCompaniesDocument,
    "\n    query ListCompaniesWithIdName($input: ListCompanyInput!) {\n      ListCompanies(input: $input) {\n        pagination {\n          totalPages\n          count\n        }\n        docs {\n          _id\n          name\n        }\n      }\n    }\n  ": types.ListCompaniesWithIdNameDocument,
    "\n  query ListPermissionGroups {\n    ListPermissionGroups {\n      options{\n        label\n        value\n    }\n    docs{\n      title\n      color\n      permissions{\n        permission\n        schema{\n          read\n          write\n        }\n      }\n    }\n    \n    }\n  }\n": types.ListPermissionGroupsDocument,
    "\n  query ListUsers($input: ListUserInput!) {\n    ListUsers(input: $input) {\n      pagination {\n        totalPages\n        count\n      }\n      docs {\n        _id\n        name\n        email\n        twoFactor\n        registerDate\n        enabled\n        companyDoc {\n            _id\n            name\n          }\n      }\n    }\n  }\n": types.ListUsersDocument,
    "\n  query ListUserPermission {\n    ListUserPermission {\n      menuItems {\n        title\n        resourceKey\n        childs {\n          title\n          resourceKey\n        }\n      }\n      uiSchema {\n        resources {\n          resourceKey\n          features {\n            feature\n            meta {\n              read\n              write\n              resourceKey\n            }\n          }\n        }\n      }\n    }\n  }\n": types.ListUserPermissionDocument,
};

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 *
 *
 * @example
 * ```ts
 * const query = graphql(`query GetUser($id: ID!) { user(id: $id) { name } }`);
 * ```
 *
 * The query argument is unknown!
 * Please regenerate the types.
 */
export function graphql(source: string): unknown;

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query ListCompanies($input: ListCompanyInput!) {\n    ListCompanies(input: $input) {\n      pagination {\n        totalPages\n        count\n      }\n      docs {\n        _id\n        name\n        epikCustomerId\n        companyState \n        companyType\n        managedDevices\n        monitoredDevices\n        enterpriseNames\n        createdAt\n      }\n    }\n  }\n"): (typeof documents)["\n  query ListCompanies($input: ListCompanyInput!) {\n    ListCompanies(input: $input) {\n      pagination {\n        totalPages\n        count\n      }\n      docs {\n        _id\n        name\n        epikCustomerId\n        companyState \n        companyType\n        managedDevices\n        monitoredDevices\n        enterpriseNames\n        createdAt\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n    query ListCompaniesWithIdName($input: ListCompanyInput!) {\n      ListCompanies(input: $input) {\n        pagination {\n          totalPages\n          count\n        }\n        docs {\n          _id\n          name\n        }\n      }\n    }\n  "): (typeof documents)["\n    query ListCompaniesWithIdName($input: ListCompanyInput!) {\n      ListCompanies(input: $input) {\n        pagination {\n          totalPages\n          count\n        }\n        docs {\n          _id\n          name\n        }\n      }\n    }\n  "];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query ListPermissionGroups {\n    ListPermissionGroups {\n      options{\n        label\n        value\n    }\n    docs{\n      title\n      color\n      permissions{\n        permission\n        schema{\n          read\n          write\n        }\n      }\n    }\n    \n    }\n  }\n"): (typeof documents)["\n  query ListPermissionGroups {\n    ListPermissionGroups {\n      options{\n        label\n        value\n    }\n    docs{\n      title\n      color\n      permissions{\n        permission\n        schema{\n          read\n          write\n        }\n      }\n    }\n    \n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query ListUsers($input: ListUserInput!) {\n    ListUsers(input: $input) {\n      pagination {\n        totalPages\n        count\n      }\n      docs {\n        _id\n        name\n        email\n        twoFactor\n        registerDate\n        enabled\n        companyDoc {\n            _id\n            name\n          }\n      }\n    }\n  }\n"): (typeof documents)["\n  query ListUsers($input: ListUserInput!) {\n    ListUsers(input: $input) {\n      pagination {\n        totalPages\n        count\n      }\n      docs {\n        _id\n        name\n        email\n        twoFactor\n        registerDate\n        enabled\n        companyDoc {\n            _id\n            name\n          }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query ListUserPermission {\n    ListUserPermission {\n      menuItems {\n        title\n        resourceKey\n        childs {\n          title\n          resourceKey\n        }\n      }\n      uiSchema {\n        resources {\n          resourceKey\n          features {\n            feature\n            meta {\n              read\n              write\n              resourceKey\n            }\n          }\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query ListUserPermission {\n    ListUserPermission {\n      menuItems {\n        title\n        resourceKey\n        childs {\n          title\n          resourceKey\n        }\n      }\n      uiSchema {\n        resources {\n          resourceKey\n          features {\n            feature\n            meta {\n              read\n              write\n              resourceKey\n            }\n          }\n        }\n      }\n    }\n  }\n"];

export function graphql(source: string) {
  return (documents as any)[source] ?? {};
}

export type DocumentType<TDocumentNode extends DocumentNode<any, any>> = TDocumentNode extends DocumentNode<  infer TType,  any>  ? TType  : never;