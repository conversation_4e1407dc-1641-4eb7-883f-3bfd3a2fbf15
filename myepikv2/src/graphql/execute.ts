import { print } from 'graphql';
import type { TypedDocumentNode } from '@graphql-typed-document-node/core';

export async function execute<TResult, TVariables>(
  query: TypedDocumentNode<TResult, TVariables>,
  variables?: TVariables
): Promise<TResult> {
  const response = await fetch('http://dev.epik.io/apps/auth-api/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/graphql-response+json',
      Authorization: `Bearer YOUR_TOKEN_HERE`,
    },
    body: JSON.stringify({
      query: print(query), // <-- IMPORTANT: convert to string
      variables,
    }),
  });

  if (!response.ok) {
    throw new Error('Network response was not ok');
  }

  const result = await response.json();

  if (result.errors) {
    throw new Error(result.errors.map((e: any) => e.message).join(', '));
  }

  return result.data;
}
