.network-interface-container {
  padding: 16px;
  background-color: #f0f2f5;
}

.network-card {
  background-color: #e6f7ff;
  border-radius: 8px;
  padding: 16px;
}

.table-cell,
.table-header {
  font-weight: bold;
}

.ip-column,
.mac-column,
.switch-column,
.ip-type-column {
  padding: 8px;
  text-align: center;
}

.card-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
}

.action-button {
  margin-top: 12px;
}

.network-card .ant-card-head {
  background-color: #e6f7ff;
}

.network-card .ant-card-body {
  padding-top: 0;
  padding-bottom: 16px;
}

.card-content p,
.card-content div {
  margin: 0;
  padding: 4px 0;
}
