import PropTypes from 'prop-types';
import {
  Modal,
  Flex,
  Button,
  Typography,
  Input,
  AlertTriangleIcon,
} from '@/components';

// Title Component
const Title = () => {
  return (
    <Flex vertical align="start">
      <Flex
        justify="center"
        align="center"
        style={{
          height: '48px',
          width: '48px',
          borderRadius: '28px',
          textAlign: 'center',
          background: '#FEF0C7',
        }}
      >
        <AlertTriangleIcon height={24} width={24} />
      </Flex>
    </Flex>
  );
};

const DeleteWithSecurityCode = ({ onCancel, handleOk }) => {
  return (
    <Modal
      title={<Title />}
      open={true}
      closable={false}
      width={'45%'}
      footer={
        <Flex
          gap={8}
          justify="center"
          style={{ width: '100%', marginTop: '32px' }}
        >
          <Button
            variant="outlined"
            onClick={onCancel}
            style={{ width: '45%' }}
          >
            Cancel
          </Button>
          <Button onClick={handleOk} style={{ width: '45%' }}>
            Submit
          </Button>
        </Flex>
      }
    >
      <Flex vertical gap={8} align="start" style={{ padding: '0px' }}>
        <Typography className="heading-four-bold">
          Enter Security Code
        </Typography>
        <Flex style={{ width: '100%' }} align="center">
          <Typography style={{ width: '25%' }}>Security Code</Typography>
          <Input />
        </Flex>
      </Flex>
    </Modal>
  );
};

DeleteWithSecurityCode.propTypes = {
  onCancel: PropTypes.func.isRequired, // Validate `onCancel` as a required function
  handleOk: PropTypes.func.isRequired, // Validate `handleOk` as a required function
};

export default DeleteWithSecurityCode;
