import PropTypes from 'prop-types';
import { Pagination as AntPagination } from 'antd';
import { <PERSON>ton, ArrowLeftIcon, ArrowRightIcon, Typography } from '../../..';
import './PaginationStyles.css';

/**
 * A Pagination component for handling large data sets with multiple pages.
 *
 * @param {Object} props - The properties for the Pagination component.
 * @param {"start" | "center" | "end"} [props.align] - Align the pagination controls. Available options are "start", "center", and "end". Available since version 5.19.0.
 * @param {number} [props.current] - The current page number.
 * @param {number} [props.defaultCurrent=1] - The initial default page number.
 * @param {number} [props.defaultPageSize=10] - The initial default number of items per page.
 * @param {boolean} [props.disabled=false] - If true, disables the pagination component.
 * @param {boolean} [props.hideOnSinglePage=false] - If true, hides the pagination controls when there is only one page.
 * @param {function(number, 'page'|'prev'|'next', React.ReactNode): React.ReactNode} [props.itemRender] - A function to customize the content of each pagination item, with access to the page number, the type of the item, and the original element.
 * @param {number} [props.pageSize] - The number of data items per page.
 * @param {Array<string|number>} [props.pageSizeOptions=[10, 20, 50, 100]] - The options for the number of items per page in the size changer.
 * @param {boolean} [props.responsive=false] - If true, the Pagination will adjust its size based on the width of the window.
 * @param {boolean} [props.showLessItems=false] - If true, shows fewer page items in the Pagination.
 * @param {boolean|Object} [props.showQuickJumper=false] - Allows quick navigation between pages. If true, a text input appears to allow users to jump to a specific page. If an object with a `goButton` is provided, a go button is also rendered.
 * @param {boolean|Object} [props.showSizeChanger=false] - Shows a dropdown for selecting the number of items per page if `total > 50`. Accepts a boolean or an object of `SelectProps` since version 5.21.0.
 * @param {boolean} [props.showTitle=true] - If true, shows the title for each page item on hover.
 * @param {function(number, Array<number>): React.ReactNode} [props.showTotal] - A function to display the total number of items and the current range.
 * @param {boolean|Object} [props.simple=false] - Enables simple mode pagination. If an object with a `readOnly` property is provided, it controls whether the input is read-only.
 * @param {"default" | "small"} [props.size="default"] - The size of the pagination controls. Can be "default" or "small".
 * @param {number} [props.total=0] - The total number of data items.
 * @param {function(number, number): void} [props.onChange] - A callback function executed when the page number or page size is changed. Receives the new page number and page size as arguments.
 * @param {function(number, number): void} [props.onShowSizeChange] - A callback function executed when the page size is changed. Receives the current page number and the new page size as arguments.
 *
 * @returns {JSX.Element} The rendered Ant Design Pagination component.
 */
const Pagination = ({
  onPaginationChange,
  paginationData,
  pageSizeOptions,
  paginationBtnOutside,
  paginationBtnText,
  align,
  customPaginationClass = '',
  showSizeChanger = false,
}) => {
  const { page = 1, pageSize = 5, totalRecord = 0 } = paginationData;

  const onPageNumberChange = (page, pageSize) => {
    onPaginationChange(page, pageSize);
  };

  const itemRender = (_, type, originalElement) => {
    if (paginationBtnOutside) {
      if (type === 'prev') {
        return (
          <Button
            color="default"
            variant="outlined"
            icon={<ArrowLeftIcon />}
            className="ant-pagination-prev-btn"
            size="small"
            style={{ marginRight: 10 }}
          >
            <Typography>{paginationBtnText?.[0] || 'Previous'}</Typography>
          </Button>
        );
      }
      if (type === 'next') {
        return (
          <Button
            color="default"
            variant="outlined"
            icon={<ArrowRightIcon />}
            iconPosition="end"
            style={{ marginLeft: 10 }}
            size="small"
            className="ant-pagination-next-btn"
          >
            <Typography>{paginationBtnText?.[1] || 'Next'}</Typography>
          </Button>
        );
      }
    }
    return originalElement;
  };

  return (
    <AntPagination
      current={page}
      onChange={(page, pageSize) => {
        if (pageSize !== paginationData.pageSize) {
          return onPageNumberChange(1, pageSize);
        }
        onPageNumberChange(page, pageSize);
      }}
      pageSize={pageSize}
      total={totalRecord}
      size="small"
      showSizeChanger={showSizeChanger}
      pageSizeOptions={pageSizeOptions}
      itemRender={itemRender}
      className={`pagination-container ${customPaginationClass}`}
      align={align}
    />
  );
};

// PropTypes validation
Pagination.propTypes = {
  onPaginationChange: PropTypes.func.isRequired,
  paginationData: PropTypes.shape({
    page: PropTypes.number,
    pageSize: PropTypes.number,
    totalRecord: PropTypes.number,
  }).isRequired,
  pageSizeOptions: PropTypes.arrayOf(
    PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  ),
  paginationBtnOutside: PropTypes.bool,
  paginationBtnText: PropTypes.arrayOf(PropTypes.string),
  align: PropTypes.oneOf(['start', 'center', 'end']),
  customPaginationClass: PropTypes.string,
  showSizeChanger: PropTypes.bool,
};

export default Pagination;
