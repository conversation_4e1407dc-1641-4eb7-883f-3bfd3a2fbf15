import { useState } from 'react';
import PropTypes from 'prop-types';
import { Table as AntTable } from 'antd';
import Pagination from './pagination/pagination';
import './styles.css';

const defaultFooter = () => 'Here is footer';

const Table = ({
  bordered = false,
  loading = false,
  showTitle = '',
  size = 'large',
  showFooter = false,
  rowSelection = false,
  columns = [],
  data = [],
  onRowsSelectionChange = () => {
    console.log('this is default');
  },
  onPaginationChange,
  paginationData,
  pageSizeOptions = ['10', '20', '50', '100'],
  paginationBtnOutside = false,
  paginationBtnText = ['Previous', 'Next'],
  onRowClick,
  customPaginationClass = '',
  paginationAlign = 'center',
  headerClass = '',
  isPagination = false,
  rowClassName = '',
  scroll = { y: '100vh', x: 'fit-content' },
  showSizeChanger = true,
  tableLayout = 'fixed',
  sticky = true,
  minHeight = 390,
  rowKey = (row) => row._id,
  locale,
}) => {
  const [selectedRows, setSelectedRows] = useState([]);

  const rowSelectionProps = {
    selectedRowKeys: selectedRows,
    onChange: (keys, rows) => {
      setSelectedRows(keys);
      onRowsSelectionChange(keys, rows);
    },
    getCheckboxProps: (record) => ({
      disabled: record.age === 12,
    }),
    ...rowSelection,
  };

  const tableProps = {
    bordered,
    loading,
    size,
    title: showTitle ? () => showTitle : undefined,
    rowSelection: rowSelection ? { ...rowSelectionProps } : false,
    onRow: onRowClick
      ? (row, index) => ({
          onClick: () => {
            onRowClick(row, index);
          },
        })
      : undefined, // Ensure no undefined variable usage
    footer: showFooter ? defaultFooter : undefined,
  };

  const CustomHeader = ({ className, children, ...props }) => {
    return (
      <th {...props} className={className}>
        {children}
      </th>
    );
  };

  CustomHeader.propTypes = {
    /** CSS class for custom header styles */
    className: PropTypes.string,
    /** Children nodes of the header cell */
    children: PropTypes.node,
    /** classname for row */
    rowClassName: PropTypes.string,
  };

  return (
    <div className="table-container box-shadow-light">
      <AntTable
        {...tableProps}
        pagination={false}
        dataSource={data}
        columns={columns}
        rowClassName={rowClassName}
        components={{
          header: {
            cell: (props) => (
              <CustomHeader {...props} className={headerClass} />
            ),
          },
        }}
        size={size}
        className={`custom-table-container min-height-${minHeight}`}
        scroll={{ ...scroll }}
        tableLayout={tableLayout}
        sticky={sticky}
        rowKey={rowKey}
        locale={locale}
      />
      {isPagination && (
        <Pagination
          onPaginationChange={onPaginationChange}
          paginationData={paginationData}
          pageSizeOptions={pageSizeOptions}
          paginationBtnOutside={paginationBtnOutside}
          paginationBtnText={paginationBtnText}
          customPaginationClass={customPaginationClass}
          align={paginationAlign}
          showSizeChanger={showSizeChanger}
        />
      )}
    </div>
  );
};

Table.propTypes = {
  bordered: PropTypes.bool,
  loading: PropTypes.oneOfType([PropTypes.bool, PropTypes.object]),
  showTitle: PropTypes.string,
  size: PropTypes.oneOf(['large', 'middle', 'small']),
  showFooter: PropTypes.bool,
  rowSelection: PropTypes.oneOfType([PropTypes.bool, PropTypes.object]),
  columns: PropTypes.arrayOf(PropTypes.object),
  data: PropTypes.arrayOf(PropTypes.object),
  onRowsSelectionChange: PropTypes.func,
  onPaginationChange: PropTypes.func,
  paginationData: PropTypes.shape({
    page: PropTypes.number,
    pageSize: PropTypes.number,
    totalRecord: PropTypes.number,
  }),
  pageSizeOptions: PropTypes.arrayOf(
    PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  ),
  paginationBtnOutside: PropTypes.bool,
  paginationBtnText: PropTypes.arrayOf(PropTypes.string),
  onRowClick: PropTypes.func,
  customPaginationClass: PropTypes.string,
  paginationAlign: PropTypes.oneOf(['start', 'center', 'end']),
  headerClass: PropTypes.string,
  isPagination: PropTypes.bool,
  rowClassName: PropTypes.string,
  scroll: PropTypes.shape({
    y: PropTypes.string,
    x: PropTypes.string,
  }),
  showSizeChanger: PropTypes.bool,
  tableLayout: PropTypes.string,
  sticky: PropTypes.bool,
  minHeight: PropTypes.number,
  rowKey: PropTypes.func,
  locale: PropTypes.object,
};

export default Table;
