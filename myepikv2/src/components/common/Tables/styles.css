.table-container {
  /* display: flex;
  flex-direction: column;
  height: 100%; */
  /* min-height: 700px; */
  /* min-height: calc(100vh - 185px); */
}

.table-container .ant-table-header {
  border-radius: 0 !important;
}

.custom-table-container {
  /* flex: 1; */
}

.device-table-header-row {
  text-align: center;
  border-bottom: 1px solid #afb5d9 !important;
  border-top-left-radius: 0 !important;
  border-top-right-radius: 0 !important;
  padding: 8px 16px !important;
  padding-left: 8px !important;
}

.min-height-390 .ant-table-body {
  min-height: 390px;
}

.min-height-200 .ant-table-body {
  min-height: 200px;
}

.custom-table-container .ant-table-body,
.custom-table-container .ant-table-content {
  /* Scrollbar Styling */
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: #eaeaea transparent; /* Firefox */

  /* For WebKit browsers (Chrome, Safari) */
  /* overflow: auto; */
}

.custom-table-container .ant-table-body::-webkit-scrollbar,
.custom-table-container .ant-table-content::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-table-container .ant-table-body::-webkit-scrollbar-thumb,
.custom-table-container .ant-table-content::-webkit-scrollbar-thumb {
  background-color: #eaeaea;
  border-radius: 4px;
}

.custom-table-container .ant-table-body::-webkit-scrollbar-track,
.custom-table-container .ant-table-content::-webkit-scrollbar-track {
  background: transparent;
}
