import PropTypes from 'prop-types';
import { Tag } from 'antd';

// DotTag Component
export const DotTag = ({ content, success }) => (
  <Tag className="tag-addable" style={{ display: 'flex' }}>
    <span className={`tag-dot ${success ? 'tag-dot-green' : 'tag-dot-red'}`}>
      2
    </span>
    {content}
  </Tag>
);

DotTag.propTypes = {
  content: PropTypes.node.isRequired, // The content inside the tag
  success: PropTypes.bool.isRequired, // Indicates success (green) or failure (red)
};

// AntTag Component
export const AntTag = ({ content, color, icon, ...rest }) => {
  const style = `${color}-tag`;
  return (
    <Tag className={['tag-addable', style].join(' ')} icon={icon} {...rest}>
      {content}
    </Tag>
  );
};

AntTag.propTypes = {
  content: PropTypes.node.isRequired, // The content inside the tag
  color: PropTypes.string, // The color of the tag
  icon: PropTypes.node, // Icon to be displayed inside the tag
};

AntTag.defaultProps = {
  color: 'default', // Default color if none is provided
  icon: null, // No icon by default
};
