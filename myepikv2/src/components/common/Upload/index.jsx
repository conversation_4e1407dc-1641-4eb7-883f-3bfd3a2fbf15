import PropTypes from 'prop-types';
import { Upload as AntdUpload, message } from 'antd';
import { UploadCloudIcon, Typography, Flex } from '@/components';

const { Dragger } = AntdUpload;

/**
 * CustomUpload - A reusable file upload component using Ant Design's Dragger.
 *
 * @param {Object} props - Component props
 * @param {string} props.action - The URL for uploading files.
 * @param {string} props.name - The name of the file field.
 * @param {boolean} [props.multiple=false] - Whether to support multiple file uploads.
 * @param {function} [props.onChange] - Callback triggered when file upload status changes.
 * @param {function} [props.onDrop] - Callback triggered when files are dropped.
 * @param {React.ReactNode} [props.children] - Optional children (content) to display in the upload box.
 * @returns {React.ReactElement} A draggable upload component.
 */
const Upload = ({
  action,
  name,
  multiple = false,
  onChange,
  onDrop,
  children,
}) => {
  const uploadProps = {
    name,
    multiple,
    action,
    onChange(info) {
      const { status } = info.file;
      // if (status !== 'uploading') {
      // }
      if (status === 'done') {
        message.success(`${info.file.name} file uploaded successfully.`);
      } else if (status === 'error') {
        message.error(`${info.file.name} file upload failed.`);
      }

      if (onChange) {
        onChange(info);
      }
    },
    onDrop(e) {
      if (onDrop) {
        onDrop(e);
      }
    },
  };

  return (
    <Dragger {...uploadProps} style={{ background: 'white' }}>
      {children || (
        <Flex vertical justify="center" align="center">
          <Flex
            justify="center"
            align="center"
            style={{
              height: '40px',
              width: '40px',
              borderRadius: '28px',
              background: '#F9FAFB',
            }}
          >
            <UploadCloudIcon height={20} width={20} />
          </Flex>
          <Typography className="small-text" style={{ color: '#475467' }}>
            <Typography
              className="small-text-bold"
              style={{ color: '#3B5CA9' }}
            >
              Click to upload{' '}
            </Typography>
            or drag and drop
          </Typography>
          <Typography className="small-text" style={{ color: '#475467' }}>
            SVG, PNG, JPG or GIF (max. 800x400px)
          </Typography>
        </Flex>
      )}
    </Dragger>
  );
};

Upload.propTypes = {
  /** The URL for uploading files */
  action: PropTypes.string.isRequired,
  /** Name of the file field */
  name: PropTypes.string,
  /** Whether multiple file upload is allowed */
  multiple: PropTypes.bool,
  /** Callback triggered when file upload changes */
  onChange: PropTypes.func,
  /** Callback triggered when files are dropped */
  onDrop: PropTypes.func,
  /** Custom content to display inside the upload box */
  children: PropTypes.node,
};

Upload.defaultProps = {
  name: 'file',
  multiple: false,
  onChange: null,
  onDrop: null,
  children: null,
};

export default Upload;
