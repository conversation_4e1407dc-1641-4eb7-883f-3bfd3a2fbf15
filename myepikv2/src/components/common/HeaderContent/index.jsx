import PropTypes from 'prop-types';
import { Avatar } from 'antd';
import Flex from '../Flex';
import AvatarIcon from '@/assets/Avatar.png';
import { BellIcon, NotificationIcon, SettingIcon } from '../SvgIcons';
import Button from '../Button';

const HeaderContent = ({ className = '' }) => {
  return (
    <Flex
      justify="flex-end"
      align="center"
      className={`header-content ${className}`}
      style={{ width: '100%' }}
    >
      <Flex gap={20} align="center">
        <Button
          variant="outlined"
          color="default"
          icon={<NotificationIcon height={18} width={18} />}
        >
          Alert
        </Button>
        <Flex gap={10}>
          <SettingIcon height={18} width={18} style={{ cursor: 'pointer' }} />
          <BellIcon height={18} width={18} style={{ cursor: 'pointer' }} />
        </Flex>
        <Avatar
          size={{ xs: 24, sm: 32, md: 32, lg: 32, xl: 32 }}
          src={AvatarIcon}
        />
      </Flex>
    </Flex>
  );
};

export default HeaderContent;

HeaderContent.propTypes = {
  className: PropTypes.string,
};
