import React, { forwardRef } from 'react';
import PropTypes from 'prop-types';
import { <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Space, theme } from 'antd';
import './styles.css';

const { useToken } = theme;

/**
 * A customizable Button component that wraps the Ant Design Button.
 *
 * @param {Object} props - The properties for the Button component.
 * @param {"default" | "primary" | "dashed" | "link" | "text"} [props.type="primary"] - The type of button.
 * @param {"default" | "circle" | "round"} [props.shape="default"] - The shape of the button.
 * @param {"large" | "middle" | "small"} [props.size="middle"] - The size of the button.
 * @param {"outlined" | "dashed" | "solid" | "filled" | "text" | "link"} [props.variant="solid"] - The style variant of the button.
 * @param {React.ReactNode} [props.icon] - The icon element for the button.
 * @param {"start" | "end"} [props.iconPosition="start"] - The position of the icon relative to the button text.
 * @param {boolean | { delay: number }} [props.loading=false] - Whether the button is in a loading state. Can also accept a delay in milliseconds.
 * @param {boolean} [props.block=false] - If true, the button takes up the full width of its container.
 * @param {boolean} [props.danger=false] - If true, applies danger styling to the button.
 * @param {boolean} [props.ghost=false] - If true, applies ghost styling to the button.
 * @param {"default" | "primary" | "danger"} [props.color="primary"] - The theme color of the button.
 * @param {boolean} [props.disabled=false] - Whether the button is disabled.
 * @param {string} [props.href] - The URL for a link button.
 * @param {"submit" | "reset" | "button"} [props.htmlType="button"] - The native HTML button type.
 * @param {Object} [props.classNames] - Custom class names for the button.
 * @param {Object} [props.styles] - Custom inline styles for the button.
 * @param {string} [props.target] - Specifies where to open the linked document for a link button.
 * @param {React.ReactNode} [props.children] - The content of the button.
 * @param {function} [props.onClick] - The callback function triggered when the button is clicked.
 * @param {boolean} [props.autoInsertSpace=true] - Automatically adds a space between the icon and text.
 * @param {boolean} [props.onlyIcon=false] - If true, the button displays only the icon without any text.
 * @param {boolean} [props.variantColorBgContainer=false] - If true, applies a custom background container color.
 *
 * @returns {JSX.Element} The rendered Ant Design Button component with added functionality.
 */
const Button = forwardRef(
  (
    {
      type = 'primary',
      shape = 'default',
      size = 'middle',
      variant = 'solid',
      icon,
      iconPosition = 'start',
      loading = false,
      block = false,
      danger = false,
      ghost = false,
      color = 'primary',
      disabled = false,
      href,
      htmlType = 'button',
      classNames,
      styles,
      target,
      children,
      onClick,
      autoInsertSpace = true,
      onlyIcon = false,
      variantColorBgContainer = false,
      ...rest
    },
    ref,
  ) => {
    const {
      token: {
        alertColor,
        colorPrimary,
        alertColorDisabled,
        defaultColorDisabled,
        colorBgContainerDisabled,
        customColorBgContainer,
        defaultColorBgContainer,
      },
    } = useToken();

    const buttonTheme = {
      components: {
        Button: {
          colorPrimary: danger ? alertColor : colorPrimary,
          color: danger ? alertColor : colorPrimary,
          colorBgContainerDisabled: danger
            ? alertColorDisabled
            : color === 'default'
              ? defaultColorDisabled
              : colorBgContainerDisabled,
          colorBgContainer: variantColorBgContainer
            ? customColorBgContainer
            : defaultColorBgContainer,
        },
      },
    };

    const renderIcon = () => {
      if (React.isValidElement(icon)) {
        return (
          <span style={{ display: 'flex', alignItems: 'center' }}>{icon}</span>
        );
      }
      return null;
    };

    return (
      <ConfigProvider theme={buttonTheme}>
        <AntButton
          ref={ref}
          type={type}
          shape={shape}
          size={size}
          variant={variant}
          loading={loading}
          block={block}
          danger={danger}
          ghost={ghost}
          color={color}
          disabled={disabled}
          href={href}
          htmlType={htmlType}
          style={styles}
          target={target}
          onClick={onClick}
          data-auto-insert-space={autoInsertSpace}
          className={`custom-button ${classNames || ''}`}
          {...rest}
        >
          {onlyIcon && icon && renderIcon()}
          {!onlyIcon && icon && iconPosition === 'start' && (
            <Space>
              {renderIcon()}
              {children && <span>{children}</span>}
            </Space>
          )}
          {!onlyIcon && icon && iconPosition === 'end' && (
            <Space>
              {children && <span style={{ marginRight: 8 }}>{children}</span>}
              {renderIcon()}
            </Space>
          )}
          {!icon && children}
        </AntButton>
      </ConfigProvider>
    );
  },
);

Button.displayName = 'CustomButton';

Button.propTypes = {
  type: PropTypes.oneOf(['default', 'primary', 'dashed', 'link', 'text']),
  shape: PropTypes.oneOf(['default', 'circle', 'round']),
  size: PropTypes.oneOf(['large', 'middle', 'small']),
  variant: PropTypes.oneOf([
    'outlined',
    'dashed',
    'solid',
    'filled',
    'text',
    'link',
  ]),
  icon: PropTypes.node,
  iconPosition: PropTypes.oneOf(['start', 'end']),
  loading: PropTypes.oneOfType([
    PropTypes.bool,
    PropTypes.shape({
      delay: PropTypes.number,
    }),
  ]),
  block: PropTypes.bool,
  danger: PropTypes.bool,
  ghost: PropTypes.bool,
  color: PropTypes.oneOf(['default', 'primary', 'danger']),
  disabled: PropTypes.bool,
  href: PropTypes.string,
  htmlType: PropTypes.oneOf(['submit', 'reset', 'button']),
  classNames: PropTypes.objectOf(PropTypes.string),
  styles: PropTypes.objectOf(PropTypes.object),
  target: PropTypes.string,
  children: PropTypes.node,
  onClick: PropTypes.func,
  autoInsertSpace: PropTypes.bool,
  onlyIcon: PropTypes.bool,
  variantColorBgContainer: PropTypes.bool,
};

Button.defaultProps = {
  type: 'primary',
  shape: 'default',
  size: 'middle',
  variant: 'solid',
  iconPosition: 'start',
  loading: false,
  block: false,
  danger: false,
  ghost: false,
  color: 'primary',
  disabled: false,
  htmlType: 'button',
  autoInsertSpace: true,
  onlyIcon: false,
  variantColorBgContainer: false,
};

export default Button;
