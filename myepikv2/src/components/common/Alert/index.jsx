import PropTypes from 'prop-types';
import { Alert as AntAlert } from 'antd';
import { CloseIcon } from '../SvgIcons';

/**
 * A customizable Alert component that wraps the Ant Design Alert.
 *
 * @param {Object} props - The component properties.
 * @param {'success' | 'info' | 'warning' | 'error'} [props.type='info'] - The type of alert.
 * @param {string} [props.message] - The message content of the alert.
 * @param {string} [props.description] - Additional content for the alert.
 * @param {boolean} [props.showIcon=false] - Whether to show an icon in the alert.
 * @param {boolean | object} [props.closable=false] - Whether the alert is closable.
 * @param {React.ReactNode} [props.icon] - Custom icon for the alert.
 * @param {function} [props.onClose] - Callback when alert is closed.
 * @param {React.ReactNode} [props.action] - Additional action to show in the alert.
 * @param {boolean} [props.banner=false] - Whether to show the alert as a banner.
 * @param {function} [props.afterClose] - Callback when the close animation is finished.
 * @param {object} rest - Additional properties for the Ant Alert component.
 *
 * @returns {JSX.Element} The rendered Ant Design Alert component.
 */
const Alert = ({
  type = 'info',
  message,
  description,
  showIcon = false,
  closable = false,
  icon,
  onClose,
  action,
  banner = false,
  afterClose,
  ...rest
}) => {
  return (
    <AntAlert
      type={type}
      message={message}
      description={description}
      showIcon={showIcon}
      closable={closable}
      icon={icon || (closable?.closeIcon ? <CloseIcon /> : null)}
      onClose={onClose}
      action={action}
      banner={banner}
      afterClose={afterClose}
      {...rest}
    />
  );
};

Alert.propTypes = {
  type: PropTypes.oneOf(['success', 'info', 'warning', 'error']),
  message: PropTypes.string,
  description: PropTypes.string,
  showIcon: PropTypes.bool,
  closable: PropTypes.oneOfType([PropTypes.bool, PropTypes.object]),
  icon: PropTypes.node,
  onClose: PropTypes.func,
  action: PropTypes.node,
  banner: PropTypes.bool,
  afterClose: PropTypes.func,
  ...AntAlert.propTypes, // Include any additional props for Ant Alert.
};

Alert.defaultProps = {
  type: 'info',
  showIcon: false,
  closable: false,
  banner: false,
};

export default Alert;
