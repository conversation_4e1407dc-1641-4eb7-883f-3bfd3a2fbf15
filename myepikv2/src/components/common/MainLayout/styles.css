.main-layout-container {
  height: 100vh;
  overflow: hidden;
}

.sider-container {
  border-right: 1px solid var(--bg-gray);
}

.sider-container .ant-layout-sider-children {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.toggle-button {
  height: fit-content;
  width: fit-content;
  padding: 0;
  cursor: pointer;
}

.sider-search-input {
  padding: 0 12px;
  margin-top: 24px;
}

.sider-search-input .custom-input {
  height: 33px;
}

.header-container {
  height: 54px;
  position: sticky;
  top: 0;
  z-index: 1;
  display: flex;
  padding: 0px 24px;
}

.content-container {
  padding: 18px 0;
  position: relative;
}

.sidebar-meun-container {
  height: 100%;
}

.custom-scrollbar {
  overflow: auto;
  scrollbar-width: thin;
  scrollbar-color: #babac0 transparent;
}

/* WebKit-specific */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #babac0;
  border-radius: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-drawer .ant-drawer-body {
  scrollbar-width: thin;
  scrollbar-color: #babac0 transparent;
}

.logo-container {
  height: 34.5px;
  text-align: center;
  color: #fff;
  margin-top: 18px;
  padding: 0px 12px;
}

.logo-img-container > img {
  width: 69.33px;
  height: 34.66px;
}

.logo-container > svg {
  width: 18px;
  height: 18px;
}

@media (min-width: 1920px) {
  .header-container {
    height: 72px;
    padding: 0px 32px;
  }
  .logo-container {
    height: 52px;
    margin-top: 24px;
  }

  .logo-container > svg {
    width: 24px;
    height: 24px;
  }

  .logo-img-container > img {
    width: 104px;
    height: 52px;
  }

  .sider-search-input .custom-input {
    height: 44px;
  }

  .sider-search-input .custom-input .ant-input-prefix > svg {
    height: 20px;
    width: 20px;
  }
}
