import epikLogo from '@/assets/epik-logo.png';
import {
  Drawer,
  Flex,
  Input,
  MenuToggleIcon,
  SearchIcon,
  SideBar,
} from '@/components';
import { useSearchStore } from '@/store/searchStore';
import { useStore } from '@/store';
import { Layout } from 'antd';
import PropTypes from 'prop-types';
import { useState } from 'react';
import { Outlet } from 'react-router-dom';
import EpikToggle from '../EpikToggle';
import './styles.css';
import HeaderContent from '../HeaderContent';

const { Header, Sider, Content } = Layout;

const MainLayout = ({ menuKeys = [], loading }) => {
  const [collapsed, setCollapsed] = useState(false);
  const { addSidebarSearch } = useSearchStore();
  const { isOpen, ContentComponent, contentProps, handleOk } = useStore(
    (state) => state.drawer,
  );

  const { key: contentKey, ...restContentProps } = contentProps;

  return (
    <Layout className="main-layout-container">
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        onCollapse={(value) => setCollapsed(value)} // Handle collapse
        breakpoint="lg" // Collapse the sider on small screens
        collapsedWidth={'4.2vw'} // Width when collapsed
        width={'16vw'} // Full width
        className="sider-container"
      >
        <Flex vertical style={{ height: '100%' }}>
          <Flex
            align="center"
            justify={`${collapsed ? 'center' : 'space-between'}`}
            className="logo-container"
          >
            {!collapsed && (
              <div className="logo-img-container">
                <img src={epikLogo} />
              </div>
            )}
            <MenuToggleIcon
              className="toggle-button"
              onClick={() => setCollapsed(!collapsed)}
            />
          </Flex>
          {!collapsed && (
            <div className="sider-search-input">
              <Input
                size="middle"
                prefix={<SearchIcon height={15} width={15} />}
                onChange={(e) => addSidebarSearch(e?.target?.value)}
              />
            </div>
          )}
          <Flex vertical style={{ height: '100%', overflow: 'auto' }}>
            <div className="sidebar-meun-container custom-scrollbar">
              <SideBar
                collapsed={collapsed}
                menuKeys={menuKeys}
                loading={loading}
              />
            </div>
            {!collapsed && !loading && <EpikToggle />}
          </Flex>
        </Flex>
      </Sider>

      <Layout>
        <Header className="header-container">
          <HeaderContent />
        </Header>
        <Content className="content-container">
          <Outlet />
          <Drawer
            open={isOpen}
            size="large"
            styles={{ header: { display: 'none' } }}
            maskClosable={false}
            width={900}
            className="custom-drawer"
          >
            {ContentComponent ? (
              <ContentComponent
                key={contentKey}
                {...restContentProps}
                handleOk={handleOk}
              />
            ) : null}
          </Drawer>
        </Content>
      </Layout>
    </Layout>
  );
};

// Add PropTypes validation
MainLayout.propTypes = {
  /** Array of menu keys for the sidebar */
  menuKeys: PropTypes.arrayOf(PropTypes.string), // `menuKeys` should be an array of strings
  /** Indicates if the layout is loading */
  loading: PropTypes.bool, // `loading` should be a boolean
};

export default MainLayout;
