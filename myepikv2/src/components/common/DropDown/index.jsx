import { forwardRef } from 'react';
import PropTypes from 'prop-types';
import { Dropdown as AntDropdown } from 'antd';
import { DownIcon } from '../SvgIcons';
import Button from '../Button';

/**
 * A customizable Dropdown component that wraps Ant Design Dropdown.
 *
 * @param {Object} props - The properties for the DropDown component.
 * @param {Array<Object>} [props.menuItems=[]] - Menu items for the dropdown. Each item should have `key`, `label`, and optionally `disabled`.
 * @param {boolean} [props.arrow=false] - Whether to show an arrow with the dropdown.
 * @param {string} [props.placement='bottomLeft'] - The position of the dropdown relative to its trigger.
 * @param {Array<string>} [props.trigger=['hover']] - The trigger mode for the dropdown. Can be `click`, `hover`, or `contextMenu`.
 * @param {boolean} [props.disabled=false] - Disables the dropdown if set to true.
 * @param {boolean} [props.useButton=false] - If true, renders the dropdown as a button with menu items.
 * @param {string} [props.buttonLabel='Dropdown'] - The label for the dropdown button.
 * @param {React.ReactNode} [props.icon=<DownIcon stroke="white" />] - An icon displayed alongside the button label.
 * @param {React.ReactNode} [props.children] - The custom trigger content for the dropdown.
 * @param {Object} [props.buttonProps={}] - Additional props for the button when `useButton` is true.
 * @param {Object} [props.dropDownStyles={}] - Custom styles for the dropdown trigger container.
 * @param {Object} [rest] - Additional props passed to the Ant Design Dropdown component.
 *
 * @returns {JSX.Element} The rendered Ant Design Dropdown component.
 */
const DropDown = forwardRef(
  (
    {
      menuItems = [],
      arrow = false,
      placement = 'bottomLeft',
      trigger = ['hover'],
      disabled = false,
      useButton = false,
      buttonLabel = 'Dropdown',
      icon = <DownIcon stroke="white" />,
      children,
      buttonProps = {},
      dropDownStyles = {},
      ...rest
    },
    ref,
  ) => {
    const menu = {
      items: menuItems,
      onClick: rest.onClick,
    };

    if (useButton) {
      return (
        <AntDropdown.Button
          ref={ref}
          menu={menu}
          placement={placement}
          disabled={disabled}
          {...rest}
        >
          {buttonLabel}
        </AntDropdown.Button>
      );
    }

    return (
      <AntDropdown
        ref={ref}
        menu={menu}
        arrow={arrow}
        placement={placement}
        trigger={trigger}
        disabled={disabled}
        dropDownStyles={dropDownStyles}
        {...rest}
      >
        <div style={{ ...dropDownStyles }}>
          {children || (
            <Button {...buttonProps}>
              {buttonLabel}
              {icon}
            </Button>
          )}
        </div>
      </AntDropdown>
    );
  },
);

// Add displayName for better debugging with forwardRef
DropDown.displayName = 'DropDown';

// Add PropTypes validation
DropDown.propTypes = {
  menuItems: PropTypes.arrayOf(
    PropTypes.shape({
      key: PropTypes.string.isRequired,
      label: PropTypes.node.isRequired,
      disabled: PropTypes.bool,
    }),
  ),
  arrow: PropTypes.bool,
  placement: PropTypes.oneOf([
    'topLeft',
    'topCenter',
    'topRight',
    'bottomLeft',
    'bottomCenter',
    'bottomRight',
    'top',
    'bottom',
  ]),
  trigger: PropTypes.arrayOf(
    PropTypes.oneOf(['click', 'hover', 'contextMenu']),
  ),
  disabled: PropTypes.bool,
  useButton: PropTypes.bool,
  buttonLabel: PropTypes.string,
  icon: PropTypes.node,
  children: PropTypes.node,
  buttonProps: PropTypes.object,
  dropDownStyles: PropTypes.object,
};

export default DropDown;
