import PropTypes from 'prop-types';
import { Flex as AntFlex } from 'antd';
import './styles.css';

/**
 * A reusable Flex component wrapping Ant Design Flex.
 *
 * @param {Object} props - The component properties.
 * @param {boolean} [props.vertical=false] - Whether the layout direction is vertical.
 * @param {string | number} [props.gap] - Sets the gap between child elements.
 * @param {string} [props.justify] - Alignment along the main axis.
 * @param {string} [props.align] - Alignment along the cross axis.
 * @param {string | boolean} [props.wrap] - Whether to allow wrapping of child elements.
 * @param {React.ReactNode} props.children - Child elements to be rendered inside the Flex container.
 * @param {string} [props.className] - Custom class name for the Flex container.
 * @param {object} rest - Additional props for the Ant Design Flex component.
 *
 * @returns {JSX.Element} A customizable Flex container.
 */
const Flex = ({
  vertical = false,
  gap,
  justify,
  align,
  wrap = false,
  children,
  className,
  ...rest
}) => {
  return (
    <AntFlex
      vertical={vertical}
      gap={gap}
      justify={justify}
      align={align}
      wrap={wrap}
      className={className}
      {...rest}
    >
      {children}
    </AntFlex>
  );
};

Flex.propTypes = {
  vertical: PropTypes.bool,
  gap: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  justify: PropTypes.oneOf([
    'flex-start',
    'center',
    'flex-end',
    'space-between',
    'space-around',
    'space-evenly',
  ]),
  align: PropTypes.oneOf([
    'flex-start',
    'center',
    'flex-end',
    'stretch',
    'baseline',
  ]),
  wrap: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
  children: PropTypes.node,
  className: PropTypes.string,
};

export default Flex;
