import PropTypes from 'prop-types';
import { Drawer as AntDrawer } from 'antd';

/**
 * A customizable Drawer component that wraps the Ant Design Drawer.
 *
 * @param {Object} props - The properties for the Drawer component.
 * @param {boolean} [props.autoFocus=true] - Whether the Drawer should receive focus when opened.
 * @param {function} [props.afterOpenChange] - Callback after the open/close animation ends.
 * @param {string} [props.className] - Custom class name for the Drawer panel.
 * @param {Object} [props.classNames] - Semantic structure class names (Record&lt;SemanticDOM, string&gt;).
 * @param {React.ReactNode} [props.closeIcon] - Custom close icon.
 * @param {boolean} [props.destroyOnClose=false] - Unmount children on close if true.
 * @param {React.ReactNode} [props.extra] - Extra actions in the header.
 * @param {React.ReactNode} [props.footer] - Footer content.
 * @param {boolean} [props.forceRender=false] - Whether to pre-render the Drawer content.
 * @param {HTMLElement|Function|string|boolean} [props.getContainer] - The container node or function that returns it, or false.
 * @param {Object} [props.headerStyle] - Style for the Drawer header.
 * @param {string|number} [props.height] - Height of the Drawer (when placement is top or bottom).
 * @param {boolean} [props.keyboard=true] - Whether to support closing via the Esc key.
 * @param {boolean} [props.mask=true] - Whether to show a mask behind the Drawer.
 * @param {boolean} [props.maskClosable=true] - Whether clicking on the mask closes the Drawer.
 * @param {('top'|'right'|'bottom'|'left')} [props.placement='right'] - The placement of the Drawer.
 * @param {boolean|Object} [props.push={ distance: 180 }] - Push behavior for nested drawers.
 * @param {Object} [props.rootStyle] - Style for the root wrapper element.
 * @param {Object} [props.style] - Style for the Drawer panel.
 * @param {Object} [props.styles] - Semantic structure styles (Record&lt;SemanticDOM, CSSProperties&gt;).
 * @param {('default'|'large')} [props.size='default'] - Preset size of the Drawer.
 * @param {React.ReactNode} [props.title] - The title content.
 * @param {boolean} [props.loading=false] - Whether to show a loading skeleton.
 * @param {boolean} props.open - Whether the Drawer is visible.
 * @param {string|number} [props.width] - Width of the Drawer (when placement is left or right).
 * @param {number} [props.zIndex=1000] - The z-index of the Drawer.
 * @param {function} props.onClose - Callback when the Drawer is requested to be closed.
 * @param {function} [props.drawerRender] - Custom render function for the Drawer content.
 * @param {React.ReactNode} [props.children] - The content inside the Drawer.
 *
 * @returns {JSX.Element} The rendered Drawer component.
 */
const Drawer = ({
  autoFocus = true,
  afterOpenChange,
  className,
  classNames,
  closeIcon,
  destroyOnClose = true,
  extra,
  footer,
  forceRender = true,
  getContainer,
  height,
  keyboard = true,
  mask = true,
  maskClosable = true,
  placement = 'right',
  push = { distance: 180 },
  rootStyle,
  style,
  styles,
  size = 'default',
  title,
  loading = false,
  open,
  width,
  zIndex = 1000,
  onClose,
  drawerRender,
  children,
  ...rest
}) => {
  return (
    <AntDrawer
      autoFocus={autoFocus}
      afterOpenChange={afterOpenChange}
      className={className}
      classNames={classNames}
      closeIcon={closeIcon}
      destroyOnClose={destroyOnClose}
      extra={extra}
      footer={footer}
      forceRender={forceRender}
      getContainer={getContainer}
      height={height}
      keyboard={keyboard}
      mask={mask}
      maskClosable={maskClosable}
      placement={placement}
      push={push}
      rootStyle={rootStyle}
      style={style}
      styles={{ ...styles }}
      size={size}
      title={title}
      loading={loading}
      open={open}
      width={width}
      zIndex={zIndex}
      onClose={onClose}
      drawerRender={drawerRender}
      {...rest}
    >
      {children}
    </AntDrawer>
  );
};

Drawer.propTypes = {
  autoFocus: PropTypes.bool,
  afterOpenChange: PropTypes.func,
  className: PropTypes.string,
  classNames: PropTypes.object,
  closeIcon: PropTypes.node,
  destroyOnClose: PropTypes.bool,
  extra: PropTypes.node,
  footer: PropTypes.node,
  forceRender: PropTypes.bool,
  getContainer: PropTypes.oneOfType([
    PropTypes.func,
    PropTypes.bool,
    PropTypes.object,
    PropTypes.string,
  ]),
  headerStyle: PropTypes.object,
  height: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  keyboard: PropTypes.bool,
  mask: PropTypes.bool,
  maskClosable: PropTypes.bool,
  placement: PropTypes.oneOf(['top', 'right', 'bottom', 'left']),
  push: PropTypes.oneOfType([
    PropTypes.bool,
    PropTypes.shape({
      distance: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    }),
  ]),
  rootStyle: PropTypes.object,
  style: PropTypes.object,
  styles: PropTypes.object,
  size: PropTypes.oneOf(['default', 'large']),
  title: PropTypes.node,
  loading: PropTypes.bool,
  open: PropTypes.bool.isRequired,
  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  zIndex: PropTypes.number,
  onClose: PropTypes.func,
  drawerRender: PropTypes.func,
  children: PropTypes.node,
};

export default Drawer;
