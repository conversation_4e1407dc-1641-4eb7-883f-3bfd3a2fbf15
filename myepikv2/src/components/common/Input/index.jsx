import { Input as AntInput } from 'antd';
import PropTypes from 'prop-types';
import './InputStyles.css';

const { TextArea, Search, Password } = AntInput;

/**
 * A customizable Input component that wraps the Ant Design Input.
 *
 * @param {Object} props - The properties for the Input component.
 * @param {React.ReactNode} [props.addonBefore] - Content to be added before the input box.
 * @param {React.ReactNode} [props.addonAfter] - Content to be added after the input box.
 * @param {boolean|Object} [props.allowClear=false] - If true, shows a clear icon to allow clearing the input. Accepts an object for custom clear icon.
 * @param {'large'|'middle'|'small'} [props.size='middle'] - Size of the input box.
 * @param {React.ReactNode} [props.prefix] - Prefix element for the input box.
 * @param {React.ReactNode} [props.suffix] - Suffix element for the input box.
 * @param {'text'|'textarea'|'search'|'password'} [props.type='text'] - The type of input box.
 * @param {'outlined'|'borderless'|'filled'} [props.variant='outlined'] - Style variant of the input box.
 * @param {string} [props.placeholder] - Placeholder text for the input.
 * @param {string} [props.value] - Controlled value of the input.
 * @param {string} [props.defaultValue] - Default value of the input.
 * @param {function} [props.onChange] - Callback executed when the value changes.
 * @param {function} [props.onPressEnter] - Callback executed when the Enter key is pressed.
 * @param {boolean|Object} [props.autoSize] - Auto-size for the textarea. Accepts an object with minRows and maxRows.
 * @param {boolean} [props.disabled=false] - If true, disables the input box.
 * @param {'error'|'warning'} [props.status] - Validation status of the input box.
 * @param {Object} [rest] - Additional properties for the input box.
 *
 * @returns {JSX.Element} The rendered Input component.
 */
const Input = ({
  addonBefore,
  addonAfter,
  allowClear = false,
  size = 'middle',
  prefix,
  suffix,
  type = 'text',
  variant = 'outlined',
  placeholder,
  value,
  defaultValue,
  onChange,
  onPressEnter,
  autoSize,
  disabled = false,
  status,
  ...rest
}) => {
  const variantClass = `input-${variant}`;

  // Render TextArea if type is textarea
  if (type === 'textarea') {
    return (
      <TextArea
        className={`custom-input ${variantClass}`}
        placeholder={placeholder}
        value={value}
        defaultValue={defaultValue}
        onChange={onChange}
        allowClear={allowClear}
        autoSize={autoSize}
        disabled={disabled}
        {...rest}
      />
    );
  }

  // Render Search if type is search
  if (type === 'search') {
    return (
      <Search
        className={`custom-input ${variantClass}`}
        placeholder={placeholder}
        size={size}
        onSearch={rest.onSearch}
        enterButton={rest.enterButton}
        allowClear={allowClear}
        suffix={suffix}
        addonBefore={addonBefore}
        addonAfter={addonAfter}
        disabled={disabled}
        {...rest}
      />
    );
  }

  // Render Password if type is password
  if (type === 'password') {
    return (
      <Password
        className={`custom-input ${variantClass}`}
        placeholder={placeholder}
        prefix={prefix}
        suffix={suffix}
        size={size}
        allowClear={allowClear}
        disabled={disabled}
        {...rest}
      />
    );
  }

  // Render regular Input
  return (
    <AntInput
      className={`custom-input ${variantClass}`}
      placeholder={placeholder}
      addonBefore={addonBefore}
      addonAfter={addonAfter}
      allowClear={allowClear}
      prefix={prefix}
      suffix={suffix}
      size={size}
      type={type}
      value={value}
      defaultValue={defaultValue}
      onChange={onChange}
      onPressEnter={onPressEnter}
      disabled={disabled}
      status={status}
      {...rest}
    />
  );
};

// Add PropTypes validation
Input.propTypes = {
  /** Content to be added before the input box. */
  addonBefore: PropTypes.node,
  /** Content to be added after the input box. */
  addonAfter: PropTypes.node,
  /** If true, shows a clear icon to allow clearing the input. Accepts an object for custom clear icon. */
  allowClear: PropTypes.oneOfType([
    PropTypes.bool,
    PropTypes.shape({
      clearIcon: PropTypes.node,
    }),
  ]),
  /** Size of the input box. */
  size: PropTypes.oneOf(['large', 'middle', 'small']),
  /** Prefix element for the input box. */
  prefix: PropTypes.node,
  /** Suffix element for the input box. */
  suffix: PropTypes.node,
  /** The type of input box. */
  type: PropTypes.oneOf(['text', 'textarea', 'search', 'password']),
  /** Style variant of the input box. */
  variant: PropTypes.oneOf(['outlined', 'borderless', 'filled']),
  /** Placeholder text for the input. */
  placeholder: PropTypes.string,
  /** Controlled value of the input. */
  value: PropTypes.string,
  /** Default value of the input. */
  defaultValue: PropTypes.string,
  /** Callback executed when the value changes. */
  onChange: PropTypes.func,
  /** Callback executed when the Enter key is pressed. */
  onPressEnter: PropTypes.func,
  /** Auto-size for the textarea. Accepts an object with minRows and maxRows. */
  autoSize: PropTypes.oneOfType([
    PropTypes.bool,
    PropTypes.shape({
      minRows: PropTypes.number,
      maxRows: PropTypes.number,
    }),
  ]),
  /** If true, disables the input box. */
  disabled: PropTypes.bool,
  /** Validation status of the input box. */
  status: PropTypes.oneOf(['error', 'warning']),
};

export default Input;
