import { Progress as AntProgress } from 'antd';
import PropTypes from 'prop-types';

/**
 * A customizable Progress component that wraps the Ant Design Progress.
 *
 * @param {Object} props - The properties for the Progress component.
 * @param {"line" | "circle" | "dashboard"} [props.type="line"] - The type of progress indicator.
 * @param {number} [props.percent=0] - The percentage of progress to display.
 * @param {"success" | "exception" | "normal" | "active"} [props.status] - The status of the progress indicator.
 * @param {string | string[] | Object} [props.strokeColor] - The color of the progress bar. Can be a string, gradient, or array of colors.
 * @param {"round" | "butt" | "square"} [props.strokeLinecap="round"] - The shape of the progress line ends.
 * @param {string} [props.trailColor] - The color of the trail (unfilled) portion of the progress bar.
 * @param {boolean} [props.showInfo=true] - If true, shows the percentage or custom text inside the progress indicator.
 * @param {"small" | "default" | number | Array | Object} [props.size="default"] - The size of the progress indicator. Can be a preset value, number, or object for custom dimensions.
 * @param {number | Object} [props.steps] - For `line` type, divides the progress into multiple steps. Can be a number or an object with `count` and `gap`.
 * @param {function} [props.format] - Custom format function for displaying progress text.
 * @param {number} [props.gapDegree] - The degree of gap in the circle or dashboard progress type.
 * @param {"top" | "bottom" | "left" | "right"} [props.gapPosition] - The position of the gap in the progress circle.
 * @param {Object} [props.percentPosition] - Custom positioning for percentage text, applicable for `circle` and `dashboard` types.
 * @param {string} [props.className] - Additional custom class name for the progress component.
 * @param {Object} [props.props] - Additional properties to pass to the Ant Design Progress component.
 *
 * @returns {JSX.Element} The rendered Progress component.
 */
const Progress = ({
  type = 'line',
  percent = 0,
  status,
  strokeColor,
  strokeLinecap = 'round',
  trailColor,
  showInfo = true,
  size = 'default',
  steps,
  format,
  gapDegree,
  gapPosition,
  percentPosition,
  className,
  ...props
}) => {
  return (
    <AntProgress
      type={type}
      percent={percent}
      status={status}
      strokeColor={strokeColor}
      strokeLinecap={strokeLinecap}
      trailColor={trailColor}
      showInfo={showInfo}
      size={size}
      steps={steps}
      format={format}
      gapDegree={gapDegree}
      gapPosition={gapPosition}
      percentPosition={percentPosition}
      className={`custom-progress ${className || ''}`}
      {...props}
    />
  );
};

// Add PropTypes validation
Progress.propTypes = {
  /** The type of progress indicator. */
  type: PropTypes.oneOf(['line', 'circle', 'dashboard']),
  /** The percentage of progress to display. */
  percent: PropTypes.number,
  /** The status of the progress indicator. */
  status: PropTypes.oneOf(['success', 'exception', 'normal', 'active']),
  /** The color of the progress bar. */
  strokeColor: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.arrayOf(PropTypes.string),
    PropTypes.shape({
      from: PropTypes.string,
      to: PropTypes.string,
      direction: PropTypes.string,
    }),
  ]),
  /** The shape of the progress line ends. */
  strokeLinecap: PropTypes.oneOf(['round', 'butt', 'square']),
  /** The color of the trail (unfilled) portion of the progress bar. */
  trailColor: PropTypes.string,
  /** If true, shows the percentage or custom text inside the progress indicator. */
  showInfo: PropTypes.bool,
  /** The size of the progress indicator. */
  size: PropTypes.oneOfType([
    PropTypes.oneOf(['small', 'default']),
    PropTypes.number,
    PropTypes.arrayOf(
      PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
    ),
    PropTypes.shape({
      width: PropTypes.number,
      height: PropTypes.number,
    }),
  ]),
  /** For `line` type, divides the progress into multiple steps. */
  steps: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.shape({
      count: PropTypes.number,
      gap: PropTypes.number,
    }),
  ]),
  /** Custom format function for displaying progress text. */
  format: PropTypes.func,
  /** The degree of gap in the circle or dashboard progress type. */
  gapDegree: PropTypes.number,
  /** The position of the gap in the progress circle. */
  gapPosition: PropTypes.oneOf(['top', 'bottom', 'left', 'right']),
  /** Custom positioning for percentage text. */
  percentPosition: PropTypes.shape({
    align: PropTypes.string,
    type: PropTypes.string,
  }),
  /** Additional custom class name for the progress component. */
  className: PropTypes.string,
};

export default Progress;
