import PropTypes from 'prop-types';
import {
  DropDown,
  Button,
  FilterIcon,
  DownIcon,
  Flex,
  Typography,
} from '@/components';
import './styles.css';

const CustomDropDown = ({
  filterLabel = 'Select Filter ',
  menuItems,
  handleMenuClick,
  disabled,
}) => {
  return (
    <DropDown
      menuItems={menuItems}
      trigger={['click']}
      onClick={handleMenuClick}
      dropDownStyles={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        border: `1px solid var(--granite-blue)`,
        borderRadius: '8px',
        background: 'var(--granite-blue-light)',
        cursor: 'pointer',
        minWidth: '160px',
      }}
      disabled={disabled}
    >
      <Flex align="center">
        <Button
          icon={<FilterIcon stroke={'var(--granite-blue)'} />}
          variant="outlined"
          variantColorBgContainer
          className="dropdown-btn--left"
        />
        <Typography className="text-inter-600-12-18-granite dropdown-label">
          {filterLabel}
        </Typography>
      </Flex>
      <Button
        icon={<DownIcon stroke={'var(--granite-blue)'} />}
        variant="outlined"
        variantColorBgContainer
        className="dropdown-btn--right"
      />
    </DropDown>
  );
};
CustomDropDown.propTypes = {
  filterLabel: PropTypes.string,
  menuItems: PropTypes.array.isRequired,
  handleMenuClick: PropTypes.func,
  disabled: PropTypes.bool,
};

export default CustomDropDown;
