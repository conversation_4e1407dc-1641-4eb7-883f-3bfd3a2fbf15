import PropTypes from 'prop-types';
import { DatePicker as AntdDatePicker, TimePicker } from 'antd';

const { RangePicker } = AntdDatePicker;

const DatePicker = ({
  type = 'date',
  picker = 'date',
  format,
  showTime,
  allowClear,
  disabled,
  defaultValue,
  value,
  onChange,
  onOk,
  onFocus,
  onBlur,
  onOpenChange,
  onPanelChange,
  disabledDate,
  disabledTime,
  minDate,
  maxDate,
  multiple,
  presets,
  size,
  style,
  className,
  placeholder,
  popupClassName,
  popupStyle,
  ...restProps
}) => {
  // Props common to all
  const commonProps = {
    picker,
    format,
    showTime,
    allowClear,
    disabled,
    defaultValue,
    value,
    onChange,
    onOk,
    onFocus,
    onBlur,
    onOpenChange,
    onPanelChange,
    disabledDate,
    disabledTime,
    minDate,
    maxDate,
    multiple,
    presets,
    size,
    style,
    className,
    placeholder,
    popupClassName,
    popupStyle,
    ...restProps,
  };

  if (type === 'range') {
    return <RangePicker {...commonProps} />;
  } else if (type === 'time') {
    return <TimePicker {...commonProps} />;
  } else {
    return <AntdDatePicker {...commonProps} />;
  }
};

DatePicker.propTypes = {
  /** Type of picker: 'date', 'range', 'time' */
  type: PropTypes.oneOf(['date', 'range', 'time']),
  /** Antd picker prop: 'date', 'week', 'month', 'quarter', 'year' */
  picker: PropTypes.oneOf(['date', 'week', 'month', 'quarter', 'year']),
  format: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.array,
    PropTypes.func,
    PropTypes.object,
  ]),
  showTime: PropTypes.oneOfType([PropTypes.bool, PropTypes.object]),
  allowClear: PropTypes.oneOfType([PropTypes.bool, PropTypes.object]),
  disabled: PropTypes.oneOfType([PropTypes.bool, PropTypes.array]),
  defaultValue: PropTypes.any,
  value: PropTypes.any,
  onChange: PropTypes.func,
  onOk: PropTypes.func,
  onFocus: PropTypes.func,
  onBlur: PropTypes.func,
  onOpenChange: PropTypes.func,
  onPanelChange: PropTypes.func,
  disabledDate: PropTypes.func,
  disabledTime: PropTypes.func,
  minDate: PropTypes.any,
  maxDate: PropTypes.any,
  multiple: PropTypes.bool,
  presets: PropTypes.array,
  size: PropTypes.oneOf(['large', 'middle', 'small']),
  style: PropTypes.object,
  className: PropTypes.string,
  placeholder: PropTypes.oneOfType([PropTypes.string, PropTypes.array]),
  popupClassName: PropTypes.string,
  popupStyle: PropTypes.object,
};

export default DatePicker;
