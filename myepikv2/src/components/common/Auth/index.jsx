import React from 'react';
import PropTypes from 'prop-types';
import { useStore } from '../../../store';

const myEpik = 'https://my.epik.io/';

function Auth({ children }) {
  const user = useStore((state) => state.user);

  React.useEffect(() => {
    user.fetch().catch((error) => {
      console.error('Failed to fetch user:', error);
      window.location.href = myEpik;
    });
  }, [user]); // Add `user` as a dependency

  if (user.status === 'idle' || user.status === 'fetching') {
    return (
      <div style={{ margin: '1rem' }}>
        <h2>Authenticating</h2>
        <p>Please wait while we verify your credentials.</p>
      </div>
    );
  }

  if (user.status === 'failure') {
    window.location.href = myEpik;
    return null; // Return `null` to satisfy React
  }

  if (user.status === 'success' && !user.data.warehouseQcAccess) {
    window.location.href = myEpik;
    return null;
  }

  return children;
}

Auth.propTypes = {
  children: PropTypes.node.isRequired,
};

export default Auth;
