import { Switch, ConfigProvider, theme } from 'antd';
import PropTypes from 'prop-types';
import './styles.css';

const { useToken } = theme;

/**
 * A customizable Toggle component that wraps the Ant Design Switch.
 *
 * @param {Object} props - The properties for the Toggle component.
 * @param {boolean} [props.autoFocus=false] - Whether to automatically focus the toggle when it is mounted.
 * @param {boolean} [props.checked] - Specifies whether the toggle is checked.
 * @param {React.ReactNode} [props.checkedChildren] - Content to show when the toggle is checked.
 * @param {string} [props.className=""] - Additional CSS class names for the toggle.
 * @param {boolean} [props.defaultChecked=false] - Specifies the initial checked state of the toggle.
 * @param {boolean} [props.defaultValue] - An alias for `defaultChecked` (for backward compatibility).
 * @param {boolean} [props.disabled=false] - Whether the toggle is disabled.
 * @param {boolean} [props.loading=false] - Whether the toggle is in a loading state.
 * @param {"default" | "small"} [props.size="default"] - The size of the toggle.
 * @param {React.ReactNode} [props.unCheckedChildren] - Content to show when the toggle is not checked.
 * @param {boolean} [props.value] - Specifies the value of the toggle (alias for `checked`).
 * @param {function(boolean): void} [props.onChange] - Callback function when the toggle's state changes.
 * @param {function(event): void} [props.onClick] - Callback function when the toggle is clicked.
 * @param {"primary" | "secondary"} [props.variant] - Specifies the variant of the toggle for custom theming.
 * @param {Object} [props.rest] - Additional properties passed to the Ant Design Switch component.
 *
 * @returns {JSX.Element} The rendered Toggle component.
 */
const Toggle = ({
  autoFocus = false,
  checked,
  checkedChildren,
  className = '',
  defaultChecked = false,
  defaultValue,
  disabled = false,
  loading = false,
  size = 'default',
  unCheckedChildren,
  value,
  onChange,
  onClick,
  variant,
  ...rest
}) => {
  const {
    token: { colorPrimary, switchSecondaryVariant },
  } = useToken();

  const switchTheme = {
    components: {
      Switch: {
        colorPrimary:
          variant === 'secondary' ? switchSecondaryVariant : colorPrimary,
        colorPrimaryBorder:
          variant === 'secondary' ? switchSecondaryVariant : colorPrimary,
        colorPrimaryHover:
          variant === 'secondary' ? switchSecondaryVariant : colorPrimary,
      },
    },
  };

  return (
    <ConfigProvider theme={switchTheme}>
      <Switch
        autoFocus={autoFocus}
        checked={checked !== undefined ? checked : value}
        checkedChildren={checkedChildren}
        className={`custom-toggle ${className}`}
        defaultChecked={
          defaultChecked !== undefined ? defaultChecked : defaultValue
        }
        disabled={disabled}
        loading={loading}
        size={size}
        unCheckedChildren={unCheckedChildren}
        onChange={onChange}
        onClick={onClick}
        {...rest}
      />
    </ConfigProvider>
  );
};

// Add PropTypes validation
Toggle.propTypes = {
  autoFocus: PropTypes.bool,
  checked: PropTypes.bool,
  checkedChildren: PropTypes.node,
  className: PropTypes.string,
  defaultChecked: PropTypes.bool,
  defaultValue: PropTypes.bool,
  disabled: PropTypes.bool,
  loading: PropTypes.bool,
  size: PropTypes.oneOf(['default', 'small']),
  unCheckedChildren: PropTypes.node,
  value: PropTypes.bool,
  onChange: PropTypes.func,
  onClick: PropTypes.func,
  variant: PropTypes.oneOf(['primary', 'secondary']),
};

export default Toggle;
