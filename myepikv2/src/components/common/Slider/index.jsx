import { Slider } from 'antd';
import PropTypes from 'prop-types';
import './styles.css';

/**
 * A customizable Slider component from Ant Design.
 *
 * @param {Object} props - The properties for the Slider component.
 * @param {number|Array<number>} props.value - The current value of the slider. For a single slider, use a number; for a range slider, use an array of two numbers.
 * @param {boolean} [props.range=false] - Whether the slider is a range slider (with two handles).
 * @param {number} [props.min] - The minimum value of the slider. Defaults to the first value in `value` if not provided.
 * @param {number} [props.max] - The maximum value of the slider. Defaults to the second value in `value` if not provided.
 * @param {string} [props.tooltipPlacement=''] - The placement of the tooltip. Can be `top`, `bottom`, `left`, or `right`.
 * @param {function(number|Array<number>): void} props.onChange - A callback function triggered when the slider value changes. Receives the new value as an argument.
 * @param {Object} [props.rest] - Additional properties passed to the Ant Design Slider component.
 *
 * @returns {JSX.Element} The rendered Ant Design Slider component.
 */
const Index = ({
  value = [],
  range = false,
  min = value?.[0],
  max = value?.[1],
  tooltipPlacement = '',
  onChange,
  ...rest
}) => (
  <Slider
    range={range}
    min={min}
    max={max}
    value={value}
    onChange={onChange}
    tooltip={{ placement: tooltipPlacement }}
    styles={{
      track: {
        background: 'var(--primary-purple)',
      },
      rail: {
        background: 'var(--bg-gray)',
      },
      handle: {},
    }}
    {...rest}
  />
);

// Add PropTypes validation
Index.propTypes = {
  value: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.arrayOf(PropTypes.number),
  ]).isRequired,
  range: PropTypes.bool,
  min: PropTypes.number,
  max: PropTypes.number,
  tooltipPlacement: PropTypes.string,
  onChange: PropTypes.func.isRequired,
};

export default Index;
