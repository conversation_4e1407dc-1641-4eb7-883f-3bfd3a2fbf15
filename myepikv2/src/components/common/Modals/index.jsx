import { Modal as AntModal } from 'antd';
import { CheckCircleTwoTone } from '@ant-design/icons';
import PropTypes from 'prop-types';

/**
 * A customizable Modal component from Ant Design.
 *
 * @param {Object} props - The properties for the Modal component.
 * @param {boolean} props.open - Controls whether the modal is visible.
 * @param {string} [props.title='Confirmation'] - Title of the modal.
 * @param {string} [props.bodyText='Are you sure to continue?'] - Default body text to display in the modal.
 * @param {string} [props.cancelText='No'] - Text for the cancel button.
 * @param {string} [props.okText='Yes'] - Text for the confirm (OK) button.
 * @param {string} [props.okType='primary'] - Type of the confirm (OK) button.
 * @param {string|number} [props.width=500] - Width of the modal dialog.
 * @param {boolean} [props.contentLoading=false] - If true, shows a loading indicator for the content area.
 * @param {boolean} [props.submitLoading=false] - If true, shows a loading indicator for the OK button.
 * @param {boolean} [props.closable=true] - If true, allows the modal to be closed using the close button.
 * @param {boolean} [props.centered=false] - If true, centers the modal vertically in the viewport.
 * @param {React.ReactNode} [props.customFooter] - Custom footer content for the modal.
 * @param {React.ReactNode} [props.extendFooterLeft] - Additional content to render to the left of the footer buttons.
 * @param {React.ReactNode} [props.extendFooterRight] - Additional content to render to the right of the footer buttons.
 * @param {function} props.handleOk - Callback function executed when the OK button is clicked.
 * @param {function} props.handleCancel - Callback function executed when the cancel button is clicked.
 * @param {React.ReactNode} [props.children] - Custom content to display in the body of the modal. Overrides `bodyText` if provided.
 * @param {Object} [rest] - Additional properties to pass to the Ant Design Modal component.
 *
 * @returns {JSX.Element} The rendered Modal component.
 */
const Modal = ({
  open,
  title,
  closeIcon,
  bodyText = 'Are you sure to continue?',
  cancelText = 'No',
  okText = 'Yes',
  okType = 'primary',
  width = 500,
  contentLoading,
  submitLoading,
  closable = true,
  centered = false,
  customFooter,
  extendFooterLeft,
  extendFooterRight,
  handleOk,
  handleCancel,
  children,
  ...rest
}) => {
  return (
    <AntModal
      title={
        title ? (
          title
        ) : (
          <>
            <CheckCircleTwoTone />
            <span style={{ marginLeft: 5 }}>{title}</span>
          </>
        )
      }
      closeIcon={closeIcon}
      open={open}
      onOk={handleOk}
      confirmLoading={submitLoading}
      loading={contentLoading}
      onCancel={handleCancel}
      cancelText={cancelText}
      okText={okText}
      okType={okType}
      centered={centered}
      width={width}
      closable={closable}
      footer={
        customFooter
          ? customFooter
          : (_, { OkBtn, CancelBtn }) => (
              <>
                {extendFooterLeft}
                <CancelBtn />
                <OkBtn color="danger" />
                {extendFooterRight}
              </>
            )
      }
      {...rest}
    >
      {children ? children : bodyText}
    </AntModal>
  );
};

// Add PropTypes validation
Modal.propTypes = {
  /** Controls whether the modal is visible. */
  open: PropTypes.bool.isRequired,
  /** Title of the modal. */
  title: PropTypes.node,
  /** Default body text to display in the modal. */
  bodyText: PropTypes.string,
  /** Text for the cancel button. */
  cancelText: PropTypes.string,
  /** Text for the confirm (OK) button. */
  okText: PropTypes.string,
  /** Type of the confirm (OK) button. */
  okType: PropTypes.string,
  /** Width of the modal dialog. */
  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  /** If true, shows a loading indicator for the content area. */
  contentLoading: PropTypes.bool,
  /** If true, shows a loading indicator for the OK button. */
  submitLoading: PropTypes.bool,
  /** If true, allows the modal to be closed using the close button. */
  closable: PropTypes.bool,
  /** If true, centers the modal vertically in the viewport. */
  centered: PropTypes.bool,
  /** Custom footer content for the modal. */
  customFooter: PropTypes.node,
  /** Additional content to render to the left of the footer buttons. */
  extendFooterLeft: PropTypes.node,
  /** Additional content to render to the right of the footer buttons. */
  extendFooterRight: PropTypes.node,
  /** Callback function executed when the OK button is clicked. */
  handleOk: PropTypes.func,
  /** Callback function executed when the cancel button is clicked. */
  handleCancel: PropTypes.func,
  /** Custom content to display in the body of the modal. Overrides `bodyText` if provided. */
  children: PropTypes.node,
  closeIcon: PropTypes.node,
};

export default Modal;
