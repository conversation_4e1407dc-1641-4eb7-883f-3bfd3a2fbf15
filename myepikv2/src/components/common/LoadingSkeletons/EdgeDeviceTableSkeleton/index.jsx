import PropTypes from 'prop-types';
import { Skeleton } from 'antd';
import { Flex } from '@/components';
import './styles.css';

const HeaderRowButton = () => (
  <Skeleton.Button
    style={{ width: '70%' }}
    active
    size="small"
    shape="square"
    className="custom-skeleton"
  />
);

const TenRowsSkeleton = ({
  style,
  paraRows = 10,
  paraWidth = '100%',
  className,
}) => (
  <Skeleton
    title={false}
    paragraph={{
      rows: paraRows,
      width: paraWidth,
    }}
    style={style}
    className={className}
  />
);

// Add PropTypes for TenRowsSkeleton Component
TenRowsSkeleton.propTypes = {
  style: PropTypes.object, // `style` is an optional object
  paraRows: PropTypes.number, // `paraRows` is an optional number
  paraWidth: PropTypes.oneOfType([PropTypes.string, PropTypes.number]), // `paraWidth` can be a string or number
  className: PropTypes.string, // `className` is an optional string
};

const RowItems = () => (
  <Flex vertical flex={1}>
    <HeaderRowButton />
    <TenRowsSkeleton />
  </Flex>
);

const FirstColumn = () => {
  return (
    <Flex vertical flex={1}>
      <Flex gap="small" flex={1}>
        <Skeleton.Avatar active size="small" shape="square" />
        <HeaderRowButton />
      </Flex>
      <Flex gap="small">
        <TenRowsSkeleton
          paraRows={10}
          paraWidth="20px"
          className="custom-skeleton"
        />
        <TenRowsSkeleton />
      </Flex>
    </Flex>
  );
};

const LastColumn = () => {
  return (
    <Flex vertical style={{ width: '5%' }}>
      <Flex gap={10} flex={1}>
        <TenRowsSkeleton style={{ marginTop: '32px', width: '50%' }} />
        <TenRowsSkeleton style={{ marginTop: '32px', width: '50%' }} />
      </Flex>
    </Flex>
  );
};

const EdgeDeviceTableSkeleton = () => (
  <Flex vertical style={{ padding: '8px 12px' }}>
    <Flex gap={20}>
      <FirstColumn />
      <RowItems />
      <RowItems />
      <RowItems />
      <RowItems />
      <RowItems />
      <RowItems />
      <LastColumn />
    </Flex>
    <Flex gap={10} justify="flex-end" style={{ marginRight: '20px' }}>
      <Skeleton.Button active size="small" shape="square" />
      <Skeleton.Button active size="small" shape="square" />
      <Skeleton.Button active size="small" shape="square" />
      <Skeleton.Button active size="small" shape="square" />
      <Skeleton.Button active size="small" shape="square" />
      <Skeleton.Button active size="small" shape="square" />
    </Flex>
  </Flex>
);

export default EdgeDeviceTableSkeleton;
