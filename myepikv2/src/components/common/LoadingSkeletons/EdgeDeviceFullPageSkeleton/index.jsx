import { Skeleton } from 'antd';
import PropTypes from 'prop-types';
import { Flex } from '@/components';

const EdgeDevicesSkeleton = ({
  firstSectLoading,
  secondSectLoading,
  detailedSectLoading,
}) => (
  <Flex vertical width="100%">
    {firstSectLoading && (
      <Flex justify="space-between" style={{ marginTop: '16px' }}>
        <Skeleton.Button
          style={{ width: '90px' }}
          active
          size="default"
          shape="square"
        />
        <Flex gap="small" align="center">
          <Skeleton.Avatar active size="small" shape="square" />
          <Skeleton.Button
            style={{ width: '90px' }}
            active
            size="default"
            shape="square"
          />
          <Skeleton.Avatar active size="small" shape="square" />
          <Skeleton.Button
            style={{ width: '40px' }}
            active
            size="small"
            shape="square"
          />
        </Flex>
        <Flex gap="small">
          <Skeleton.Button
            style={{ width: '90px' }}
            active
            size="default"
            shape="square"
          />
          <Skeleton.Button
            style={{ width: '90px' }}
            active
            size="default"
            shape="square"
          />
          <Skeleton.Button
            style={{ width: '90px' }}
            active
            size="default"
            shape="square"
          />
        </Flex>
      </Flex>
    )}

    {secondSectLoading && (
      <Flex justify="space-between" style={{ marginTop: '16px' }}>
        <Flex gap="small" align="center">
          <Skeleton.Button
            style={{ width: '150px' }}
            active
            size="default"
            shape="square"
          />
          <Skeleton.Button
            style={{ width: '90px' }}
            active
            size="small"
            shape="square"
          />
        </Flex>
        <Flex gap="small">
          <Skeleton.Avatar active size="default" shape="square" />
        </Flex>
      </Flex>
    )}
    {detailedSectLoading && (
      <Flex justify="space-between" style={{ marginTop: '16px' }}>
        <Flex justify="flex-start" style={{ marginTop: '16px' }}>
          <Flex vertical style={{ marginTop: '16px' }}>
            <Flex gap="small" align="center">
              <Skeleton.Button
                style={{ width: '90px' }}
                active
                size="small"
                shape="square"
              />
              <Skeleton.Avatar active size="small" shape="square" />
            </Flex>
            <Skeleton
              title={false}
              paragraph={{
                rows: 10,
                width: '150px',
              }}
            />
          </Flex>
          <Flex vertical style={{ marginLeft: '20px' }}>
            <Skeleton.Button
              style={{ width: '150px', marginTop: '16px' }}
              active
              size="small"
              shape="square"
            />
            <Skeleton
              title={false}
              style={{ width: '90px' }}
              paragraph={{
                rows: 10,
                width: '90px',
              }}
            />
          </Flex>
        </Flex>

        <Flex justify="space-between" style={{ marginTop: '16px' }}>
          <Flex vertical style={{ marginTop: '16px' }}>
            <Skeleton.Button
              style={{ width: '90px' }}
              active
              size="small"
              shape="square"
            />
            <Skeleton
              title={false}
              style={{ width: '90px' }}
              paragraph={{
                rows: 10,
                width: '150px',
              }}
            />
          </Flex>
          <Flex vertical style={{ marginLeft: '20px' }}>
            <Skeleton.Button
              style={{ width: '60px', marginTop: '16px' }}
              active
              size="small"
              shape="square"
            />
            <Skeleton
              title={false}
              style={{ width: '90px' }}
              paragraph={{
                rows: 10,
                width: '150px',
              }}
            />
          </Flex>
        </Flex>

        {/* Summary Row */}
        <Flex vertical style={{ marginTop: '32px' }}>
          <Flex gap="small" align="center">
            <Skeleton.Button
              style={{ width: '90px' }}
              active
              size="small"
              shape="square"
            />
            <Skeleton.Avatar active size="small" shape="square" />
            <Skeleton.Avatar active size="small" shape="square" />
            <Skeleton.Button
              style={{ width: '20px' }}
              active
              size="small"
              shape="square"
            />
          </Flex>

          <Flex justify="space-between" gap={1} style={{ marginTop: '16px' }}>
            <Skeleton
              title={false}
              style={{ width: '90px' }}
              paragraph={{
                rows: 10,
                width: '90px',
              }}
            />
            <Skeleton
              title={false}
              paragraph={{
                rows: 10,
                width: '10px',
              }}
            />
            <Skeleton
              title={false}
              paragraph={{
                rows: 10,
                width: '10px',
              }}
            />
            <Skeleton
              title={false}
              paragraph={{
                rows: 10,
                width: '10px',
              }}
            />
          </Flex>
        </Flex>
      </Flex>
    )}
  </Flex>
);

EdgeDevicesSkeleton.propTypes = {
  firstSectLoading: PropTypes.bool.isRequired,
  secondSectLoading: PropTypes.bool.isRequired,
  detailedSectLoading: PropTypes.bool.isRequired,
};

export default EdgeDevicesSkeleton;
