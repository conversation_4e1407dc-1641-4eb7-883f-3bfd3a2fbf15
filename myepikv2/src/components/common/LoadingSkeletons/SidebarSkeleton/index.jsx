import { Skeleton } from 'antd';
import { Flex } from '@/components';
import './styles.css';

const MenuButton = () => (
  <Skeleton.Button
    style={{ width: '100%', borderRadius: '8px' }}
    active
    size="large"
    shape="default"
  />
);

const Menu = () => (
  <Flex
    vertical
    gap={8}
    flex={1}
    style={{ padding: '0px 8px', marginTop: '16px' }}
  >
    <MenuButton />
  </Flex>
);

const SidebarSkeleton = () => (
  <Flex vertical style={{ padding: '8px 12px' }}>
    {[...Array(4)].map((_, idx) => (
      <Menu key={idx} />
    ))}
  </Flex>
);

export default SidebarSkeleton;
