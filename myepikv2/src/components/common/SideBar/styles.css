.sidebar-menu {
  height: fit-content;
  margin-top: 16px;
  padding: 0 6px;

  &.collapsed {
    margin-top: 20px;
  }
}

.sidebar-menu .force-parent-selected .ant-menu-submenu-title {
  color: white !important;
  background-color: var(--button-light-blue) !important;
  display: block;
}

.force-submenu-selected {
  color: white !important;
  background-color: var(--granite-blue-light-active) !important;
  display: block;
}

.sidebar-menu .ant-menu-item,
.sidebar-menu .ant-menu-submenu-title {
  height: 36px;
  padding-left: 8px !important;
  font-weight: 600;
  font-size: 12px;
}

.sidebar-menu .ant-menu-item > svg,
.sidebar-menu .ant-menu-submenu-title > svg {
  height: 18px;
  width: 18px;
}

.sidebar-menu .ant-menu-sub .ant-menu-title-content {
  padding-left: 24px !important;
}

@media (min-width: 1920px) {
  .sidebar-menu .ant-menu-item,
  .sidebar-menu .ant-menu-submenu-title {
    height: 48px !important;
    font-size: 16px;
  }

  .sidebar-menu .ant-menu-item > svg,
  .sidebar-menu .ant-menu-submenu-title > svg {
    height: 24px;
    width: 24px;
  }

  .sidebar-menu .ant-menu-sub .ant-menu-title-content {
    padding-left: 24px !important;
  }

  .sidebar-menu .force-parent-selected .ant-menu-submenu-title > svg {
    margin-top: 8px;
    padding: 0;
  }
}
