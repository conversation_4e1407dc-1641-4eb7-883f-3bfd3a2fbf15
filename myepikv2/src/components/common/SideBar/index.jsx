import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Menu, theme, ConfigProvider, Flex } from 'antd';
import {
  HomeIcon,
  AdminToolIcon,
  PhoneIcon,
  UsersIcon,
  CompaniesIcon,
  HardDriveIcon,
  VoiceMailIcon,
  AntennaIcon,
  SidebarSkeleton,
  CarrierHubIcon,
  UsageIcon,
  Typography,
} from '@/components';
import PropTypes from 'prop-types';
import './styles.css';
import { useStore } from '@/store';

const { useToken } = theme;

const SideBar = ({ collapsed, menuKeys, loading }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const {
    token: { sidebarMenuIconColor, colorPrimary, defaultColorBgContainer },
  } = useToken();

  const [selectedKeys, setSelectedKeys] = useState([]);
  const [openKeys, setOpenKeys] = useState([]);

  const userPermissionsError = useStore(
    (s) => s.error.errorState['user-permissions'] || null,
  );

  useEffect(() => {
    const path = location.pathname;
    const keys = [];

    if (path.startsWith('/usage')) {
      keys.push('/usage');
      if (path.startsWith('/usage/calls')) {
        keys.push('/usage/calls');
      } else if (path.startsWith('/usage/legacy-fax')) {
        keys.push('/usage/legacy-fax');
      } else if (path.startsWith('/usage/fax')) {
        keys.push('/usage/fax');
      } else if (path.startsWith('/usage/numbers')) {
        keys.push('/usage/numbers');
      }
    } else if (path.startsWith('/carrier-hub')) {
      keys.push('/carrier-hub');
      if (path.startsWith('/carrier-hub/number-ordering')) {
        keys.push('/carrier-hub/number-ordering');
      } else if (path.startsWith('/carrier-hub/porting')) {
        keys.push('/carrier-hub/porting');
      } else if (path.startsWith('/carrier-hub/e911')) {
        keys.push('/carrier-hub/e911');
      } else if (path.startsWith('/carrier-hub/carrier-lookup')) {
        keys.push('/carrier-hub/carrier-lookup');
      }
    } else if (path.startsWith('/edge-devices')) {
      keys.push('/edge-devices');
    } else {
      keys.push(path);
    }

    setSelectedKeys(keys);
    setOpenKeys(
      keys.filter((k) => k.includes('/usage') || k.includes('/carrier-hub')),
    );
  }, [location.pathname]);

  const MenuTheme = {
    components: {
      Menu: {
        itemSelectedBg: colorPrimary,
        itemSelectedColor: defaultColorBgContainer,
      },
    },
  };

  const handleMenuClick = ({ key }) => {
    setSelectedKeys([key]);
    navigate(key);
  };

  const allMenuItems = [
    {
      key: '/dashboard',
      icon: (
        <HomeIcon
          stroke={
            selectedKeys.includes('/dashboard') ? 'white' : sidebarMenuIconColor
          }
        />
      ),
      label: 'Dashboard',
      style: { marginBottom: '8px' },
    },
    {
      key: '/adminTool',
      label: 'Admin Tool',
      icon: (
        <AdminToolIcon
          stroke={
            selectedKeys.includes('/adminTool') ? 'white' : sidebarMenuIconColor
          }
        />
      ),
      children: [],
      style: { marginBottom: '8px' },
    },
    {
      key: '/activitiesCenter',
      icon: (
        <PhoneIcon
          stroke={
            selectedKeys.includes('/activitiesCenter')
              ? 'white'
              : sidebarMenuIconColor
          }
        />
      ),
      label: 'Activities Center',
      children: [],
      style: { marginBottom: '8px' },
    },
    {
      key: '/edge-devices',
      icon: (
        <HardDriveIcon
          stroke={
            selectedKeys.includes('/edge-devices')
              ? 'white'
              : sidebarMenuIconColor
          }
        />
      ),
      label: 'Edge Devices',
      style: { marginBottom: '8px' },
    },
    {
      key: '/companies',
      icon: (
        <CompaniesIcon
          stroke={
            selectedKeys.includes('/companies') ? 'white' : sidebarMenuIconColor
          }
        />
      ),
      label: 'Companies',
      style: { marginBottom: '8px' },
    },
    {
      key: '/users',
      icon: (
        <UsersIcon
          stroke={
            selectedKeys.includes('/users') ? 'white' : sidebarMenuIconColor
          }
        />
      ),
      label: 'Users',
      style: { marginBottom: '8px' },
    },
    {
      key: '/usage',
      icon: (
        <UsageIcon
          stroke={
            location.pathname.startsWith('/usage')
              ? 'white'
              : sidebarMenuIconColor
          }
        />
      ),
      label: 'Usage',
      children: [
        {
          key: '/usage/calls',
          label: 'Calls',
          className: location.pathname.startsWith('/usage/calls')
            ? 'force-submenu-selected'
            : '',
        },
        {
          key: '/usage/legacy-fax',
          label: 'Legacy FAX',
          className: location.pathname.startsWith('/usage/legacy-fax')
            ? 'force-submenu-selected'
            : '',
        },
        {
          key: '/usage/fax',
          label: 'FAX',
          className: location.pathname.startsWith('/usage/fax')
            ? 'force-submenu-selected'
            : '',
        },
        {
          key: '/usage/numbers',
          label: 'Numbers',
          className: location.pathname.startsWith('/usage/numbers')
            ? 'force-submenu-selected'
            : '',
        },
      ],
      style: { marginBottom: '8px' },
      className: location.pathname.startsWith('/usage')
        ? 'force-parent-selected'
        : '',
    },
    {
      key: '/carrier-hub',
      icon: (
        <CarrierHubIcon
          stroke={
            location.pathname.startsWith('/carrier-hub')
              ? 'white'
              : sidebarMenuIconColor
          }
        />
      ),
      label: 'Carrier Hub',
      children: [
        {
          key: '/carrier-hub/number-ordering',
          label: 'Number Ordering',
          className: location.pathname.startsWith(
            '/carrier-hub/number-ordering',
          )
            ? 'force-submenu-selected'
            : '',
        },
        {
          key: '/carrier-hub/porting',
          label: 'Porting',
          className: location.pathname.startsWith('/carrier-hub/porting')
            ? 'force-submenu-selected'
            : '',
        },
        {
          key: '/carrier-hub/e911',
          label: 'E911',
          className: location.pathname.startsWith('/carrier-hub/e911')
            ? 'force-submenu-selected'
            : '',
        },
        {
          key: '/carrier-hub/carrier-lookup',
          label: 'Carrier Lookup',
          className: location.pathname.startsWith('/carrier-hub/carrier-lookup')
            ? 'force-submenu-selected'
            : '',
        },
      ],
      style: { marginBottom: '8px' },
      className: location.pathname.startsWith('/carrier-hub')
        ? 'force-parent-selected'
        : '',
    },
    {
      key: '/voicemail',
      icon: (
        <VoiceMailIcon
          stroke={
            selectedKeys.includes('/voicemail') ? 'white' : sidebarMenuIconColor
          }
        />
      ),
      label: 'Voicemail',
      children: [],
      style: { marginBottom: '8px' },
    },
    {
      key: '/carrierHub',
      icon: (
        <AntennaIcon
          stroke={
            selectedKeys.includes('/carrierHub')
              ? 'white'
              : sidebarMenuIconColor
          }
        />
      ),
      label: 'Carrier hub',
      children: [],
      style: { marginBottom: '8px' },
    },
  ];

  const filteredMenuItems = allMenuItems
    .map((item) => {
      if (!item.children || item.children.length === 0) {
        return menuKeys.includes(item.key) ? item : null;
      }

      const filteredChildren = item.children.filter((child) =>
        menuKeys.includes(child.key),
      );

      if (menuKeys.includes(item.key) || filteredChildren.length > 0) {
        return {
          ...item,
          children: filteredChildren,
        };
      }

      return null;
    })
    .filter(Boolean);

  if (loading) {
    return <SidebarSkeleton loading={loading} />;
  }

  return (
    <ConfigProvider theme={MenuTheme}>
      <Menu
        mode="inline"
        selectedKeys={selectedKeys}
        openKeys={openKeys}
        onOpenChange={setOpenKeys}
        onClick={handleMenuClick}
        className={`sidebar-menu ${collapsed ? 'collapsed' : ''}`}
        items={filteredMenuItems}
      />
      {userPermissionsError && (
        <Flex align="center" justify="center" style={{ height: '90%' }}>
          <Typography
            className="small-text"
            style={{ color: 'var(--error-600)' }}
          >
            {userPermissionsError}
          </Typography>
        </Flex>
      )}
    </ConfigProvider>
  );
};

// Add PropTypes validation
SideBar.propTypes = {
  collapsed: PropTypes.bool.isRequired,
  menuKeys: PropTypes.array.isRequired,
  loading: PropTypes.bool.isRequired,
};

export default SideBar;
