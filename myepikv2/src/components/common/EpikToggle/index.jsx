import { useState, useEffect } from 'react';
import { Flex, Typography, Toggle } from '@/components';

const EpikToggle = () => {
  const [showLegacyMyepik, setShowLegacyMyepik] = useState(true);

  useEffect(() => {
    setShowLegacyMyepik(true);
  }, []);

  return (
    <Flex
      gap={8}
      justify="center"
      align="center"
      style={{ marginBottom: '16px' }}
    >
      <Typography className="small-text" style={{ color: '#ff4d4f' }}>
        Switch Back to MyEpik 1.0
      </Typography>
      <Toggle
        size="small"
        checked={showLegacyMyepik}
        onChange={() => {
          if (showLegacyMyepik) {
            setShowLegacyMyepik(!showLegacyMyepik);
            setTimeout(() => {
              window.location.href = 'https://my.epik.io/';
            }, 300);
          }
        }}
      />
    </Flex>
  );
};

export default EpikToggle;
