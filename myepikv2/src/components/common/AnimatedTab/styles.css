.animated-tabs {
  position: relative;
  background: var(--blue-2);
  border-radius: 8px;
  /* this hardcoded 4 should match setIndicatorStyle({
        left: activeRect.left - containerRect.left - 4,
        width: activeRect.width,
  }); */
  padding: 4px;
  display: inline-flex;
  user-select: none;
}

.animated-tabs-container {
  display: flex;
  position: relative;
  z-index: 1;
}

.animated-tab {
  padding: 6px 16px;
  cursor: pointer;
  border-radius: 4px;
  color: var(--gray-600);
  transition: color 0.3s;
  white-space: nowrap;
  font-size: 14px;
}

/* .animated-tab:hover:not(.active) {
  background-color: var(--epik-color-primary-hover);
  color: var(--bg-white-connection);
} */

.animated-tab.active {
  color: var(--bg-white-connection);
}

.animated-indicator {
  position: absolute;
  height: calc(100% - 8px);
  background: var(--granite-blue);
  border-radius: 4px;
  top: 4px;
  left: 4px;
  z-index: 0;
}

/* Size variants */
.animated-tabs-small .animated-tab {
  padding: 2px 12px;
  font-size: 12px;
}

.animated-tabs-large .animated-tab {
  padding: 8px 20px;
  font-size: 16px;
}

.animated-tabs-middle {
  height: 40px;
}

.animated-tabs-middle .animated-tab {
  font-weight: 400;
  font-size: 13.33px;
  line-height: 20px;
  padding: 5px 10px;
}

@media (min-width: 1920px) {
  .animated-tabs-middle {
    height: 48px;
  }

  .animated-tabs-middle .animated-tab {
    font-size: 13.33px;
    line-height: 20px;
    padding: 6px 12px;
  }
}
