import PropTypes from 'prop-types';
import TweenOne from 'rc-tween-one';
import { useEffect, useRef, useState } from 'react';
import './styles.css';

const AnimatedTab = ({ options, value, onChange, size = 'default' }) => {
  const [activeTab, setActiveTab] = useState(value);
  const containerRef = useRef(null);
  const [indicatorStyle, setIndicatorStyle] = useState({});

  useEffect(() => {
    setActiveTab(value);
  }, [value]);

  useEffect(() => {
    updateIndicatorPosition();
  }, [activeTab]);

  const updateIndicatorPosition = () => {
    if (!containerRef.current) return;

    const activeElement = containerRef.current.querySelector(
      `[data-value="${activeTab}"]`,
    );
    if (activeElement) {
      const containerRect = containerRef.current.getBoundingClientRect();
      const activeRect = activeElement.getBoundingClientRect();

      setIndicatorStyle({
        left: activeRect.left - containerRect.left - 4, // this hardcoded 4 should match .animated-tabs padding
        width: activeRect.width,
      });
    }
  };

  const handleTabClick = (tabValue) => {
    setActiveTab(tabValue);
    if (onChange) {
      onChange(tabValue);
    }
  };

  return (
    <div className={`animated-tabs animated-tabs-${size}`} ref={containerRef}>
      <div className="animated-tabs-container">
        {options?.map((option) => {
          const tabValue = typeof option === 'string' ? option : option.value;
          const tabLabel = typeof option === 'string' ? option : option.label;

          return (
            <div
              key={tabValue}
              data-value={tabValue}
              className={`animated-tab  ${activeTab === tabValue ? 'active' : ''}`}
              onClick={() => handleTabClick(tabValue)}
            >
              {tabLabel}
            </div>
          );
        })}
      </div>
      <TweenOne
        className="animated-indicator"
        animation={{
          x: indicatorStyle.left ?? 0,
          width: indicatorStyle.width ?? 0,
          duration: 300,
          ease: 'easeOutQuint',
        }}
      />
    </div>
  );
};

AnimatedTab.propTypes = {
  options: PropTypes.arrayOf(
    PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.shape({
        label: PropTypes.node,
        value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
      }),
    ]),
  ),
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  onChange: PropTypes.func,
  size: PropTypes.oneOf(['small', 'default', 'large']),
};

export default AnimatedTab;
