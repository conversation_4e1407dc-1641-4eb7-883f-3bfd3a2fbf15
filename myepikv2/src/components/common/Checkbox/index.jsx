import { memo } from 'react';
import PropTypes from 'prop-types';
import { Checkbox as AntCheckbox } from 'antd';

const CheckboxGroup = AntCheckbox.Group;

/**
 * A customizable Checkbox component that wraps the Ant Design Checkbox.
 *
 * @param {Object} props - The properties for the Checkbox component.
 * @param {boolean} [props.autoFocus=false] - Automatically focus on the Checkbox when it is mounted.
 * @param {boolean} [props.checked] - Specifies whether the Checkbox is checked.
 * @param {boolean} [props.defaultChecked=false] - Specifies the initial checked state of the Checkbox.
 * @param {boolean} [props.disabled=false] - Disables the Checkbox if set to true.
 * @param {boolean} [props.indeterminate=false] - Specifies whether the Checkbox is in an indeterminate state.
 * @param {function} [props.onChange] - A callback function triggered when the checked state changes.
 * @param {function} [props.onBlur] - A callback function triggered when the Checkbox loses focus.
 * @param {function} [props.onFocus] - A callback function triggered when the Checkbox gains focus.
 * @param {React.ReactNode} [props.children] - Content or label associated with the Checkbox.
 *
 * @returns {JSX.Element} The rendered Ant Design Checkbox component.
 */
const CheckboxComponent = ({
  autoFocus = false,
  checked,
  defaultChecked = false,
  disabled = false,
  indeterminate = false,
  onChange,
  onBlur,
  onFocus,
  children,
  ...rest
}) => {
  return (
    <AntCheckbox
      autoFocus={autoFocus}
      checked={checked}
      defaultChecked={defaultChecked}
      disabled={disabled}
      indeterminate={indeterminate}
      onChange={onChange}
      onBlur={onBlur}
      onFocus={onFocus}
      {...rest}
    >
      {children}
    </AntCheckbox>
  );
};

CheckboxComponent.propTypes = {
  autoFocus: PropTypes.bool,
  checked: PropTypes.bool,
  defaultChecked: PropTypes.bool,
  disabled: PropTypes.bool,
  indeterminate: PropTypes.bool,
  onChange: PropTypes.func,
  onBlur: PropTypes.func,
  onFocus: PropTypes.func,
  children: PropTypes.node,
};

const Checkbox = memo(CheckboxComponent);

/**
 * A customizable Checkbox Group component that wraps the Ant Design Checkbox.Group.
 *
 * @param {Object} props - The properties for the Checkbox Group component.
 * @param {Array<string|number|Object>} [props.options=[]] - The options for the Checkbox Group, which can be strings, numbers, or objects with `label`, `value`, and `disabled`.
 * @param {boolean} [props.disabled=false] - Disables all Checkboxes in the group if set to true.
 * @param {Array<string|number>} [props.defaultValue=[]] - Specifies the default selected values for the Checkbox Group.
 * @param {Array<string|number>} [props.value] - The current selected values for the Checkbox Group.
 * @param {function} [props.onChange] - A callback function triggered when the selected values change.
 * @param {React.ReactNode} [props.children] - Additional content or custom Checkboxes to be rendered inside the group.
 *
 * @returns {JSX.Element} The rendered Ant Design Checkbox.Group component.
 */
function CheckboxGroupWrapper({
  options = [],
  disabled = false,
  defaultValue = [],
  value,
  onChange,
  children,
  ...rest
}) {
  return (
    <CheckboxGroup
      options={options}
      disabled={disabled}
      defaultValue={defaultValue}
      value={value}
      onChange={onChange}
      {...rest}
    >
      {children}
    </CheckboxGroup>
  );
}

CheckboxGroupWrapper.propTypes = {
  options: PropTypes.arrayOf(
    PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.number,
      PropTypes.shape({
        label: PropTypes.node,
        value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        disabled: PropTypes.bool,
      }),
    ]),
  ),
  disabled: PropTypes.bool,
  defaultValue: PropTypes.arrayOf(
    PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  ),
  value: PropTypes.arrayOf(
    PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  ),
  onChange: PropTypes.func,
  children: PropTypes.node,
};

Checkbox.Group = CheckboxGroupWrapper;

export default Checkbox;
