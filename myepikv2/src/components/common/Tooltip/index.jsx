import { Tooltip as AntTooltip } from 'antd';
import PropTypes from 'prop-types';

/**
 * A customizable Tooltip component that wraps the Ant Design Tooltip.
 *
 * @param {Object} props - The properties for the Tooltip component.
 * @param {React.ReactNode | function} props.title - The content to display inside the tooltip. Can be a node or a function returning a node.
 * @param {React.ReactNode} props.children - The element that triggers the tooltip when hovered or focused.
 * @param {string} [props.placement="top"] - The position of the tooltip relative to the target element.
 * @param {string} [props.color] - The background color of the tooltip.
 * @param {boolean | Object} [props.arrow=true] - Whether to display an arrow on the tooltip. Can be an object with `pointAtCenter` to align the arrow with the center.
 * @param {boolean} [props.disabled=false] - If true, disables the tooltip from being displayed.
 * @param {Object} [props.rest] - Additional properties passed to the Ant Design Tooltip component.
 *
 * @returns {JSX.Element} The rendered Tooltip component.
 */
const Tooltip = ({
  title,
  children,
  placement = 'top',
  color,
  arrow = true,
  disabled = false,
  ...rest
}) => {
  return (
    <AntTooltip
      title={!disabled ? title : ''}
      placement={placement}
      color={color}
      arrow={arrow}
      {...rest}
    >
      <span>{children}</span>
    </AntTooltip>
  );
};

// Add PropTypes validation
Tooltip.propTypes = {
  /** The content to display inside the tooltip. */
  title: PropTypes.oneOfType([PropTypes.node, PropTypes.func]),
  /** The element that triggers the tooltip. */
  children: PropTypes.node,
  /** The position of the tooltip relative to the target element. */
  placement: PropTypes.oneOf([
    'top',
    'left',
    'right',
    'bottom',
    'topLeft',
    'topRight',
    'bottomLeft',
    'bottomRight',
    'leftTop',
    'leftBottom',
    'rightTop',
    'rightBottom',
  ]),
  /** The background color of the tooltip. */
  color: PropTypes.string,
  /** Whether to display an arrow on the tooltip. */
  arrow: PropTypes.oneOfType([
    PropTypes.bool,
    PropTypes.shape({
      pointAtCenter: PropTypes.bool,
    }),
  ]),
  /** If true, disables the tooltip from being displayed. */
  disabled: PropTypes.bool,
};

export default Tooltip;
