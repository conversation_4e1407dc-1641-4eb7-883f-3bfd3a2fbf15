import { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { Typography, Tooltip } from '@/components';
import './styles.css';

/**
 * A component that displays text with an ellipsis if it overflows its container.
 *
 * @param {Object} props - The component properties.
 * @param {string} props.text - The text content to display.
 * @param {number} [props.minWidth=200] - The minimum width of the text container.
 *
 * @returns {JSX.Element} The rendered EllipsisText component.
 */
const EllipsisText = ({ text, minWidth = 200, className = '' }) => {
  const containerRef = useRef(null);
  const [isEllipsis, setIsEllipsis] = useState(false);

  useEffect(() => {
    if (containerRef.current) {
      const { scrollWidth, clientWidth } = containerRef.current;
      setIsEllipsis(scrollWidth > clientWidth);
    }
  }, [text]);

  return (
    <Tooltip
      title={isEllipsis ? text : null}
      className="ellipsis-text__tooltip"
    >
      <div
        ref={containerRef}
        className="ellipsis-text__container"
        style={{
          minWidth: `${minWidth}px`,
          paddingRight: '20px',
        }}
      >
        <Typography className={`${className} ellipsis-text__content`}>
          {text || '-'}
        </Typography>
      </div>
    </Tooltip>
  );
};

// Define PropTypes
EllipsisText.propTypes = {
  /** The text content to display. */
  text: PropTypes.string,
  /** The minimum width of the text container. Defaults to 200px. */
  minWidth: PropTypes.number,
  className: PropTypes.string,
};

export default EllipsisText;
