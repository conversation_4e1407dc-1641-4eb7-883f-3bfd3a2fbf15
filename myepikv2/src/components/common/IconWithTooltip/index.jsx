import PropTypes from 'prop-types';
import { InfoIcon, Tooltip } from '@/components';

/**
 * A reusable IconWithTooltip component.
 *
 * @param {Object} props - The component properties.
 * @param {string} [props.title='Info tooltip'] - The text to display in the tooltip.
 * @param {string} [props.placement='top'] - Tooltip placement (e.g., 'top', 'left', 'right', 'bottom').
 * @param {React.ReactNode} [props.icon=<InfoCircleOutlined />] - The icon to display.
 * @param {object} [props.style] - Additional CSS styles for the icon container.
 *
 * @returns {JSX.Element} A tooltip-wrapped icon.
 */
const IconWithTooltip = ({
  title = 'Info tooltip',
  placement = 'top',
  icon = <InfoIcon />,
  style = {},
}) => {
  return (
    <Tooltip title={title} placement={placement}>
      <span style={{ marginLeft: 4, ...style }}>{icon}</span>
    </Tooltip>
  );
};

IconWithTooltip.propTypes = {
  title: PropTypes.string,
  placement: PropTypes.oneOf([
    'top',
    'left',
    'right',
    'bottom',
    'topLeft',
    'topRight',
    'bottomLeft',
    'bottomRight',
    'leftTop',
    'leftBottom',
    'rightTop',
    'rightBottom',
  ]),
  icon: PropTypes.node,
  style: PropTypes.object,
};

export default IconWithTooltip;
