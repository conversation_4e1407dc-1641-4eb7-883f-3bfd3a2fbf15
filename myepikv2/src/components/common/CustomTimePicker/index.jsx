import PropTypes from 'prop-types';
import { TimePicker } from 'antd';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';

dayjs.extend(customParseFormat);
const { RangePicker } = TimePicker;

/**
 * Reusable Ant Design TimePicker or TimeRangePicker component.
 *
 * @component
 * @example
 * <CustomTimePicker
 *   value={dayjs('13:30:56', 'HH:mm:ss')}
 *   onChange={(time, timeStr) => console.log(timeStr)}
 * />
 */
const CustomTimePicker = ({
  isRange,
  value,
  defaultValue,
  onChange,
  format,
  size,
  disabled,
  allowClear,
  needConfirm,
  open,
  onOpenChange,
  renderExtraFooter,
  minuteStep,
  secondStep,
  hourStep,
  changeOnScroll,
  showNow,
  placeholder,
  status,
  variant,
  use12Hours,
  disabledTime,
  popupClassName,
  popupStyle,
  suffixIcon,
  prefix,
  className,
  style,
  ...restProps
}) => {
  const Picker = isRange ? RangePicker : TimePicker;

  return (
    <Picker
      value={value}
      defaultValue={defaultValue}
      onChange={onChange}
      format={format}
      size={size}
      disabled={disabled}
      allowClear={allowClear}
      needConfirm={needConfirm}
      open={open}
      onOpenChange={onOpenChange}
      renderExtraFooter={renderExtraFooter}
      minuteStep={minuteStep}
      secondStep={secondStep}
      hourStep={hourStep}
      changeOnScroll={changeOnScroll}
      showNow={showNow}
      placeholder={placeholder}
      status={status}
      variant={variant}
      use12Hours={use12Hours}
      disabledTime={disabledTime}
      popupClassName={popupClassName}
      popupStyle={popupStyle}
      suffixIcon={suffixIcon}
      prefix={prefix}
      className={className}
      style={style}
      {...restProps}
    />
  );
};

CustomTimePicker.propTypes = {
  isRange: PropTypes.bool,
  value: PropTypes.any,
  defaultValue: PropTypes.any,
  onChange: PropTypes.func,
  format: PropTypes.string,
  size: PropTypes.oneOf(['small', 'middle', 'large']),
  disabled: PropTypes.bool,
  allowClear: PropTypes.oneOfType([PropTypes.bool, PropTypes.object]),
  needConfirm: PropTypes.bool,
  open: PropTypes.bool,
  onOpenChange: PropTypes.func,
  renderExtraFooter: PropTypes.func,
  minuteStep: PropTypes.number,
  secondStep: PropTypes.number,
  hourStep: PropTypes.number,
  changeOnScroll: PropTypes.bool,
  showNow: PropTypes.bool,
  placeholder: PropTypes.oneOfType([PropTypes.string, PropTypes.array]),
  status: PropTypes.oneOf(['error', 'warning', 'success', 'validating']),
  variant: PropTypes.oneOf(['outlined', 'borderless', 'filled', 'underlined']),
  use12Hours: PropTypes.bool,
  disabledTime: PropTypes.func,
  popupClassName: PropTypes.string,
  popupStyle: PropTypes.object,
  suffixIcon: PropTypes.node,
  prefix: PropTypes.node,
  className: PropTypes.string,
  style: PropTypes.object,
};

CustomTimePicker.defaultProps = {
  isRange: false,
  format: 'HH:mm:ss',
  allowClear: true,
  disabled: false,
  size: 'middle',
};

export default CustomTimePicker;
