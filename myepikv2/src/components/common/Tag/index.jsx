import React from 'react';
import { Tag as AntTag, ConfigProvider } from 'antd';
import PropTypes from 'prop-types';
import { CloseIcon, PlusIcon } from '../SvgIcons';
import Input from '../Input';
import './TagStyles.css';

const { CheckableTag } = AntTag;

/**
 * A customizable Tag component that wraps the Ant Design Tag.
 *
 * @param {Object} props - The properties for the Tag component.
 * @param {string} props.text - The text content of the tag.
 * @param {string} [props.color] - The background color of the tag.
 * @param {React.ReactNode} [props.icon] - An icon to display inside the tag.
 * @param {"default" | "small" | "large"} [props.size="default"] - The size of the tag.
 * @param {boolean} [props.bordered=true] - Whether the tag has a border.
 * @param {boolean} [props.closable=false] - Whether the tag can be closed.
 * @param {React.ReactNode} [props.closeIcon] - Custom close icon for the tag.
 * @param {boolean} [props.checkable=false] - Whether the tag is checkable.
 * @param {boolean} [props.checked=false] - Whether the tag is checked (for checkable tags).
 * @param {function(boolean): void} [props.onCheckChange] - Callback when the checked state of a checkable tag changes.
 * @param {function(): void} [props.onClose] - Callback when the tag is closed.
 * @param {function(): void} [props.onAdd] - Callback for adding a new tag (only for addable tags).
 * @param {boolean} [props.addable=false] - Whether the tag is addable (displays a "+" icon and triggers `onAdd`).
 * @param {boolean} [props.editable=false] - Whether the tag is editable.
 * @param {string} [props.classNames=""] - Additional custom class names for the tag.
 * @param {function(string): void} [props.onEditConfirm] - Callback when editing of a tag is confirmed.
 * @param {Object} [props.rest] - Additional properties passed to the Ant Design Tag component.
 *
 * @returns {JSX.Element} The rendered Tag component.
 */
const Tag = ({
  text,
  color,
  icon,
  size = 'default',
  bordered = true,
  closable = false,
  closeIcon,
  checkable = false,
  checked = false,
  onCheckChange,
  onClose,
  onAdd,
  addable = false,
  editable = false,
  classNames = '',
  onEditConfirm,
  ...rest
}) => {
  const [isEditing, setIsEditing] = React.useState(false);
  const [inputValue, setInputValue] = React.useState(text);

  const tagTheme = {
    components: {
      Tag: {
        defaultBg: '#E4F0F8',
        defaultColor: '#3B5CA9',
      },
    },
  };

  // Handle edit confirm
  const handleEditConfirm = () => {
    if (inputValue && inputValue !== text) {
      onEditConfirm(inputValue);
    }
    setIsEditing(false);
  };

  // Render addable Tag
  if (addable) {
    return (
      <AntTag
        className="tag-addable"
        size={size}
        icon={<PlusIcon width="14" height="14" />}
        onClick={onAdd}
      >
        New Tag
      </AntTag>
    );
  }

  // Render editable input if editing
  if (editable && isEditing) {
    return (
      <Input
        size="small"
        value={inputValue}
        onChange={(e) => setInputValue(e.target.value)}
        onBlur={handleEditConfirm}
        onPressEnter={handleEditConfirm}
        autoFocus
      />
    );
  }

  // Render checkable Tag
  if (checkable) {
    return (
      <CheckableTag
        className={`custom-tag`}
        size={size}
        checked={checked}
        onChange={onCheckChange}
      >
        {text}
      </CheckableTag>
    );
  }

  // Render regular Tag
  return (
    <ConfigProvider theme={tagTheme}>
      <AntTag
        color={color}
        icon={icon}
        size={size}
        closable={closable}
        closeIcon={closeIcon ? closeIcon : <CloseIcon height={14} width={14} />}
        bordered={bordered}
        onClose={onClose}
        onDoubleClick={editable ? () => setIsEditing(true) : null}
        className={`custom-tag ${classNames}`}
        {...rest}
      >
        {text}
      </AntTag>
    </ConfigProvider>
  );
};

// Add PropTypes validation
Tag.propTypes = {
  text: PropTypes.string.isRequired,
  color: PropTypes.string,
  icon: PropTypes.node,
  size: PropTypes.oneOf(['default', 'small', 'large']),
  bordered: PropTypes.bool,
  closable: PropTypes.bool,
  closeIcon: PropTypes.node,
  checkable: PropTypes.bool,
  checked: PropTypes.bool,
  onCheckChange: PropTypes.func,
  onClose: PropTypes.func,
  onAdd: PropTypes.func,
  addable: PropTypes.bool,
  editable: PropTypes.bool,
  classNames: PropTypes.string,
  onEditConfirm: PropTypes.func,
};

export default Tag;
