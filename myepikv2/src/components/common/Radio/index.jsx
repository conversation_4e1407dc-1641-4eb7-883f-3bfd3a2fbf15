import { Radio as AntRadio, ConfigProvider } from 'antd';
import PropTypes from 'prop-types';

/**
 * A customizable Radio component that wraps the Ant Design Radio.
 *
 * @param {Object} props - The properties for the Radio component.
 * @param {boolean} [props.autoFocus=false] - Whether the Radio should automatically get focus when the component is mounted.
 * @param {boolean} [props.checked] - Specifies whether the Radio is selected.
 * @param {boolean} [props.defaultChecked=false] - Specifies the initial checked state of the Radio.
 * @param {boolean} [props.disabled=false] - Whether the Radio is disabled.
 * @param {*} [props.value] - The value of the Radio when it is checked.
 * @param {React.ReactNode} [props.children] - The content to be displayed inside the Radio.
 *
 * @returns {JSX.Element} The rendered Ant Design Radio component.
 */
const Radio = ({ children, ...props }) => {
  return <AntRadio {...props}>{children}</AntRadio>;
};

Radio.propTypes = {
  autoFocus: PropTypes.bool,
  checked: PropTypes.bool,
  defaultChecked: PropTypes.bool,
  disabled: PropTypes.bool,
  value: PropTypes.any,
  children: PropTypes.node,
};

/**
 * A customizable RadioGroup component that wraps the Ant Design Radio.Group.
 *
 * @param {Object} props - The properties for the RadioGroup component.
 * @param {Array<Object|string|number>} [props.options=[]] - Specifies the options for the RadioGroup. Each option can be a string, number, or an object with `label`, `value`, and `disabled` properties.
 * @param {*} [props.value] - The value of the currently selected Radio button in the group.
 * @param {function} [props.onChange] - A callback function triggered when the selected Radio button changes. It receives the event object as a parameter.
 * @param {string} [props.optionType='default'] - Determines whether the Radio buttons are rendered as `default` or `button` styles.
 * @param {string} [props.buttonStyle='outline'] - The button style of the Radio buttons when `optionType` is `button`. Can be either `outline` or `solid`.
 * @param {boolean} [props.disabled=false] - Whether all Radio buttons in the group are disabled.
 * @param {string} [props.size] - The size of the Radio buttons. Can be `large`, `middle`, or `small`.
 * @param {string} [props.buttonBg] - The background color for the Radio buttons when `optionType` is `button`.
 * @param {string} [props.buttonColor] - The text color for the Radio buttons when `optionType` is `button`.
 *
 * @returns {JSX.Element} The rendered Ant Design Radio.Group component.
 */
const RadioGroup = ({
  options = [],
  value,
  onChange,
  optionType = 'default',
  buttonStyle = 'outline',
  disabled = false,
  size,
  buttonBg,
  buttonColor,
}) => {
  const theme = {
    components: {
      Radio: {
        buttonBg: buttonBg || 'var(--blue-2)',
        buttonColor: buttonColor || 'var(--gray-600)',
      },
    },
  };
  return (
    <ConfigProvider theme={theme}>
      <AntRadio.Group
        options={options}
        value={value}
        onChange={onChange}
        optionType={optionType}
        buttonStyle={buttonStyle}
        disabled={disabled}
        size={size}
      />
    </ConfigProvider>
  );
};

RadioGroup.propTypes = {
  options: PropTypes.arrayOf(
    PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.number,
      PropTypes.shape({
        label: PropTypes.node,
        value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        disabled: PropTypes.bool,
      }),
    ]),
  ),
  value: PropTypes.any,
  onChange: PropTypes.func,
  optionType: PropTypes.oneOf(['default', 'button']),
  buttonStyle: PropTypes.oneOf(['outline', 'solid']),
  disabled: PropTypes.bool,
  size: PropTypes.oneOf(['large', 'middle', 'small']),
  buttonBg: PropTypes.string,
  buttonColor: PropTypes.string,
};

Radio.Group = RadioGroup;

export default Radio;
