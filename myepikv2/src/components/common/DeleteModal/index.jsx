import PropTypes from 'prop-types';
import { Modal, Flex, Button, TrashRedIcon, Typography } from '@/components';

const Title = () => {
  return (
    <Flex vertical align="center">
      <Flex
        justify="center"
        align="center"
        style={{
          height: '48px',
          width: '48px',
          borderRadius: '28px',
          textAlign: 'center',
          background: 'var(--error)',
        }}
      >
        <TrashRedIcon height={24} width={24} />
      </Flex>
    </Flex>
  );
};

// Main Component: DeleteModal
const DeleteModal = ({ onCancel, handleOk }) => {
  return (
    <Modal
      title={<Title />}
      open={true}
      closable={false}
      width={'30%'}
      footer={
        <Flex
          gap={8}
          justify="center"
          style={{ width: '100%', marginTop: '32px' }}
        >
          <Button
            variant="outlined"
            onClick={onCancel}
            style={{ width: '45%' }}
          >
            Cancel
          </Button>
          <Button onClick={handleOk} style={{ width: '45%' }}>
            Submit
          </Button>
        </Flex>
      }
    >
      <Flex vertical gap={8} align="center" style={{ padding: '0px  30px' }}>
        <Typography className="heading-four-bold">Are you Sure?</Typography>
        <Typography className="small-text" style={{ textAlign: 'center' }}>
          This entity would be removed from the system permanently
        </Typography>
      </Flex>
    </Modal>
  );
};

DeleteModal.propTypes = {
  onCancel: PropTypes.func.isRequired, // Validate `onCancel` as a required function
  handleOk: PropTypes.func.isRequired, // Validate `handleOk` as a required function
  // deviceId: PropTypes.string,
};

export default DeleteModal;
