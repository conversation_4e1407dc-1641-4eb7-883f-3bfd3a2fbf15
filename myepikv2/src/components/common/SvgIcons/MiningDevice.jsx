import PropTypes from 'prop-types';

const MiningDeviceIcon = ({
  width = 16,
  height = 16,
  stroke = 'white',
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M4.66602 5H26.666V27H4.66602V5Z"
        stroke={stroke}
        strokeWidth="1.5"
      />
      <path
        d="M15.666 23C19.532 23 22.666 19.866 22.666 16C22.666 12.134 19.532 9 15.666 9C11.8 9 8.66602 12.134 8.66602 16C8.66602 19.866 11.8 23 15.666 23Z"
        stroke="white"
        strokeWidth="1.5"
      />
      <path
        d="M7.66602 8L23.666 24M23.666 8L7.66602 24"
        stroke={stroke}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
MiningDeviceIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default MiningDeviceIcon;
