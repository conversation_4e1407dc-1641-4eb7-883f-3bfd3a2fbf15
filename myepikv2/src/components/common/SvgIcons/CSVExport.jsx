import PropTypes from 'prop-types';

const CSVExportIcon = ({
  width = 16,
  height = 16,
  className = '',
  fill = 'white',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 21 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M21 6.75V21C21 21.7956 20.6839 22.5587 20.1213 23.1213C19.5587 23.6839 18.7956 24 18 24H16.5V22.5H18C18.3978 22.5 18.7794 22.342 19.0607 22.0607C19.342 21.7794 19.5 21.3978 19.5 21V6.75H16.5C15.9033 6.75 15.331 6.51295 14.909 6.09099C14.4871 5.66903 14.25 5.09674 14.25 4.5V1.5H6C5.60218 1.5 5.22064 1.65804 4.93934 1.93934C4.65804 2.22064 4.5 2.60218 4.5 3V16.5H3V3C3 2.20435 3.31607 1.44129 3.87868 0.87868C4.44129 0.316071 5.20435 0 6 0L14.25 0L21 6.75ZM5.2755 22.2615C5.28409 22.4978 5.34204 22.7298 5.44563 22.9424C5.54922 23.155 5.69616 23.3436 5.877 23.496C6.072 23.658 6.311 23.784 6.594 23.874C6.878 23.965 7.2105 24.0105 7.5915 24.0105C8.0985 24.0105 8.528 23.9315 8.88 23.7735C9.234 23.6155 9.5035 23.3955 9.6885 23.1135C9.8755 22.8295 9.969 22.5015 9.969 22.1295C9.969 21.7935 9.902 21.5135 9.768 21.2895C9.63076 21.0647 9.43676 20.88 9.2055 20.754C8.94007 20.6066 8.65381 20.5004 8.3565 20.439L7.425 20.223C7.20588 20.1812 6.99886 20.091 6.819 19.959C6.75056 19.9062 6.6954 19.8382 6.6579 19.7603C6.62039 19.6825 6.6016 19.5969 6.603 19.5105C6.603 19.2765 6.6955 19.0845 6.8805 18.9345C7.0685 18.7825 7.3245 18.7065 7.6485 18.7065C7.8625 18.7065 8.0475 18.7405 8.2035 18.8085C8.34786 18.8657 8.47498 18.9592 8.5725 19.08C8.66482 19.1914 8.72682 19.3247 8.7525 19.467H9.8775C9.85815 19.1617 9.75428 18.8677 9.5775 18.618C9.38848 18.3482 9.12906 18.1355 8.8275 18.003C8.45936 17.8408 8.05962 17.7629 7.6575 17.775C7.2185 17.775 6.8305 17.85 6.4935 18C6.1565 18.149 5.893 18.3595 5.703 18.6315C5.513 18.9045 5.418 19.224 5.418 19.59C5.418 19.892 5.479 20.154 5.601 20.376C5.725 20.599 5.901 20.7825 6.129 20.9265C6.357 21.0695 6.6265 21.176 6.9375 21.246L7.8645 21.462C8.1745 21.535 8.406 21.6315 8.559 21.7515C8.63346 21.8087 8.69306 21.883 8.73274 21.9681C8.77243 22.0533 8.79104 22.1467 8.787 22.2405C8.79039 22.3952 8.74585 22.5471 8.6595 22.6755C8.56285 22.8071 8.42993 22.9077 8.277 22.965C8.11 23.035 7.9035 23.07 7.6575 23.07C7.4825 23.07 7.3225 23.05 7.1775 23.01C7.04452 22.9743 6.91868 22.9159 6.8055 22.8375C6.70576 22.7727 6.62036 22.6882 6.55457 22.5891C6.48878 22.4901 6.444 22.3786 6.423 22.2615H5.2755ZM1.209 20.5395C1.209 20.1665 1.26 19.85 1.362 19.59C1.45113 19.3504 1.60865 19.1423 1.815 18.9915C2.02485 18.8495 2.27426 18.7776 2.5275 18.786C2.7525 18.786 2.9515 18.8345 3.1245 18.9315C3.29355 19.0218 3.43464 19.1567 3.5325 19.3215C3.63705 19.495 3.69875 19.6909 3.7125 19.893H4.86V19.785C4.85005 19.5087 4.78277 19.2375 4.66244 18.9886C4.54211 18.7396 4.37135 18.5184 4.161 18.339C3.94645 18.155 3.69696 18.0163 3.4275 17.931C3.13506 17.8308 2.82759 17.7816 2.5185 17.7855C1.9845 17.7855 1.529 17.897 1.152 18.12C0.777 18.342 0.491 18.658 0.294 19.068C0.099 19.478 0.001 19.9675 0 20.5365V21.2835C0 21.8515 0.0965 22.3395 0.2895 22.7475C0.4865 23.1545 0.7725 23.4675 1.1475 23.6865C1.5225 23.9035 1.9795 24.012 2.5185 24.012C2.9575 24.012 3.35 23.93 3.696 23.766C4.042 23.602 4.317 23.375 4.521 23.085C4.72794 22.7871 4.84554 22.4364 4.86 22.074V21.96H3.714C3.6998 22.1531 3.63907 22.3399 3.537 22.5045C3.43703 22.6638 3.29613 22.7933 3.129 22.8795C2.94122 22.9687 2.73537 23.0134 2.5275 23.01C2.27413 23.0167 2.02451 22.9478 1.8105 22.812C1.6049 22.666 1.44849 22.4609 1.362 22.224C1.25236 21.9229 1.20045 21.6038 1.209 21.2835V20.5395ZM13.5675 23.8965H12.138L10.131 17.898H11.5065L12.8505 22.605H12.9075L14.2395 17.898H15.558L13.5675 23.8965Z"
        fill={fill}
      />
    </svg>
  );
};
export default CSVExportIcon;

CSVExportIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  fill: PropTypes.string,
  className: PropTypes.string,
  stroke: PropTypes.string,
};
