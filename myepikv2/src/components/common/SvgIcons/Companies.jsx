import PropTypes from 'prop-types';

const Companies = ({
  width = 16,
  height = 16,
  stroke = '#667085',
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M1.69389 23C1.17038 23 0.757105 22.8298 0.454071 22.4894C0.174717 22.1748 0.0243195 21.7622 0.00287996 21.2516L0 21.122V2.87852C0 2.30056 0.151357 1.84315 0.454071 1.50628C0.733425 1.19497 1.10702 1.02734 1.57485 1.0034L1.69437 1H14.5615C15.0783 1 15.4882 1.1686 15.7912 1.50579C16.0706 1.81645 16.221 2.23018 16.2424 2.74697L16.2448 2.87803V6.07685H22.3167C22.7935 6.07685 23.1792 6.22198 23.4739 6.51226L23.5459 6.58798C23.8256 6.90187 23.976 7.31431 23.9971 7.82528L24 7.95488V21.122C24 21.6928 23.8486 22.1486 23.5459 22.4894C23.2666 22.8032 22.8959 22.9725 22.4338 22.9971L22.3162 23H1.69389ZM16.2443 21.122C16.2443 21.1964 16.2418 21.2687 16.2366 21.3389H21.7748C21.9722 21.3389 22.1186 21.2907 22.214 21.1943C22.2956 21.1115 22.3421 20.9885 22.3536 20.8254L22.357 20.7404V8.33689C22.357 8.13043 22.3093 7.97899 22.214 7.88255C22.1324 7.80004 22.0132 7.75295 21.8564 7.7413L21.7748 7.73839L16.2443 7.7379V21.122ZM14.0205 2.66154H2.23484C2.03068 2.66154 1.88092 2.7096 1.78556 2.80571C1.70397 2.88855 1.65741 3.01152 1.64589 3.17461L1.64301 3.26004V20.7404C1.64301 20.9466 1.69053 21.0979 1.78556 21.1943C1.86748 21.2768 1.98908 21.3239 2.15036 21.3355L2.23484 21.3389L4.57095 21.3385V18.295C4.57095 17.8882 4.65415 17.5796 4.82054 17.3693L4.87238 17.3091C5.05478 17.1182 5.32581 17.0142 5.68549 16.997L5.79588 16.9946H10.4489C10.8703 16.9946 11.1798 17.0994 11.3772 17.3091C11.5567 17.5 11.6546 17.7889 11.671 18.1756L11.6734 18.2945L11.6729 21.3385H14.02C14.2175 21.3385 14.3637 21.2904 14.4588 21.1943C14.5403 21.1115 14.5871 20.9885 14.5989 20.8254L14.6018 20.7404V3.26004C14.6018 3.05359 14.5541 2.90214 14.4588 2.80571C14.3637 2.7096 14.218 2.66154 14.0205 2.66154ZM10.0102 18.3464H6.2346C6.03684 18.3464 5.92884 18.4374 5.9106 18.6192L5.9082 18.6765L5.90772 21.3385H10.3361V18.6765C10.3361 18.4765 10.2464 18.3673 10.0668 18.3489L10.0102 18.3464ZM20.1222 17.3246C20.334 17.3246 20.4487 17.4229 20.4663 17.6193L20.4687 17.6756V19.2852C20.4687 19.4997 20.3716 19.6159 20.1774 19.6337L20.1222 19.6361H18.4792C18.2737 19.6361 18.1622 19.5379 18.1446 19.3415L18.1427 19.2852V17.6756C18.1427 17.461 18.2371 17.3449 18.4259 17.3271L18.4792 17.3246H20.1222ZM20.1222 13.3832C20.334 13.3832 20.4487 13.4814 20.4663 13.6778L20.4687 13.7336V15.3432C20.4687 15.5578 20.3716 15.6739 20.1774 15.6917L20.1222 15.6942H18.4792C18.2737 15.6942 18.1622 15.596 18.1446 15.3995L18.1427 15.3432V13.7336C18.1427 13.5191 18.2371 13.4029 18.4259 13.3851L18.4792 13.3827L20.1222 13.3832ZM6.72419 12.5468C6.9821 12.5468 7.12082 12.667 7.14034 12.9075L7.14274 12.9701V14.9204C7.14274 15.1806 7.02386 15.3207 6.7861 15.3408L6.72419 15.3432H4.73463C4.48983 15.3432 4.35799 15.2232 4.33911 14.9831L4.33671 14.92V12.9701C4.33671 12.7099 4.44967 12.5696 4.67559 12.5492L4.73463 12.5468H6.72419ZM11.5001 12.5468C11.7574 12.5468 11.8959 12.667 11.9158 12.9075L11.9182 12.9701V14.9204C11.9182 15.1806 11.7993 15.3207 11.5615 15.3408L11.4996 15.3432H9.52013C9.26893 15.3432 9.13374 15.2232 9.11454 14.9831L9.11214 14.92V12.9701C9.11214 12.7099 9.22798 12.5696 9.45965 12.5492L9.52013 12.5468H11.5001ZM20.1222 9.4407C20.334 9.4407 20.4487 9.53908 20.4663 9.73583L20.4687 9.79165V11.4017C20.4687 11.616 20.3716 11.732 20.1774 11.7498L20.1222 11.7522H18.4792C18.2737 11.7522 18.1622 11.654 18.1446 11.4576L18.1427 11.4017V9.79165C18.1427 9.57775 18.2371 9.46157 18.4259 9.44313L18.4792 9.4407H20.1222ZM6.72419 8.46019C6.9821 8.46019 7.12082 8.58057 7.14034 8.82133L7.14274 8.88346V10.8338C7.14274 11.0946 7.02386 11.2349 6.7861 11.2547L6.72419 11.2571H4.73463C4.48983 11.2571 4.35799 11.1369 4.33911 10.8964L4.33671 10.8338V8.88346C4.33671 8.62328 4.44967 8.48316 4.67559 8.4631L4.73463 8.46067L6.72419 8.46019ZM11.5001 8.46019C11.7574 8.46019 11.8959 8.58057 11.9158 8.82133L11.9182 8.88346V10.8338C11.9182 11.0946 11.7993 11.2349 11.5615 11.2547L11.4996 11.2571H9.52013C9.26893 11.2571 9.13374 11.1369 9.11454 10.8964L9.11214 10.8338V8.88346C9.11214 8.62328 9.22798 8.48316 9.45965 8.4631L9.52013 8.46067L11.5001 8.46019ZM6.72419 4.37453C6.9821 4.37453 7.12082 4.49459 7.14034 4.7347L7.14274 4.79781V6.74768C7.14274 7.00785 7.02386 7.14797 6.7861 7.16804L6.72419 7.17095H4.73463C4.48983 7.17095 4.35799 7.05073 4.33911 6.81029L4.33671 6.74768V4.79732C4.33671 4.53714 4.44967 4.39702 4.67559 4.37696L4.73463 4.37453H6.72419ZM11.5001 4.37453C11.7574 4.37453 11.8959 4.49459 11.9158 4.7347L11.9182 4.79781V6.74768C11.9182 7.00785 11.7993 7.14797 11.5615 7.16804L11.4996 7.17095H9.52013C9.26893 7.17095 9.13374 7.05073 9.11454 6.81029L9.11214 6.74768V4.79732C9.11214 4.53714 9.22798 4.39702 9.45965 4.37696L9.52013 4.37453H11.5001Z"
        fill={stroke}
      />
    </svg>
  );
};
Companies.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default Companies;
