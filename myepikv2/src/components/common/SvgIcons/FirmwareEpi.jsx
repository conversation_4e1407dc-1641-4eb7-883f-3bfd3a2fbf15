import PropTypes from 'prop-types';
const FirmwareEpi = ({ width = 24, height = 24, className = '', ...props }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M12 20V4M12 4L18 10M12 4L6 10"
        stroke="white" // Set stroke color to white
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
FirmwareEpi.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default FirmwareEpi;
