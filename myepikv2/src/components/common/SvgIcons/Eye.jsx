import PropTypes from 'prop-types';

const EyeIcon = ({
  width = 16,
  height = 16,
  stroke = '#475467',
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M0.833008 10.0007C0.833008 10.0007 4.16634 3.33398 9.99967 3.33398C15.833 3.33398 19.1663 10.0007 19.1663 10.0007C19.1663 10.0007 15.833 16.6673 9.99967 16.6673C4.16634 16.6673 0.833008 10.0007 0.833008 10.0007Z"
        stroke={stroke}
        strokeWidth="1.66667"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10 12.5C11.3807 12.5 12.5 11.3807 12.5 10C12.5 8.61929 11.3807 7.5 10 7.5C8.61929 7.5 7.5 8.61929 7.5 10C7.5 11.3807 8.61929 12.5 10 12.5Z"
        stroke={stroke}
        strokeWidth="1.66667"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
EyeIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default EyeIcon;
