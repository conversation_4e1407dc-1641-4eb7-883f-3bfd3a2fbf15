import PropTypes from 'prop-types';

const OOBMIcon = ({ width = 28, height = 28, className = '', ...props }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 28 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.4401 0.616941L14.0115 0.406738L13.5785 0.607886C8.98852 2.74027 6.15288 3.66327 1.38188 4.1312L0.479492 4.2197V5.12642C0.479492 9.47252 0.719501 12.7772 2.02923 15.8641C3.03562 18.3301 4.32194 20.6255 6.16458 22.4385C7.57621 23.9457 9.11167 25.1372 10.4591 25.9868C11.7815 26.8205 13.0112 27.3806 13.8073 27.5367L14.0935 27.5929L14.365 27.4864C15.6593 26.9786 19.0054 25.466 21.8498 22.4228L21.8577 22.4143L21.8654 22.4056C23.5945 20.4675 25.1806 18.4218 25.9879 15.8227C27.2823 12.7476 27.5201 9.4531 27.5201 5.12642V4.21548L26.6131 4.13076C21.5954 3.66207 18.7666 2.7386 14.4401 0.616941ZM3.87286 15.0888C2.81114 12.5896 2.51951 9.86259 2.4835 6.02476C6.85857 5.51627 9.77656 4.5541 13.9885 2.62146C18.0094 4.56311 20.9304 5.52343 25.5161 6.02949C25.4799 9.86483 25.188 12.5907 24.1268 15.0888L24.1062 15.1371L24.0909 15.1873C23.4213 17.3763 22.0738 19.1669 20.3808 21.0654C18.0203 23.5875 15.2527 24.9526 13.9308 25.5045C13.4251 25.3436 12.571 24.9539 11.5258 24.295C10.3003 23.5223 8.89578 22.4317 7.61099 21.0571L7.59615 21.0412L7.58062 21.026C5.99634 19.4722 4.83276 17.4421 3.87842 15.1021L3.87569 15.0954L3.87286 15.0888ZM8.87305 13.1265C9.04622 12.8205 9.13281 12.4448 9.13281 11.9995C9.13281 11.5542 9.04622 11.1792 8.87305 10.8745C8.69987 10.5685 8.46615 10.3374 8.17188 10.1812C7.8776 10.0236 7.54948 9.94482 7.1875 9.94482C6.82292 9.94482 6.49349 10.0236 6.19922 10.1812C5.90625 10.3374 5.67318 10.5685 5.5 10.8745C5.32812 11.1792 5.24219 11.5542 5.24219 11.9995C5.24219 12.4422 5.32812 12.8166 5.5 13.1226C5.67318 13.4272 5.90625 13.659 6.19922 13.8179C6.49349 13.9754 6.82292 14.0542 7.1875 14.0542C7.54948 14.0542 7.8776 13.9761 8.17188 13.8198C8.46615 13.6623 8.69987 13.4312 8.87305 13.1265ZM7.91992 11.394C7.98372 11.5581 8.01562 11.7599 8.01562 11.9995C8.01562 12.2391 7.98372 12.4416 7.91992 12.6069C7.85742 12.771 7.76432 12.896 7.64062 12.9819C7.51823 13.0666 7.36719 13.1089 7.1875 13.1089C7.00781 13.1089 6.85612 13.0666 6.73242 12.9819C6.61003 12.896 6.51693 12.771 6.45312 12.6069C6.39062 12.4416 6.35938 12.2391 6.35938 11.9995C6.35938 11.7599 6.39062 11.5581 6.45312 11.394C6.51693 11.2287 6.61003 11.1037 6.73242 11.019C6.85612 10.9331 7.00781 10.8901 7.1875 10.8901C7.36719 10.8901 7.51823 10.9331 7.64062 11.019C7.76432 11.1037 7.85742 11.2287 7.91992 11.394ZM13.2505 13.1265C13.4237 12.8205 13.5103 12.4448 13.5103 11.9995C13.5103 11.5542 13.4237 11.1792 13.2505 10.8745C13.0773 10.5685 12.8436 10.3374 12.5493 10.1812C12.255 10.0236 11.9269 9.94482 11.5649 9.94482C11.2004 9.94482 10.8709 10.0236 10.5767 10.1812C10.2837 10.3374 10.0506 10.5685 9.87744 10.8745C9.70557 11.1792 9.61963 11.5542 9.61963 11.9995C9.61963 12.4422 9.70557 12.8166 9.87744 13.1226C10.0506 13.4272 10.2837 13.659 10.5767 13.8179C10.8709 13.9754 11.2004 14.0542 11.5649 14.0542C11.9269 14.0542 12.255 13.9761 12.5493 13.8198C12.8436 13.6623 13.0773 13.4312 13.2505 13.1265ZM12.2974 11.394C12.3612 11.5581 12.3931 11.7599 12.3931 11.9995C12.3931 12.2391 12.3612 12.4416 12.2974 12.6069C12.2349 12.771 12.1418 12.896 12.0181 12.9819C11.8957 13.0666 11.7446 13.1089 11.5649 13.1089C11.3853 13.1089 11.2336 13.0666 11.1099 12.9819C10.9875 12.896 10.8944 12.771 10.8306 12.6069C10.7681 12.4416 10.7368 12.2391 10.7368 11.9995C10.7368 11.7599 10.7681 11.5581 10.8306 11.394C10.8944 11.2287 10.9875 11.1037 11.1099 11.019C11.2336 10.9331 11.3853 10.8901 11.5649 10.8901C11.7446 10.8901 11.8957 10.9331 12.0181 11.019C12.1418 11.1037 12.2349 11.2287 12.2974 11.394ZM15.7471 9.99951H14.0127V13.9995H15.8955C16.1859 13.9995 16.4359 13.9507 16.6455 13.853C16.8551 13.7554 17.0166 13.6213 17.1299 13.4507C17.2445 13.2801 17.3018 13.0854 17.3018 12.8667C17.3018 12.6779 17.2601 12.5138 17.1768 12.3745C17.0934 12.2339 16.9814 12.1239 16.8408 12.0444C16.7002 11.9637 16.5439 11.9201 16.3721 11.9136V11.8745C16.527 11.8433 16.6624 11.7873 16.7783 11.7065C16.8942 11.6245 16.984 11.5229 17.0479 11.4019C17.113 11.2808 17.1455 11.1466 17.1455 10.9995C17.1455 10.7964 17.0921 10.62 16.9854 10.4702C16.8799 10.3205 16.723 10.2046 16.5146 10.1226C16.3076 10.0405 16.0518 9.99951 15.7471 9.99951ZM15.6064 13.1323H15.0986V12.2886H15.6221C15.7327 12.2886 15.8271 12.3055 15.9053 12.3394C15.9847 12.3732 16.0459 12.4227 16.0889 12.4878C16.1318 12.5529 16.1533 12.6323 16.1533 12.7261C16.1533 12.8602 16.1077 12.9618 16.0166 13.0308C15.9255 13.0985 15.7887 13.1323 15.6064 13.1323ZM15.5439 11.6167H15.0986V10.8511H15.5596C15.695 10.8511 15.807 10.8843 15.8955 10.9507C15.984 11.0158 16.0283 11.1102 16.0283 11.2339C16.0283 11.3159 16.0075 11.3856 15.9658 11.4429C15.9255 11.4989 15.8688 11.5418 15.7959 11.5718C15.723 11.6017 15.639 11.6167 15.5439 11.6167ZM19.0381 9.99951H17.6865V13.9995H18.749V11.6714H18.7803L19.6709 13.9683H20.3115L21.2021 11.687H21.2334V13.9995H22.2959V9.99951H20.9443L20.0146 12.2651H19.9678L19.0381 9.99951Z"
        fill="black"
      />
    </svg>
  );
};
OOBMIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default OOBMIcon;
