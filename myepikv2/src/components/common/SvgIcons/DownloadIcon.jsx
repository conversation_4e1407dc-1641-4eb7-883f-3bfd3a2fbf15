import PropTypes from 'prop-types';

const DownloadIcon = ({
  width = 16,
  height = 16,
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <rect width="40" height="40" rx="5" fill="#E8E8E8" />
      <path
        d="M26.2577 11.5083C26.3948 11.5651 26.5119 11.6612 26.5943 11.7846C26.6767 11.908 26.7207 12.053 26.7207 12.2013V16.4443C26.7207 16.6432 26.6417 16.834 26.501 16.9747C26.3604 17.1153 26.1696 17.1943 25.9707 17.1943H21.7277C21.5794 17.1942 21.4346 17.1501 21.3114 17.0677C21.1881 16.9853 21.0921 16.8682 21.0354 16.7313C20.9787 16.5943 20.9638 16.4436 20.9927 16.2982C21.0216 16.1528 21.0929 16.0192 21.1977 15.9143L22.7997 14.3103C21.3281 13.6941 19.6944 13.5805 18.1517 13.987C16.609 14.3936 15.2435 15.2976 14.2668 16.5591C13.2901 17.8205 12.7567 19.3689 12.7494 20.9642C12.742 22.5596 13.2611 24.1128 14.2261 25.3832C15.1912 26.6536 16.5483 27.5702 18.0872 27.991C19.626 28.4117 21.2607 28.3132 22.7379 27.7105C24.2151 27.1079 25.4522 26.0349 26.2576 24.6577C27.063 23.2806 27.3917 21.6762 27.1927 20.0933C27.1721 19.8981 27.2289 19.7025 27.3508 19.5487C27.4728 19.3948 27.6502 19.2949 27.845 19.2705C28.0398 19.246 28.2365 19.2989 28.3927 19.4178C28.549 19.5366 28.6523 19.7121 28.6807 19.9063C28.9241 21.8425 28.5133 23.8045 27.5136 25.4804C26.5139 27.1562 24.9827 28.4499 23.1634 29.1556C21.344 29.8613 19.341 29.9385 17.4727 29.3751C15.6044 28.8117 13.978 27.6399 12.8522 26.046C11.7263 24.4522 11.1656 22.5277 11.2591 20.5785C11.3526 18.6293 12.095 16.7674 13.3682 15.2886C14.6415 13.8098 16.3725 12.7991 18.2862 12.4171C20.1998 12.0351 22.1863 12.3037 23.9297 13.1803L25.4397 11.6703C25.5446 11.5656 25.6782 11.4943 25.8236 11.4655C25.969 11.4367 26.1207 11.4516 26.2577 11.5083Z"
        fill="black"
      />
    </svg>
  );
};
DownloadIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default DownloadIcon;
