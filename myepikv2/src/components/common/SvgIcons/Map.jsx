import PropTypes from 'prop-types';

const MapIcon = ({
  width = 16,
  height = 16,
  stroke = '#475467',
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 25 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M1.04199 6.75065V23.4173L8.33366 19.2507L16.667 23.4173L23.9587 19.2507V2.58398L16.667 6.75065L8.33366 2.58398L1.04199 6.75065Z"
        stroke={stroke}
        strokeWidth="1.875"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.33301 2.58398V19.2507"
        stroke={stroke}
        strokeWidth="1.875"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.667 6.75V23.4167"
        stroke={stroke}
        strokeWidth="1.875"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
MapIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default MapIcon;
