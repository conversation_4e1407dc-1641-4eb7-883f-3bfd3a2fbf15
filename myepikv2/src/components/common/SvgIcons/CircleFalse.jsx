import PropTypes from 'prop-types'; // Import PropTypes for validation
const CircleFalse = ({ width = 16, height = 16, className = '', ...props }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 29 29"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M14.25 2.125C7.48516 2.125 2 7.61016 2 14.375C2 21.1398 7.48516 26.625 14.25 26.625C21.0148 26.625 26.5 21.1398 26.5 14.375C26.5 7.61016 21.0148 2.125 14.25 2.125ZM14.25 24.5469C8.63359 24.5469 4.07812 19.9914 4.07812 14.375C4.07812 8.75859 8.63359 4.20312 14.25 4.20312C19.8664 4.20312 24.4219 8.75859 24.4219 14.375C24.4219 19.9914 19.8664 24.5469 14.25 24.5469Z"
        fill="#F33E41" // Red circle fill
      />
      <path
        d="M14.25 4.20312C8.63359 4.20312 4.07812 8.75859 4.07812 14.375C4.07812 19.9914 8.63359 24.5469 14.25 24.5469C19.8664 24.5469 24.4219 19.9914 24.4219 14.375C24.4219 8.75859 19.8664 4.20312 14.25 4.20312ZM18.9477 18.616C18.9805 18.657 18.9996 18.7062 18.9996 18.7582C18.9996 18.8812 18.9012 18.977 18.7809 18.977L16.9762 18.9688L14.2609 15.7312L11.5457 18.9715L9.73828 18.9797C9.61797 18.9797 9.51953 18.8813 9.51953 18.7609C9.51953 18.709 9.53867 18.6598 9.57148 18.6188L13.1289 14.3832L9.57148 10.1449C9.53844 10.1048 9.52011 10.0547 9.51953 10.0027C9.51953 9.87969 9.61797 9.78398 9.73828 9.78398L11.5457 9.79219L14.2609 13.0297L16.9789 9.78945L18.7836 9.78125C18.9039 9.78125 19.0023 9.87969 19.0023 10C19.0023 10.052 18.9859 10.1039 18.9531 10.1422L15.3957 14.3805L18.9477 18.616Z"
        fill="#F33E41" // Red cross in the circle
        fillOpacity="0.15"
      />
      <path
        d="M19.0023 10C19.0023 9.87969 18.9039 9.78125 18.7836 9.78125L16.9789 9.78945L14.2609 13.0297L11.5457 9.79219L9.73828 9.78398C9.61797 9.78398 9.51953 9.87969 9.51953 10.0027C9.51953 10.0547 9.53867 10.1039 9.57148 10.1449L13.1289 14.3832L9.57148 18.6188C9.53844 18.6588 9.52011 18.709 9.51953 18.7609C9.51953 18.8813 9.61797 18.9797 9.73828 18.9797L11.5457 18.9715L14.2609 15.7312L16.9762 18.9688L18.7809 18.977C18.9012 18.977 18.9996 18.8813 18.9996 18.7582C18.9996 18.7063 18.9805 18.657 18.9477 18.616L15.3957 14.3805L18.9531 10.1422C18.9859 10.1039 19.0023 10.052 19.0023 10Z"
        fill="#F33E41" // Red cross line
      />
    </svg>
  );
};
// Add propTypes to validate the props
CircleFalse.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  className: PropTypes.string,
};

export default CircleFalse;
