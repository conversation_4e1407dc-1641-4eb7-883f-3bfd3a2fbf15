import PropTypes from 'prop-types';

const DragIcon = ({ width = 16, height = 16, className = '', ...props }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 25 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M7.08301 2.25H10.083V5.25H7.08301V2.25ZM14.583 2.25H17.583V5.25H14.583V2.25ZM7.08301 7.75H10.083V10.75H7.08301V7.75ZM14.583 7.75H17.583V10.75H14.583V7.75ZM7.08301 13.25H10.083V16.25H7.08301V13.25ZM14.583 13.25H17.583V16.25H14.583V13.25ZM7.08301 18.75H10.083V21.75H7.08301V18.75ZM14.583 18.75H17.583V21.75H14.583V18.75Z"
        fill="#475467"
      />
    </svg>
  );
};

// Add PropTypes validation
DragIcon.propTypes = {
  /** Width of the icon */
  width: PropTypes.number, // `width` should be a number
  /** Height of the icon */
  height: PropTypes.number, // `height` should be a number
  /** CSS class for custom styles */
  className: PropTypes.string, // `className` should be a string
};

export default DragIcon;
