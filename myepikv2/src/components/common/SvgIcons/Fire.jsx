import PropTypes from 'prop-types';

const FireIcon = ({ width = 16, height = 16, className = '', ...props }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M10.9465 20.6976C22.7981 17.7886 16.1615 6.1522 8.10233 1.30371C7.15397 4.69765 5.73142 5.66735 2.8873 9.06129C-0.879004 13.5549 0.991542 18.7583 6.2056 20.6976C5.4153 19.728 3.377 17.6916 4.78306 14.8795C5.26791 13.9098 6.2376 12.9401 5.75275 11.0007C6.70112 11.4855 8.66185 11.9704 9.14669 14.3946C9.937 13.4249 10.7564 11.3886 9.99809 9.06129C15.9346 13.4249 13.5103 17.7886 10.9465 20.6976Z"
        stroke="black"
        strokeWidth="1.09091"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
export default FireIcon;

FireIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  className: PropTypes.string,
  stroke: PropTypes.string,
};
