import PropTypes from 'prop-types';

const ReloadErrorIcon = ({
  width = 16,
  height = 16,
  fill = '#ED2B2E',
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 18 16"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M8.88788 14.6667C8.88788 14.8877 8.80292 15.0996 8.65168 15.2559C8.50044 15.4122 8.29532 15.5 8.08144 15.5C4.00409 15.5 0.666504 12.1611 0.666504 8C0.666504 3.83889 4.00409 0.5 8.08144 0.5C11.4201 0.5 14.2631 2.73889 15.1835 5.83444L15.8319 4.70111C15.9407 4.51075 16.1182 4.37283 16.3254 4.31772C16.5326 4.2626 16.7525 4.2948 16.9367 4.40722C17.1209 4.51965 17.2544 4.70309 17.3077 4.91719C17.361 5.13129 17.3299 5.35852 17.2211 5.54889L15.4684 8.61C15.3611 8.79778 15.1868 8.93461 14.983 8.99111C14.7791 9.04762 14.562 9.01929 14.3781 8.91222L11.3438 7.14444C11.2515 7.09068 11.1704 7.01865 11.105 6.93248C11.0397 6.84632 10.9914 6.7477 10.9629 6.64225C10.9345 6.5368 10.9264 6.4266 10.9392 6.31793C10.9519 6.20925 10.9853 6.10424 11.0373 6.00889C11.0893 5.91354 11.159 5.8297 11.2424 5.76219C11.3258 5.69467 11.4213 5.64478 11.5233 5.61537C11.6253 5.58597 11.732 5.57762 11.8372 5.59081C11.9423 5.60399 12.0439 5.63845 12.1362 5.69222L13.719 6.61445C13.1018 4.07222 10.8255 2.16667 8.08252 2.16667C4.85891 2.16667 2.27938 4.79667 2.27938 8C2.27938 11.2033 4.85891 13.8333 8.08144 13.8333C8.29532 13.8333 8.50044 13.9211 8.65168 14.0774C8.80292 14.2337 8.88788 14.4457 8.88788 14.6667Z"
        fill={fill}
      />
    </svg>
  );
};
ReloadErrorIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  fill: PropTypes.string,
  className: PropTypes.string,
};
export default ReloadErrorIcon;
