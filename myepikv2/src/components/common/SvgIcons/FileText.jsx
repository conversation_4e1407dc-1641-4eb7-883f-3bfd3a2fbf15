import PropTypes from 'prop-types';

const FileTextIcon = ({
  width = 16,
  height = 16,
  stroke = '#475467',
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M11.6663 1.66602H4.99967C4.55765 1.66602 4.13372 1.84161 3.82116 2.15417C3.5086 2.46673 3.33301 2.89065 3.33301 3.33268V16.666C3.33301 17.108 3.5086 17.532 3.82116 17.8445C4.13372 18.1571 4.55765 18.3327 4.99967 18.3327H14.9997C15.4417 18.3327 15.8656 18.1571 16.1782 17.8445C16.4907 17.532 16.6663 17.108 16.6663 16.666V6.66602L11.6663 1.66602Z"
        stroke={stroke}
        strokeWidth="1.66667"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.667 1.66602V6.66602H16.667"
        stroke={stroke}
        strokeWidth="1.66667"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.3337 10.834H6.66699"
        stroke={stroke}
        strokeWidth="1.66667"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.3337 14.166H6.66699"
        stroke={stroke}
        strokeWidth="1.66667"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.33366 7.5H7.50033H6.66699"
        stroke={stroke}
        strokeWidth="1.66667"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
FileTextIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default FileTextIcon;
