import PropTypes from 'prop-types';

const DevicePowerIcon = ({
  width = 32,
  height = 32,
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M16.3337 29C12.8528 29 9.58678 27.6498 7.13581 25.1989C4.68417 22.7472 3.33398 19.4806 3.33398 16.001C3.33398 12.5214 4.68417 9.2554 7.13581 6.80444C9.58876 4.35084 12.8561 3 16.3344 3C19.8107 3 23.0774 4.3515 25.531 6.8051C30.6016 11.8764 30.6016 20.1283 25.531 25.1995C23.0787 27.6498 19.8127 29 16.3337 29ZM16.3344 3.66153C13.0327 3.66153 9.9321 4.94423 7.60351 7.27214C5.2769 9.59873 3.99552 12.6986 3.99552 16.001C3.99552 19.304 5.2769 22.4046 7.60351 24.7312C9.92945 27.0578 13.0301 28.3385 16.3331 28.3385C19.6354 28.3385 22.736 27.0571 25.0626 24.7312C29.8753 19.9179 29.8753 12.0861 25.0626 7.27214C22.7341 4.94357 19.6341 3.66153 16.3344 3.66153Z"
        fill="white"
      />
      <path
        d="M14.5703 25C14.5149 25 14.4558 24.9909 14.3942 24.974C14.2564 24.9298 14.1397 24.8385 14.0656 24.7171C13.9916 24.5956 13.9653 24.4522 13.9916 24.3133L15.4839 16.9206L12.7637 16.1783C12.6779 16.1547 12.5987 16.1126 12.5321 16.0549C12.4655 15.9973 12.4131 15.9258 12.379 15.8456C12.3448 15.7655 12.3298 15.6789 12.335 15.5923C12.3402 15.5057 12.3655 15.4214 12.409 15.3457L17.5375 6.29387C17.6078 6.17436 17.7192 6.08286 17.8521 6.03554C17.985 5.98823 18.1308 5.98815 18.2637 6.03532C18.3961 6.08251 18.5063 6.17478 18.5741 6.29503C18.6418 6.41528 18.6625 6.55535 18.6322 6.68927L17.2967 13.1138L19.8805 13.712C20.061 13.7599 20.2029 13.8731 20.2776 14.0251C20.3158 14.1042 20.3351 14.1908 20.3339 14.2783C20.3327 14.3658 20.3111 14.4519 20.2707 14.5301L15.0999 24.6803C15.0509 24.7762 14.9753 24.8569 14.8818 24.9134C14.7883 24.9698 14.6804 24.9998 14.5703 25Z"
        fill="white"
      />
    </svg>
  );
};
DevicePowerIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default DevicePowerIcon;
