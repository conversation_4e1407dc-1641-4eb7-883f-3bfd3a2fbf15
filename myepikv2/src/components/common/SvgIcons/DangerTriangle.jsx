import PropTypes from 'prop-types';

const DangerTriangleIcon = ({
  width = 16,
  height = 16,
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 20 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.31224 7.762C6.23024 2.587 7.68924 0 10.0002 0C12.3112 0 13.7702 2.587 16.6882 7.762L17.0522 8.406C19.4772 12.706 20.6902 14.856 19.5942 16.428C18.4982 18 15.7862 18 10.3642 18H9.63624C4.21424 18 1.50224 18 0.40624 16.428C-0.689759 14.856 0.523241 12.706 2.94824 8.406L3.31224 7.762ZM10.0002 4.25C10.1992 4.25 10.3899 4.32902 10.5306 4.46967C10.6712 4.61032 10.7502 4.80109 10.7502 5V10C10.7502 10.1989 10.6712 10.3897 10.5306 10.5303C10.3899 10.671 10.1992 10.75 10.0002 10.75C9.80133 10.75 9.61056 10.671 9.46991 10.5303C9.32926 10.3897 9.25024 10.1989 9.25024 10V5C9.25024 4.80109 9.32926 4.61032 9.46991 4.46967C9.61056 4.32902 9.80133 4.25 10.0002 4.25ZM10.0002 14C10.2655 14 10.5198 13.8946 10.7073 13.7071C10.8949 13.5196 11.0002 13.2652 11.0002 13C11.0002 12.7348 10.8949 12.4804 10.7073 12.2929C10.5198 12.1054 10.2655 12 10.0002 12C9.73502 12 9.48067 12.1054 9.29313 12.2929C9.1056 12.4804 9.00024 12.7348 9.00024 13C9.00024 13.2652 9.1056 13.5196 9.29313 13.7071C9.48067 13.8946 9.73502 14 10.0002 14Z"
        fill="#D92D20"
      />
    </svg>
  );
};
export default DangerTriangleIcon;

DangerTriangleIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  className: PropTypes.string,
  stroke: PropTypes.string,
};
