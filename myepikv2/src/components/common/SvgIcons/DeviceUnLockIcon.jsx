import PropTypes from 'prop-types';

const DeviceUnLockIcon = ({
  width = 23,
  height = 23,
  stroke = 'black',
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 23 23"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M12 15.8235V18.2941M6.66667 10.8824V5.94118C6.66667 4.63069 7.22857 3.37389 8.22876 2.44724C9.22896 1.52059 10.5855 1 12 1C13.4145 1 14.771 1.52059 15.7712 2.44724C16.7714 3.37389 17.3333 4.63069 17.3333 5.94118M5.33333 22H18.6667C19.0203 22 19.3594 21.8699 19.6095 21.6382C19.8595 21.4065 20 21.0923 20 20.7647V12.1176C20 11.79 19.8595 11.4758 19.6095 11.2442C19.3594 11.0125 19.0203 10.8824 18.6667 10.8824H5.33333C4.97971 10.8824 4.64057 11.0125 4.39052 11.2442C4.14048 11.4758 4 11.79 4 12.1176V20.7647C4 21.0923 4.14048 21.4065 4.39052 21.6382C4.64057 21.8699 4.97971 22 5.33333 22Z"
        stroke={stroke}
        strokeWidth="2"
        strokeLinejoin="round"
      />
    </svg>
  );
};
DeviceUnLockIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default DeviceUnLockIcon;
