import PropTypes from 'prop-types';
const DowwnloadEpiIcon = ({
  width = 20,
  height = 20,
  stroke = 'white',
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M10.0003 4.16699V15.8337M10.0003 15.8337L15.8337 10.0003M10.0003 15.8337L4.16699 10.0003"
        stroke={stroke}
        strokeWidth="1.67"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
DowwnloadEpiIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default DowwnloadEpiIcon;
