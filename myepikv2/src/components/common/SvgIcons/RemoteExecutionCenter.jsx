import PropTypes from 'prop-types';

const RemoteExecutionCenter = ({
  width = 40,
  height = 40,
  stroke = 'black',
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <rect width="40" height="40" rx="5" fill="#E8E8E8" />
      <path
        d="M20.11 24.4275C20.11 24.5485 20.2035 24.6475 20.3162 24.6475H25.4037C25.5165 24.6475 25.61 24.5485 25.61 24.4275V23.1075C25.61 22.9865 25.5165 22.8875 25.4037 22.8875H20.3162C20.2035 22.8875 20.11 22.9865 20.11 23.1075V24.4275ZM14.7503 24.5952L20.0302 20.1677C20.1347 20.0797 20.1347 19.9175 20.0302 19.8295L14.7503 15.4047C14.7184 15.3776 14.6794 15.3603 14.6379 15.3546C14.5965 15.349 14.5543 15.3554 14.5163 15.3731C14.4784 15.3908 14.4463 15.4189 14.4239 15.4543C14.4016 15.4896 14.3898 15.5307 14.39 15.5725V17.2968C14.39 17.3628 14.4175 17.4232 14.4697 17.4645L17.4893 20L14.4697 22.5355C14.4452 22.5561 14.4253 22.5817 14.4116 22.6107C14.3978 22.6396 14.3904 22.6712 14.39 22.7032V24.4275C14.39 24.6145 14.6073 24.7162 14.7503 24.5952ZM30.12 9H9.88C9.39325 9 9 9.39325 9 9.88V30.12C9 30.6068 9.39325 31 9.88 31H30.12C30.6068 31 31 30.6068 31 30.12V9.88C31 9.39325 30.6068 9 30.12 9ZM29.02 29.02H10.98V10.98H29.02V29.02Z"
        fill="black"
        stroke={stroke}
        strokeWidth="1.25"
        strokeLinecap="round"
      />
    </svg>
  );
};

RemoteExecutionCenter.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};

export default RemoteExecutionCenter;
