import PropTypes from 'prop-types';
const DeleteIcon = ({
  width = 24,
  height = 24,
  stroke = '#ED2B2E',
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <g filter="url(#filter0_d_2593_7312)">
        <g clipPath="url(#clip0_2593_7312)">
          <g clipPath="url(#clip1_2593_7312)">
            <path
              d="M12.0003 19.3337C16.6027 19.3337 20.3337 15.6027 20.3337 11.0003C20.3337 6.39795 16.6027 2.66699 12.0003 2.66699C7.39795 2.66699 3.66699 6.39795 3.66699 11.0003C3.66699 15.6027 7.39795 19.3337 12.0003 19.3337Z"
              stroke={stroke}
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M14.5 8.5L9.5 13.5"
              stroke={stroke}
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M9.5 8.5L14.5 13.5"
              stroke={stroke}
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </g>
        </g>
      </g>
      <defs>
        <filter
          id="filter0_d_2593_7312"
          x="0"
          y="0"
          width="24"
          height="24"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="1" />
          <feGaussianBlur stdDeviation="1" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.05 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_2593_7312"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_2593_7312"
            result="shape"
          />
        </filter>
        <clipPath id="clip0_2593_7312">
          <rect x="2" y="1" width="20" height="20" rx="8" fill="white" />
        </clipPath>
        <clipPath id="clip1_2593_7312">
          <rect
            width="20"
            height="20"
            fill="white"
            transform="translate(2 1)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
DeleteIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default DeleteIcon;
