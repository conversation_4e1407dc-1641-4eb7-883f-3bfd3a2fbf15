import PropTypes from 'prop-types';

const DeviceHubIcon = ({
  width = 16,
  height = 17,
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M22.1111 21.8889L17.2222 17V13.1133C18.64 12.6 19.6667 11.2556 19.6667 9.66667C19.6667 7.63778 18.0289 6 16 6C13.9711 6 12.3333 7.63778 12.3333 9.66667C12.3333 11.2556 13.36 12.6 14.7778 13.1133V17L9.88889 21.8889H5V28H11.1111V24.2722L16 19.1389L20.8889 24.2722V28H27V21.8889H22.1111Z"
        fill="white"
      />
    </svg>
  );
};
DeviceHubIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default DeviceHubIcon;
