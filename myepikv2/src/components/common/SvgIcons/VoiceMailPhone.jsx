import PropTypes from 'prop-types';

const VoiceMailPhoneIcon = ({
  width = 16,
  height = 16,
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 22 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M9.37673 6.72031C9.6585 7.20836 9.77136 7.77574 9.6978 8.33447C9.62424 8.89319 9.36837 9.41203 8.96988 9.81052C8.57139 10.209 8.05255 10.4649 7.49382 10.5384C6.9351 10.612 6.36771 10.4991 5.87967 10.2173C5.39163 9.93554 5.01021 9.5006 4.79455 8.97995C4.5789 8.45929 4.54108 7.88203 4.68694 7.33769C4.83281 6.79335 5.15422 6.31235 5.60132 5.96929C6.04842 5.62624 6.59622 5.4403 7.15977 5.44031H14.8398C15.4033 5.4403 15.9511 5.62624 16.3982 5.96929C16.8453 6.31235 17.1667 6.79335 17.3126 7.33769C17.4585 7.88203 17.4206 8.45929 17.205 8.97995C16.9893 9.5006 16.6079 9.93554 16.1199 10.2173C15.6318 10.4991 15.0644 10.612 14.5057 10.5384C13.947 10.4649 13.4281 10.209 13.0296 9.81052C12.6312 9.41203 12.3753 8.89319 12.3017 8.33447C12.2282 7.77574 12.341 7.20836 12.6228 6.72031H9.37673ZM5.87977 8.00031C5.87977 8.33979 6.01462 8.66536 6.25467 8.90541C6.49472 9.14546 6.82029 9.28031 7.15977 9.28031C7.49924 9.28031 7.82482 9.14546 8.06486 8.90541C8.30491 8.66536 8.43977 8.33979 8.43977 8.00031C8.43977 7.66084 8.30491 7.33526 8.06486 7.09522C7.82482 6.85517 7.49924 6.72031 7.15977 6.72031C6.82029 6.72031 6.49472 6.85517 6.25467 7.09522C6.01462 7.33526 5.87977 7.66084 5.87977 8.00031ZM14.8398 6.72031C14.5003 6.72031 14.1747 6.85517 13.9347 7.09522C13.6946 7.33526 13.5598 7.66084 13.5598 8.00031C13.5598 8.33979 13.6946 8.66536 13.9347 8.90541C14.1747 9.14546 14.5003 9.28031 14.8398 9.28031C15.1792 9.28031 15.5048 9.14546 15.7449 8.90541C15.9849 8.66536 16.1198 8.33979 16.1198 8.00031C16.1198 7.66084 15.9849 7.33526 15.7449 7.09522C15.5048 6.85517 15.1792 6.72031 14.8398 6.72031ZM3.31977 0.320312C2.64081 0.320313 1.98967 0.590026 1.50957 1.07012C1.02948 1.55021 0.759766 2.20136 0.759766 2.88031V13.1203C0.759766 13.7993 1.02948 14.4504 1.50957 14.9305C1.98967 15.4106 2.64081 15.6803 3.31977 15.6803H18.6798C19.3587 15.6803 20.0099 15.4106 20.49 14.9305C20.9701 14.4504 21.2398 13.7993 21.2398 13.1203V2.88031C21.2398 2.20136 20.9701 1.55021 20.49 1.07012C20.0099 0.590026 19.3587 0.320313 18.6798 0.320312H3.31977ZM2.03977 2.88031C2.03977 2.54084 2.17462 2.21526 2.41467 1.97522C2.65472 1.73517 2.98029 1.60031 3.31977 1.60031H18.6798C19.0192 1.60031 19.3448 1.73517 19.5849 1.97522C19.8249 2.21526 19.9598 2.54084 19.9598 2.88031V13.1203C19.9598 13.4598 19.8249 13.7854 19.5849 14.0254C19.3448 14.2655 19.0192 14.4003 18.6798 14.4003H3.31977C2.98029 14.4003 2.65472 14.2655 2.41467 14.0254C2.17462 13.7854 2.03977 13.4598 2.03977 13.1203V2.88031Z"
        fill="black"
      />
    </svg>
  );
};
export default VoiceMailPhoneIcon;

VoiceMailPhoneIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  className: PropTypes.string,
  stroke: PropTypes.string,
};
