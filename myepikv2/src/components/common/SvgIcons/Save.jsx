import PropTypes from 'prop-types';

const SaveIcon = ({
  width = 16,
  height = 16,
  stroke = 'black',
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <rect width="40" height="40" rx="5" fill="#E8E8E8" />
      <path
        d="M26.25 31.25H13.75M26.25 31.25H27.2537C28.6512 31.25 29.35 31.25 29.8837 30.9775C30.355 30.7375 30.7375 30.355 30.9775 29.885C31.25 29.3513 31.25 28.6512 31.25 27.2537V16.525C31.25 15.9625 31.25 15.6813 31.19 15.4138C31.1367 15.1751 31.0487 14.9456 30.9288 14.7325C30.7963 14.495 30.6088 14.2888 30.2413 13.88L26.7975 10.0525C26.3712 9.58 26.155 9.34 25.8962 9.1675C25.6629 9.01267 25.4051 8.89816 25.1338 8.82875C24.8288 8.75 24.5 8.75 23.8437 8.75H12.75C11.35 8.75 10.65 8.75 10.115 9.0225C9.64462 9.26218 9.26218 9.64462 9.0225 10.115C8.75 10.65 8.75 11.35 8.75 12.75V27.25C8.75 28.65 8.75 29.35 9.0225 29.8837C9.2625 30.355 9.64375 30.7375 10.115 30.9775C10.6487 31.25 11.3487 31.25 12.7463 31.25H13.75M26.25 31.25V26.4963C26.25 25.0988 26.25 24.3987 25.9775 23.865C25.7375 23.3944 25.3546 23.012 24.8837 22.7725C24.35 22.5 23.65 22.5 22.25 22.5H17.75C16.35 22.5 15.65 22.5 15.115 22.7725C14.6446 23.0122 14.2622 23.3946 14.0225 23.865C13.75 24.4 13.75 25.1 13.75 26.5V31.25M23.75 13.75H16.25"
        stroke={stroke}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
SaveIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default SaveIcon;
