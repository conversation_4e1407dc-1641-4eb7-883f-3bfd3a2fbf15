import PropTypes from 'prop-types';

const FilterResetIcon = ({
  width = 16,
  height = 16,
  fill = 'white',
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M16.875 6.75C15.8831 6.74868 14.9088 7.01143 14.052 7.51124C13.1953 8.01105 12.487 8.72992 12 9.594V6H10.5V12H16.5V10.5H13.2128C13.5467 9.84839 14.0483 9.29754 14.6659 8.90416C15.2835 8.51078 15.9947 8.28907 16.7264 8.26185C17.4582 8.23463 18.1839 8.40288 18.829 8.74929C19.4741 9.0957 20.0152 9.60776 20.3967 10.2328C20.7782 10.8577 20.9863 11.5731 20.9995 12.3052C21.0127 13.0373 20.8305 13.7597 20.4718 14.3981C20.1131 15.0364 19.5908 15.5676 18.9586 15.9371C18.3264 16.3065 17.6072 16.5008 16.875 16.5H16.5V18H16.875C18.3668 18 19.7976 17.4074 20.8525 16.3525C21.9074 15.2976 22.5 13.8668 22.5 12.375C22.5 10.8832 21.9074 9.45242 20.8525 8.39752C19.7976 7.34263 18.3668 6.75 16.875 6.75Z"
        fill={fill}
      />
      <path
        d="M19.5 4.5H3V6.87825L8.5605 12.4387L9 12.8783V19.5H12V18H13.5V19.5C13.5 19.8978 13.342 20.2794 13.0607 20.5607C12.7794 20.842 12.3978 21 12 21H9C8.60218 21 8.22064 20.842 7.93934 20.5607C7.65804 20.2794 7.5 19.8978 7.5 19.5V13.5L1.9395 7.93875C1.65818 7.65751 1.50008 7.27604 1.5 6.87825V4.5C1.5 4.10218 1.65804 3.72064 1.93934 3.43934C2.22064 3.15804 2.60218 3 3 3H19.5V4.5Z"
        fill={fill}
      />
    </svg>
  );
};
FilterResetIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  fill: PropTypes.string,
  className: PropTypes.string,
};
export default FilterResetIcon;
