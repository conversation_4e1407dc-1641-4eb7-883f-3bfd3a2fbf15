import PropTypes from 'prop-types';

const CheckCircleIcon = ({
  width = 16,
  height = 16,
  stroke = '#12B76A',
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11 0.25C5.063 0.25 0.25 5.063 0.25 11C0.25 16.937 5.063 21.75 11 21.75C16.937 21.75 21.75 16.937 21.75 11C21.75 5.063 16.937 0.25 11 0.25ZM6.53 10.97C6.38783 10.8375 6.19978 10.7654 6.00548 10.7688C5.81118 10.7723 5.62579 10.851 5.48838 10.9884C5.35097 11.1258 5.27225 11.3112 5.26883 11.5055C5.2654 11.6998 5.33752 11.8878 5.47 12.03L8.47 15.03C8.61063 15.1705 8.80125 15.2493 9 15.2493C9.19875 15.2493 9.38937 15.1705 9.53 15.03L16.53 8.03C16.6625 7.88783 16.7346 7.69978 16.7312 7.50548C16.7277 7.31118 16.649 7.12579 16.5116 6.98838C16.3742 6.85097 16.1888 6.77225 15.9945 6.76883C15.8002 6.7654 15.6122 6.83752 15.47 6.97L9 13.44L6.53 10.97Z"
        fill={stroke}
      />
    </svg>
  );
};
export default CheckCircleIcon;

CheckCircleIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  className: PropTypes.string,
  stroke: PropTypes.string,
};
