import PropTypes from 'prop-types';

const PowerEpi = ({ width = 20, height = 20, stroke = 'white' }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_2156_20456)">
        <path
          d="M14.3682 2.90968C17.8232 5.03135 19.3007 9.41718 17.6682 13.2622C15.8699 17.4988 10.9782 19.4755 6.74154 17.6772C2.50487 15.8788 0.528208 10.9863 2.32654 6.74968C2.99971 5.15669 4.15413 3.81405 5.62821 2.90968"
          stroke={stroke}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M5.83301 5.00049L5.83301 2.80049C5.83301 2.72092 5.8014 2.64462 5.74514 2.58836C5.68888 2.53209 5.61257 2.50049 5.53301 2.50049L3.33301 2.50049"
          stroke={stroke}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M10 0.833496V9.16683"
          stroke={stroke}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_2156_20456">
          <rect width={width} height={height} fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};
PowerEpi.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
};
export default PowerEpi;
