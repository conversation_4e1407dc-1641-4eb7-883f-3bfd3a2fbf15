import PropTypes from 'prop-types';

const UsersGroupIcon = ({
  width = 16,
  height = 17,
  stroke = 'white',
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 27 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M17 25V23C17 21.9391 16.5786 20.9217 15.8284 20.1716C15.0783 19.4214 14.0609 19 13 19H5C3.93913 19 2.92172 19.4214 2.17157 20.1716C1.42143 20.9217 1 21.9391 1 23V25"
        stroke={stroke}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9 15C11.2091 15 13 13.2091 13 11C13 8.79086 11.2091 7 9 7C6.79086 7 5 8.79086 5 11C5 13.2091 6.79086 15 9 15Z"
        stroke={stroke}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M23 24.9999V22.9999C22.9993 22.1136 22.7044 21.2527 22.1614 20.5522C21.6184 19.8517 20.8581 19.3515 20 19.1299"
        stroke={stroke}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16 7.12988C16.8604 7.35018 17.623 7.85058 18.1676 8.55219C18.7122 9.2538 19.0078 10.1167 19.0078 11.0049C19.0078 11.8931 18.7122 12.756 18.1676 13.4576C17.623 14.1592 16.8604 14.6596 16 14.8799"
        stroke={stroke}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M23 1V7"
        stroke={stroke}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M26 4H20"
        stroke={stroke}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
UsersGroupIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default UsersGroupIcon;
