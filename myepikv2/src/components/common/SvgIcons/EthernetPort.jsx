import PropTypes from 'prop-types';

const EthernetPortIcon = ({
  width = 16,
  height = 16,
  stroke = 'black',
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 22 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M4.89246 5.12098V6.09067M8.77125 5.12098V6.09067M12.65 5.12098V6.09067M16.5288 5.12098V6.09067M13.6197 16.7573L16.5288 13.8482H18.4682C18.9826 13.8482 19.4759 13.6439 19.8396 13.2802C20.2033 12.9165 20.4076 12.4232 20.4076 11.9089V3.18158C20.4076 2.66722 20.2033 2.17393 19.8396 1.81022C19.4759 1.44652 18.9826 1.24219 18.4682 1.24219H2.95307C2.43871 1.24219 1.94541 1.44652 1.58171 1.81022C1.218 2.17393 1.01367 2.66722 1.01367 3.18158V11.9089C1.01367 12.4232 1.218 12.9165 1.58171 13.2802C1.94541 13.6439 2.43871 13.8482 2.95307 13.8482H4.89246L7.80155 16.7573H13.6197Z"
        stroke={stroke}
        strokeWidth="1.45455"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default EthernetPortIcon;

EthernetPortIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  className: PropTypes.string,
  stroke: PropTypes.string,
};
