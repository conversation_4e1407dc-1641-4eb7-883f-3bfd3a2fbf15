import PropTypes from 'prop-types';

const DeviceMeetingIcon = ({
  width = 16,
  height = 16,
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M6.46988 8.05325C6.68374 7.31833 7.14061 6.67107 7.77084 6.21011C8.40107 5.74916 9.17011 5.49979 9.96075 5.5H22.0465C22.8371 5.49979 23.6062 5.74916 24.2364 6.21011C24.8666 6.67107 25.3235 7.31833 25.5374 8.05325L28.5461 18.3748C28.7038 18.9163 28.7294 19.4857 28.6208 20.0383C28.5123 20.591 28.2725 21.1118 27.9204 21.5599C27.5683 22.0081 27.1134 22.3713 26.5914 22.6212C26.0695 22.8711 25.4947 23.0007 24.9121 23H14.1911V21.25H24.9139C25.2161 21.2501 25.5143 21.1825 25.785 21.0527C26.0558 20.9229 26.2917 20.7343 26.4743 20.5017C26.6569 20.2691 26.7812 19.9989 26.8375 19.7121C26.8938 19.4254 26.8806 19.13 26.7989 18.849L23.7901 8.52575C23.6832 8.15903 23.4552 7.83602 23.1409 7.60576C22.8265 7.37551 22.4429 7.2506 22.0483 7.25H9.96075C9.56531 7.24995 9.1807 7.37476 8.86557 7.60541C8.55044 7.83605 8.32208 8.15988 8.21531 8.5275L7.56825 10.75H6.03125C5.91162 10.75 5.79442 10.7541 5.67963 10.7622L6.46988 8.05325ZM14.1005 26.5H22.3492C22.5895 26.5 22.82 26.4078 22.99 26.2437C23.16 26.0796 23.2554 25.8571 23.2554 25.625C23.2554 25.3929 23.16 25.1704 22.99 25.0063C22.82 24.8422 22.5895 24.75 22.3492 24.75H14.1929V25.625C14.1917 25.9248 14.1597 26.2165 14.1005 26.5ZM7.84556 18.625C8.20609 18.625 8.55185 18.4867 8.80679 18.2406C9.06172 17.9944 9.20494 17.6606 9.20494 17.3125C9.20494 16.9644 9.06172 16.6306 8.80679 16.3844C8.55185 16.1383 8.20609 16 7.84556 16C7.48503 16 7.13927 16.1383 6.88434 16.3844C6.62941 16.6306 6.48619 16.9644 6.48619 17.3125C6.48619 17.6606 6.62941 17.9944 6.88434 18.2406C7.13927 18.4867 7.48503 18.625 7.84556 18.625ZM3.3125 15.125C3.3125 14.4288 3.59894 13.7611 4.1088 13.2688C4.61867 12.7766 5.31019 12.5 6.03125 12.5H9.65806C10.3791 12.5 11.0706 12.7766 11.5805 13.2688C12.0904 13.7611 12.3768 14.4288 12.3768 15.125V25.625C12.3768 26.3212 12.0904 26.9889 11.5805 27.4812C11.0706 27.9734 10.3791 28.25 9.65806 28.25H6.03306C5.67588 28.2502 5.32215 28.1825 4.99209 28.0507C4.66202 27.9189 4.3621 27.7256 4.10944 27.4818C3.85679 27.238 3.65637 26.9485 3.51963 26.6299C3.38288 26.3114 3.3125 25.9699 3.3125 25.625V15.125ZM6.03125 14.25C5.7909 14.25 5.56039 14.3422 5.39043 14.5063C5.22048 14.6704 5.125 14.8929 5.125 15.125V25.625C5.125 25.8571 5.22048 26.0796 5.39043 26.2437C5.56039 26.4078 5.7909 26.5 6.03125 26.5H9.65806C9.89841 26.5 10.1289 26.4078 10.2989 26.2437C10.4688 26.0796 10.5643 25.8571 10.5643 25.625V15.125C10.5643 14.8929 10.4688 14.6704 10.2989 14.5063C10.1289 14.3422 9.89841 14.25 9.65806 14.25H6.03125Z"
        fill="white"
      />
    </svg>
  );
};
DeviceMeetingIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default DeviceMeetingIcon;
