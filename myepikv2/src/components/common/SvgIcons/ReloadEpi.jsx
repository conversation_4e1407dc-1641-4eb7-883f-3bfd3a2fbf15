import PropTypes from 'prop-types';
const ReloadEpi = ({ width = 16, height = 16, stroke = 'white', ...props }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 20 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M0.833008 2.33331V7.33332M0.833008 7.33332H5.83301M0.833008 7.33332L4.69967 3.69998C5.5953 2.80391 6.70332 2.14932 7.92035 1.79729C9.13738 1.44527 10.4238 1.40727 11.6594 1.68686C12.8951 1.96645 14.0398 2.55451 14.9868 3.39616C15.9337 4.23782 16.652 5.30564 17.0747 6.49998M19.1663 15.6666V10.6666M19.1663 10.6666H14.1663M19.1663 10.6666L15.2997 14.3C14.4041 15.1961 13.296 15.8506 12.079 16.2027C10.862 16.5547 9.5756 16.5927 8.33991 16.3131C7.10423 16.0335 5.95951 15.4455 5.01256 14.6038C4.06562 13.7621 3.34731 12.6943 2.92467 11.5"
        stroke={stroke}
        strokeWidth="1.67"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
ReloadEpi.propTypes = {
  stroke: PropTypes.string,
  width: PropTypes.number,
  height: PropTypes.number,
  className: PropTypes.string,
};
export default ReloadEpi;
