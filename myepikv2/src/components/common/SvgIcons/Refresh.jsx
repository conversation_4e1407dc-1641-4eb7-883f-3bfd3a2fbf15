import PropTypes from 'prop-types';

const Refresh = ({
  width = 40,
  height = 40,
  stroke = '#4D9CD3',
  strokeWidth = 1.5,
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 19 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <g clipPath="url(#clip0_2925_33974)">
        <path
          d="M0.875 3.75V8.25H5.375"
          stroke={stroke}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M17.375 15.75V11.25H12.875"
          stroke={stroke}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M15.4925 7.49823C15.1121 6.42331 14.4657 5.46228 13.6134 4.70479C12.7611 3.9473 11.7309 3.41805 10.6188 3.16642C9.50667 2.91479 8.34893 2.94898 7.25361 3.26581C6.15828 3.58263 5.16106 4.17176 4.355 4.97823L0.875 8.24823M17.375 11.2482L13.895 14.5182C13.0889 15.3247 12.0917 15.9138 10.9964 16.2306C9.90107 16.5475 8.74333 16.5817 7.63121 16.33C6.5191 16.0784 5.48885 15.5491 4.6366 14.7917C3.78435 14.0342 3.13788 13.0731 2.7575 11.9982"
          stroke={stroke}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_2925_33974">
          <rect
            width="18"
            height="18"
            fill="white"
            transform="translate(0.125 0.75)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

Refresh.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  strokeWidth: PropTypes.number,
  className: PropTypes.string,
};

export default Refresh;
