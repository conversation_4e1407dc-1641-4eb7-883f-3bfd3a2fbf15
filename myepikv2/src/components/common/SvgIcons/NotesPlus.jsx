import PropTypes from 'prop-types';

const NotesPlusIcon = ({
  width = 16,
  height = 16,
  stroke = '#3E4042',
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M12.0001 6.5C13.4155 6.50002 14.7853 7.0004 15.8674 7.9127C16.9495 8.825 17.6743 10.0905 17.9136 11.4855C18.1529 12.8804 17.8914 14.3151 17.1752 15.5359C16.4591 16.7568 15.3344 17.6851 14 18.1569C12.6655 18.6287 11.2073 18.6135 9.88295 18.1141C8.55861 17.6147 7.45346 16.6632 6.76282 15.4278C6.07217 14.1923 5.84051 12.7525 6.10876 11.3628C6.37702 9.97309 7.12793 8.72295 8.22877 7.83333"
        stroke={stroke}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6 7.5H8.66667V10.1667"
        stroke={stroke}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
NotesPlusIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default NotesPlusIcon;
