import PropTypes from 'prop-types';

const CodeDownloadIcon = ({
  width = 16,
  height = 16,
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 31 31"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M9.38881 22.3197L1.24023 15.4997L9.38881 8.67969M21.6117 22.3197L29.7602 15.4997L21.6117 8.67969M11.4259 17.4543L15.5002 21.3454L19.5745 17.4543M15.5002 9.65397V20.3729"
        stroke="#475467"
        strokeWidth="1.88"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
export default CodeDownloadIcon;

CodeDownloadIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  className: PropTypes.string,
  stroke: PropTypes.string,
};
