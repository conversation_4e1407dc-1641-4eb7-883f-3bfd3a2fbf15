import PropTypes from 'prop-types';

const DocumentAddIcon = ({
  width = 16,
  height = 16,
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 25 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <g clipPath="url(#clip0_2375_16164)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M11.444 1.25H13.556C15.394 1.25 16.85 1.25 17.989 1.403C19.161 1.561 20.11 1.893 20.859 2.641C20.9915 2.78317 21.0636 2.97122 21.0602 3.16552C21.0567 3.35982 20.978 3.54521 20.8406 3.68262C20.7032 3.82003 20.5178 3.89875 20.3235 3.90217C20.1292 3.9056 19.9412 3.83348 19.799 3.701C19.375 3.279 18.795 3.025 17.789 2.89C16.762 2.752 15.407 2.75 13.5 2.75H11.5C9.593 2.75 8.239 2.752 7.21 2.89C6.205 3.025 5.625 3.279 5.202 3.702C4.779 4.125 4.525 4.705 4.39 5.71C4.252 6.739 4.25 8.093 4.25 10V14C4.25 15.907 4.252 17.262 4.39 18.29C4.525 19.295 4.779 19.875 5.202 20.298C5.625 20.721 6.205 20.975 7.211 21.11C8.239 21.248 9.593 21.25 11.5 21.25H13.5C15.407 21.25 16.762 21.248 17.79 21.11C18.795 20.975 19.375 20.721 19.798 20.298C20.494 19.602 20.705 18.521 20.741 15.989C20.7439 15.7901 20.8257 15.6005 20.9684 15.4619C21.0391 15.3933 21.1226 15.3392 21.2141 15.3029C21.3057 15.2665 21.4035 15.2486 21.502 15.25C21.6005 15.2514 21.6977 15.2723 21.7882 15.3113C21.8786 15.3503 21.9605 15.4068 22.0291 15.4774C22.0977 15.5481 22.1518 15.6316 22.1881 15.7232C22.2245 15.8147 22.2424 15.9125 22.241 16.011C22.206 18.438 22.049 20.169 20.859 21.359C20.11 22.107 19.161 22.439 17.989 22.597C16.849 22.75 15.394 22.75 13.556 22.75H11.444C9.606 22.75 8.15 22.75 7.011 22.597C5.839 22.439 4.89 22.107 4.141 21.359C3.393 20.61 3.061 19.661 2.903 18.489C2.75 17.349 2.75 15.894 2.75 14.056V9.944C2.75 8.106 2.75 6.65 2.903 5.511C3.061 4.339 3.393 3.39 4.141 2.641C4.89 1.893 5.839 1.561 7.011 1.403C8.151 1.25 9.606 1.25 11.444 1.25ZM7.75 9C7.75 8.80109 7.82902 8.61032 7.96967 8.46967C8.11032 8.32902 8.30109 8.25 8.5 8.25H14C14.1989 8.25 14.3897 8.32902 14.5303 8.46967C14.671 8.61032 14.75 8.80109 14.75 9C14.75 9.19891 14.671 9.38968 14.5303 9.53033C14.3897 9.67098 14.1989 9.75 14 9.75H8.5C8.30109 9.75 8.11032 9.67098 7.96967 9.53033C7.82902 9.38968 7.75 9.19891 7.75 9ZM7.75 13C7.75 12.8011 7.82902 12.6103 7.96967 12.4697C8.11032 12.329 8.30109 12.25 8.5 12.25H11C11.1989 12.25 11.3897 12.329 11.5303 12.4697C11.671 12.6103 11.75 12.8011 11.75 13C11.75 13.1989 11.671 13.3897 11.5303 13.5303C11.3897 13.671 11.1989 13.75 11 13.75H8.5C8.30109 13.75 8.11032 13.671 7.96967 13.5303C7.82902 13.3897 7.75 13.1989 7.75 13ZM7.75 17C7.75 16.8011 7.82902 16.6103 7.96967 16.4697C8.11032 16.329 8.30109 16.25 8.5 16.25H10C10.1989 16.25 10.3897 16.329 10.5303 16.4697C10.671 16.6103 10.75 16.8011 10.75 17C10.75 17.1989 10.671 17.3897 10.5303 17.5303C10.3897 17.671 10.1989 17.75 10 17.75H8.5C8.30109 17.75 8.11032 17.671 7.96967 17.5303C7.82902 17.3897 7.75 17.1989 7.75 17Z"
          fill="black"
        />
        <path
          d="M18.9205 12.381L14.7069 11.3501C14.4571 11.2916 14.3115 11.1394 14.2702 10.8933C14.2288 10.6472 14.3125 10.4543 14.5212 10.3145L22.3431 5.01428C22.3793 4.98638 22.4195 4.96735 22.4637 4.95719C22.5079 4.94656 22.5698 4.94629 22.6494 4.95639C22.7788 4.97283 22.874 5.04058 22.9349 5.15964C22.9958 5.27871 22.9819 5.39401 22.8932 5.50555L19.6502 9.6202L23.8911 10.6545C24.1409 10.713 24.2892 10.8632 24.3359 11.1052C24.3826 11.3472 24.3014 11.5383 24.0921 11.6785L16.2684 16.9928C16.2323 17.0202 16.1924 17.0369 16.1488 17.0428C16.1052 17.0488 16.0436 17.0467 15.964 17.0366C15.8345 17.0201 15.7439 16.9532 15.6921 16.8358C15.6402 16.7183 15.6536 16.6075 15.7321 16.5033L18.9205 12.381ZM17.6978 15.105L23.431 11.2677L18.4159 10.0634L20.9423 6.86774L15.1835 10.7161L20.176 11.9548L17.6978 15.105Z"
          fill="black"
        />
      </g>
      <defs>
        <clipPath id="clip0_2375_16164">
          <rect
            width="24"
            height="24"
            fill="white"
            transform="translate(0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
export default DocumentAddIcon;

DocumentAddIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  className: PropTypes.string,
  stroke: PropTypes.string,
};
