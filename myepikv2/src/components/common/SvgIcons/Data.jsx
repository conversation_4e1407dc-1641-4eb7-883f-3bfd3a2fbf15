import PropTypes from 'prop-types';

const DataIcon = ({
  width = 16,
  height = 16,
  stroke = 'white',
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M16.0007 12C21.8917 12 26.6673 10.2091 26.6673 8C26.6673 5.79086 21.8917 4 16.0007 4C10.1096 4 5.33398 5.79086 5.33398 8C5.33398 10.2091 10.1096 12 16.0007 12Z"
        stroke={stroke}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.04998 16C6.36065 16.7067 5.33398 17.6427 5.33398 18.6667C5.33398 20.876 10.11 22.6667 16.0007 22.6667C21.8913 22.6667 26.6673 20.876 26.6673 18.6667C26.6673 17.6427 25.6407 16.7067 23.9513 16"
        stroke={stroke}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.33398 8V13.3333C5.33398 15.5427 10.11 17.3333 16.0007 17.3333C21.8913 17.3333 26.6673 15.5427 26.6673 13.3333V8M5.33398 18.6667V24C5.33398 26.2093 10.11 28 16.0007 28C21.8913 28 26.6673 26.2093 26.6673 24V18.6667"
        stroke={stroke}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
DataIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default DataIcon;
