import PropTypes from 'prop-types';

const TimerAdd = ({
  width = 20,
  height = 20,
  stroke = 'black',
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M9.7193 14.3447V11.45M9.7193 11.45V8.55524M9.7193 11.45H6.82456M9.7193 11.45H12.614M18.4035 4.69559L16.4737 2.76576M7.78947 0.835938H11.6491M9.7193 19.1693C7.67201 19.1693 5.70858 18.356 4.26093 16.9083C2.81328 15.4607 2 13.4973 2 11.45C2 9.40269 2.81328 7.43925 4.26093 5.9916C5.70858 4.54396 7.67201 3.73067 9.7193 3.73067C11.7666 3.73067 13.73 4.54396 15.1777 5.9916C16.6253 7.43925 17.4386 9.40269 17.4386 11.45C17.4386 13.4973 16.6253 15.4607 15.1777 16.9083C13.73 18.356 11.7666 19.1693 9.7193 19.1693Z"
        stroke={stroke}
        strokeWidth="1.25"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
TimerAdd.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default TimerAdd;
