import PropTypes from 'prop-types';

const ExpandIcon = ({
  width = 16,
  height = 16,
  stroke = '#4D9CD3',
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <rect
        width={32}
        height={32}
        rx="5.33333"
        fill={stroke}
        fillOpacity="0.2"
      />
      <path
        d="M5.33333 5.33333L12 12M12 12L12 6.66667M12 12L6.66667 12"
        stroke={stroke}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M26.6667 26.6667L20 20M20 20L20 25.3333M20 20L25.3333 20"
        stroke={stroke}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M26.6667 5.33333L20 12M20 12L25.3333 12M20 12L20 6.66667"
        stroke={stroke}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.33333 26.6667L12 20M12 20L6.66667 20M12 20L12 25.3333"
        stroke={stroke}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
ExpandIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default ExpandIcon;
