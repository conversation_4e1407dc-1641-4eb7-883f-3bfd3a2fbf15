import PropTypes from 'prop-types';
const ProvisionEpi = ({
  width = 24,
  height = 24,
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M17.3153 4.44273C17.7842 3.84963 17.3423 3.00003 16.5629 3.00003H10.5185C10.3549 2.99883 10.1936 3.03976 10.0504 3.11889C9.90709 3.19801 9.78658 3.31267 9.70043 3.45183L5.12662 11.0622C4.76572 11.6616 5.21932 12.4095 5.94382 12.4095H9.02993L6.12292 19.6681C5.70262 20.5861 6.83842 21.4258 7.60162 20.7616L19.3997 9.59794H13.2356L17.3153 4.44273Z"
        stroke="white" // Set stroke color to white
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
ProvisionEpi.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};

export default ProvisionEpi;
