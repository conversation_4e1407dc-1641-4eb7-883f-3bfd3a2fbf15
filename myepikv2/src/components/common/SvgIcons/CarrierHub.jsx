import PropTypes from 'prop-types';

const CarrierHubIcon = ({
  width = 16,
  height = 16,
  stroke = 'white',
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M9.76717 18.849L12.0002 14L14.2332 18.849C14.8802 20.255 15.2042 20.958 14.8612 21.466L14.8322 21.506C14.4662 22 13.6442 22 12.0002 22C10.3562 22 9.53417 22 9.16817 21.507L9.13817 21.466C8.79617 20.958 9.11917 20.255 9.76717 18.849Z"
        stroke={stroke}
        strokeWidth="1.75"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12 14C13.1046 14 14 13.1046 14 12C14 10.8954 13.1046 10 12 10C10.8954 10 10 10.8954 10 12C10 13.1046 10.8954 14 12 14Z"
        stroke={stroke}
        strokeWidth="1.75"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M4.00001 18.001C2.69898 16.2711 1.9969 14.1645 2.00001 12C2.00001 6.477 6.47701 2 12 2C17.523 2 22 6.477 22 12C22 14.252 21.256 16.33 20 18.001"
        stroke={stroke}
        strokeWidth="1.75"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.528 15.9999C6.75579 15.1366 6.24997 14.0682 6.07158 12.9238C5.8932 11.7793 6.04989 10.6077 6.52274 9.55036C6.99558 8.493 7.76437 7.5951 8.73629 6.96506C9.70822 6.33501 10.8417 5.99976 12 5.99976C13.1583 5.99976 14.2918 6.33501 15.2637 6.96506C16.2356 7.5951 17.0044 8.493 17.4773 9.55036C17.9501 10.6077 18.1068 11.7793 17.9284 12.9238C17.75 14.0682 17.2442 15.1366 16.472 15.9999"
        stroke={stroke}
        strokeWidth="1.75"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
export default CarrierHubIcon;

CarrierHubIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  className: PropTypes.string,
  stroke: PropTypes.string,
};
