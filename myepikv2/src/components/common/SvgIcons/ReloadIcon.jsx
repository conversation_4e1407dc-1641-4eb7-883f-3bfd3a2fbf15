import PropTypes from 'prop-types';

const ReloadIcon = ({ width = 16, height = 16, className = '', ...props }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <rect width="40" height="40" rx="5" fill="#E8E8E8" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.66281 19C8.75281 12.8 13.8453 7.81251 20.0991 7.81251C22.057 7.80948 23.9829 8.30855 25.693 9.26204C27.403 10.2155 28.8399 11.5916 29.8666 13.2588C29.9893 13.4702 30.0247 13.7212 29.965 13.9583C29.9054 14.1954 29.7555 14.3998 29.5473 14.528C29.3391 14.6561 29.089 14.6979 28.8505 14.6443C28.6119 14.5907 28.4037 14.4461 28.2703 14.2413C27.4114 12.8468 26.2092 11.6959 24.7786 10.8986C23.348 10.1013 21.7368 9.68435 20.0991 9.68751C14.8678 9.68751 10.6303 13.8475 10.5391 18.9975L11.0391 18.5013C11.2156 18.3261 11.4545 18.2282 11.7032 18.2291C11.9519 18.23 12.1901 18.3297 12.3653 18.5063C12.5405 18.6828 12.6384 18.9217 12.6375 19.1704C12.6365 19.4191 12.5368 19.6573 12.3603 19.8325L10.2603 21.915C10.0847 22.0891 9.84753 22.1867 9.60031 22.1867C9.35309 22.1867 9.11588 22.0891 8.94031 21.915L6.84031 19.8325C6.75282 19.7458 6.68325 19.6428 6.63559 19.5292C6.58792 19.4157 6.56309 19.2938 6.56251 19.1707C6.56193 19.0475 6.58561 18.9255 6.6322 18.8115C6.6788 18.6975 6.74739 18.5938 6.83406 18.5063C6.92073 18.4188 7.02379 18.3492 7.13735 18.3015C7.2509 18.2539 7.37274 18.229 7.49589 18.2285C7.61905 18.2279 7.74111 18.2516 7.85511 18.2982C7.96911 18.3448 8.07282 18.4133 8.16031 18.5L8.66281 19ZM29.7328 18.0838C29.9083 17.9101 30.1453 17.8127 30.3922 17.8127C30.6391 17.8127 30.876 17.9101 31.0516 18.0838L33.1591 20.1663C33.249 20.2522 33.3209 20.3552 33.3706 20.4693C33.4203 20.5833 33.4468 20.7061 33.4486 20.8305C33.4503 20.9549 33.4273 21.0784 33.3809 21.1938C33.3345 21.3092 33.2655 21.4142 33.1781 21.5027C33.0907 21.5912 32.9865 21.6614 32.8717 21.7092C32.7569 21.7571 32.6337 21.7816 32.5093 21.7814C32.3849 21.7811 32.2618 21.7561 32.1471 21.7078C32.0325 21.6595 31.9286 21.5889 31.8416 21.5L31.3291 20.9938C31.2416 27.2 26.1278 32.1875 19.8541 32.1875C17.8913 32.1919 15.96 31.6938 14.2442 30.7407C12.5284 29.7876 11.085 28.4112 10.0516 26.7425C9.98688 26.6377 9.94349 26.5212 9.92384 26.3996C9.9042 26.278 9.9087 26.1537 9.93708 26.0339C9.96546 25.9141 10.0172 25.801 10.0892 25.7011C10.1613 25.6013 10.2524 25.5166 10.3572 25.4519C10.462 25.3872 10.5785 25.3438 10.7001 25.3242C10.8217 25.3045 10.9459 25.309 11.0658 25.3374C11.1856 25.3658 11.2987 25.4175 11.3986 25.4896C11.4984 25.5617 11.5831 25.6527 11.6478 25.7575C12.5132 27.154 13.7217 28.3057 15.1581 29.1031C16.5945 29.9004 18.2112 30.3167 19.8541 30.3125C25.1116 30.3125 29.3666 26.1463 29.4541 20.9963L28.9441 21.5C28.7672 21.6749 28.5281 21.7723 28.2794 21.7709C28.0307 21.7695 27.7927 21.6694 27.6178 21.4925C27.4429 21.3156 27.3455 21.0766 27.3469 20.8278C27.3483 20.5791 27.4484 20.3411 27.6253 20.1663L29.7328 18.0838Z"
        fill="black"
      />
    </svg>
  );
};
ReloadIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default ReloadIcon;
