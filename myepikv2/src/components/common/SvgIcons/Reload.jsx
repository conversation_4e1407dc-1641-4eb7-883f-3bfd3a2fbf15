import PropTypes from 'prop-types';

const Reload = ({
  width = 40,
  height = 40,
  stroke = 'black',
  strokeWidth = 2,
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <rect width="40" height="40" rx="4" fill="#E8E8E8" />
      <path
        d="M31 12V18H25"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M28.4899 23C27.8399 24.8399 26.6094 26.4187 24.984 27.4985C23.3586 28.5783 21.4263 29.1006 19.4783 28.9866C17.5303 28.8726 15.672 28.1286 14.1836 26.8667C12.6952 25.6047 11.6573 23.8932 11.2262 21.9901C10.7952 20.0869 10.9944 18.0952 11.7937 16.3151C12.5931 14.535 13.9494 13.0629 15.6582 12.1207C17.367 11.1784 19.3358 10.8171 21.2678 11.0912C23.1999 11.3652 24.9905 12.2598 26.3699 13.64L30.9999 18"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

Reload.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  strokeWidth: PropTypes.number,
  className: PropTypes.string,
};

export default Reload;
