import PropTypes from 'prop-types';

const EditTwoIcon = ({ width = 16, height = 16, className = '', ...props }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <rect width="32" height="32" rx="3.2" fill="#3B5AA9" />
      <path
        d="M9.18784 22.428C9.24141 22.428 9.29498 22.4227 9.34855 22.4146L13.8539 21.6244C13.9075 21.6137 13.9584 21.5896 13.9959 21.5494L25.3503 10.195C25.3752 10.1702 25.3949 10.1408 25.4083 10.1084C25.4217 10.076 25.4287 10.0412 25.4287 10.0061C25.4287 9.97106 25.4217 9.93632 25.4083 9.90392C25.3949 9.87151 25.3752 9.84208 25.3503 9.8173L20.8986 5.36283C20.8477 5.31194 20.7807 5.28516 20.7084 5.28516C20.636 5.28516 20.5691 5.31194 20.5182 5.36283L9.16373 16.7173C9.12355 16.7575 9.09944 16.8057 9.08873 16.8593L8.29855 21.3646C8.27249 21.5081 8.2818 21.6558 8.32568 21.7949C8.36955 21.934 8.44666 22.0603 8.55033 22.1628C8.72712 22.3343 8.94944 22.428 9.18784 22.428ZM10.9932 17.7566L20.7084 8.04408L22.6718 10.0075L12.9566 19.72L10.5753 20.1405L10.9932 17.7566ZM25.8566 24.678H6.1423C5.66819 24.678 5.28516 25.061 5.28516 25.5352V26.4994C5.28516 26.6173 5.38158 26.7137 5.49944 26.7137H26.4994C26.6173 26.7137 26.7137 26.6173 26.7137 26.4994V25.5352C26.7137 25.061 26.3307 24.678 25.8566 24.678Z"
        fill="white"
      />
    </svg>
  );
};

// Add PropTypes validation
EditTwoIcon.propTypes = {
  /** Width of the icon */
  width: PropTypes.number, // `width` should be a number
  /** Height of the icon */
  height: PropTypes.number, // `height` should be a number
  /** CSS class for custom styles */
  className: PropTypes.string, // `className` should be a string
};

export default EditTwoIcon;
