import PropTypes from 'prop-types';

const NotificationIcon = ({
  width = 16,
  height = 16,
  stroke = '#344054',
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M9.83333 1.66675L1.5 11.6667H9L8.16667 18.3334L16.5 8.33342H9L9.83333 1.66675Z"
        stroke={stroke}
        strokeWidth="1.67"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
NotificationIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default NotificationIcon;
