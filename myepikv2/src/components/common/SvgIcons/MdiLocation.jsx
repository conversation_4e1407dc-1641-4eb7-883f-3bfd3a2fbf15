import PropTypes from 'prop-types';

const MdiLocationIcon = ({
  width = 16,
  height = 16,
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M15.834 15.35C14.9341 15.35 14.0712 15.0076 13.4349 14.3981C12.7986 13.7886 12.4411 12.962 12.4411 12.1C12.4411 11.238 12.7986 10.4114 13.4349 9.8019C14.0712 9.19241 14.9341 8.85 15.834 8.85C16.7338 8.85 17.5968 9.19241 18.2331 9.8019C18.8694 10.4114 19.2268 11.238 19.2268 12.1C19.2268 12.5268 19.1391 12.9494 18.9686 13.3437C18.7981 13.738 18.5482 14.0963 18.2331 14.3981C17.918 14.6999 17.544 14.9393 17.1324 15.1026C16.7207 15.2659 16.2795 15.35 15.834 15.35ZM15.834 3C13.3144 3 10.8981 3.95875 9.11647 5.66533C7.33487 7.37191 6.33398 9.68653 6.33398 12.1C6.33398 18.925 15.834 29 15.834 29C15.834 29 25.334 18.925 25.334 12.1C25.334 9.68653 24.3331 7.37191 22.5515 5.66533C20.7699 3.95875 18.3535 3 15.834 3Z"
        fill="white"
      />
    </svg>
  );
};
MdiLocationIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default MdiLocationIcon;
