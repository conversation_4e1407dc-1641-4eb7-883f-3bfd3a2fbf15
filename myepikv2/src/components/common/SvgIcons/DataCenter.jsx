import PropTypes from 'prop-types';

const DataCenterIcon = ({
  width = 16,
  height = 16,
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M26.5 10.75H22.125V7.25C22.125 6.78587 21.9406 6.34075 21.6124 6.01256C21.2842 5.68437 20.8391 5.5 20.375 5.5H11.625C11.1609 5.5 10.7158 5.68437 10.3876 6.01256C10.0594 6.34075 9.875 6.78587 9.875 7.25V10.75H5.5C5.03587 10.75 4.59075 10.9344 4.26256 11.2626C3.93437 11.5908 3.75 12.0359 3.75 12.5V26.5C3.75 26.9641 3.93437 27.4092 4.26256 27.7374C4.59075 28.0656 5.03587 28.25 5.5 28.25H26.5C26.9641 28.25 27.4092 28.0656 27.7374 27.7374C28.0656 27.4092 28.25 26.9641 28.25 26.5V12.5C28.25 12.0359 28.0656 11.5908 27.7374 11.2626C27.4092 10.9344 26.9641 10.75 26.5 10.75ZM5.5 26.5V12.5H9.875V14.25H8.125V16H9.875V17.75H8.125V19.5H9.875V21.25H8.125V23H9.875V26.5H5.5ZM20.375 26.5H11.625V7.25H20.375V26.5ZM26.5 26.5H22.125V23H23.875V21.25H22.125V19.5H23.875V17.75H22.125V16H23.875V14.25H22.125V12.5H26.5V26.5Z"
        fill="white"
      />
      <path
        d="M14.25 9H17.75V10.75H14.25V9ZM14.25 12.5H17.75V14.25H14.25V12.5ZM14.25 16H17.75V17.75H14.25V16Z"
        fill="white"
      />
    </svg>
  );
};
DataCenterIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default DataCenterIcon;
