import PropTypes from 'prop-types';

const SwapCalls = ({ width = 16, height = 16, className = '', ...props }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 28 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M5.14578 23.3333L0 18.2791L1.10133 17.1806L4.368 20.3891V8.61227C4.368 7.18625 4.87252 5.9777 5.88156 4.98662C6.89059 3.99554 8.12104 3.5 9.57289 3.5C11.0247 3.5 12.2552 3.99554 13.2642 4.98662C14.2733 5.97872 14.7778 7.18778 14.7778 8.61379V18.2211C14.7778 19.2183 15.1324 20.0647 15.8418 20.7604C16.5501 21.4571 17.4119 21.8055 18.4271 21.8055C19.4424 21.8055 20.3047 21.4571 21.014 20.7604C21.7233 20.0637 22.0775 19.2172 22.0764 18.2211V6.44421L18.8098 9.65275L17.71 8.55421L22.8542 3.5L28 8.55421L26.8987 9.65428L23.632 6.44574V18.2211C23.632 19.6471 23.1275 20.8556 22.1184 21.8467C21.1094 22.8378 19.879 23.3333 18.4271 23.3333C16.9753 23.3333 15.7448 22.8378 14.7358 21.8467C13.7267 20.8556 13.2222 19.6471 13.2222 18.2211V8.61379C13.2222 7.61558 12.8676 6.76863 12.1582 6.07294C11.4499 5.37623 10.5881 5.02787 9.57289 5.02787C8.55763 5.02787 7.69533 5.37623 6.986 6.07294C6.27667 6.76965 5.92252 7.6166 5.92356 8.61379V20.3891L9.19022 17.1806L10.2916 18.2807L5.14578 23.3333Z"
        fill="#475467"
      />
    </svg>
  );
};
export default SwapCalls;

SwapCalls.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  className: PropTypes.string,
  stroke: PropTypes.string,
};
