import PropTypes from 'prop-types'; // Import PropTypes for validation
const CircleTrue = ({ width = 16, height = 16, className = '', ...props }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 29 29"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M14.25 2.625C7.48516 2.625 2 8.11016 2 14.875C2 21.6398 7.48516 27.125 14.25 27.125C21.0148 27.125 26.5 21.6398 26.5 14.875C26.5 8.11016 21.0148 2.625 14.25 2.625ZM14.25 25.0469C8.63359 25.0469 4.07812 20.4914 4.07812 14.875C4.07812 9.25859 8.63359 4.70312 14.25 4.70312C19.8664 4.70312 24.4219 9.25859 24.4219 14.875C24.4219 20.4914 19.8664 25.0469 14.25 25.0469Z"
        fill="#7CC247" // Green circle fill
      />
      <path
        d="M14.25 4.70312C8.63359 4.70312 4.07812 9.25859 4.07812 14.875C4.07812 20.4914 8.63359 25.0469 14.25 25.0469C19.8664 25.0469 24.4219 20.4914 24.4219 14.875C24.4219 9.25859 19.8664 4.70312 14.25 4.70312ZM19.5383 10.8746L13.7797 18.859C13.6992 18.9713 13.5931 19.0629 13.4702 19.126C13.3473 19.1891 13.211 19.2221 13.0729 19.2221C12.9347 19.2221 12.7985 19.1891 12.6755 19.126C12.5526 19.0629 12.4465 18.9713 12.366 18.859L8.95898 14.134C8.85508 13.9891 8.95898 13.7867 9.13672 13.7867H10.4191C10.7008 13.7867 10.9633 13.9234 11.1273 14.1504L13.0742 16.852L17.3727 10.891C17.5367 10.6613 17.802 10.5273 18.0809 10.5273H19.3633C19.541 10.5273 19.6449 10.7297 19.5383 10.8746Z"
        fill="#7CC247" // Green arrow fill
        fillOpacity="0.15"
      />
      <path
        d="M19.3631 10.5273H18.0807C17.8018 10.5273 17.5366 10.6613 17.3725 10.891L13.0741 16.852L11.1272 14.1504C10.9631 13.9234 10.7006 13.7867 10.419 13.7867H9.13658C8.95884 13.7867 8.85494 13.9891 8.95884 14.134L12.3659 18.859C12.4464 18.9713 12.5525 19.0629 12.6754 19.126C12.7983 19.1891 12.9345 19.2221 13.0727 19.2221C13.2109 19.2221 13.3471 19.1891 13.47 19.126C13.593 19.0629 13.6991 18.9713 13.7795 18.859L19.5381 10.8746C19.6448 10.7297 19.5409 10.5273 19.3631 10.5273Z"
        fill="#7CC247" // Green arrow outline
      />
    </svg>
  );
};

// Add propTypes to validate the props
CircleTrue.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  className: PropTypes.string,
};

export default CircleTrue;
