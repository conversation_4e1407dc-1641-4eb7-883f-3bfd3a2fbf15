import PropTypes from 'prop-types';

const BatteryIcon = ({ width = 16, height = 16, className = '', ...props }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M6.25 12.5H8.75V28.125H6.25V12.5ZM11.25 12.5H13.75V28.125H11.25V12.5ZM16.25 12.5H18.75V28.125H16.25V12.5Z"
        fill="#F7C000"
      />
      <path
        d="M33.7491 13.7501V9.34302C33.7443 8.85018 33.5442 8.37938 33.1927 8.03388C32.8412 7.68839 32.367 7.49641 31.8741 7.50005H3.12305C2.63019 7.49641 2.15601 7.68839 1.80451 8.03388C1.45302 8.37938 1.25289 8.85018 1.24805 9.34302V31.2821C1.25289 31.7749 1.45302 32.2457 1.80451 32.5912C2.15601 32.9367 2.63019 33.1287 3.12305 33.125H31.8741C32.367 33.1287 32.8412 32.9367 33.1927 32.5912C33.5442 32.2457 33.7443 31.7749 33.7491 31.2821V26.8751H38.7491V13.7501H33.7491ZM36.2491 24.3751H31.2491V30.625H3.74805V10.0001H31.2491V16.2501H36.2491V24.3751Z"
        fill="black"
      />
    </svg>
  );
};
BatteryIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default BatteryIcon;
