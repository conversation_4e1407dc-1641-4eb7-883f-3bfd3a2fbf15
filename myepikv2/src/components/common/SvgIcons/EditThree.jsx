import PropTypes from 'prop-types';

const EditThreeIcon = ({
  width = 16,
  height = 16,
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <g clipPath="url(#clip0_1620_24217)">
        <path
          d="M7.33301 2.66602H2.66634C2.31272 2.66602 1.97358 2.80649 1.72353 3.05654C1.47348 3.30659 1.33301 3.64573 1.33301 3.99935V13.3327C1.33301 13.6863 1.47348 14.0254 1.72353 14.2755C1.97358 14.5255 2.31272 14.666 2.66634 14.666H11.9997C12.3533 14.666 12.6924 14.5255 12.9425 14.2755C13.1925 14.0254 13.333 13.6863 13.333 13.3327V8.66602"
          stroke="#3B5CA9"
          strokeWidth="1.3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M12.333 1.66617C12.5982 1.40095 12.9579 1.25195 13.333 1.25195C13.7081 1.25195 14.0678 1.40095 14.333 1.66617C14.5982 1.93138 14.7472 2.29109 14.7472 2.66617C14.7472 3.04124 14.5982 3.40095 14.333 3.66617L7.99967 9.9995L5.33301 10.6662L5.99967 7.9995L12.333 1.66617Z"
          stroke="#3B5CA9"
          strokeWidth="1.3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_1620_24217">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

// Add PropTypes validation
EditThreeIcon.propTypes = {
  /** Width of the icon */
  width: PropTypes.number, // `width` should be a number
  /** Height of the icon */
  height: PropTypes.number, // `height` should be a number
  /** CSS class for custom styles */
  className: PropTypes.string, // `className` should be a string
};

export default EditThreeIcon;
