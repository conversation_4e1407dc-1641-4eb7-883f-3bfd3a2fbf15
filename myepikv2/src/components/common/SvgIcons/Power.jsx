import PropTypes from 'prop-types';

const PowerIcon = ({ width = 16, height = 16, className = '', ...props }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M16 28C9.38327 28 4 22.7565 4 16.3119C4 12.8085 5.57212 9.52394 8.31308 7.30524C8.44106 7.20102 8.58892 7.12237 8.74821 7.07379C8.90749 7.02522 9.07506 7.00768 9.24133 7.02217C9.4076 7.03667 9.56929 7.08291 9.71715 7.15826C9.86501 7.2336 9.99614 7.33657 10.103 7.46126C10.2099 7.58595 10.2904 7.72992 10.34 7.88492C10.3896 8.03992 10.4072 8.20291 10.3919 8.36455C10.3766 8.52619 10.3286 8.6833 10.2508 8.8269C10.173 8.97049 10.0668 9.09775 9.93827 9.20137C7.77769 10.9516 6.53846 13.5434 6.53846 16.3119C6.53846 21.3955 10.7829 25.5317 16 25.5317C21.2171 25.5317 25.4615 21.3955 25.4615 16.3119C25.4614 14.9532 25.1518 13.6115 24.555 12.3831C23.9582 11.1546 23.089 10.07 22.0098 9.20698C21.8761 9.1055 21.7647 8.97901 21.6822 8.83497C21.5996 8.69093 21.5476 8.53226 21.5292 8.36833C21.5107 8.20441 21.5263 8.03856 21.5748 7.88057C21.6234 7.72259 21.7041 7.57567 21.812 7.4485C21.92 7.32133 22.0531 7.21648 22.2034 7.14016C22.3537 7.06383 22.5183 7.01758 22.6873 7.00412C22.8563 6.99066 23.0264 7.01027 23.1875 7.0618C23.3486 7.11332 23.4974 7.19572 23.6252 7.30412C24.9933 8.39823 26.0951 9.77344 26.8515 11.3309C27.6079 12.8884 28.0002 14.5894 28 16.3119C28 22.7565 22.6167 28 16 28Z"
        fill="white"
      />
      <path
        d="M16 17C15.7348 17 15.4804 16.8655 15.2929 16.626C15.1054 16.3866 15 16.0618 15 15.7232V5.27679C15 4.93816 15.1054 4.61341 15.2929 4.37396C15.4804 4.13452 15.7348 4 16 4C16.2652 4 16.5196 4.13452 16.7071 4.37396C16.8946 4.61341 17 4.93816 17 5.27679V15.7232C17 16.0618 16.8946 16.3866 16.7071 16.626C16.5196 16.8655 16.2652 17 16 17Z"
        fill="white"
      />
    </svg>
  );
};
PowerIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default PowerIcon;
