import PropTypes from 'prop-types';

const CheckPrimaryTickIcon = ({
  width = 16,
  height = 14,
  fill = '#3B5CA9',
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 21 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M17.9927 1.31677L6.05937 12.8334L2.89271 9.4501C2.30937 8.9001 1.39271 8.86677 0.726041 9.33344C0.076041 9.81677 -0.107293 10.6668 0.292707 11.3501L4.04271 17.4501C4.40937 18.0168 5.04271 18.3668 5.75937 18.3668C6.44271 18.3668 7.09271 18.0168 7.45937 17.4501C8.05937 16.6668 19.5094 3.01677 19.5094 3.01677C21.0094 1.48344 19.1927 0.133436 17.9927 1.3001V1.31677Z"
        fill={fill}
      />
    </svg>
  );
};
export default CheckPrimaryTickIcon;

CheckPrimaryTickIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  className: PropTypes.string,
  fill: PropTypes.string,
};
