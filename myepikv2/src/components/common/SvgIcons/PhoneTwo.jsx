import PropTypes from 'prop-types';

const PhoneTwoIcon = ({
  width = 16,
  height = 16,
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M5.33596 13.3267L5.33584 13.3263C4.12098 13.7276 2.80914 13.9444 1.4651 13.9444C0.988895 13.9444 0.598437 14.3348 0.598437 14.8111V18.5337C0.598437 19.0099 0.988894 19.4004 1.4651 19.4004C11.3706 19.4004 19.3984 11.3726 19.3984 1.46706C19.3984 0.990847 19.008 0.600391 18.5318 0.600391H14.7984C14.3222 0.600391 13.9318 0.990848 13.9318 1.46706C13.9318 2.8213 13.7152 4.12196 13.3142 5.33632M5.33596 13.3267L13.1238 5.27506M5.33596 13.3267L5.34738 13.3221C5.41977 13.2931 5.50777 13.2831 5.60377 13.2831C5.82737 13.2831 6.04447 13.3683 6.22157 13.5357M5.33596 13.3267L5.2731 13.1364C5.37977 13.0937 5.4971 13.0831 5.60377 13.0831C5.8811 13.0831 6.14777 13.1897 6.3611 13.3924M13.3142 5.33632C13.314 5.33681 13.3138 5.3373 13.3137 5.33779L13.1238 5.27506M13.3142 5.33632C13.3143 5.33589 13.3144 5.33545 13.3146 5.33502L13.1238 5.27506M13.3142 5.33632C13.2188 5.64139 13.2885 5.97831 13.5319 6.22164L15.8785 8.5683L15.9812 8.67099L15.9154 8.80042C14.36 11.8571 11.8546 14.3514 8.79897 15.9171L8.66934 15.9835L8.56635 15.8805L6.22157 13.5357M13.1238 5.27506C13.0064 5.64839 13.0918 6.06439 13.3904 6.36306L15.7371 8.70972C14.2011 11.7284 11.7264 14.1924 8.70777 15.7391L6.3611 13.3924M6.22157 13.5357C6.22216 13.5363 6.22276 13.5368 6.22335 13.5374L6.3611 13.3924M6.22157 13.5357L6.21968 13.5338L6.3611 13.3924M5.44593 15.213L5.36307 15.1309L5.25076 15.1626C4.3575 15.4148 3.44316 15.5725 2.51817 15.6355L2.33177 15.6482V15.8351V17.4244V17.6385L2.54538 17.6239C3.97089 17.5267 5.34206 17.246 6.64788 16.8143L6.95743 16.712L6.72593 16.4824L5.44593 15.213ZM15.8224 2.33372H15.6355L15.6229 2.52027C15.5598 3.45608 15.4021 4.36992 15.1503 5.24084L15.1175 5.3543L15.201 5.43781L16.481 6.71781L16.7079 6.94473L16.8117 6.64105C17.2546 5.34462 17.5354 3.97279 17.6326 2.54733L17.6472 2.33372H17.4331H15.8224Z"
        fill="black"
        stroke="white"
        strokeWidth="0.4"
      />
    </svg>
  );
};
export default PhoneTwoIcon;

PhoneTwoIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  className: PropTypes.string,
  stroke: PropTypes.string,
};
