import PropTypes from 'prop-types';

const SuccessLiveIcon = ({
  width = 16,
  height = 12,
  fill = '#2FD214',
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 16 12"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M2.353 0.333627C2.44358 0.245724 2.56537 0.197396 2.69158 0.199271C2.81778 0.201147 2.93808 0.253071 3.026 0.343627C3.12166 0.444132 3.17409 0.57814 3.17203 0.716874C3.16998 0.855607 3.1136 0.988002 3.015 1.08563C1.72205 2.39411 0.997907 4.1601 1 5.99963C1 7.98963 1.83 9.78563 3.164 11.0596C3.21528 11.1074 3.25657 11.1648 3.28551 11.2286C3.31445 11.2924 3.33045 11.3613 3.33258 11.4313C3.33472 11.5014 3.32296 11.5711 3.29797 11.6366C3.27298 11.702 3.23527 11.7618 3.187 11.8126C3.10098 11.9015 2.98369 11.9533 2.86007 11.957C2.73644 11.9608 2.61624 11.9161 2.525 11.8326C1.72698 11.0852 1.09106 10.1818 0.6567 9.17837C0.222346 8.17497 -0.00117614 7.09301 4.654e-06 5.99963C4.654e-06 3.78563 0.900005 1.78163 2.353 0.333627ZM12.985 1.08563C12.8865 0.98787 12.8304 0.855402 12.8285 0.716669C12.8266 0.577937 12.8792 0.444002 12.975 0.343627C13.0629 0.253375 13.183 0.201645 13.3089 0.19977C13.4349 0.197896 13.5565 0.246031 13.647 0.333627C14.3936 1.07611 14.9859 1.95902 15.3898 2.93146C15.7936 3.90391 16.001 4.94666 16 5.99963C16 8.29963 15.03 10.3736 13.475 11.8326C13.3838 11.9161 13.2636 11.9608 13.1399 11.957C13.0163 11.9533 12.899 11.9015 12.813 11.8126C12.7649 11.7619 12.7273 11.7021 12.7024 11.6367C12.6775 11.5714 12.6658 11.5017 12.6679 11.4318C12.6701 11.3619 12.686 11.2931 12.7149 11.2294C12.7437 11.1657 12.7849 11.1084 12.836 11.0606C13.5209 10.4078 14.0659 9.62246 14.4379 8.75244C14.8099 7.88242 15.0012 6.94584 15 5.99963C15.0024 4.15976 14.2782 2.39436 12.985 1.08563ZM4.132 2.08963C4.22112 2.00476 4.34029 1.95876 4.46332 1.96177C4.58634 1.96477 4.70314 2.01651 4.788 2.10563C4.99601 2.31363 4.975 2.65563 4.77 2.86563C3.95533 3.70546 3.4998 4.82958 3.5 5.99963C3.5 7.28963 4.043 8.45363 4.914 9.27463C5.134 9.48163 5.164 9.83563 4.949 10.0506C4.86659 10.1363 4.75438 10.187 4.6356 10.1922C4.51683 10.1974 4.40061 10.1568 4.311 10.0786C3.74085 9.56303 3.28511 8.93365 2.97318 8.23107C2.66125 7.5285 2.50006 6.76834 2.5 5.99963C2.4989 5.2722 2.64264 4.55183 2.92284 3.88052C3.20304 3.20922 3.61408 2.60042 4.132 2.08963ZM11.23 2.86563C11.025 2.65563 11.004 2.31363 11.212 2.10563C11.2969 2.01651 11.4137 1.96477 11.5367 1.96177C11.6597 1.95876 11.7789 2.00476 11.868 2.08963C12.3859 2.60042 12.797 3.20922 13.0772 3.88052C13.3574 4.55183 13.5011 5.2722 13.5 5.99963C13.5002 6.76843 13.3393 7.52874 13.0275 8.23149C12.7157 8.93424 12.2601 9.56383 11.69 10.0796C11.6 10.1581 11.4832 10.1988 11.364 10.1932C11.2447 10.1876 11.1322 10.1362 11.05 10.0496C10.836 9.83563 10.866 9.48163 11.086 9.27463C11.5325 8.85394 11.8882 8.34641 12.1314 7.78322C12.3745 7.22003 12.5 6.61307 12.5 5.99963C12.5002 4.82958 12.0447 3.70546 11.23 2.86563ZM8 4.74963C7.66848 4.74963 7.35054 4.88132 7.11612 5.11574C6.8817 5.35016 6.75 5.66811 6.75 5.99963C6.75 6.33115 6.8817 6.64909 7.11612 6.88351C7.35054 7.11793 7.66848 7.24963 8 7.24963C8.33153 7.24963 8.64947 7.11793 8.88389 6.88351C9.11831 6.64909 9.25 6.33115 9.25 5.99963C9.25 5.66811 9.11831 5.35016 8.88389 5.11574C8.64947 4.88132 8.33153 4.74963 8 4.74963Z"
        fill={fill}
      />
    </svg>
  );
};
SuccessLiveIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  fill: PropTypes.string,
  className: PropTypes.string,
};
export default SuccessLiveIcon;
