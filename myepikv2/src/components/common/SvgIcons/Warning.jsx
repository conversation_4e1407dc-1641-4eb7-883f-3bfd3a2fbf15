import PropTypes from 'prop-types';

const WarningIcon = ({ width = 16, height = 16, className = '', ...props }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <rect width="40" height="40" rx="5" fill="#E8E8E8" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M19.2316 23.8603L18.7816 14.7122H21.2191L20.7503 23.8622L19.2316 23.8603ZM19.9703 27.4247C19.8138 27.4282 19.6582 27.399 19.5135 27.339C19.3689 27.2789 19.2384 27.1893 19.1303 27.0759C19.0179 26.9687 18.9288 26.8394 18.8688 26.6962C18.8088 26.5529 18.7791 26.3987 18.7816 26.2434C18.7816 25.9172 18.8978 25.6384 19.1303 25.4072C19.2387 25.2944 19.3694 25.2055 19.514 25.1461C19.6586 25.0867 19.814 25.0581 19.9703 25.0622C20.2866 25.0622 20.5616 25.1759 20.7953 25.4034C21.0278 25.6309 21.1441 25.9109 21.1441 26.2434C21.1464 26.3998 21.1161 26.5549 21.0551 26.6989C20.994 26.8429 20.9037 26.9726 20.7897 27.0797C20.6837 27.1898 20.5564 27.2772 20.4156 27.3365C20.2747 27.3958 20.1232 27.4258 19.9703 27.4247ZM8.75035 31.2497C8.42501 31.2497 8.10526 31.1651 7.82252 31.0042C7.53978 30.8433 7.30377 30.6115 7.1377 30.3318C6.97162 30.052 6.8812 29.7339 6.87531 29.4086C6.86942 29.0833 6.94826 28.7621 7.1041 28.4765L18.3541 7.85153C18.5154 7.55572 18.7534 7.30883 19.0431 7.13682C19.3327 6.9648 19.6634 6.87402 20.0003 6.87402C20.3373 6.87402 20.668 6.9648 20.9576 7.13682C21.2473 7.30883 21.4853 7.55572 21.6466 7.85153L32.8966 28.4765C33.0524 28.7621 33.1313 29.0833 33.1254 29.4086C33.1195 29.7339 33.0291 30.052 32.863 30.3318C32.6969 30.6115 32.4609 30.8433 32.1782 31.0042C31.8954 31.1651 31.5757 31.2497 31.2503 31.2497H8.75035ZM8.75035 29.3747H31.2503L20.0003 8.74966L8.75035 29.3747Z"
        fill="black"
      />
    </svg>
  );
};
WarningIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default WarningIcon;
