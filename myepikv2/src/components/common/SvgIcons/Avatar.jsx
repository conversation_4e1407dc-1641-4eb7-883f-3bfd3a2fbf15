import PropTypes from 'prop-types';

const AvatarIcon = ({ width = 16, height = 16, className = '', ...props }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 331.108 331.108"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <g id="XMLID_1481_">
        <g id="XMLID_1482_">
          <g id="XMLID_1483_">
            <path
              id="XMLID_1484_"
              style="fill:#F3D8B6;"
              d="M248.039,236.042c-18.667-6.681-51.458-11.736-51.458-81.376h-29.23h-5.002
				h-29.23c0,69.64-32.791,74.695-51.458,81.376c0,47.368,68.832,48.824,80.688,53.239v1.537c0,0,0.922-0.188,2.501-0.68
				c1.579,0.492,2.501,0.68,2.501,0.68v-1.537C179.207,284.866,248.039,283.409,248.039,236.042z"
            />
          </g>
          <path
            id="XMLID_1485_"
            style="fill:#EEC8A2;"
            d="M196.582,154.666h-29.23h-2.251v135.547c1.421,0.433,2.251,0.604,2.251,0.604
			v-1.537c11.855-4.414,80.688-5.871,80.688-53.239C229.373,229.361,196.582,224.306,196.582,154.666z"
          />
        </g>
        <g id="XMLID_1486_">
          <path
            id="XMLID_1487_"
            style="fill:#F3DBC4;"
            d="M165.103,176.828c-27.454,0-48.409-23.119-57.799-40.456
			s-15.888-79.445,4.34-106.897c19.808-26.883,53.459-13.838,53.459-13.838s33.649-13.045,53.458,13.838
			c20.226,27.452,13.726,89.56,4.335,106.897C213.504,153.709,192.551,176.828,165.103,176.828z"
          />
          <path
            id="XMLID_1488_"
            style="fill:#EDCEAE;"
            d="M218.561,29.476c-19.81-26.883-53.458-13.838-53.458-13.838h-0.002v161.191h0.002
			c27.449,0,48.401-23.119,57.794-40.456C232.287,119.036,238.787,56.928,218.561,29.476z"
          />
        </g>
        <g id="XMLID_1489_">
          <path
            id="XMLID_1492_"
            style="fill:#E1A98C;"
            d="M222.556,25.321c-2.159,0.08-12.282-31.303-39.282-24.303
			c-18.537,4.806-20.877,7.419-28.12,9.463c-29.41-9.014-57.539,14.472-56.495,36.488c1.759,37.07-4.778,36.505-0.295,49.454
			s8.466,23.407,8.466,23.407s0.996,3.565,2.988-16.854s-4.705-31.379,11.137-31.379c52.452,0-19.698,20.372,13.952,20.372
			c33.391,0,59.203-27.381,74.92-29.372c15.716-1.992,9.145,19.96,11.137,40.379s2.988,16.854,2.988,16.854s8.92-9.712,8.466-23.407
			C231.908,80.969,238.788,24.719,222.556,25.321z"
          />
          <path
            id="XMLID_1493_"
            style="fill:#D2987B;"
            d="M222.556,25.322c-2.159,0.08-12.282-31.303-39.282-24.303
			c-9.171,2.377-14.375,4.218-18.173,5.719V85.04c18.973-8.253,34.142-21.103,44.726-22.443c15.717-1.992,9.145,19.96,11.137,40.379
			s2.988,16.854,2.988,16.854s8.92-9.712,8.466-23.407C231.908,80.969,238.788,24.719,222.556,25.322z"
          />
        </g>
        <g id="XMLID_1494_">
          <path
            id="XMLID_1495_"
            style="fill:#A7B8D4;"
            d="M207.267,101.856h-27.689c-1.185,0-2.356,0.243-3.431,0.713l-10.874,3.873
			l-10.874-3.873c-1.075-0.47-2.246-0.713-3.431-0.713H123.28c-5.509,0-7.411,9.474-7.411,9.474
			c0,12.532,9.191,22.692,23.756,22.692c8.882,0,17.409-9.44,22.649-17.081h6c5.24,7.641,13.768,17.081,22.649,17.081
			c14.565,0,23.756-10.16,23.756-22.692C214.678,111.33,212.776,101.856,207.267,101.856z"
          />
          <path
            id="XMLID_1496_"
            style="fill:#8AA4C2;"
            d="M207.267,101.856h-27.689c-1.185,0-2.356,0.243-3.431,0.713l-11.041,3.813v10.559
			h3.167c5.24,7.641,13.768,17.081,22.649,17.081c14.565,0,23.756-10.16,23.756-22.692
			C214.678,111.33,212.776,101.856,207.267,101.856z"
          />
        </g>
        <g id="XMLID_1497_">
          <path
            id="XMLID_1498_"
            style="fill:#682234;"
            d="M245.383,219.513c-2.201-1.327-5.416-2.538-8.955-3.63
			c-5.291-1.905-11.248-3.442-15.795-4.565c-1.89-0.473-9.146-0.102-15.118-13.184h-79.463
			c-5.328,13.132-11.814,12.767-13.484,13.184c-7.154,1.766-20.802,4.554-26.844,8.195c-23.228,13.951-40.605,40.482-40.605,70.914
			v40.681h239.964h0.906v-40.681C285.988,259.995,268.611,233.464,245.383,219.513z"
          />
          <path
            id="XMLID_1499_"
            style="fill:#581A2B;"
            d="M245.383,219.513c-2.201-1.327-5.416-2.538-8.955-3.63
			c-5.291-1.905-11.248-3.442-15.795-4.565c-1.89-0.473-9.146-0.102-15.118-13.184h-40.867v132.974h120.435h0.906v-40.681
			C285.988,259.995,268.611,233.464,245.383,219.513z"
          />
        </g>
        <g id="XMLID_1500_">
          <g id="XMLID_1504_">
            <g id="XMLID_1502_">
              <path
                id="XMLID_1503_"
                style="fill:#E1A98C;"
                d="M231.779,102.653c-0.725,14.745-22.425,29.324-29.599,39.061
					c-16.801,22.803-37.079,3.255-37.079,3.255s-19.826,19.548-36.629-3.255c-7.173-9.735-28.871-24.311-29.596-39.054
					c-1.19,20.863,11.665,42.273,16.381,50.979c7.973,14.719,26.764,34.348,50.072,34.348c23.304,0,42.094-19.629,50.068-34.348
					C220.113,144.932,232.969,123.518,231.779,102.653z"
              />
            </g>
          </g>
          <path
            id="XMLID_1506_"
            style="fill:#D2987B;"
            d="M231.779,102.653c-0.725,14.745-22.424,29.324-29.599,39.061
			c-16.801,22.803-37.08,3.255-37.08,3.255v43.012c0.076,0,0.152,0.006,0.228,0.006c23.304,0,42.094-19.628,50.068-34.348
			C220.113,144.932,232.969,123.518,231.779,102.653z"
          />
        </g>
        <g id="XMLID_1507_">
          <ellipse
            id="XMLID_33_"
            transform="matrix(0.3543 -0.9351 0.9351 0.3543 44.7116 282.9862)"
            style="fill:#EDCEAE;"
            cx="227.275"
            cy="109.116"
            rx="17.187"
            ry="10.048"
          />

          <ellipse
            id="XMLID_32_"
            transform="matrix(0.3543 0.9351 -0.9351 0.3543 168.501 -25.7861)"
            style="fill:#F3DBC4;"
            cx="102.923"
            cy="109.124"
            rx="17.187"
            ry="10.048"
          />
        </g>
      </g>
    </svg>
  );
};
AvatarIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default AvatarIcon;
