import PropTypes from 'prop-types';

const VoiceMail = ({
  width = 16,
  height = 16,
  stroke = '#667085',
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M22 10.0007C21.9967 9.67606 21.9917 9.35072 21.985 9.02471C21.92 5.95962 21.887 4.42657 20.756 3.29154C19.625 2.1555 18.051 2.1165 14.9019 2.0375C12.9619 1.9875 11.0399 1.9875 9.09986 2.0375C5.95083 2.1165 4.37681 2.1555 3.2458 3.29154C2.11479 4.42657 2.08179 5.95962 2.01579 9.02471C1.99474 10.0086 1.99474 10.9929 2.01579 11.9768C2.08179 15.0419 2.11479 16.5749 3.2458 17.71C4.37581 18.846 5.95083 18.885 9.09986 18.964C9.90187 18.984 10.7019 18.996 11.4999 19"
        stroke={stroke}
        strokeWidth="1.75"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7 7L9.84407 8.74C11.5014 9.754 12.1634 9.754 13.8217 8.74L16.6638 7M15.2353 17.969C15.8238 19.006 17.0038 19.975 18.6157 19.975C20.2267 19.975 21.4124 19.005 22 17.969M18.6254 20.135V22M18.6041 17.12C18.3734 17.1196 18.145 17.0722 17.9319 16.9804C17.7189 16.8885 17.5255 16.7542 17.3627 16.585C17.1999 16.4158 17.0709 16.215 16.9831 15.9942C16.8953 15.7733 16.8505 15.5368 16.8511 15.298V13.818C16.8511 12.812 17.6358 11.997 18.6041 11.997C19.5715 11.997 20.3562 12.812 20.3562 13.818V15.298C20.3567 15.5366 20.3118 15.773 20.224 15.9936C20.1362 16.2143 20.0073 16.4149 19.8446 16.584C19.6819 16.7531 19.4886 16.8874 19.2758 16.9792C19.063 17.071 18.8347 17.1185 18.6041 17.119"
        stroke={stroke}
        strokeWidth="1.75"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
VoiceMail.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default VoiceMail;
