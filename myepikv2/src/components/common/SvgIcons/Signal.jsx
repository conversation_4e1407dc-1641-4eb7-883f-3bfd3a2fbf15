import PropTypes from 'prop-types';

const SignalIcon = ({ width = 16, height = 16, className = '', ...props }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <g clipPath="url(#clip0_564_142)">
        <path
          d="M11.9997 16.101C11.699 16.1023 11.4016 16.1629 11.1244 16.2792C10.8472 16.3956 10.5956 16.5654 10.3841 16.779C10.1725 16.9926 10.0051 17.2457 9.89142 17.5241C9.77774 17.8024 9.72 18.1003 9.72152 18.401C9.72152 19.6706 10.7416 20.701 11.9997 20.701C12.3002 20.6995 12.5975 20.6388 12.8746 20.5224C13.1516 20.406 13.4031 20.2361 13.6145 20.0226C13.826 19.809 13.9933 19.5559 14.1069 19.2776C14.2205 18.9994 14.2782 18.7015 14.2767 18.401C14.2767 17.1302 13.2578 16.101 11.9997 16.101ZM7.16967 13.5227L8.77966 15.1488C9.20045 14.722 9.70185 14.3832 10.2547 14.1519C10.8076 13.9206 11.4009 13.8015 12.0002 13.8015C12.5995 13.8015 13.1929 13.9206 13.7458 14.1519C14.2986 14.3832 14.8 14.722 15.2208 15.1488L16.8297 13.5227C16.1986 12.8826 15.4467 12.3744 14.6175 12.0275C13.7883 11.6806 12.8985 11.5019 11.9997 11.5019C11.1008 11.5019 10.211 11.6806 9.38183 12.0275C8.55265 12.3744 7.8007 12.8826 7.16967 13.5227ZM3.94967 10.2682L5.55967 11.8943C6.40102 11.041 7.40356 10.3635 8.50901 9.90109C9.61447 9.43865 10.8008 9.20051 11.9991 9.20051C13.1974 9.20051 14.3837 9.43865 15.4892 9.90109C16.5946 10.3635 17.5972 11.041 18.4385 11.8943L20.0497 10.2682C18.9979 9.20156 17.7446 8.3546 16.3626 7.77651C14.9807 7.19843 13.4976 6.90073 11.9997 6.90073C10.5017 6.90073 9.01864 7.19843 7.63669 7.77651C6.25475 8.3546 5.00147 9.20156 3.94967 10.2682ZM0.728516 7.01597L2.33852 8.64207C3.60068 7.36193 5.10469 6.34541 6.76313 5.65159C8.42157 4.95776 10.2014 4.60047 11.9991 4.60047C13.7968 4.60047 15.5766 4.95776 17.2351 5.65159C18.8935 6.34541 20.3975 7.36193 21.6597 8.64207L23.2697 7.01597C21.7972 5.52248 20.0425 4.33655 18.1077 3.52709C16.1728 2.71762 14.0964 2.30078 11.9991 2.30078C9.90176 2.30078 7.82534 2.71762 5.89051 3.52709C3.95568 4.33655 2.20103 5.52248 0.728516 7.01597Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_564_142">
          <rect
            width="23"
            height="23"
            fill="white"
            transform="translate(0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
SignalIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default SignalIcon;
