import PropTypes from 'prop-types';

const NetworkIcon = ({
  width = 16,
  height = 16,
  stroke = 'white',
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M27.9987 21.3359H22.6654C21.929 21.3359 21.332 21.9329 21.332 22.6693V28.0026C21.332 28.739 21.929 29.3359 22.6654 29.3359H27.9987C28.7351 29.3359 29.332 28.739 29.332 28.0026V22.6693C29.332 21.9329 28.7351 21.3359 27.9987 21.3359Z"
        stroke={stroke}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.33268 21.3359H3.99935C3.26297 21.3359 2.66602 21.9329 2.66602 22.6693V28.0026C2.66602 28.739 3.26297 29.3359 3.99935 29.3359H9.33268C10.0691 29.3359 10.666 28.739 10.666 28.0026V22.6693C10.666 21.9329 10.0691 21.3359 9.33268 21.3359Z"
        stroke={stroke}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M18.6667 2.66797H13.3333C12.597 2.66797 12 3.26492 12 4.0013V9.33464C12 10.071 12.597 10.668 13.3333 10.668H18.6667C19.403 10.668 20 10.071 20 9.33464V4.0013C20 3.26492 19.403 2.66797 18.6667 2.66797Z"
        stroke={stroke}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.66602 21.3346V17.3346C6.66602 16.981 6.80649 16.6419 7.05654 16.3918C7.30659 16.1418 7.64573 16.0013 7.99935 16.0013H23.9993C24.353 16.0013 24.6921 16.1418 24.9422 16.3918C25.1922 16.6419 25.3327 16.981 25.3327 17.3346V21.3346M15.9993 16.0013V10.668"
        stroke={stroke}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
NetworkIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default NetworkIcon;
