import PropTypes from 'prop-types';

const ResetLineIcon = ({
  width = 16,
  height = 16,
  fill = '#4D9CD3',
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M17.3307 9.00008C17.3307 13.6026 13.5999 17.3334 8.9974 17.3334C4.3949 17.3334 0.664062 13.6026 0.664062 9.00008C0.664062 4.39758 4.3949 0.666748 8.9974 0.666748V2.33341C7.40117 2.3336 5.858 2.9065 4.64836 3.94799C3.43871 4.98947 2.64291 6.43041 2.40559 8.00889C2.16826 9.58738 2.50518 11.1986 3.35509 12.5498C4.205 13.9009 5.51149 14.9022 7.03709 15.3718C8.56269 15.8413 10.2061 15.7479 11.6687 15.1085C13.1312 14.4691 14.3158 13.3261 15.0071 11.8874C15.6985 10.4486 15.8506 8.80958 15.4359 7.26816C15.0212 5.72674 14.0673 4.38527 12.7474 3.48758V5.66675H11.0807V0.666748H16.0807V2.33341H13.9974C15.0328 3.10919 15.8732 4.11563 16.4518 5.27286C17.0305 6.43009 17.3314 7.70626 17.3307 9.00008Z"
        fill={fill}
      />
    </svg>
  );
};

// Add PropTypes validation
ResetLineIcon.propTypes = {
  /** Width of the icon */
  width: PropTypes.number, // `width` should be a number
  /** Height of the icon */
  height: PropTypes.number, // `height` should be a number
  /** Fill color for the icon */
  fill: PropTypes.string, // `fill` should be a string
  /** CSS class for custom styles */
  className: PropTypes.string, // `className` should be a string
};

export default ResetLineIcon;
