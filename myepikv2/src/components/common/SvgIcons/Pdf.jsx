import PropTypes from 'prop-types';

const PdfIcon = ({ width = 17, height = 16, className = '', ...props }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 25 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <g clipPath="url(#clip0_4694_56383)">
        <path
          d="M21.8091 21.7143C21.8091 21.8644 21.7796 22.013 21.7221 22.1516C21.6647 22.2903 21.5805 22.4163 21.4744 22.5224C21.3683 22.6285 21.2423 22.7127 21.1036 22.7701C20.965 22.8276 20.8164 22.8571 20.6663 22.8571H4.66629C4.51621 22.8571 4.3676 22.8276 4.22894 22.7701C4.09028 22.7127 3.9643 22.6285 3.85817 22.5224C3.75205 22.4163 3.66787 22.2903 3.61043 22.1516C3.553 22.013 3.52344 21.8644 3.52344 21.7143V1.14288C3.52344 0.992793 3.553 0.844181 3.61043 0.705523C3.66787 0.566865 3.75205 0.440878 3.85817 0.334754C3.9643 0.22863 4.09028 0.144448 4.22894 0.0870138C4.3676 0.0295799 4.51621 1.9102e-05 4.66629 1.9102e-05H14.4834C14.6338 -0.000850488 14.7829 0.0279781 14.9222 0.0848519C15.0614 0.141726 15.1881 0.225526 15.2949 0.331447L21.4777 6.5143C21.5836 6.62109 21.6674 6.74773 21.7243 6.88698C21.7812 7.02622 21.81 7.17532 21.8091 7.32573V21.7143Z"
          fill="#EBECF0"
        />
        <path
          d="M21.8091 21.7109V22.8538C21.8091 23.0039 21.7796 23.1525 21.7221 23.2911C21.6647 23.4298 21.5805 23.5558 21.4744 23.6619C21.3683 23.768 21.2423 23.8522 21.1036 23.9097C20.965 23.9671 20.8164 23.9967 20.6663 23.9967H4.66629C4.36319 23.9967 4.0725 23.8762 3.85817 23.6619C3.64385 23.4476 3.52344 23.1569 3.52344 22.8538V21.7109C3.52344 21.861 3.553 22.0096 3.61043 22.1483C3.66787 22.2869 3.75205 22.4129 3.85817 22.5191C3.9643 22.6252 4.09028 22.7094 4.22894 22.7668C4.3676 22.8242 4.51621 22.8538 4.66629 22.8538H20.6663C20.8164 22.8538 20.965 22.8242 21.1036 22.7668C21.2423 22.7094 21.3683 22.6252 21.4744 22.5191C21.5805 22.4129 21.6647 22.2869 21.7221 22.1483C21.7796 22.0096 21.8091 21.861 21.8091 21.7109Z"
          fill="#C1C7D0"
        />
        <path
          d="M1.23438 12.5703H24.0915V18.2846C24.0915 18.4347 24.0619 18.5833 24.0045 18.7219C23.9471 18.8606 23.8629 18.9866 23.7568 19.0927C23.6506 19.1988 23.5246 19.283 23.386 19.3405C23.2473 19.3979 23.0987 19.4274 22.9486 19.4274H2.37723C2.22715 19.4274 2.07854 19.3979 1.93988 19.3405C1.80122 19.283 1.67523 19.1988 1.56911 19.0927C1.46299 18.9866 1.3788 18.8606 1.32137 18.7219C1.26394 18.5833 1.23437 18.4347 1.23438 18.2846V12.5703Z"
          fill="#FF5630"
        />
        <path
          d="M3.52009 12.5748V10.2891L1.23438 12.5748H3.52009ZM21.8058 12.5748L21.8286 10.2891L24.1029 12.5748H21.8058Z"
          fill="#DE350B"
        />
        <path
          d="M6.95312 13.7124H8.94169C9.12171 13.704 9.30157 13.732 9.47047 13.7949C9.63937 13.8577 9.79382 13.9541 9.92455 14.0781C10.05 14.2036 10.1482 14.3537 10.2131 14.5188C10.278 14.684 10.3082 14.8608 10.3017 15.0381C10.3075 15.2154 10.277 15.3919 10.2122 15.557C10.1473 15.7221 10.0494 15.8722 9.92455 15.9981C9.78918 16.1263 9.6293 16.2258 9.45451 16.2906C9.27971 16.3554 9.09362 16.3842 8.90741 16.3753H7.76455V18.1695H6.95312V13.7124ZM7.71884 15.6324H8.74741C8.96002 15.6491 9.17238 15.597 9.35312 15.4838C9.41803 15.4254 9.46837 15.3526 9.50012 15.2712C9.53187 15.1899 9.54415 15.1022 9.53598 15.0153C9.53598 14.6267 9.28074 14.4324 8.77027 14.4324H7.71884V15.6324ZM10.8617 13.781H12.6674C12.926 13.7701 13.1838 13.8179 13.4213 13.9208C13.6588 14.0237 13.8699 14.1791 14.0388 14.3753C14.4003 14.8277 14.5833 15.397 14.5531 15.9753C14.5783 16.5598 14.4051 17.1358 14.0617 17.6095C13.9004 17.8255 13.6896 17.9996 13.4469 18.1169C13.2042 18.2343 12.9369 18.2915 12.6674 18.2838H10.8617V13.781ZM11.6274 17.5067H12.6674C12.8301 17.5126 12.9918 17.4793 13.139 17.4097C13.2861 17.3401 13.4144 17.2362 13.5131 17.1067C13.7333 16.7807 13.8379 16.3905 13.8103 15.9981C13.8483 15.5907 13.7344 15.1836 13.4903 14.8553C13.3806 14.7304 13.2451 14.631 13.0931 14.5639C12.9412 14.4968 12.7764 14.4636 12.6103 14.4667H11.6274V17.5067ZM18.3817 14.501H16.016V15.5638H18.3817V16.2953H16.016V18.2153H15.2503V13.7581H18.3817V14.501Z"
          fill="white"
        />
        <path
          d="M21.8101 7.32573V7.48573H15.6272C15.3241 7.48573 15.0334 7.36532 14.8191 7.15099C14.6048 6.93666 14.4844 6.64598 14.4844 6.34287V1.9102e-05C14.6348 -0.000850488 14.7839 0.0279781 14.9231 0.0848519C15.0624 0.141726 15.189 0.225526 15.2958 0.331447L21.4901 6.5143C21.6998 6.73183 21.8149 7.02358 21.8101 7.32573Z"
          fill="#C1C7D0"
        />
      </g>
      <defs>
        <clipPath id="clip0_4694_56383">
          <rect
            width="24"
            height="24"
            fill="white"
            transform="translate(0.671875)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

PdfIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  className: PropTypes.string,
};
export default PdfIcon;
