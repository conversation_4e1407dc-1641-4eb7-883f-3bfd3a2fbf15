import PropTypes from 'prop-types';

const MdiSignalIcon = ({
  width = 16,
  height = 16,
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 68 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <g clipPath="url(#clip0_564_69)">
        <path
          d="M0 24H11.3333V20H0M18.8889 24H30.2222V14.6667H18.8889M37.7778 24H49.1111V8H37.7778M56.6667 24H68V0H56.6667V24Z"
          fill="#2FD214"
        />
      </g>
      <defs>
        <clipPath id="clip0_564_69">
          <rect width="68" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};
MdiSignalIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default MdiSignalIcon;
