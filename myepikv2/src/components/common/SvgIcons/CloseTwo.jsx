import PropTypes from 'prop-types';

const CloseTwoIcon = ({
  width = 16,
  height = 16,
  stroke = '#ED2B2E',
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
        stroke={stroke}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15 9L9 15"
        stroke={stroke}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9 9L15 15"
        stroke={stroke}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

// Add PropTypes validation
CloseTwoIcon.propTypes = {
  /** Width of the icon */
  width: PropTypes.number, // `width` should be a number
  /** Height of the icon */
  height: PropTypes.number, // `height` should be a number
  /** Stroke color for the icon */
  stroke: PropTypes.string, // `stroke` should be a string
  /** CSS class for custom styles */
  className: PropTypes.string, // `className` should be a string
};

export default CloseTwoIcon;
