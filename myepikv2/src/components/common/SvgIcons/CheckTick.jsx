import PropTypes from 'prop-types';

const CheckTick = ({
  width = 16,
  height = 16,
  stroke = '#12B76A',
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <rect x="0.5" width="24" height="24" rx="12" fill="#D1FADF" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M17.5965 7.38967L10.4365 14.2997L8.5365 12.2697C8.1865 11.9397 7.6365 11.9197 7.2365 12.1997C6.8465 12.4897 6.7365 12.9997 6.9765 13.4097L9.2265 17.0697C9.4465 17.4097 9.8265 17.6197 10.2565 17.6197C10.6665 17.6197 11.0565 17.4097 11.2765 17.0697C11.6365 16.5997 18.5065 8.40967 18.5065 8.40967C19.4065 7.48967 18.3165 6.67967 17.5965 7.37967V7.38967Z"
        fill={stroke}
      />
    </svg>
  );
};
export default CheckTick;

CheckTick.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  className: PropTypes.string,
  stroke: PropTypes.string,
};
