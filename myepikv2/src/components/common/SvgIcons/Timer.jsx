import PropTypes from 'prop-types';

const Timer = ({
  width = 20,
  height = 20,
  stroke = 'black',
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M9.3858 8.55523V12.0859L13.7498 9.58594M18.07 4.69559L16.1402 2.76576M7.45598 0.835938H11.3156M9.3858 19.1693C7.33852 19.1693 5.37508 18.356 3.92743 16.9083C2.47978 15.4607 1.6665 13.4973 1.6665 11.45C1.6665 9.40269 2.47978 7.43925 3.92743 5.9916C5.37508 4.54396 7.33852 3.73067 9.3858 3.73067C11.4331 3.73067 13.3965 4.54396 14.8442 5.9916C16.2918 7.43925 17.1051 9.40269 17.1051 11.45C17.1051 13.4973 16.2918 15.4607 14.8442 16.9083C13.3965 18.356 11.4331 19.1693 9.3858 19.1693Z"
        stroke={stroke}
        strokeWidth="1.25"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
Timer.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default Timer;
