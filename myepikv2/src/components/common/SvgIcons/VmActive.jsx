import PropTypes from 'prop-types';

const VmActiveIcon = ({
  width = 16,
  height = 16,
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.98251 5H26.4497L27.2369 5.7872V14.4511C26.7494 14.0852 26.2211 13.7772 25.6625 13.5332V6.5744H6.76971V20.744H14.6417C14.6417 22.4473 15.1941 24.1046 16.2161 25.4672H9.91851V23.8928H14.6417V22.3184H5.98251L5.19531 21.5312V5.7872L5.98251 5Z"
        fill="white"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M19.016 15.5072C19.7035 15.0427 20.4761 14.7185 21.2893 14.5535C22.1026 14.3885 22.9404 14.3858 23.7546 14.5456C24.5689 14.7054 25.3435 15.0246 26.034 15.4848C26.7246 15.9449 27.3173 16.537 27.7783 17.227C28.2392 17.917 28.5593 18.6912 28.7201 19.5053C28.8808 20.3194 28.8791 21.1572 28.715 21.9706C28.5509 22.784 28.2277 23.557 27.764 24.2451C27.3002 24.9331 26.705 25.5228 26.0126 25.9801C24.6237 26.8975 22.9281 27.2277 21.2965 26.8986C19.6649 26.5695 18.2299 25.6078 17.3052 24.2237C16.3806 22.8396 16.0415 21.1458 16.3621 19.5125C16.6826 17.8791 17.6368 16.4391 19.016 15.5072ZM22.3695 23.3635L26.1276 18.3537L24.868 17.4091L21.598 21.7686L19.5497 20.1297L18.5657 21.3577L21.2485 23.5052L22.371 23.3635H22.3695Z"
        fill="white"
      />
    </svg>
  );
};
VmActiveIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default VmActiveIcon;
