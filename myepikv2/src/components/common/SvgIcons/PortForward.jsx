import PropTypes from 'prop-types';

const PortForwardIcon = ({
  width = 16,
  height = 16,
  stroke = 'white',
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M5.08403 17.2671H5.01486L4.96047 17.3098L0.532031 20.7889V1.46706C0.532031 1.2372 0.62334 1.01676 0.785872 0.854231C0.948403 0.6917 1.16884 0.600391 1.3987 0.600391H20.5987C20.8286 0.600391 21.049 0.6917 21.2115 0.854231C21.3741 1.01676 21.4654 1.2372 21.4654 1.46706V16.4004C21.4654 16.6302 21.3741 16.8507 21.2115 17.0132C21.049 17.1757 20.8286 17.2671 20.5987 17.2671H5.08403ZM2.26536 16.8111V17.2225L2.58892 16.9683L4.41506 15.5337H19.532H19.732V15.3337V2.53372V2.33372H19.532H2.46536H2.26536V2.53372V16.8111ZM10.9987 8.06706H11.1987V7.86706V5.1499L14.9825 8.93372L11.1987 12.7175V10.0004V9.80039H10.9987H6.93203V8.06706H10.9987Z"
        fill="black"
        stroke={stroke}
        strokeWidth="0.4"
      />
    </svg>
  );
};
export default PortForwardIcon;

PortForwardIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  className: PropTypes.string,
  stroke: PropTypes.string,
};
