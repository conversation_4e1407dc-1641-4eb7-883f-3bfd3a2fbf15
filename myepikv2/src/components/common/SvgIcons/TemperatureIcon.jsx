import PropTypes from 'prop-types';

const TemperatureIcon = ({
  width = 16,
  height = 16,
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M16 5C15.2044 5 14.4413 5.31607 13.8787 5.87868C13.3161 6.44129 13 7.20435 13 8V17.975L12.667 18.273C11.9121 18.948 11.3801 19.8364 11.1411 20.8205C10.9022 21.8045 10.9678 22.838 11.3291 23.784C11.6904 24.73 12.3304 25.544 13.1645 26.1183C13.9985 26.6926 14.9873 27.0001 16 27.0001C17.0127 27.0001 18.0015 26.6926 18.8355 26.1183C19.6696 25.544 20.3096 24.73 20.6709 23.784C21.0322 22.838 21.0978 21.8045 20.8589 20.8205C20.6199 19.8364 20.0879 18.948 19.333 18.273L19 17.975V8C19 7.20435 18.6839 6.44129 18.1213 5.87868C17.5587 5.31607 16.7957 5 16 5ZM11 8C11 6.67392 11.5268 5.40215 12.4645 4.46447C13.4022 3.52678 14.6739 3 16 3C17.3261 3 18.5979 3.52678 19.5355 4.46447C20.4732 5.40215 21 6.67392 21 8V17.101C21.9647 18.0856 22.6172 19.333 22.8758 20.6869C23.1343 22.0409 22.9874 23.441 22.4534 24.7117C21.9194 25.9825 21.0222 27.0673 19.8741 27.8302C18.7261 28.593 17.3784 29 16 29C14.6216 29 13.2739 28.593 12.1259 27.8302C10.9778 27.0673 10.0806 25.9825 9.5466 24.7117C9.01262 23.441 8.8657 22.0409 9.12426 20.6869C9.38281 19.333 10.0353 18.0856 11 17.101V8ZM15 12C15 11.7348 15.1054 11.4804 15.2929 11.2929C15.4804 11.1054 15.7348 11 16 11C16.2652 11 16.5196 11.1054 16.7071 11.2929C16.8946 11.4804 17 11.7348 17 12V19.17C17.6675 19.4059 18.2301 19.8702 18.5884 20.4808C18.9466 21.0914 19.0775 21.809 18.9578 22.5068C18.8381 23.2046 18.4756 23.8375 17.9344 24.2939C17.3931 24.7502 16.708 25.0005 16 25.0005C15.292 25.0005 14.6069 24.7502 14.0656 24.2939C13.5244 23.8375 13.1619 23.2046 13.0422 22.5068C12.9225 21.809 13.0534 21.0914 13.4116 20.4808C13.7699 19.8702 14.3325 19.4059 15 19.17V12Z"
        fill="white"
      />
    </svg>
  );
};
TemperatureIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default TemperatureIcon;
