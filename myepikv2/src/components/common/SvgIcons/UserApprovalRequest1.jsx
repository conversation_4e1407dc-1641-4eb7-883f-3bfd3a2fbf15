import PropTypes from 'prop-types';

const UserApprovalRequest1Icon = ({
  width = 16,
  height = 16,
  stroke = 'white',
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M16 21V19C16 17.9391 15.5786 16.9217 14.8284 16.1716C14.0783 15.4214 13.0609 15 12 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21"
        stroke={stroke}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.5 11C10.7091 11 12.5 9.20914 12.5 7C12.5 4.79086 10.7091 3 8.5 3C6.29086 3 4.5 4.79086 4.5 7C4.5 9.20914 6.29086 11 8.5 11Z"
        stroke={stroke}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M21.4042 2.06572C21.981 2.18322 22.5149 2.45535 22.9484 2.85288C23.3819 3.25041 23.6988 3.75831 23.8649 4.32205C24.0187 4.83901 24.0416 5.38595 23.9314 5.91433C23.7726 6.63843 23.372 7.28716 22.7953 7.7542C22.2187 8.22123 21.5002 8.47886 20.7576 8.48484C20.4443 8.48484 20.1324 8.43985 19.8327 8.34917L19.2604 9.01893L18.9893 9.14389H18.2919V10.2149L17.9342 10.5719H16.8613V11.643L16.5036 12H14.3577L14 11.643V9.99572L14.1044 9.74367L17.6624 6.19209C17.5619 5.86735 17.5136 5.52873 17.5194 5.18888C17.5259 4.71336 17.6374 4.24514 17.846 3.81758C18.0546 3.39002 18.3551 3.01366 18.7261 2.71532C19.0971 2.41699 19.5295 2.20402 19.9924 2.09162C20.4553 1.97921 20.9374 1.97084 21.4042 2.06572ZM22.347 7.20673C22.7963 6.84343 23.1087 6.3387 23.2333 5.7751L23.2362 5.77867C23.3248 5.36653 23.3088 4.93882 23.1898 4.5344C23.0707 4.12998 22.8523 3.76167 22.5543 3.46294C22.2564 3.1642 21.8884 2.94451 21.4838 2.82383C21.0792 2.70314 20.6508 2.68529 20.2376 2.77189C19.6802 2.89538 19.1802 3.20147 18.8172 3.64141C18.4543 4.08135 18.2492 4.62985 18.2347 5.19959C18.2275 5.52519 18.2847 5.84793 18.4056 6.14925L18.3269 6.53697L14.7153 10.1435V11.286H16.1459V10.2149L16.5036 9.85791H17.5766V8.78687L17.9342 8.42986H18.8248L19.4514 7.7044L19.8527 7.60444C20.1421 7.71679 20.4499 7.77441 20.7604 7.77438C21.3384 7.76984 21.8978 7.56972 22.347 7.20673ZM21.7476 5.256C21.804 5.17821 21.8441 5.08986 21.8655 4.99623C21.8869 4.90261 21.8891 4.80564 21.8721 4.71113C21.8551 4.61662 21.8192 4.5265 21.7665 4.44617C21.7138 4.36583 21.6454 4.29694 21.5654 4.24361C21.4854 4.19027 21.3954 4.1536 21.3009 4.13577C21.2064 4.11794 21.1092 4.11933 21.0152 4.13985C20.9212 4.16037 20.8324 4.19961 20.7539 4.25521C20.6755 4.3108 20.6091 4.38162 20.5587 4.46343C20.4618 4.62079 20.4298 4.80952 20.4693 4.98998C20.5089 5.17044 20.6169 5.32857 20.7708 5.43116C20.9247 5.53376 21.1124 5.57283 21.2946 5.54016C21.4767 5.5075 21.6391 5.40565 21.7476 5.256Z"
        fill="white"
      />
    </svg>
  );
};
UserApprovalRequest1Icon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default UserApprovalRequest1Icon;
