import PropTypes from 'prop-types';

const VswitchOutput = ({
  width = 40,
  height = 40,
  stroke = 'black',
  strokeWidth = 2,
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <rect width="40" height="40" rx="4" fill="#E8E8E8" />
      <path
        d="M25 9L29 13L25 17"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11 19V17C11 15.9391 11.4214 14.9217 12.1716 14.1716C12.9217 13.4214 13.9391 13 15 13H29"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15 31L11 27L15 23"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M29 21V23C29 24.0609 28.5786 25.0783 27.8284 25.8284C27.0783 26.5786 26.0609 27 25 27H11"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <g clipPath="url(#clip0_1620_23744)">
        <path
          d="M27.0007 23.6663C25.1597 23.6663 23.6673 25.1587 23.6673 26.9997C23.6673 28.8406 25.1597 30.333 27.0007 30.333C28.8416 30.333 30.334 28.8406 30.334 26.9997C30.334 25.1587 28.8416 23.6663 27.0007 23.6663Z"
          fill="#E8E8E8"
          stroke={stroke}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M27 28.333L27 26.9997"
          stroke={stroke}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M27 25.667L26.9967 25.667"
          stroke={stroke}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_1620_23744">
          <rect
            width="8"
            height="8"
            fill="white"
            transform="translate(31 31) rotate(-180)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

VswitchOutput.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  strokeWidth: PropTypes.number,
  className: PropTypes.string,
};

export default VswitchOutput;
