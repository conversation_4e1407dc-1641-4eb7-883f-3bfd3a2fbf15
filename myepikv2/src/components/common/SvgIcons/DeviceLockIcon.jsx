import PropTypes from 'prop-types';

const DeviceLockIcon = ({
  width = 20,
  height = 20,
  stroke = '#D92D20',
  className = '',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M15.8333 9.16406H4.16667C3.24619 9.16406 2.5 9.91025 2.5 10.8307V16.6641C2.5 17.5845 3.24619 18.3307 4.16667 18.3307H15.8333C16.7538 18.3307 17.5 17.5845 17.5 16.6641V10.8307C17.5 9.91025 16.7538 9.16406 15.8333 9.16406Z"
        stroke={stroke}
        strokeWidth="1.66667"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.83325 9.16406V5.83073C5.83325 4.72566 6.27224 3.66585 7.05364 2.88445C7.83504 2.10305 8.89485 1.66406 9.99992 1.66406C11.105 1.66406 12.1648 2.10305 12.9462 2.88445C13.7276 3.66585 14.1666 4.72566 14.1666 5.83073V9.16406"
        stroke={stroke}
        strokeWidth="1.66667"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
DeviceLockIcon.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  stroke: PropTypes.string,
  className: PropTypes.string,
};
export default DeviceLockIcon;
