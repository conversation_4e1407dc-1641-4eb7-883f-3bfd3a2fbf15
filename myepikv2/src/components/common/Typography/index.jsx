import { Typography as AntTypography } from 'antd';
import PropTypes from 'prop-types';
import './styles.css';

/**
 * A reusable Typography component that wraps Ant Design Typography.
 *
 * @param {Object} props - The properties for the Typography component.
 * @param {"secondary" | "success" | "warning" | "danger"} [props.type] - The type of text, used to set the color and tone.
 * @param {boolean} [props.code=false] - If true, formats the text to look like a piece of code.
 * @param {boolean} [props.paragraph=false] - for Typography.paragraph.
 * @param {boolean} [props.copyable=false] - If true, allows the text to be copied to the clipboard by clicking on it.
 * @param {boolean} [props.deleted=false] - If true, renders the text with a strikethrough to indicate deletion.
 * @param {boolean} [props.disabled=false] - If true, disables the text styling.
 * @param {boolean | Object} [props.editable=false] - If true, makes the text editable. Accepts an object for advanced configuration.
 * @param {boolean | Object} [props.ellipsis=false] - If true, truncates the text with ellipsis. Accepts an object for advanced configuration.
 * @param {boolean} [props.keyboard=false] - If true, formats the text to look like a keyboard input.
 * @param {boolean} [props.mark=false] - If true, highlights the text with a yellow background.
 * @param {boolean} [props.italic=false] - If true, renders the text in italics.
 * @param {boolean} [props.strong=false] - If true, renders the text in bold.
 * @param {boolean} [props.underline=false] - If true, underlines the text.
 * @param {1 | 2 | 3 | 4 | 5} [props.level] - The heading level, renders as a title (h1-h5).
 * @param {React.ReactNode} props.children - The content of the typography component.
 * @param {string} [props.href] - If provided, renders the text as a clickable link.
 * @param {string} [props.className] - Additional CSS class names to apply to the component.
 *
 * @returns {JSX.Element} The rendered Typography component.
 */
const Typography = ({
  type,
  code = false,
  paragraph = false,
  copyable = false,
  deleted = false,
  disabled = false,
  editable = false,
  ellipsis = false,
  keyboard = false,
  mark = false,
  italic = false,
  strong = false,
  underline = false,
  level,
  children,
  href,
  className,
  ...rest
}) => {
  const customClass = className || '';

  if (level) {
    // Render Title if level is provided
    return (
      <AntTypography.Title
        type={type}
        code={code}
        copyable={copyable}
        delete={deleted}
        disabled={disabled}
        editable={editable}
        ellipsis={ellipsis}
        mark={mark}
        italic={italic}
        strong={strong}
        underline={underline}
        level={level}
        className={customClass}
        {...rest}
      >
        {children}
      </AntTypography.Title>
    );
  }

  if (href) {
    // Render Link if href is provided
    return (
      <AntTypography.Link
        type={type}
        disabled={disabled}
        underline={underline}
        className={customClass}
        {...rest}
        href={href}
      >
        {children}
      </AntTypography.Link>
    );
  }

  // Render Text or Paragraph
  const Component =
    ellipsis || editable || copyable || paragraph
      ? AntTypography.Paragraph
      : AntTypography.Text;

  return (
    <Component
      type={type}
      code={code}
      copyable={copyable}
      delete={deleted}
      disabled={disabled}
      editable={editable}
      ellipsis={ellipsis}
      keyboard={keyboard}
      mark={mark}
      italic={italic}
      strong={strong}
      underline={underline}
      className={customClass}
      {...rest}
    >
      {children}
    </Component>
  );
};

// Add PropTypes validation
Typography.propTypes = {
  /** The type of text, used to set the color and tone. */
  type: PropTypes.oneOf(['secondary', 'success', 'warning', 'danger']),
  /** Formats the text to look like a piece of code. */
  code: PropTypes.bool,
  /** Allows the text to be copied to the clipboard by clicking on it. */
  copyable: PropTypes.bool,
  /** for typography.paragraph. */
  paragraph: PropTypes.bool,
  /** Renders the text with a strikethrough to indicate deletion. */
  deleted: PropTypes.bool,
  /** Disables the text styling. */
  disabled: PropTypes.bool,
  /** Makes the text editable. */
  editable: PropTypes.oneOfType([
    PropTypes.bool,
    PropTypes.shape({
      editing: PropTypes.bool,
      onStart: PropTypes.func,
      onChange: PropTypes.func,
    }),
  ]),
  /** Truncates the text with ellipsis. */
  ellipsis: PropTypes.oneOfType([
    PropTypes.bool,
    PropTypes.shape({
      rows: PropTypes.number,
      expandable: PropTypes.bool,
      onExpand: PropTypes.func,
      suffix: PropTypes.string,
    }),
  ]),
  /** Formats the text to look like a keyboard input. */
  keyboard: PropTypes.bool,
  /** Highlights the text with a yellow background. */
  mark: PropTypes.bool,
  /** Renders the text in italics. */
  italic: PropTypes.bool,
  /** Renders the text in bold. */
  strong: PropTypes.bool,
  /** Underlines the text. */
  underline: PropTypes.bool,
  /** The heading level, renders as a title (h1-h5). */
  level: PropTypes.oneOf([1, 2, 3, 4, 5]),
  /** The content of the typography component. */
  children: PropTypes.node,
  /** If provided, renders the text as a clickable link. */
  href: PropTypes.string,
  /** Additional CSS class names to apply to the component. */
  className: PropTypes.string,
};

export default Typography;
