.extra-small-text {
  font-size: 0.8rem;
  font-weight: 500;
  line-height: 18px;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  margin: 0;
}

.small-text {
  font-size: 0.9rem;
  font-weight: 500;
  line-height: 20px;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  color: var(--gray-600);
  margin-bottom: 0 !important;
}

.small-text-bold {
  font-size: 0.9rem;
  font-weight: 600;
  line-height: 20px;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  color: var(--gray-600);
  margin-bottom: 0 !important;
}

.heading-two {
  font-size: 1.8rempx;
  font-weight: 600;
  line-height: 38px;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
}

.heading-three {
  font-size: 1.7rem;
  font-weight: 500;
  line-height: 32px;
  text-align: center;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
}

.heading-four {
  font-size: 1.3rem;
  font-weight: 500;
  line-height: 28px;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
}

.heading-four-bold {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 30px;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
}

/* md/medium */
.heading-five {
  font-size: 1rem;
  font-weight: 600;
  line-height: 24px;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
}

/* md/regular */
.text-medium-regular-500 {
  font-size: 1rem;
  font-weight: 500;
  line-height: 24px;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
}

/* md/regular */
.text-medium-regular {
  font-size: 1rem;
  font-weight: 400;
  line-height: 24px;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  color: var(--gray-600);
}

.text-medium-semibold {
  font-size: 1rem;
  font-weight: 600;
  line-height: 24px;
  text-align: center;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
}

.text-medium-large {
  font-size: 1.1rem;
  font-weight: 500;
  line-height: 28px;
  text-align: center;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
}

/* responsive classes */

.responsive-filter-text {
  display: flex;
  align-items: baseline;
  min-width: fit-content;

  font-weight: 500;
  font-size: 15px;
  line-height: 21px;
  color: var(--gray-900);
  text-align: left;
  margin: 0 !important;
}

.responsive-column-text {
  font-weight: 500;
  font-size: 10.5px;
  line-height: 15px;
  color: var(--gray-600);
  text-align: left;
  margin: 0 !important;
}

.responsive-row-text {
  font-weight: 400;
  font-size: 12px;
  line-height: 18px;
  color: var(--gray-600);
  text-align: left;
  margin: 0 !important;
}

.text-inter-600-22-28 {
  font-weight: 600;
  font-size: 22px;
  line-height: 28px;
  margin: 0 !important;
}

.text-inter-600-20-30 {
  font-weight: 600;
  font-size: 20px;
  line-height: 30px;
  color: var(--primary-gray4);
  margin: 0 !important;
}

.text-inter-600-16-24-gray-500 {
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: var(--primary-gray5);
  margin: 0 !important;
}

.text-inter-600-14-20-gray-900 {
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: var(--gray-900);
  margin: 0 !important;
}

.text-inter-600-16-24 {
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: var(--black-3);
  margin: 0 !important;
}

.text-inter-500-12-18 {
  font-weight: 500;
  font-size: 12px;
  line-height: 18px;
  margin: 0 !important;
}
/* base - starting companies  */
.text-inter-600-20-25-gray-900 {
  font-weight: 600;
  font-size: 20px;
  line-height: 25px;
  letter-spacing: 0px;
  color: var(--gray-900);
  margin: 0;
}

.text-inter-600-10-16-granite-blue {
  font-weight: 600;
  font-size: 10px;
  line-height: 16px;
  letter-spacing: 0px;
  color: var(--granite-blue);
  margin: 0;
}

.text-inter-600-10-16-white {
  font-weight: 600;
  font-size: 10px;
  line-height: 16px;
  letter-spacing: 0px;
  color: var(--bg-white-connection);
  margin: 0;
}

.text-inter-500-9-13-gray-600 {
  font-weight: 500;
  font-size: 9px;
  line-height: 13px;
  color: var(--gray-600);
  margin: 0;
}

.text-inter-400-10-16-gray-600 {
  font-weight: 400;
  font-size: 10px;
  line-height: 16px;
  letter-spacing: 0px;
  color: var(--gray-600);
  margin: 0 !important;
}

.text-inter-500-10-16-gray-900 {
  font-weight: 500;
  font-size: 10px;
  line-height: 16px;
  color: var(--gray-900);
  margin: 0 !important;
}

.text-inter-600-13-20-gray-900 {
  font-weight: 600;
  font-size: 13px;
  line-height: 20px;
  color: var(--gray-900);
  margin: 0 !important;
}

.text-inter-500-9-13-gray-700 {
  font-weight: 500;
  font-size: 9px;
  line-height: 13px;
  color: var(--gray-700);
  margin: 0 !important;
}

.text-inter-500-10-16-gray-700 {
  font-weight: 500;
  font-size: 10px;
  line-height: 16px;
  color: var(--gray-700);
  margin: 0 !important;
}

.text-inter-500-10-16-gray-500 {
  font-weight: 400;
  font-size: 10px;
  line-height: 16px;
  color: var(--gray-500);
  margin: 0 !important;
}

.text-inter-600-10-16-gray-700 {
  font-weight: 600;
  font-size: 10px;
  line-height: 16px;
  color: var(--gray-700);
  margin: 0 !important;
}

.text-inter-600-12-18-granite {
  font-weight: 600;
  font-size: 12px;
  line-height: 18px;
  color: var(--granite-blue);
  margin: 0 !important;
}

.text-inter-500-18-28-gray-900 {
  font-weight: 500;
  font-size: 18px;
  line-height: 28px;
  color: var(--gray-900);
  margin: 0 !important;
}

.text-montserrat-500-12-16-white {
  font-family: Montserrat;
  font-weight: 500;
  font-size: 12px;
  line-height: 16px;
  color: #ffffff;
  margin: 0 !important;
}

.text-montserrat-600-18-16-white {
  font-family: Montserrat;
  font-weight: 600;
  font-size: 18px;
  line-height: 16px;
  color: #ffffff;
  margin: 0 !important;
}

.text-montserrat-600-16-16-gray-500 {
  font-family: Montserrat;
  font-weight: 600;
  font-size: 16px;
  line-height: 16px;
  color: var(--primary-gray5);
  margin: 0;
}

.text-montserrat-600-14-14-gray-500 {
  font-family: Montserrat;
  font-weight: 600;
  font-size: 14px;
  line-height: 14px;
  color: var(--primary-gray5);
  margin: 0;
}

.text-montserrat-600-14-14-granite-primary {
  font-family: Montserrat;
  font-weight: 600;
  font-size: 14px;
  line-height: 14px;
  color: var(--granite-blue);
  margin: 0;
}

.text-montserrat-500-16-20-black {
  font-family: Montserrat;
  font-weight: 500;
  font-size: 16px;
  line-height: 20px;
  color: var(--black-1);
  margin: 0;
}

.text-montserrat-600-20-100-black {
  font-family: Montserrat;
  font-weight: 600;
  font-size: 20px;
  line-height: 100%;
  color: var(--black-1);
  margin: 0;
}

.text-montserrat-600-13-100-black {
  font-family: Montserrat;
  font-weight: 600;
  font-size: 13px;
  line-height: 100%;
  color: var(--black-1);
  margin: 0;
}

.text-inter-500-14-20-gray-600 {
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  color: var(--gray-600);
  margin: 0 !important;
}

.text-inter-500-14-20-gray-700 {
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  color: var(--primary-gray);
  margin: 0;
}

.text-inter-600-14-20-gray3 {
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: var(--primary-gray3);
  margin: 0 !important;
}

.text-inter-400-14-20 {
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  margin: 0;
}

.text-inter-400-16-24 {
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: var(--black-3);
  margin: 0;
}

.text-inter-700-14-20 {
  font-weight: 700;
  font-size: 14px;
  line-height: 100%;
  color: var(--black-1);
  margin: 0;
}

.text-inter-700-11-15 {
  font-weight: 700;
  font-size: 11px;
  line-height: 15px;
  color: var(--black-1);
  margin: 0;
}

@media (min-width: 1920px) {
  .responsive-filter-text {
    font-size: 20px;
    line-height: 28px;
  }
  .responsive-column-text {
    font-size: 14px;
    line-height: 20px;
  }

  .responsive-row-text {
    font-size: 16px;
    line-height: 24px;
  }

  .text-inter-600-22-28 {
    font-size: 30px;
    line-height: 38px;
  }

  .text-inter-600-20-30 {
    font-size: 24px;
    line-height: 32px;
  }

  .text-inter-600-16-24-gray-500 {
    font-size: 18px;
    line-height: 28px;
  }

  .text-inter-600-16-24 {
    font-size: 18px;
    line-height: 28px;
  }

  .text-inter-500-12-18 {
    font-size: 14px;
    line-height: 20px;
  }

  .text-montserrat-500-12-16-white {
    font-size: 14px;
    line-height: 16px;
  }

  .text-montserrat-600-18-16-white {
    font-size: 24px;
    line-height: 16px;
  }

  .text-inter-500-14-20-gray-600 {
    font-size: 16px;
    line-height: 24px;
  }

  .text-inter-600-14-20-gray3 {
    font-size: 16px;
    line-height: 24px;
  }

  .text-inter-400-14-20 {
    font-size: 16px;
    line-height: 24px;
  }

  .text-inter-600-20-25-gray-900 {
    font-size: 30px;
    line-height: 38px;
  }

  .text-inter-600-10-16-granite-blue {
    font-size: 16px;
    line-height: 24px;
  }

  .text-inter-600-10-16-white {
    font-size: 16px;
    line-height: 24px;
  }

  .text-inter-500-9-13-gray-600 {
    font-size: 14px;
    line-height: 20px;
  }

  .text-inter-400-10-16-gray-600 {
    font-size: 16px;
    line-height: 24px;
  }

  .text-inter-500-10-16-gray-900 {
    font-size: 16px;
    line-height: 24px;
  }

  .text-inter-600-13-20-gray-900 {
    font-size: 20px;
    line-height: 30px;
  }

  .text-inter-500-9-13-gray-700 {
    font-size: 14px;
    line-height: 20px;
  }

  .text-inter-500-10-16-gray-700 {
    font-size: 16px;
    line-height: 24px;
  }

  .text-inter-500-10-16-gray-500 {
    font-size: 16px;
    line-height: 24px;
  }

  .text-inter-600-10-16-gray-700 {
    font-size: 16px;
    line-height: 24px;
  }

  .text-inter-600-12-18-granite {
    font-size: 16px;
    line-height: 24px;
  }

  .text-inter-500-18-28-gray-900 {
    font-size: 20px;
    line-height: 28px;
  }
}
