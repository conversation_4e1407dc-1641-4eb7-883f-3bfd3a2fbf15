import { Select as AntSelect } from 'antd';
import PropTypes from 'prop-types';

/**
 * A customizable Select component that wraps the Ant Design Select.
 *
 * @param {Object} props - The properties for the Select component.
 * @param {Array<Object>} [props.options=[]] - An array of options for the Select component. Each option should have `label` and `value` properties.
 * @param {boolean|Object} [props.allowClear=false] - Whether to allow clearing the selected value. Can also be an object specifying a custom clear icon.
 * @param {boolean} [props.disabled=false] - Whether the Select is disabled.
 * @param {boolean} [props.loading=false] - Whether the Select is in a loading state.
 * @param {React.ReactNode} [props.placeholder='Please select'] - The placeholder text displayed when no value is selected.
 * @param {boolean} [props.showSearch=false] - Whether to show a search input for filtering options.
 * @param {boolean} [props.multiple=false] - Whether to allow selecting multiple options.
 * @param {boolean} [props.tags=false] - Whether to enable the "tags" mode for creating new options.
 * @param {'large'|'middle'|'small'} [props.size='middle'] - The size of the Select component.
 * @param {'error'|'warning'} [props.status] - The status of the Select for validation feedback.
 * @param {boolean} [props.labelInValue=false] - Whether the selected value should include the `label` and `value` as an object.
 * @param {number|'responsive'} [props.maxTagCount] - The maximum number of tags to display when using `multiple` or `tags` mode. Can also be `responsive`.
 * @param {function} [props.onChange] - A callback function triggered when the selected value changes. Receives the new value as an argument.
 * @param {function} [props.onSearch] - A callback function triggered when the search input value changes. Receives the search input value as an argument.
 * @param {function} [props.customRender] - A custom render function for options. Each option is passed to this function for rendering.
 * @param {Object} [props.props] - Additional properties passed to the Ant Design Select component.
 *
 * @returns {JSX.Element} The rendered Ant Design Select component.
 */
const Select = ({
  options = [],
  allowClear = false,
  disabled = false,
  loading = false,
  placeholder = 'Please select',
  showSearch = false,
  multiple = false,
  tags = false,
  size = 'middle',
  status,
  labelInValue = false,
  maxTagCount,
  onChange,
  onSearch,
  customRender,
  ...props
}) => {
  const mode = multiple ? 'multiple' : tags ? 'tags' : undefined;

  return (
    <AntSelect
      mode={mode}
      options={options}
      allowClear={allowClear}
      disabled={disabled}
      loading={loading}
      placeholder={placeholder}
      showSearch={showSearch}
      size={size}
      status={status}
      labelInValue={labelInValue}
      maxTagCount={maxTagCount}
      onChange={onChange}
      onSearch={onSearch}
      {...props}
    >
      {customRender && options.map(customRender)}
    </AntSelect>
  );
};

// Add PropTypes validation
Select.propTypes = {
  options: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      value: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
        .isRequired,
    }),
  ),
  allowClear: PropTypes.oneOfType([
    PropTypes.bool,
    PropTypes.shape({
      clearIcon: PropTypes.node,
    }),
  ]),
  disabled: PropTypes.bool,
  loading: PropTypes.bool,
  placeholder: PropTypes.node,
  showSearch: PropTypes.bool,
  multiple: PropTypes.bool,
  tags: PropTypes.bool,
  size: PropTypes.oneOf(['large', 'middle', 'small']),
  status: PropTypes.oneOf(['error', 'warning']),
  labelInValue: PropTypes.bool,
  maxTagCount: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.oneOf(['responsive']),
  ]),
  onChange: PropTypes.func,
  onSearch: PropTypes.func,
  customRender: PropTypes.func,
};

export default Select;
