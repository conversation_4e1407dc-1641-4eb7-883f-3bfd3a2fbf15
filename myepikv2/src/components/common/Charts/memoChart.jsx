import { memo, useState } from 'react';
import { Pie } from '@ant-design/plots';
import { Button } from 'antd';
import PropTypes from 'prop-types';

// Memoized Pie Component
const DemoPie = memo(
  ({ data, onReady }) => {
    const config = {
      data,
      angleField: 'value',
      colorField: 'type',
      label: {
        text: 'value',
        position: 'outside',
      },
      onReady,
    };
    return <Pie {...config} />;
  },
  (pre, next) => JSON.stringify(pre?.data) === JSON.stringify(next?.data),
);

// Add display name for debugging
DemoPie.displayName = 'DemoPie';

// Define PropTypes
DemoPie.propTypes = {
  data: PropTypes.arrayOf(
    PropTypes.shape({
      type: PropTypes.string.isRequired,
      value: PropTypes.number.isRequired,
    }),
  ).isRequired,
  onReady: PropTypes.func,
};

// Main Component
const DemoMemo = () => {
  const [count, setCount] = useState(0);
  const [data, setData] = useState([
    {
      type: 'Devices',
      value: 27,
    },
    {
      type: 'Nodes',
      value: 25,
    },
    {
      type: 'Edges',
      value: 18,
    },
    {
      type: 'Boxes',
      value: 15,
    },
    {
      type: 'Users',
      value: 10,
    },
    {
      type: 'ed',
      value: 5,
    },
  ]);

  return (
    <div>
      <Button onClick={() => setCount((prev) => prev + 1)}>Counter</Button>
      <Button
        style={{ margin: '0 10px' }}
        type="primary"
        onClick={() =>
          setData(
            data.map((d) => ({
              ...d,
              value: Math.floor(Math.random() * 100),
            })),
          )
        }
      >
        Reset data
      </Button>
      <span>{count}</span>
      <DemoPie
        data={data}
        onReady={() => {
          // Use the chart object here if necessary
        }}
      />
    </div>
  );
};

export default DemoMemo;
