export const pieConfig = ({
  angleField = 'value',
  colorField = 'type',
  innerRadius = 0,
  radius = 0.7,
  colors = [],
  label,
  legend,
} = {}) => ({
  angleField,
  colorField,
  innerRadius,
  label,
  radius,
  legend,
  scale: { color: { palette: colors } },
});

export const lineConfig = ({
  width = 800,
  height = 400,
  autoFit = false,
  xField = 'year',
  yField = 'value',
  pointSize = 5,
  pointShape = 'diamond',
  labelStyle = { fill: '#aaa' },
} = {}) => ({
  width,
  height,
  autoFit,
  xField,
  yField,
  point: {
    size: pointSize,
    shape: pointShape,
  },
  label: {
    style: labelStyle,
  },
});

export const demoColumnConfig = ({
  xField = 'letter',
  yField = 'frequency',
  labelFormatter = (d) => `${(d.frequency * 100).toFixed(1)}%`,
  axisFormatter = '.0%',
  radiusTopLeft = 10,
  radiusTopRight = 10,
} = {}) => ({
  xField,
  yField,
  label: {
    text: labelFormatter,
    textBaseline: 'bottom',
  },
  axis: {
    y: {
      labelFormatter: axisFormatter,
    },
  },
  style: {
    radiusTopLeft,
    radiusTopRight,
  },
});

export const radialBarConfig = ({
  xField = 'name',
  yField = 'star',
  radius = 1,
  innerRadius = 0.2,
  tooltipItems = ['star'],
} = {}) => ({
  xField,
  yField,
  radius,
  innerRadius,
  tooltip: {
    items: tooltipItems,
  },
});

export const demoLineConfig = ({
  xField = 'date',
  yField = 'value',
  colorField = 'type',
  colorRange = ['#30BF78', '#F4664A', '#FAAD14'],
  lineWidth = 2,
  lineDash = (data) => (data[0].type === 'register' ? [4, 4] : []),
  opacity = (data) => (data[0].type !== 'register' ? 0.5 : 1),
} = {}) => ({
  xField,
  yField,
  colorField,
  axis: {
    y: {
      labelFormatter: (v) =>
        `${v}`.replace(/\d{1,3}(?=(\d{3})+$)/g, (s) => `${s},`),
    },
  },
  scale: { color: { range: colorRange } },
  style: {
    lineWidth,
    lineDash,
    opacity,
  },
});

export const radarConfig = ({
  xField = 'name',
  yField = 'star',
  fillOpacity = 0.2,
} = {}) => ({
  xField,
  yField,
  area: {
    style: {
      fillOpacity,
    },
  },
  scale: {
    x: {
      padding: 0.5,
      align: 0,
    },
    y: {
      nice: true,
    },
  },
  axis: {
    x: {
      title: false,
      grid: true,
    },
    y: {
      gridAreaFill: 'rgba(0, 0, 0, 0.04)',
      label: false,
      title: false,
    },
  },
});
