import { memo } from 'react';
import * as Charts from '@ant-design/plots';
import PropTypes from 'prop-types';

/**
 * Reusable Chart component that supports multiple chart types from Ant Design Charts.
 *
 * @param {Object} props - The component properties.
 * @param {string} props.type - Type of chart (e.g., 'Pie', 'Bar', 'Line').
 * @param {Array} props.data - Data to display in the chart.
 * @param {Object} props.config - Custom configuration options for the chart.
 * @param {Function} [props.onReady] - Callback function when the chart is ready.
 * @returns {JSX.Element} The rendered chart component.
 */
const Chart = memo(
  ({ type = 'Pie', data, config = {}, onReady }) => {
    const ChartComponent = Charts[type];

    if (!ChartComponent) {
      console.error(`Unsupported chart type: ${type}`);
      return null;
    }

    // Combine provided config with essential props for the chart type
    const chartConfig = {
      data,
      ...config,
      onReady,
    };

    return <ChartComponent {...chartConfig} />;
  },
  (prevProps, nextProps) =>
    JSON.stringify(prevProps?.data) === JSON.stringify(nextProps?.data),
);

// Set displayName for better debugging in React DevTools
Chart.displayName = 'Chart';

// Add PropTypes for props validation
Chart.propTypes = {
  type: PropTypes.string, // Type of the chart, defaults to 'Pie'
  data: PropTypes.array.isRequired, // Data is required for rendering the chart
  config: PropTypes.object, // Configuration object for the chart
  onReady: PropTypes.func, // Optional callback when the chart is ready
};

export default Chart;
