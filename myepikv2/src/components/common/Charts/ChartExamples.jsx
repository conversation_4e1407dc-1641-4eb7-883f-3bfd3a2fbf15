import Chart from './Chart';

import {
  pieConfig,
  lineConfig,
  demoColumnConfig,
  radialBarConfig,
  demoLineConfig,
  radarConfig,
} from './chartConfigs';

const ChartExamples = () => {
  const pieData = [
    {
      type: 'Devices',
      value: 27,
    },
    {
      type: 'Nodes',
      value: 25,
    },
    {
      type: 'Edges',
      value: 18,
    },
    {
      type: 'Boxes',
      value: 15,
    },
    {
      type: 'Users',
      value: 10,
    },
    {
      type: 'ed',
      value: 5,
    },
  ];

  const lineData = [
    { year: '1991', value: 3 },
    { year: '1992', value: 4 },
    { year: '1993', value: 3.5 },
    { year: '1994', value: 5 },
    { year: '1995', value: 4.9 },
    { year: '1996', value: 6 },
    { year: '1997', value: 7 },
    { year: '1998', value: 9 },
    { year: '1999', value: 13 },
  ];

  const demoColumnData = [
    {
      letter: 'A',
      frequency: 0.08167,
    },
    {
      letter: 'B',
      frequency: 0.01492,
    },
    {
      letter: 'C',
      frequency: 0.02782,
    },
    {
      letter: 'D',
      frequency: 0.04253,
    },
    {
      letter: 'E',
      frequency: 0.12702,
    },
    {
      letter: 'F',
      frequency: 0.02288,
    },
    {
      letter: 'G',
      frequency: 0.02015,
    },
    {
      letter: 'H',
      frequency: 0.06094,
    },
    {
      letter: 'I',
      frequency: 0.06966,
    },
    {
      letter: 'J',
      frequency: 0.00153,
    },
    {
      letter: 'K',
      frequency: 0.00772,
    },
    {
      letter: 'L',
      frequency: 0.04025,
    },
    {
      letter: 'M',
      frequency: 0.02406,
    },
    {
      letter: 'N',
      frequency: 0.06749,
    },
    {
      letter: 'O',
      frequency: 0.07507,
    },
    {
      letter: 'P',
      frequency: 0.01929,
    },
    {
      letter: 'Q',
      frequency: 0.00095,
    },
    {
      letter: 'R',
      frequency: 0.05987,
    },
    {
      letter: 'S',
      frequency: 0.06327,
    },
    {
      letter: 'T',
      frequency: 0.09056,
    },
    {
      letter: 'U',
      frequency: 0.02758,
    },
    {
      letter: 'V',
      frequency: 0.00978,
    },
    {
      letter: 'W',
      frequency: 0.0236,
    },
    {
      letter: 'X',
      frequency: 0.0015,
    },
    {
      letter: 'Y',
      frequency: 0.01974,
    },
    {
      letter: 'Z',
      frequency: 0.00074,
    },
  ];

  const radialBarData = [
    { name: 'X6', star: 297 },
    { name: 'G', star: 506 },
    { name: 'AVA', star: 805 },
    { name: 'G2Plot', star: 1478 },
    { name: 'L7', star: 2029 },
    { name: 'G6', star: 7100 },
    { name: 'F2', star: 7346 },
    { name: 'G2', star: 10178 },
  ];

  const demoLineData = [
    {
      date: '2018/8/1',
      type: 'download',
      value: 4623,
    },
    {
      date: '2018/8/1',
      type: 'register',
      value: 2208,
    },
    {
      date: '2018/8/1',
      type: 'bill',
      value: 182,
    },
    {
      date: '2018/8/2',
      type: 'download',
      value: 6145,
    },
    {
      date: '2018/8/2',
      type: 'register',
      value: 2016,
    },
    {
      date: '2018/8/2',
      type: 'bill',
      value: 257,
    },
    {
      date: '2018/8/3',
      type: 'download',
      value: 508,
    },
    {
      date: '2018/8/3',
      type: 'register',
      value: 2916,
    },
    {
      date: '2018/8/3',
      type: 'bill',
      value: 289,
    },
    {
      date: '2018/8/4',
      type: 'download',
      value: 6268,
    },
    {
      date: '2018/8/4',
      type: 'register',
      value: 4512,
    },
    {
      date: '2018/8/4',
      type: 'bill',
      value: 428,
    },
    {
      date: '2018/8/5',
      type: 'download',
      value: 6411,
    },
    {
      date: '2018/8/5',
      type: 'register',
      value: 8281,
    },
    {
      date: '2018/8/5',
      type: 'bill',
      value: 619,
    },
    {
      date: '2018/8/6',
      type: 'download',
      value: 1890,
    },
    {
      date: '2018/8/6',
      type: 'register',
      value: 2008,
    },
    {
      date: '2018/8/6',
      type: 'bill',
      value: 87,
    },
    {
      date: '2018/8/7',
      type: 'download',
      value: 4251,
    },
    {
      date: '2018/8/7',
      type: 'register',
      value: 1963,
    },
    {
      date: '2018/8/7',
      type: 'bill',
      value: 706,
    },
    {
      date: '2018/8/8',
      type: 'download',
      value: 2978,
    },
    {
      date: '2018/8/8',
      type: 'register',
      value: 2367,
    },
    {
      date: '2018/8/8',
      type: 'bill',
      value: 387,
    },
    {
      date: '2018/8/9',
      type: 'download',
      value: 3880,
    },
    {
      date: '2018/8/9',
      type: 'register',
      value: 2956,
    },
    {
      date: '2018/8/9',
      type: 'bill',
      value: 488,
    },
    {
      date: '2018/8/10',
      type: 'download',
      value: 3606,
    },
    {
      date: '2018/8/10',
      type: 'register',
      value: 678,
    },
    {
      date: '2018/8/10',
      type: 'bill',
      value: 507,
    },
    {
      date: '2018/8/11',
      type: 'download',
      value: 4311,
    },
    {
      date: '2018/8/11',
      type: 'register',
      value: 3188,
    },
    {
      date: '2018/8/11',
      type: 'bill',
      value: 548,
    },
    {
      date: '2018/8/12',
      type: 'download',
      value: 4116,
    },
    {
      date: '2018/8/12',
      type: 'register',
      value: 3491,
    },
    {
      date: '2018/8/12',
      type: 'bill',
      value: 456,
    },
    {
      date: '2018/8/13',
      type: 'download',
      value: 6419,
    },
    {
      date: '2018/8/13',
      type: 'register',
      value: 2852,
    },
    {
      date: '2018/8/13',
      type: 'bill',
      value: 689,
    },
    {
      date: '2018/8/14',
      type: 'download',
      value: 1643,
    },
    {
      date: '2018/8/14',
      type: 'register',
      value: 4788,
    },
    {
      date: '2018/8/14',
      type: 'bill',
      value: 280,
    },
    {
      date: '2018/8/15',
      type: 'download',
      value: 445,
    },
    {
      date: '2018/8/15',
      type: 'register',
      value: 4319,
    },
    {
      date: '2018/8/15',
      type: 'bill',
      value: 176,
    },
  ];

  const radarData = [
    { name: 'G2', star: 10371 },
    { name: 'G6', star: 7380 },
    { name: 'F2', star: 7414 },
    { name: 'L7', star: 2140 },
    { name: 'X6', star: 660 },
    { name: 'AVA', star: 885 },
    { name: 'G2Plot', star: 1626 },
  ];

  return (
    <div style={{ padding: 20, width: '50%' }}>
      <h3>Pie Chart</h3>
      <Chart
        type="Pie"
        data={pieData}
        config={pieConfig({ labelType: 'outer', innerRadius: 0.6 })}
      />

      <h3>Line Chart</h3>
      <Chart
        type="Line"
        data={lineData}
        config={lineConfig({ width: 600, pointSize: 8 })}
      />

      <h3>Demo Column Chart</h3>
      <Chart
        type="Column"
        data={demoColumnData}
        config={demoColumnConfig({ radiusTopLeft: 15 })}
      />

      <h3>Radial Bar Chart</h3>
      <Chart
        type="RadialBar"
        data={radialBarData}
        config={radialBarConfig({ innerRadius: 0.3 })}
      />

      <h3>Demo Line Chart</h3>
      <Chart
        type="Line"
        data={demoLineData}
        config={demoLineConfig({ colorRange: ['#123456', '#654321'] })}
      />

      <h3>Radar Chart</h3>
      <Chart
        type="Radar"
        data={radarData.map((d) => ({ ...d, star: Math.sqrt(d.star) }))}
        config={radarConfig()}
      />
    </div>
  );
};

export default ChartExamples;
