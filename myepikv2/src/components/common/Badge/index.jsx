import PropTypes from 'prop-types';
import { Badge as AntBadge } from 'antd';

/**
 * A customizable Badge component that wraps the Ant Design Badge.
 *
 * @param {Object} props - The properties for the Badge component.
 * @param {boolean} [props.ribbon=false] - Determines whether to display the badge as a ribbon.
 * @param {React.ReactNode} [props.ribbonText] - The text to display inside the ribbon.
 * @param {string} [props.ribbonColor] - The background color of the ribbon.
 * @param {string} [props.color] - The background color of the badge.
 * @param {React.ReactNode} [props.count] - The number to display inside the badge.
 * @param {boolean} [props.dot=false] - Whether to display the badge as a small dot without a number.
 * @param {Array<number>} [props.offset] - Adjusts the badge's position, [x, y].
 * @param {number} [props.overflowCount=99] - The maximum number to display in the badge before showing `99+`.
 * @param {boolean} [props.showZero=false] - Whether to display the badge when `count` is 0.
 * @param {React.ReactNode} [props.children] - The wrapped content to which the badge is applied.
 * @param {string} [props.className] - Additional class names for the badge.
 * @param {Object} [props.classNames={}] - Custom class names for different parts of the badge.
 * @param {Object} [props.styles={}] - Inline styles for different parts of the badge.
 * @param {"success" | "processing" | "default" | "error" | "warning"} [props.status] - Sets the status of the badge.
 * @param {React.ReactNode} [props.text] - Text to display alongside the badge when using a status badge.
 * @param {string} [props.title] - Title attribute for the badge.
 * @param {"default" | "small"} [props.size] - The size of the badge.
 * @param {"start" | "end"} [props.ribbonPlacement="end"] - The placement of the ribbon.
 *
 * @returns {JSX.Element} The rendered Ant Design Badge component with added functionality.
 */
const Badge = ({
  ribbon = false,
  ribbonText,
  ribbonColor,
  color,
  count,
  dot = false,
  offset,
  overflowCount = 99,
  showZero = false,
  children,
  className,
  classNames = {},
  styles = {},
  status,
  text,
  title,
  size,
  ribbonPlacement = 'end',
  ...rest
}) => {
  if (ribbon) {
    return (
      <AntBadge.Ribbon
        text={ribbonText}
        color={ribbonColor}
        placement={ribbonPlacement}
        className={classNames?.root || ''}
        style={styles?.root || {}}
      >
        {children}
      </AntBadge.Ribbon>
    );
  }

  return (
    <AntBadge
      color={color}
      count={count}
      dot={dot}
      offset={offset}
      overflowCount={overflowCount}
      showZero={showZero}
      status={status}
      text={text}
      title={title}
      size={size}
      className={`custom-badge ${className || ''} ${classNames?.root || ''}`}
      style={styles?.root || {}}
      {...rest}
    >
      {children}
    </AntBadge>
  );
};

Badge.propTypes = {
  ribbon: PropTypes.bool,
  ribbonText: PropTypes.node,
  ribbonColor: PropTypes.string,
  color: PropTypes.string,
  count: PropTypes.node,
  dot: PropTypes.bool,
  offset: PropTypes.arrayOf(PropTypes.number),
  overflowCount: PropTypes.number,
  showZero: PropTypes.bool,
  children: PropTypes.node,
  className: PropTypes.string,
  classNames: PropTypes.objectOf(PropTypes.string),
  styles: PropTypes.objectOf(PropTypes.object),
  status: PropTypes.oneOf([
    'success',
    'processing',
    'default',
    'error',
    'warning',
  ]),
  text: PropTypes.node,
  title: PropTypes.string,
  size: PropTypes.oneOf(['default', 'small']),
  ribbonPlacement: PropTypes.oneOf(['start', 'end']),
};

Badge.defaultProps = {
  ribbon: false,
  dot: false,
  showZero: false,
  overflowCount: 99,
  ribbonPlacement: 'end',
  classNames: {},
  styles: {},
};

export default Badge;
