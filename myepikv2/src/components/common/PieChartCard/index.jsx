import { Divider } from 'antd';
import PropTypes from 'prop-types';
import { Chart, Typography, Flex } from '@/components';
import { pieConfig } from '@/components/common/Charts/chartConfigs';

const Legend = ({ label1, label2 }) => (
  <Flex gap={16} justify="center">
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <div
        style={{
          width: 10,
          height: 10,
          backgroundColor: 'var(--granite-blue)',
          borderRadius: '50%',
          marginRight: 8,
        }}
      />
      <span>{label1}</span>
    </div>
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <div
        style={{
          width: 10,
          height: 10,
          backgroundColor: 'var(--button-light-blue-1)',
          borderRadius: '50%',
          marginRight: 8,
        }}
      />
      <span>{label2}</span>
    </div>
  </Flex>
);

Legend.propTypes = {
  label1: PropTypes.string.isRequired,
  label2: PropTypes.string.isRequired,
};

const PieChartCard = ({ title, pieData, config, label1, label2 }) => {
  return (
    <Flex
      vertical
      style={{
        border: '1px solid var(--border-grey)',
        borderRadius: '8px',
        marginBottom: 16,
        height: '100%',
      }}
    >
      <Typography className="heading-four-bold" style={{ padding: '6px 16px' }}>
        {title}
      </Typography>
      <Divider style={{ margin: 0, borderColor: 'var(--border-grey)' }} />
      <Flex
        style={{ height: 220, width: '100%' }}
        justifyContent="center"
        align="center"
      >
        <Chart type="Pie" data={pieData} config={pieConfig({ ...config })} />
      </Flex>
      <Legend label1={label1} label2={label2} />
    </Flex>
  );
};

export default PieChartCard;

PieChartCard.propTypes = {
  title: PropTypes.string.isRequired,
  pieData: PropTypes.object.isRequired,
  config: PropTypes.object.isRequired,
  label1: PropTypes.string.isRequired,
  label2: PropTypes.string.isRequired,
};
