import { Space } from 'antd';
import PropTypes from 'prop-types';
import {
  CloseOutlined,
  MoreOutlined,
  EditOutlined,
  CheckOutlined,
} from '@ant-design/icons';
import { colors } from '@/constants';
import { Button, Tag } from '@/components';

// AntTag Component
export const AntTag = ({ content, color, icon, ...rest }) => {
  const style = `${color}-tag`;
  return (
    <Tag className={['tag-addable', style].join(' ')} icon={icon} {...rest}>
      {content}
    </Tag>
  );
};

AntTag.propTypes = {
  content: PropTypes.node.isRequired, // The content inside the tag
  color: PropTypes.string, // The color of the tag
  icon: PropTypes.node, // Icon to be displayed inside the tag
};

AntTag.defaultProps = {
  color: 'default', // Default color if none is provided
  icon: null, // No icon by default
};

// DotTag Component
export const DotTag = ({ content, success }) => (
  <Tag className="tag-addable" style={{ display: 'flex' }}>
    <span className={`tag-dot ${success ? 'tag-dot-green' : 'tag-dot-red'}`}>
      2
    </span>
    {content}
  </Tag>
);

DotTag.propTypes = {
  content: PropTypes.node.isRequired, // The content inside the tag
  success: PropTypes.bool.isRequired, // Indicates success (green) or failure (red)
};

export function getColumns() {
  const onNameClick = (record, ...index) => {
    console.log('Cell ', index, ' is clicked with data', record);
  };
  return [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (text) => <a type="text">{text}</a>,
      onCell: (record, index) => {
        return {
          onClick: () => onNameClick(record, index),
          onMouseOver: () => console.log(record, index),
        };
      },
    },
    {
      title: 'City',
      dataIndex: 'city',
      render: (city) => <span className="lb1-color">{city}</span>,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      render: (text) => (
        <DotTag content={text ? 'active' : 'offline'} success={true} />
      ),
    },
    {
      title: 'Age',
      dataIndex: 'age',
      sorter: (a, b) => a.age - b.age,
      render: (text) => {
        if (text == '22')
          return (
            <AntTag content={text} color={'red'} icon={<CloseOutlined />} />
          );
        if (text == 32)
          return (
            <AntTag
              content={text}
              icon={<CheckOutlined twoToneColor={colors.danger.primaryGreen} />}
              color="default"
            />
          );
        return (
          <AntTag
            content={text}
            icon={<CheckOutlined twoToneColor={colors.danger.primaryGreen} />}
          />
        );
      },
    },
    {
      title: 'Address',
      dataIndex: 'address',
      filters: [
        {
          text: 'London',
          value: 'London',
        },
        {
          text: 'New York',
          value: 'New York',
        },
      ],
      onFilter: (value, record) => record.address.indexOf(value) === 0,
    },
    {
      title: 'Action',
      key: 'action',
      render: () => (
        <Space size="middle">
          <Button
            type="primary"
            onClick={() => {
              console.log('delete is pressed');
            }}
          >
            Delete
          </Button>
          <Button type="text">
            <EditOutlined />
          </Button>
          <a>
            <Space>
              More actions
              <MoreOutlined />
            </Space>
          </a>
        </Space>
      ),
    },
  ];
}
