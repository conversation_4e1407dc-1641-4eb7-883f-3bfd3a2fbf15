import { useEffect, useState } from 'react';
import { DataTable } from '@/components';
import { getColumns } from './constant';

const TableExamples = () => {
  const getData = (length) =>
    Array.from({
      length: length,
    }).map((_, i) => ({
      key: i,
      name: `John Brown ${i}`,
      city: `City number-${i}`,
      status: i % 2 ? true : false,
      age: Number(`${i}2`),
      address: `New York No. ${i} Lake Park`,
      description: `My name is <PERSON>, I am ${i}2 years old, living in New York No. ${i} Lake Park.`,
    }));

  const [loading, setLoading] = useState(true);
  const [paginationData, setPaginationData] = useState({
    page: 1,
    totalRecord: 350,
    pageSize: 5,
  });

  // mock data
  const [columns] = useState(getColumns() || []);
  const [data, setData] = useState(getData(5));

  useEffect(() => {
    const data = getData(paginationData.pageSize);
    setTimeout(() => {
      setLoading(false);
      setData(data);
    }, 0);
  }, [paginationData]);

  const onPaginationChange = (page, pageSize) => {
    setLoading(true);
    setPaginationData((pre) => ({
      ...pre,
      page: page,
      pageSize,
    }));
  };

  const onRowClick = (row, index) => {
    console.log('row clicked', index);
  };

  return (
    <div>
      <DataTable
        columns={columns}
        data={data}
        showTitle="This is customized datatable"
        onRowClick={onRowClick}
        loading={loading}
        rowSelection={true}
        onRowsSelectionChange={(rowsKeys, rowsData) => {
          console.log('custom row selected', rowsKeys, rowsData);
        }}
        onPaginationChange={onPaginationChange}
        paginationData={paginationData}
        pageSizeOptions={['5', '10', '15', '20', '25', '200']}
        paginationBtnOutside={true}
        paginationBtnText={['Pre']}
      />
    </div>
  );
};

export default TableExamples;
