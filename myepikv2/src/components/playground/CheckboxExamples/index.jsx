import { useState } from 'react';
import { Col, Divider, Row, Space } from 'antd';
import { Checkbox, Button } from '@/components';

const CheckboxExamples = () => {
  const [checked, setChecked] = useState(true);
  const [disabled, setDisabled] = useState(false);
  const [checkedList, setCheckedList] = useState(['Apple', 'Orange']);
  const plainOptions = ['Apple', 'Pear', 'Orange'];
  const checkAll = plainOptions.length === checkedList.length;
  const indeterminate =
    checkedList.length > 0 && checkedList.length < plainOptions.length;

  const toggleChecked = () => setChecked(!checked);
  const toggleDisable = () => setDisabled(!disabled);
  const onChange = (e) => setChecked(e.target.checked);
  const onGroupChange = (list) => setCheckedList(list);
  const onCheckAllChange = (e) =>
    setCheckedList(e.target.checked ? plainOptions : []);

  return (
    <Space
      direction="vertical"
      size="large"
      style={{ padding: '20px', maxWidth: '400px', margin: '0 auto' }}
    >
      <Checkbox checked={checked} onChange={onChange}>
        Basic Checkbox
      </Checkbox>
      <Checkbox defaultChecked={false} disabled>
        Disabled Checkbox
      </Checkbox>
      <Checkbox indeterminate disabled>
        Indeterminate Disabled Checkbox
      </Checkbox>
      <Checkbox defaultChecked disabled>
        Checked Disabled Checkbox
      </Checkbox>

      <Checkbox checked={checked} disabled={disabled} onChange={onChange}>
        {`${checked ? 'Checked' : 'Unchecked'} - ${
          disabled ? 'Disabled' : 'Enabled'
        }`}
      </Checkbox>
      <Button type="primary" onClick={toggleChecked}>
        {!checked ? 'Check' : 'Uncheck'}
      </Button>
      <Button onClick={toggleDisable} style={{ marginLeft: 10 }}>
        {!disabled ? 'Disable' : 'Enable'}
      </Button>

      <Divider />
      <Checkbox
        indeterminate={indeterminate}
        onChange={onCheckAllChange}
        checked={checkAll}
      >
        Check all
      </Checkbox>
      <Checkbox.Group
        options={plainOptions}
        value={checkedList}
        onChange={onGroupChange}
      />

      <Divider />
      <Checkbox.Group style={{ width: '100%' }} onChange={onGroupChange}>
        <Row>
          <Col span={8}>
            <Checkbox value="A">A</Checkbox>
          </Col>
          <Col span={8}>
            <Checkbox value="B">B</Checkbox>
          </Col>
          <Col span={8}>
            <Checkbox value="C">C</Checkbox>
          </Col>
          <Col span={8}>
            <Checkbox value="D">D</Checkbox>
          </Col>
          <Col span={8}>
            <Checkbox value="E">E</Checkbox>
          </Col>
        </Row>
      </Checkbox.Group>
    </Space>
  );
};

export default CheckboxExamples;
