import { useState } from 'react';
import { Space, Divider } from 'antd';
import { Radio, Button } from '@/components';

const RadioExamples = () => {
  const [disabled, setDisabled] = useState(false);
  const [radioValue, setRadioValue] = useState('Ports');
  const [groupValue, setGroupValue] = useState('Ports');

  const plainOptions = ['Ports', 'EPIS', 'Networking'];
  const optionsWithDisabled = [
    { label: 'Ports', value: 'Ports' },
    { label: 'EPIS', value: 'EPIS' },
    { label: 'Networking', value: 'Networking', disabled: true },
  ];

  const toggleDisabled = () => setDisabled(!disabled);

  const onChangeGroup = (e) => {
    setRadioValue(e.target.value);
  };

  const onChangeButtonGroup = (e) => {
    setGroupValue(e.target.value);
  };

  return (
    <div style={{ padding: '20px' }}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Divider>Basic Radio Example</Divider>
        <Radio>Basic Radio</Radio>
        <Divider>Disabled Radio</Divider>
        <Radio defaultChecked={false} disabled={disabled}>
          Disabled
        </Radio>
        <Radio defaultChecked disabled={disabled}>
          Disabled Checked
        </Radio>
        <br />
        <Button type="primary" onClick={toggleDisabled}>
          Toggle disabled
        </Button>
        <Divider>Radio Group Example</Divider>
        <Radio.Group
          options={plainOptions}
          onChange={onChangeGroup}
          value={radioValue}
        />
        <br />
        Selected: {radioValue}
        <Divider>Radio Button Group</Divider>
        <Radio.Group
          options={plainOptions}
          value={groupValue}
          onChange={onChangeButtonGroup}
          optionType="button"
          buttonStyle="solid"
        />
        <br />
        Selected: {groupValue}
        <Divider>Radio Group with Disabled Option</Divider>
        <Radio.Group
          options={optionsWithDisabled}
          onChange={onChangeGroup}
          value={radioValue}
        />
      </Space>
    </div>
  );
};

export default RadioExamples;
