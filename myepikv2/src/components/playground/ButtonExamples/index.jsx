// import SearchIcon from '../SvgIcons/SearchIcon';
import { Space, Divider } from 'antd';
import { Button, SearchIcon } from '@/components';

const ButtonExamples = () => (
  <div style={{ padding: '20px' }}>
    <Divider>Basic Button Types</Divider>
    <Space wrap>
      <Button>Primary Button</Button>
      <Button disabled>Primary Disabled</Button>
      <Button variant="outlined">Outlined</Button>
      <Button variant="dashed">Dashed</Button>
      <Button variant="filled">Filled</Button>
      <Button variant="text">Text</Button>
      <Button variant="link">Link</Button>
    </Space>

    <Divider>Default Button Type</Divider>
    <Space wrap>
      <Button color="default" variant="solid">
        Default
      </Button>
      <Button color="default" disabled>
        Default
      </Button>
      <Button color="default" variant="outlined">
        Outlined
      </Button>
      <Button color="default" variant="dashed">
        Dashed
      </Button>
      <Button color="default" variant="filled">
        Filled
      </Button>
      <Button color="default" variant="text">
        Text
      </Button>
      <Button color="default" variant="link">
        Link
      </Button>
    </Space>

    <Divider>Danger Color</Divider>
    <Space wrap>
      <Button color="danger" variant="solid" danger>
        Alert
      </Button>
      <Button color="danger" variant="solid" disabled danger>
        Alert Disabled
      </Button>
      <Button color="danger" variant="outlined" danger>
        Outlined
      </Button>
      <Button color="danger" variant="dashed" danger>
        Dashed
      </Button>
      <Button color="danger" variant="filled" danger>
        Filled
      </Button>
      <Button color="danger" variant="text" danger>
        Text
      </Button>
      <Button color="danger" variant="link" danger>
        Link
      </Button>
    </Space>

    <Divider>Icon Button Variants</Divider>
    <Space wrap>
      <Button icon={<SearchIcon stroke="white" />} iconPosition="start">
        Search
      </Button>
      <Button icon={<SearchIcon stroke="white" />} iconPosition="end">
        Search
      </Button>
      <Button shape="circle" icon={<SearchIcon stroke="white" />} />
      <Button icon={<SearchIcon stroke="white" />}>Download</Button>
    </Space>

    <Divider>Button Sizes</Divider>
    <Space wrap>
      <Button size="large">Large Button</Button>
      <Button size="small">Small Button</Button>
    </Space>

    <Divider>Loading Buttons</Divider>
    <Space wrap>
      <Button loading>Loading Button</Button>
      <Button loading>Loading with Icon</Button>
    </Space>

    <Divider>Block Button</Divider>
    <Button block>Block Button</Button>

    <Divider>Ghost Buttons</Divider>
    <Space wrap>
      <Button ghost>Primary Ghost</Button>
      <Button ghost>Default Ghost</Button>
    </Space>
  </div>
);

export default ButtonExamples;
