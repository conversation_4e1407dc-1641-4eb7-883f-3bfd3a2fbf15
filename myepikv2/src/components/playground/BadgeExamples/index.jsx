import { useState } from 'react';
import { Avatar, Card, Divider, Space, Switch } from 'antd';
import { Badge, Button, ClockIcon, MinusIcon, PlusIcon } from '@/components';

const BadgeExamples = () => {
  const [count, setCount] = useState(5);
  const [show, setShow] = useState(true);

  const increase = () => setCount(count + 1);
  const decline = () => setCount(count > 0 ? count - 1 : 0);
  const toggleShow = () => setShow(!show);

  return (
    <Space direction="vertical" size="large" style={{ padding: '20px' }}>
      <Divider>Basic Badge</Divider>
      <Badge count={5}>
        <Avatar shape="square" size="large" />
      </Badge>
      <Badge count={0} showZero>
        <Avatar shape="square" size="large" />
      </Badge>
      <Badge count={<ClockIcon stroke="#f5222d" />}>
        <Avatar shape="square" size="large" />
      </Badge>

      <Divider>Ribbon Badge</Divider>
      <Badge ribbon ribbonText="Hippies" ribbonColor="pink">
        <Card title="Ribbon Example" size="small">
          Content inside ribbon badge.
        </Card>
      </Badge>
      <Badge ribbon ribbonText="Sale" ribbonColor="red">
        <Card title="Sale Item" size="small">
          Another ribbon example.
        </Card>
      </Badge>

      <Divider>Colorful Badges</Divider>
      <Badge color="magenta" text="magenta" />
      <Badge color="red" text="red" />
      <Badge color="cyan" text="cyan" />

      <Divider>Dynamic Badge</Divider>
      <Space size="large">
        <Badge count={count}>
          <Avatar shape="square" size="large" />
        </Badge>
        <Button onClick={decline} icon={<MinusIcon stroke="white" />} />
        <Button onClick={increase} icon={<PlusIcon stroke="white" />} />
      </Space>
      <Space size="large">
        <Badge dot={show}>
          <Avatar shape="square" size="large" />
        </Badge>
        <Switch onChange={toggleShow} checked={show} />
      </Space>

      <Divider>Red Badge (Dot)</Divider>
      <Badge dot>
        <PlusIcon />
      </Badge>
      <Badge dot>
        <a href="#">Link something</a>
      </Badge>

      <Divider>Overflow Count</Divider>
      <Badge count={99} overflowCount={10}>
        <Avatar shape="square" size="large" />
      </Badge>
      <Badge count={1000} overflowCount={999}>
        <Avatar shape="square" size="large" />
      </Badge>

      <Divider>Offset</Divider>
      <Badge count={5} offset={[10, 10]}>
        <Avatar shape="square" size="large" />
      </Badge>
    </Space>
  );
};

export default BadgeExamples;
