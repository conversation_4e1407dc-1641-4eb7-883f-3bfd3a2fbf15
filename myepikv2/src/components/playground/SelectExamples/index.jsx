import { useEffect, useState, useRef } from 'react';
import PropTypes from 'prop-types';
import { Divider, Space } from 'antd';
import { Select, Input, Tooltip } from '@/components';
import { useDebounce } from '../../../hooks/useDebounce';

const fetchCompaniesList = async (searchTerm) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockResponse = {
        results: [
          { name: 'TechCorp', id: '1' },
          { name: 'HealthInc', id: '2' },
          { name: 'EduWorld', id: '3' },
        ],
      };

      if (searchTerm) {
        const filteredResults = mockResponse.results.filter((company) =>
          company.name.toLowerCase().includes(searchTerm.toLowerCase()),
        );
        resolve(
          filteredResults.map((company) => ({
            label: company.name,
            value: company.id,
          })),
        );
      } else {
        resolve(
          mockResponse.results.map((company) => ({
            label: company.name,
            value: company.id,
          })),
        );
      }
    }, 1000);
  });
};

const DebounceSelect = ({ fetchOptions, debounceTimeout = 800, ...props }) => {
  const [fetching, setFetching] = useState(false);
  const [options, setOptions] = useState([]);
  const fetchRef = useRef(0);
  const [searchTerm, setSearchTerm] = useState('');

  const debouncedSearchTerm = useDebounce(searchTerm, debounceTimeout);

  useEffect(() => {
    if (debouncedSearchTerm) {
      fetchRef.current += 1;
      const fetchId = fetchRef.current;
      setOptions([]);
      setFetching(true);

      fetchOptions(debouncedSearchTerm).then((newOptions) => {
        if (fetchId !== fetchRef.current) return;
        setOptions(newOptions);
        setFetching(false);
      });
    }
  }, [debouncedSearchTerm, fetchOptions]);

  return (
    <Select
      showSearch
      filterOption={false}
      notFoundContent={fetching ? <span>Loading...</span> : null}
      onSearch={setSearchTerm}
      options={options}
      {...props}
    />
  );
};

// Add PropTypes for DebounceSelect
DebounceSelect.propTypes = {
  fetchOptions: PropTypes.func.isRequired,
  debounceTimeout: PropTypes.number,
};

const SelectExamples = () => {
  const options = [
    { value: 'company1', label: 'Company 1' },
    { value: 'company2', label: 'Company 2' },
    { value: 'company3', label: 'Company 3' },
  ];

  return (
    <div style={{ padding: '20px' }}>
      <h3>Select Component Examples</h3>

      <Space wrap>
        <Select
          defaultValue="company1"
          options={options}
          style={{ width: 120 }}
        />
        <Select
          defaultValue="company1"
          disabled
          options={[{ value: 'company1', label: 'Company 1' }]}
        />
        <Select
          defaultValue="company2"
          loading
          options={[{ value: 'company2', label: 'Company 2' }]}
        />
        <Select
          defaultValue="company3"
          allowClear
          options={[{ value: 'company3', label: 'Company 3' }]}
          placeholder="Select it"
        />
      </Space>

      <Divider />

      <div style={{ marginTop: '20px' }}>
        <h4>Select with Search Field</h4>
        <Select
          showSearch
          placeholder="Select a person"
          options={options}
          optionFilterProp="label"
          style={{ width: 200 }}
          onSearch={(value) => console.log(`Search: ${value}`)}
        />
      </div>

      <Divider />

      <div style={{ marginTop: '20px' }}>
        <h4>Debounced Search Example</h4>
        <DebounceSelect
          fetchOptions={fetchCompaniesList}
          style={{ width: '100%' }}
          placeholder="Search Companies"
          mode="multiple"
        />
      </div>

      <Divider />

      <div style={{ marginTop: '20px' }}>
        <h4>Tooltip with Select</h4>
        <Tooltip title="Select items" placement="top">
          <Select
            mode="multiple"
            options={options}
            style={{ width: '100%' }}
            placeholder="Select with Tooltip"
          />
        </Tooltip>
      </div>

      <Divider />

      <div style={{ marginTop: '20px' }}>
        <h4>Grouped Options</h4>
        <Select
          defaultValue="company1"
          style={{ width: 200 }}
          options={[
            {
              label: 'Manager',
              options: [
                { label: 'Company 1', value: 'company1' },
                { label: 'Company 2', value: 'company2' },
              ],
            },
            {
              label: 'Engineer',
              options: [
                { label: 'Chloe', value: 'Chloe' },
                { label: 'Lucas', value: 'Lucas' },
              ],
            },
          ]}
        />
      </div>

      <Divider />

      <div style={{ marginTop: '20px' }}>
        <h4>Custom Input with Select</h4>
        <Input placeholder="Custom Input" />
      </div>
    </div>
  );
};

export default SelectExamples;
