import { useState } from 'react';
import Typography from '../../common/Typography';

const TypographyExamples = () => {
  const [editableText, setEditableText] = useState('Editable text example.');
  const [editableParagraph, setEditableParagraph] = useState(
    'Editable paragraph with long content.',
  );

  return (
    <div>
      <Typography level={1} strong className="custom-title">
        H1 Title with Strong Style
      </Typography>

      <Typography level={2} className="custom-title">
        H2 Title
      </Typography>

      <Typography level={3} className="custom-title">
        H3 Title
      </Typography>

      <Typography level={4} className="custom-title">
        H4 Title
      </Typography>

      <Typography level={5} className="custom-title">
        H5 Title
      </Typography>

      <Typography level={2} underline type="warning" className="custom-title">
        H2 Title with Underline and Warning Style
      </Typography>

      <Typography type="secondary" italic>
        Secondary Text with Italic Style
      </Typography>

      <Typography type="danger" delete>
        Deleted Danger Text
      </Typography>

      <Typography copyable>Copyable Text</Typography>

      <Typography
        copyable={{
          text: 'This is custom copyable text',
        }}
      >
        Copyable Text with Custom Content
      </Typography>

      <Typography keyboard>Keyboard Styled Text</Typography>

      <Typography
        editable={{
          onChange: setEditableText,
        }}
      >
        {editableText}
      </Typography>

      <Typography
        ellipsis={{
          rows: 2,
          expandable: true,
          symbol: 'more',
        }}
      >
        This is a long paragraph that will truncate after two lines and can be
        expanded to view more content if required. This is a long paragraph that
        will truncate after two lines and can be expanded to view more content
        if required, This is a long paragraph that will truncate after two lines
        and can be expanded to view more content if required. This is a long
        paragraph that will truncate after two lines and can be expanded to view
        more content if required Click &apos;more&apos; to expand.
      </Typography>
      <Typography
        editable={{
          onChange: setEditableParagraph,
        }}
      >
        {editableParagraph}
      </Typography>

      <Typography href="https://my.epik.io/" type="success">
        Link with Success Type
      </Typography>
    </div>
  );
};

export default TypographyExamples;
