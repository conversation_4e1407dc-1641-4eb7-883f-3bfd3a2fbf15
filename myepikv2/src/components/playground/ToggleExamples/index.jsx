import { useState } from 'react';
import Toggle from '../../common/Toggle';
import { Button, Space } from 'antd';

const ToggleExamples = () => {
  const [isDisabled, setIsDisabled] = useState(false);
  const toggleDisabled = () => setIsDisabled(!isDisabled);

  const handleChange = (checked) => {
    console.log(`Toggle switched to: ${checked}`);
  };

  return (
    <Space
      direction="vertical"
      size="large"
      style={{ padding: '20px', maxWidth: '400px', margin: '0 auto' }}
    >
      <Toggle defaultChecked onChange={handleChange} />

      <Toggle disabled={isDisabled} defaultChecked />
      <Button type="primary" onClick={toggleDisabled}>
        Toggle Disabled State
      </Button>

      <Toggle checkedChildren="On" unCheckedChildren="Off" defaultChecked />
      <Toggle checkedChildren="1" unCheckedChildren="0" />

      <Toggle size="default" defaultChecked />
      <Toggle size="small" defaultChecked />

      <Toggle loading defaultChecked />
      <Toggle size="small" loading />

      <Toggle checked={isDisabled} onChange={toggleDisabled} />
    </Space>
  );
};

export default ToggleExamples;
