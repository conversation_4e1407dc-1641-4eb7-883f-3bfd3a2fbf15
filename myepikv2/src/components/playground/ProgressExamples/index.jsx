import { useState } from 'react';
import { Divider, <PERSON>, Typography } from 'antd';
import {
  Progress,
  Button,
  Flex,
  Slider,
  MinusIcon,
  PlusIcon,
} from '@/components';

const ProgressExamples = () => {
  const [percent, setPercent] = useState(0);
  const [stepsCount, setStepsCount] = useState(5);
  const [stepsGap, setStepsGap] = useState(7);

  const increase = () =>
    setPercent((prevPercent) => Math.min(prevPercent + 10, 100));
  const decline = () =>
    setPercent((prevPercent) => Math.max(prevPercent - 10, 0));

  const twoColors = { '0%': '#108ee9', '100%': '#87d068' };
  const conicColors = { '0%': '#87d068', '50%': '#ffe58f', '100%': '#ffccc7' };

  return (
    <div style={{ padding: '20px' }}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Divider>Standard Progress Bar</Divider>
        <Flex gap="small" vertical>
          <Progress percent={30} />
          <Progress percent={50} status="active" />
          <Progress percent={70} status="exception" />
          <Progress percent={100} />
          <Progress percent={50} showInfo={false} />
        </Flex>

        <Divider>Circular Progress Bar</Divider>
        <Flex gap="small" wrap>
          <Progress type="circle" percent={75} />
          <Progress type="circle" percent={70} status="exception" />
          <Progress type="circle" percent={100} />
        </Flex>

        <Divider>Mini Size Progress Bar</Divider>
        <Flex vertical gap="small" style={{ width: 180 }}>
          <Progress percent={30} size="small" />
          <Progress percent={50} size="small" status="active" />
          <Progress percent={70} size="small" status="exception" />
          <Progress percent={100} size="small" />
        </Flex>

        <Divider>Responsive Circular Progress Bar</Divider>
        <Flex align="center" gap="small">
          <Progress
            type="circle"
            trailColor="#e6f4ff"
            percent={60}
            size={14}
            format={(number) => `进行中，已完成${number}%`}
          />
          <span>代码发布</span>
        </Flex>

        <Divider>Mini Size Circular Progress Bar</Divider>
        <Flex wrap gap="small">
          <Progress type="circle" percent={30} size={80} />
          <Progress type="circle" percent={70} size={80} status="exception" />
          <Progress type="circle" percent={100} size={80} />
        </Flex>

        <Divider>Dynamic Progress Bar</Divider>
        <Flex vertical gap="small">
          <Flex vertical gap="small">
            <Progress percent={percent} type="line" />
            <Progress percent={percent} type="circle" />
          </Flex>
        </Flex>
        <Button type="default" onClick={decline} icon={<MinusIcon />} />
        <Button onClick={increase} icon={<PlusIcon />} />

        <Divider>Custom Line Gradient</Divider>
        <Flex vertical gap="middle">
          <Progress percent={99.9} strokeColor={twoColors} />
          <Progress
            percent={50}
            status="active"
            strokeColor={{
              from: '#108ee9',
              to: '#87d068',
            }}
          />
          <Flex gap="small" wrap>
            <Progress type="circle" percent={90} strokeColor={twoColors} />
            <Progress type="circle" percent={100} strokeColor={twoColors} />
            <Progress type="circle" percent={93} strokeColor={conicColors} />
          </Flex>
        </Flex>

        <Divider>Circular Progress Bar with Steps</Divider>
        <Typography.Title level={5}>Custom count:</Typography.Title>
        <Slider min={2} max={10} value={stepsCount} onChange={setStepsCount} />
        <Typography.Title level={5}>Custom gap:</Typography.Title>
        <Slider
          step={4}
          min={0}
          max={40}
          value={stepsGap}
          onChange={setStepsGap}
        />
        <Flex wrap gap="middle" style={{ marginTop: 16 }}>
          <Progress
            type="dashboard"
            steps={8}
            percent={50}
            trailColor="rgba(0, 0, 0, 0.06)"
            size={20}
          />
          <Progress
            type="circle"
            percent={100}
            steps={{ count: stepsCount, gap: stepsGap }}
            trailColor="rgba(0, 0, 0, 0.06)"
            size={20}
          />
        </Flex>
      </Space>
    </div>
  );
};

export default ProgressExamples;
