import { useState } from 'react';
import { Tag, SearchIcon, ClockIcon, MinusIcon } from '@/components';
import { Divider, Space } from 'antd';

const TagExamples = () => {
  const [tags, setTags] = useState(['Tag 1', 'Tag 2', 'Tag 3']);
  const [selectedTags, setSelectedTags] = useState(['Movies']);

  const handleAdd = () => {
    setTags([...tags, `New Tag ${tags.length + 1}`]);
  };

  const handleEdit = (index, newText) => {
    const updatedTags = [...tags];
    updatedTags[index] = newText;
    setTags(updatedTags);
  };

  const handleCheckChange = (tag, checked) => {
    const nextSelectedTags = checked
      ? [...selectedTags, tag]
      : selectedTags.filter((t) => t !== tag);
    setSelectedTags(nextSelectedTags);
  };

  return (
    <div style={{ padding: '20px' }}>
      <Space direction="vertical" size="large">
        <Divider>Small, Medium, Large Tags</Divider>
        <Space>
          <Tag color="success" text="Small" classNames="custom-tag-small" />
          <Tag color="error" text="Medium" classNames="custom-tag-medium" />
          <Tag color="warning" text="Large Tag" classNames="custom-tag-large" />
        </Space>

        <Divider>Status Tags (Without Icons)</Divider>
        <Space>
          <Tag text="Success" color="success" />
          <Tag text="Processing" color="processing" />
          <Tag text="Error" color="error" />
          <Tag text="Warning" color="warning" />
          <Tag text="Default" color="default" />
        </Space>

        <Divider>Status Tags (With Icons)</Divider>
        <Space>
          <Tag
            text="Success"
            color="success"
            icon={<SearchIcon stroke="#52c41a" />}
          />
          <Tag
            text="Processing"
            color="processing"
            icon={<ClockIcon stroke="#1677ff" height={12} width={12} />}
          />
          <Tag
            text="Error"
            color="error"
            icon={<ClockIcon stroke="#ff4d4f" height={12} width={12} />}
          />
          <Tag
            text="Warning"
            color="warning"
            icon={<ClockIcon stroke="#faad14" height={12} width={12} />}
          />
          <Tag
            text="Waiting"
            color="default"
            icon={<ClockIcon height={12} width={12} />}
          />
          <Tag
            text="Stop"
            color="default"
            icon={<MinusIcon height={12} width={12} />}
          />
        </Space>

        <Divider>Basic Tags</Divider>
        <Space>
          <Tag text="Basic Tag" />
          <Tag
            text="Closable Tag"
            closable
            onClose={() => console.log('Tag closed')}
          />
          <Tag
            text="Custom Close Icon"
            closable
            closeIcon={<ClockIcon height={12} width={12} />}
          />
        </Space>

        <Divider>Colorful Tags</Divider>
        <Space>
          <Tag text="Magenta" color="magenta" />
          <Tag text="Red" color="red" />
          <Tag text="Green" color="green" />
        </Space>

        <Divider>Checkable Tags</Divider>
        <Space>
          {['Movies', 'Books', 'Music', 'Sports'].map((tag) => (
            <Tag
              key={tag}
              text={tag}
              checkable
              checked={selectedTags.includes(tag)}
              onCheckChange={(checked) => handleCheckChange(tag, checked)}
            />
          ))}
        </Space>

        <Divider>Add & Edit Tags</Divider>
        <Space>
          {tags.map((tag, index) => (
            <Tag
              key={index}
              text={tag}
              editable
              onEditConfirm={(newText) => handleEdit(index, newText)}
            />
          ))}
          <Tag addable onAdd={handleAdd} />
        </Space>

        <Divider>Bordered & Borderless</Divider>
        <Space>
          <Tag text="Bordered Tag" bordered />
          <Tag text="Borderless Tag" bordered={false} />
        </Space>
      </Space>
    </div>
  );
};

export default TagExamples;
