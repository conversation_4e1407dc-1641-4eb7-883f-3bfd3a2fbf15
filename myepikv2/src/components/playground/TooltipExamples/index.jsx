import AnimatedTab from '@/components/common/AnimatedTab';
import { Divider, Flex, Space } from 'antd';
import { useState } from 'react';
import Button from '../../common/Button';
import { DownIcon } from '../../common/SvgIcons';
import Tooltip from '../../common/Tooltip';

const TooltipExamples = () => {
  const [arrow, setArrow] = useState(true);
  const colors = [
    'pink',
    'red',
    'yellow',
    'orange',
    'cyan',
    'green',
    'blue',
    'purple',
    'geekblue',
    'magenta',
    'volcano',
    'gold',
    'lime',
  ];
  const customColors = ['#f50', '#2db7f5', '#87d068', '#108ee9'];
  const text = 'prompt text';

  return (
    <Space
      direction="vertical"
      size="large"
      style={{ padding: '20px', maxWidth: '600px', margin: '0 auto' }}
    >
      <Flex justify="center" gap={12}>
        <Tooltip
          title={text}
          color={'white'}
          overlayStyle={{ color: 'black' }}
          overlayInnerStyle={{ color: 'black' }}
        >
          <span>Basic Tooltip (Hover to see)</span>
        </Tooltip>
        <Tooltip title={text}>
          <DownIcon />
        </Tooltip>
      </Flex>

      <Divider>Placement Options</Divider>
      <Flex justify="center" wrap>
        <Tooltip title={text} placement="topLeft">
          <Button>Top Left</Button>
        </Tooltip>
        <Tooltip title={text} placement="top">
          <Button>Top</Button>
        </Tooltip>
        <Tooltip title={text} placement="topRight">
          <Button>Top Right</Button>
        </Tooltip>
      </Flex>

      <Divider>Arrow Options</Divider>
      <AnimatedTab
        value={arrow ? 'Show' : 'Hide'}
        options={['Show', 'Hide']}
        onChange={(val) => setArrow(val === 'Show')}
      />
      <Tooltip title={text} arrow={arrow}>
        <Button>Arrow {arrow ? 'Visible' : 'Hidden'}</Button>
      </Tooltip>

      <Divider>Colorful Tooltip</Divider>
      <Space wrap>
        {colors.map((color) => (
          <Tooltip title={text} color={color} key={color}>
            <Button>{color}</Button>
          </Tooltip>
        ))}
      </Space>

      <Divider>Disabled Tooltip</Divider>
      <Tooltip title={text} disabled>
        <span>Disabled Tooltip (No tooltip on hover)</span>
      </Tooltip>

      <Divider>Custom Colors</Divider>
      <Space wrap>
        {customColors.map((color) => (
          <Tooltip title={text} color={color} key={color}>
            <Button>{color}</Button>
          </Tooltip>
        ))}
      </Space>
    </Space>
  );
};

export default TooltipExamples;
