import { Divider, Space, message } from 'antd';
import { DropDown, Button, SettingIcon } from '@/components';

const handleMenuClick = (e) => {
  message.info(`Click on menu item ${e.key}`);
};

const menuItems = [
  { key: '1', label: '1st menu item' },
  { key: '2', label: '2nd menu item', disabled: true },
  { key: '3', label: '3rd menu item' },
];

const extraMenuItems = [
  { key: '1', label: 'My Account', disabled: true },
  { key: '2', label: 'Profile', extra: '⌘P' },
  { key: '3', label: 'Billing', extra: '⌘B' },
  { key: '4', label: 'Settings', icon: <SettingIcon />, extra: '⌘S' },
];

const cascadingMenuItems = [
  {
    key: '1',
    type: 'group',
    label: 'Group title',
    children: [
      { key: '1-1', label: '1st menu item' },
      { key: '1-2', label: '2nd menu item' },
    ],
  },
  {
    key: '2',
    label: 'sub menu',
    children: [
      { key: '2-1', label: '3rd menu item' },
      { key: '2-2', label: '4th menu item' },
    ],
  },
];

const DropdownExamples = () => (
  <div style={{ padding: '20px' }}>
    <Divider>Basic Dropdown</Divider>
    <Space wrap>
      <DropDown
        menuItems={menuItems}
        buttonLabel="Basic Dropdown"
        onClick={handleMenuClick}
      />
    </Space>

    <Divider>Dropdown with Extra Node</Divider>
    <Space wrap>
      <DropDown
        menuItems={extraMenuItems}
        buttonLabel="Dropdown with Extra Node"
        onClick={handleMenuClick}
      />
    </Space>

    <Divider>Dropdown with Placement</Divider>
    <Space wrap>
      <DropDown
        menuItems={menuItems}
        placement="bottomLeft"
        buttonLabel="Bottom Left"
        onClick={handleMenuClick}
      />
      <DropDown
        menuItems={menuItems}
        placement="bottomRight"
        buttonLabel="Bottom Right"
        onClick={handleMenuClick}
      />
      <DropDown
        menuItems={menuItems}
        placement="topLeft"
        buttonLabel="Top Left"
        onClick={handleMenuClick}
      />
      <DropDown
        menuItems={menuItems}
        placement="topRight"
        buttonLabel="Top Right"
        onClick={handleMenuClick}
      />
    </Space>

    <Divider>Dropdown with Arrow at Center</Divider>
    <Space wrap>
      <DropDown
        menuItems={menuItems}
        placement="bottomLeft"
        arrow={{ pointAtCenter: true }}
        buttonLabel="Arrow Center"
        onClick={handleMenuClick}
      />
    </Space>

    <Divider>Dropdown with Custom Icon</Divider>
    <Space wrap>
      <DropDown
        menuItems={menuItems}
        buttonLabel="Custom Icon"
        buttonProps={{ icon: <SettingIcon stroke="white" /> }}
        onClick={handleMenuClick}
      />
    </Space>

    <Divider>Cascading Menu Dropdown</Divider>
    <Space wrap>
      <DropDown
        menuItems={cascadingMenuItems}
        buttonLabel="Cascading Menu"
        onClick={handleMenuClick}
      />
    </Space>

    <Divider>Dropdown with Custom Render</Divider>
    <Space wrap>
      <DropDown
        menuItems={menuItems}
        dropdownRender={(menu) => (
          <div>
            {menu}
            <Divider />
            <Space style={{ padding: 8 }}>
              <Button type="primary">Click me!</Button>
            </Space>
          </div>
        )}
        buttonLabel="Custom Render"
        onClick={handleMenuClick}
      />
    </Space>

    <Divider>Dropdown with Button and Tooltip</Divider>
    <Space wrap>
      <DropDown
        menuItems={menuItems}
        buttonLabel="Dropdown Button"
        useButton
        onClick={handleMenuClick}
      />
      <DropDown
        menuItems={menuItems}
        buttonLabel="With Tooltip"
        useButton
        buttonProps={{
          buttonsRender: ([leftButton, rightButton]) => [
            <span key="leftButton">{leftButton}</span>,
            <span key="rightButton">{rightButton}</span>,
          ],
        }}
        onClick={handleMenuClick}
      />
    </Space>

    <Divider>Loading Dropdown</Divider>
    <Space wrap>
      <DropDown
        menuItems={menuItems}
        buttonLabel="Loading Dropdown"
        useButton
        buttonProps={{ loading: true }}
        onClick={handleMenuClick}
      />
    </Space>

    <Divider>Dropdown with Click Trigger</Divider>
    <Space wrap>
      <DropDown
        menuItems={menuItems}
        trigger={['click']}
        buttonLabel="Click Me"
        onClick={handleMenuClick}
      />
    </Space>

    <Divider>Cascading Menu Dropdown</Divider>
    <Space wrap>
      <DropDown
        menuItems={cascadingMenuItems}
        buttonLabel="Cascading Menu"
        onClick={handleMenuClick}
      />
    </Space>
  </div>
);

export default DropdownExamples;
