import { Divider, Space } from 'antd';
import { CloseIcon, Alert } from '@/components';

const AlertExamples = () => (
  <div style={{ padding: '20px' }}>
    <Divider>Basic Alert</Divider>
    <Space direction="vertical">
      <Alert message="Success Text" type="success" />
      <Alert message="Info Text" type="info" />
      <Alert message="Warning Text" type="warning" />
      <Alert message="Error Text" type="error" />
    </Space>

    <Divider>Closable Alerts</Divider>
    <Space direction="vertical">
      <Alert
        message="Warning Text"
        type="warning"
        closable
        onClose={() => console.log('Closed Warning')}
      />
      <Alert
        message="Error Text"
        description="Error Description"
        type="error"
        closable={{ 'aria-label': 'close', closeIcon: <CloseIcon /> }}
        onClose={() => console.log('Closed Error')}
      />
    </Space>

    <Divider>Alerts with Descriptions</Divider>
    <Space direction="vertical">
      <Alert
        message="Success Text"
        description="Detailed Success Description"
        type="success"
      />
      <Alert
        message="Info Text"
        description="Detailed Info Description"
        type="info"
      />
      <Alert
        message="Warning Text"
        description="Detailed Warning Description"
        type="warning"
      />
      <Alert
        message="Error Text"
        description="Detailed Error Description"
        type="error"
      />
    </Space>

    <Divider>Icon Alerts</Divider>
    <Space direction="vertical">
      <Alert message="Success Tips" type="success" showIcon />
      <Alert message="Informational Notes" type="info" showIcon />
      <Alert message="Warning" type="warning" showIcon closable />
      <Alert message="Error" type="error" showIcon />
    </Space>

    <Divider>Banner Alerts</Divider>
    <Space direction="vertical">
      <Alert message="Warning text" banner />
      <Alert message="Very long warning text" banner closable />
      <Alert showIcon={false} message="Warning text without icon" banner />
      <Alert type="error" message="Error text" banner />
    </Space>

    <Divider>Alerts with Custom Action</Divider>
    <Space direction="vertical">
      <Alert
        message="Success Tips"
        type="success"
        showIcon
        action={<button>UNDO</button>}
        closable
      />
      <Alert
        message="Warning Text"
        type="warning"
        showIcon
        action={<button>DONE</button>}
        closable
      />
    </Space>
  </div>
);

export default AlertExamples;
