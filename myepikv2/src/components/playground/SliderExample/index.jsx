import { useState } from 'react';
import { Row, Col } from 'antd';
import { Slider } from '@/components';

function SliderExample() {
  const [value, setValue] = useState(250);
  const [value1, setValue1] = useState([25, 55]);
  const onSliderChange = (newValue, val) => {
    console.log(newValue);
    val == 1 ? setValue(newValue) : setValue1(newValue);
  };

  return (
    <div>
      <Row gutter={5}>
        <Col span={5}>
          <h3>Simple</h3>
          <Slider
            value={value}
            onChange={(val) => onSliderChange(val, 1)}
            min={5}
            max={2000}
            tooltipPlacement="bottom"
          />
        </Col>
      </Row>
      <Row gutter={5}>
        <Col span={5}>
          <h3>Simple Reverse</h3>
          <Slider
            value={value}
            min={5}
            max={2000}
            tooltipPlacement="bottom"
            onChange={(val) => onSliderChange(val, 1)}
            reverse={true}
          />
        </Col>
      </Row>
      <Row gutter={5}>
        <Col span={5}>
          <h3>Range</h3>
          <Slider
            value={value1}
            onChange={(val) => onSliderChange(val, 2)}
            min={10}
            max={80}
            step={5}
            range={true}
          />
        </Col>
      </Row>

      <Row gutter={5}>
        <Col span={5}>
          <h3>Range Reverse</h3>
          <Slider
            value={value1}
            onChange={(val) => onSliderChange(val, 2)}
            min={10}
            max={80}
            step={5}
            range={true}
            reverse={true}
          />
        </Col>
      </Row>
    </div>
  );
}

export default SliderExample;
