import { AnimatedTab } from '@/components';
import { Divider, Flex, Typography } from 'antd';
import { useState } from 'react';

const AnimatedTabsExample = () => {
  const [selectedTab, setSelectedTab] = useState('tab1');

  const tabOptions = [
    { label: 'Tab x', value: 'tab1' },
    { label: 'Tab y', value: 'tab2' },
    { label: 'Tab z', value: 'tab3' },
  ];

  const sizesExample = ['small', 'default', 'large'];

  return (
    <Flex vertical gap="large" style={{ padding: '24px' }}>
      <Typography.Title level={4}>Animated Tabs Example</Typography.Title>

      <Flex vertical gap="middle">
        <Typography.Title level={5}>Basic Usage</Typography.Title>
        <AnimatedTab
          options={tabOptions}
          value={selectedTab}
          onChange={setSelectedTab}
        />
        <div style={{ marginTop: '16px' }}>Selected: {selectedTab}</div>
      </Flex>

      <Divider />

      <Flex vertical gap="middle">
        <Typography.Title level={5}>Different Sizes</Typography.Title>
        {sizesExample.map((size) => (
          <Flex key={size} align="center" gap="middle">
            <div style={{ width: '80px' }}>{size}:</div>
            <AnimatedTab
              options={tabOptions}
              value={selectedTab}
              onChange={setSelectedTab}
              size={size}
            />
          </Flex>
        ))}
      </Flex>

      <Divider />

      <Flex vertical gap="middle">
        <Typography.Title level={5}>String Options</Typography.Title>
        <AnimatedTab
          options={['Option A', 'Option B', 'Option C']}
          value="Option A"
          onChange={console.log}
        />
      </Flex>
    </Flex>
  );
};

export default AnimatedTabsExample;
