import {
  AnimatedTab,
  Button,
  Flex,
  Radio,
  Slider,
  Typography,
} from '@/components';
import { Card } from 'antd';
import { useState } from 'react';

const baseStyle = {
  width: '25%',
  height: 54,
};

const boxStyle = {
  width: '100%',
  height: 120,
  borderRadius: 6,
  border: '1px solid #40a9ff',
};

const cardStyle = {
  width: 620,
  body: {
    padding: 0,
    overflow: 'hidden',
  },
};

const imgStyle = {
  display: 'block',
  width: 273,
};

const FlexExamples = () => {
  const [direction, setDirection] = useState('Horizontal');
  const [justify, setJustify] = useState('flex-start');
  const [align, setAlign] = useState('flex-start');
  const [gapSize, setGapSize] = useState('small');
  const [customGapSize, setCustomGapSize] = useState(0);

  const onChangeGroup = (e) => {
    setDirection(e.target.value);
  };

  return (
    <Flex vertical gap="large">
      {/* Direction Example */}
      <Flex vertical gap="middle">
        <Typography level={4}>Direction Switcher</Typography>
        <Radio.Group
          options={['Horizontal', 'Vertical']}
          onChange={onChangeGroup}
          value={direction}
        />
        <Flex vertical={direction?.toLowerCase() === 'vertical'}>
          {Array.from({ length: 4 }).map((_, i) => (
            <div
              key={i}
              style={{
                ...baseStyle,
                backgroundColor: i % 2 ? '#1677ff' : '#1677ffbf',
              }}
            />
          ))}
        </Flex>
      </Flex>

      {/* Justify and Align Example */}
      <Flex vertical gap="middle">
        <Typography level={4}>Justify and Align</Typography>
        <p>Select justify:</p>
        <AnimatedTab
          options={[
            'flex-start',
            'center',
            'flex-end',
            'space-between',
            'space-around',
            'space-evenly',
          ]}
          onChange={setJustify}
        />
        <p>Select align:</p>
        <AnimatedTab
          options={['flex-start', 'center', 'flex-end']}
          onChange={setAlign}
        />
        <Flex style={boxStyle} justify={justify} align={align}>
          <Button type="primary">Primary</Button>
          <Button type="primary">Primary</Button>
          <Button type="primary">Primary</Button>
          <Button type="primary">Primary</Button>
        </Flex>
      </Flex>

      {/* Gap Example */}
      <Flex vertical gap="middle">
        <Typography level={4}>Gap Example</Typography>
        <Radio.Group
          options={['small', 'middle', 'large', 'customize']}
          value={gapSize}
          onChange={(e) => setGapSize(e.target.value)}
        />
        {gapSize === 'customize' && (
          <Slider value={customGapSize} onChange={setCustomGapSize} />
        )}
        <Flex gap={gapSize !== 'customize' ? gapSize : customGapSize}>
          <Button type="primary">Primary</Button>
          <Button>Default</Button>
          <Button type="dashed">Dashed</Button>
          <Button type="link">Link</Button>
        </Flex>
      </Flex>

      {/* Wrap Example */}
      <Flex wrap gap="small">
        <Typography level={4}>Wrap Example</Typography>
        {Array.from({ length: 24 }).map((_, i) => (
          <Button key={i} type="primary">
            Button
          </Button>
        ))}
      </Flex>

      {/* Nested Flex Example */}
      <Card hoverable style={cardStyle}>
        <Flex justify="space-between">
          <img
            alt="avatar"
            src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png"
            style={imgStyle}
          />
          <Flex
            vertical
            align="flex-end"
            justify="space-between"
            style={{
              padding: 32,
            }}
          >
            <Typography level={3}>
              “antd is an enterprise-class UI design language and React UI
              library.”
            </Typography>
            <Button type="primary" href="https://ant.design" target="_blank">
              Get Started
            </Button>
          </Flex>
        </Flex>
      </Card>
    </Flex>
  );
};

export default FlexExamples;
