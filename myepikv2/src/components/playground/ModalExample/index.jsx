import { useEffect, useState } from 'react';
import { Row, Col } from 'antd';
import { Modal, Button } from '@/components';

function Index() {
  const [modal1, setModal1] = useState(false);
  const [modal2, setModal2] = useState(false);
  const [loading, setLoading] = useState(false);
  const [customLoading, setCustomLoading] = useState(false);
  const [contentLoading, setContentLoading] = useState(true);
  const [modal3, setModal3] = useState(false);

  useEffect(() => {
    setTimeout(() => {
      setContentLoading(false);
    }, 5000);
  }, []);

  const handleCancel = (newValue, val) => {
    val == 1 ? setModal1(!modal1) : setModal2(!modal2);
    console.log(newValue, val);
  };
  const handleOkModal2 = () => {
    setLoading(true);
    setTimeout(() => {
      setModal2(false);
      setLoading(false);
    }, 3000);
  };

  return (
    <div>
      <Row>
        <Col>
          <h3>Simple</h3>
          <Button type="primary" onClick={() => setModal1(true)}>
            Open Modal 1 with async logic
          </Button>
        </Col>
      </Row>
      <Row>
        <Col>
          <h3>Modal with custom setting</h3>
          <Button type="primary" onClick={() => setModal2(true)}>
            Open Modal with custom logic
          </Button>
        </Col>
      </Row>

      <Row>
        <Col>
          <h3>Custom content rendering</h3>
          <Button type="primary" onClick={() => setModal3(true)}>
            Open Modal with custom content
          </Button>
        </Col>
      </Row>
      {modal1 && (
        <Modal
          open={modal1}
          handleOk={() => setModal1(false)}
          handleCancel={(val) => handleCancel(val, modal1 ? 1 : 2)}
          title="lazy"
          closable={false}
          okType="link"
          extendFooterRight={
            <Button
              type="primary"
              onClick={() => setCustomLoading(!customLoading)}
            >
              Open Modal 1 with async logic {customLoading ? 'loading' : ''}
            </Button>
          }
        />
      )}
      {modal2 && (
        <Modal
          open={modal2}
          title="nothing"
          handleOk={() => {
            modal1 ? setModal1(false) : handleOkModal2();
          }}
          handleCancel={(val) => handleCancel(val, modal1 ? 1 : 2)}
          okText="Confirm"
          cancelText="Cancel"
          okButtonProps={{
            danger: true,
          }}
          submitLoading={loading}
          contentLoading={contentLoading}
          extendFooterLeft={
            <Button
              type="primary"
              onClick={() => setCustomLoading(!customLoading)}
            >
              Open Modal with async
            </Button>
          }
        />
      )}

      {modal3 && (
        <Modal
          open={modal3}
          title="Render children"
          handleOk={() => {
            setModal3(false);
          }}
          handleCancel={() => setModal3(false)}
          okText="Confirm"
          cancelText="Cancel"
          customFooter={
            <Button
              type="primary"
              onClick={() => setCustomLoading(!customLoading)}
            >
              Open Modal 1 with async logic {customLoading ? 'loading' : ''}
            </Button>
          }
        >
          <div>
            <h4>This modal will render custom children</h4>
            <h4>
              Here we can add some custom or user defined elements which needs
              to be rendered in modal
            </h4>
          </div>
        </Modal>
      )}
    </div>
  );
}

export default Index;
