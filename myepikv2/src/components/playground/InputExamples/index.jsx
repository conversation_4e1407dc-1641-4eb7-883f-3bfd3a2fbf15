import { useState } from 'react';
import { Space, Typography } from 'antd';
import { Input, SearchIcon, SettingIcon, UserIcon } from '@/components';

const { Title } = Typography;

const InputExamples = () => {
  const [inputValue, setInputValue] = useState('');

  return (
    <div style={{ padding: '20px', maxWidth: '600px', margin: '0 auto' }}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Title level={4}>Basic Input</Title>
        <Input placeholder="Basic Input" />

        <Title level={4}>Input Sizes</Title>
        <Input size="large" placeholder="Large Input" prefix={<UserIcon />} />
        <Input
          size="middle"
          placeholder="Default Input"
          prefix={<UserIcon />}
        />
        <Input size="small" placeholder="Small Input" prefix={<UserIcon />} />

        <Title level={4}>Input Variants</Title>
        <Input placeholder="Outlined Input" variant="outlined" />
        <Input placeholder="Filled Input" variant="filled" />
        <Input placeholder="Borderless Input" variant="borderless" />

        <Title level={4}>Add-ons and Prefix/Suffix</Title>
        <Input
          addonBefore="http://"
          addonAfter=".com"
          placeholder="Add-on Input"
        />
        <Input
          prefix={<SearchIcon />}
          suffix={<SettingIcon />}
          placeholder="Prefix/Suffix Input"
        />

        <Title level={4}>Search Input</Title>
        <Input
          type="search"
          placeholder="Search Input"
          allowClear
          onSearch={(value) => console.log('Search:', value)}
        />

        <Title level={4}>Password Input</Title>
        <Input type="password" placeholder="Password Input" />

        <Title level={4}>Text Area</Title>
        <Input
          type="textarea"
          placeholder="Text Area"
          autoSize={{ minRows: 2, maxRows: 5 }}
        />

        <Title level={4}>Controlled Input</Title>
        <Input
          placeholder="Controlled Input"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          allowClear
        />

        <Title level={4}>Input with Character Count</Title>
        <Input placeholder="Input with maxLength" maxLength={10} showCount />

        <Title level={4}>Compact Style</Title>
        <Space.Compact>
          <Input defaultValue="Compact Input" />
          <Input defaultValue="Next Input" />
        </Space.Compact>
      </Space>
    </div>
  );
};

export default InputExamples;
