import { useEffect, useState } from 'react';
import { DataTable } from '@/components';
import { getColumns } from './constant';

const dataSource = [
  {
    key: '1',
    email: '<EMAIL>',
    note: 'Config Pushed',
    type: 'System',
    time: 'Feb 21, 2023, 03:05 pm',
    attachment: '--',
    unmonitored: '--',
  },
  {
    key: '2',
    email: '<EMAIL>',
    note: 'Firmware Updated',
    type: 'Admin',
    time: 'Mar 15, 2023, 01:15 pm',
    attachment: '--',
    unmonitored: '--',
  },
  {
    key: '3',
    email: '<EMAIL>',
    note: 'Config Pushed',
    type: 'System',
    time: 'Apr 10, 2023, 11:30 am',
    attachment: '--',
    unmonitored: '--',
  },
  {
    key: '4',
    email: '<EMAIL>',
    note: 'System Restarted',
    type: 'System',
    time: 'May 12, 2023, 02:45 pm',
    attachment: '--',
    unmonitored: '--',
  },
  {
    key: '5',
    email: '<EMAIL>',
    note: 'Patch Applied',
    type: 'Admin',
    time: 'Jun 18, 2023, 04:20 pm',
    attachment: '--',
    unmonitored: '--',
  },
  {
    key: '6',
    email: '<EMAIL>',
    note: 'Config Pushed',
    type: 'System',
    time: 'Jul 22, 2023, 09:10 am',
    attachment: '--',
    unmonitored: '--',
  },
  {
    key: '7',
    email: '<EMAIL>',
    note: 'Firmware Rolled Back',
    type: 'Admin',
    time: 'Aug 8, 2023, 06:50 pm',
    attachment: '--',
    unmonitored: '--',
  },
  {
    key: '8',
    email: '<EMAIL>',
    note: 'Patch Applied',
    type: 'System',
    time: 'Sep 14, 2023, 08:40 am',
    attachment: '--',
    unmonitored: '--',
  },
  {
    key: '9',
    email: '<EMAIL>',
    note: 'System Restarted',
    type: 'System',
    time: 'Oct 3, 2023, 03:15 pm',
    attachment: '--',
    unmonitored: '--',
  },
  {
    key: '10',
    email: '<EMAIL>',
    note: 'Config Pushed',
    type: 'Admin',
    time: 'Nov 19, 2023, 07:00 pm',
    attachment: '--',
    unmonitored: '--',
  },
];

const NotesTable = () => {
  const [paginationData, setPaginationData] = useState({
    page: 1,
    totalRecord: dataSource.length,
    pageSize: 10,
  });

  const onPaginationChange = (page, pageSize) => {
    setPaginationData((prev) => ({
      ...prev,
      page,
      pageSize,
    }));
  };

  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, 0);
  }, []);

  const columns = getColumns();

  return (
    <DataTable
      columns={columns}
      data={dataSource}
      paginationData={paginationData}
      onPaginationChange={onPaginationChange}
      pageSizeOptions={['10', '25', '50', '100']}
      paginationBtnOutside={true}
      paginationBtnText={['Previous']}
      paginationAlign="end"
      isPagination={true}
      loading={loading}
      size="small"
      headerClass="device-table-header-row"
    />
  );
};

export default NotesTable;
