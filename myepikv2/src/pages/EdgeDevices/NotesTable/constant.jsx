import { Typography, Tooltip } from '@/components';

export function getColumns() {
  return [
    {
      title: (
        <Typography className="small-text" color="#475467">
          Email
        </Typography>
      ),
      dataIndex: 'email',
      key: 'email',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text-bold"
        >
          {text}
        </Typography>
      ),
    },

    {
      title: (
        <Typography className="small-text" color="#475467">
          Note
        </Typography>
      ),
      dataIndex: 'note',
      key: 'note',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: (
        <Typography className="small-text" color="#475467">
          Type
        </Typography>
      ),
      dataIndex: 'type',
      key: 'type',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },

    {
      title: (
        <Typography className="small-text" color="#475467">
          Time
        </Typography>
      ),
      dataIndex: 'time',
      key: 'time',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: (
        <Typography className="small-text" color="#475467">
          Attachment
        </Typography>
      ),
      dataIndex: 'attachment',
      key: 'attachment',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: (
        <Typography className="small-text" color="#475467">
          Unmonitored
        </Typography>
      ),
      dataIndex: 'unmonitored',
      key: 'unmonitored',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
  ];
}
