import {
  AnimatedTab,
  Button,
  CloseIcon,
  CustomDropDown,
  Flex,
  Input,
  PlusIcon,
  SearchIcon,
  Typography,
} from '@/components';
import { useStore } from '@/store';
import { Divider } from 'antd';
import PropTypes from 'prop-types'; // Import PropTypes for prop validation
import { useState } from 'react';
import NotesTable from '../NotesTable';
import './styles.css';

const notesTabs = [
  { label: 'Work Notes', value: 'work_notes' },
  { label: 'Device Activity', value: 'device_activity' },
  { label: 'Recording', value: 'recording' },
];

const menuItems = [
  { key: 'email', label: 'Email' },
  { key: 'type', label: 'Type' },
  { key: 'dateRange', label: 'Date Range' },
];

const DeviceNotes = ({ openModal }) => {
  const [selectedTab, setSelectedTab] = useState('work_notes');
  const { closeDrawer } = useStore((state) => state.drawer);

  const onChangeTab = (value) => {
    setSelectedTab(value);
  };

  const handleMenuClick = (e) => {
    if (e.key) {
      console.log(`Click on menu item ${e.key}`);
    }
  };

  return (
    <Flex vertical>
      <Flex vertical>
        <Flex justify="space-between" align="center">
          <Typography className="heading-four-bold">Notes</Typography>
          <CloseIcon onClick={closeDrawer} style={{ cursor: 'pointer' }} />
        </Flex>
        <Divider className="divider-sm" />
      </Flex>
      <Flex
        justify="space-between"
        align="center"
        style={{ margin: '16px 0px' }}
      >
        <AnimatedTab
          value={selectedTab}
          onChange={onChangeTab}
          options={notesTabs}
          size="large"
        />
        <Flex align="center" gap={2}>
          <CustomDropDown menuItems={menuItems} onClick={handleMenuClick} />
          <Input
            prefix={<SearchIcon />}
            placeholder="Search"
            style={{
              maxWidth: '165px',
              paddingTop: '5px',
              paddingBottom: '5px',
            }}
          />
          <Button
            icon={<PlusIcon stroke={'white'} />}
            iconPosition="end"
            onClick={openModal}
          >
            Notes
          </Button>
        </Flex>
      </Flex>
      <NotesTable />
    </Flex>
  );
};

// Add PropTypes for DeviceNotes Component
DeviceNotes.propTypes = {
  openModal: PropTypes.func.isRequired,
};

export default DeviceNotes;
