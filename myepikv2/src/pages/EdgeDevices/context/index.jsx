/* eslint-disable react-hooks/exhaustive-deps */
import { createContext, useContext, useState, useEffect, useMemo } from 'react';
import PropTypes from 'prop-types';
import { useDebounce } from '@/hooks';

const EdgeDevicesContext = createContext(null);

export const EdgeDevicesProvider = ({ children }) => {
  const [selectedEdgeDevice, setSelectedEdgeDevice] = useState(null);
  const [appliedAdvanceFilters, setAppliedAdvanceFilters] = useState({});

  const [filter, setFilter] = useState({ isAll: true });
  const [selectedFilter, setSelectedFilter] = useState('');

  const [edgeDevicesView, setEdgeDevicesView] = useState('table');
  const [searchText, setSearchText] = useState('');
  const [debouncedSearch, setDebouncedSearch] = useState('');

  const [showAdvanceSearch, setShowAdvanceSearch] = useState(false);
  const [showRecentSearch, setShowRecentSearch] = useState(false);

  const [modals, setModals] = useState({});

  const [recentAdvancedResults, setRecentAdvancedResults] = useState(() => {
    const stored = sessionStorage.getItem('recentAdvancedResults');
    return stored ? JSON.parse(stored) : [];
  });
  const [selectedCompany, setSelectedCompany] = useState(null);

  const [selectedNumber, setSelectedNumber] = useState(null);

  const debouncedSearchHandler = useDebounce((value) => {
    setDebouncedSearch(value);
  }, 300);

  useEffect(() => {
    if (!debouncedSearch) {
      setFilter({});
      return;
    }

    if (typeof debouncedSearch === 'string') {
      const trimmed = debouncedSearch.trim();

      if (!trimmed) {
        setFilter({});
        return;
      }

      if (selectedFilter === 'companyName') {
        setFilter({ companyName: trimmed, isAll: true });
      } else if (selectedFilter === 'status') {
        const mappedStatus =
          trimmed.toLowerCase() === 'registered'
            ? 'registered'
            : trimmed.toLowerCase() === 'unregistered'
              ? 'unregistered'
              : undefined;

        setFilter(mappedStatus ? { status: mappedStatus } : {});
      } else if (!selectedFilter || selectedFilter === '') {
        setFilter({ query: trimmed });
      } else {
        setFilter({ [selectedFilter]: trimmed });
      }
    } else if (typeof debouncedSearch === 'object') {
      setFilter(debouncedSearch);
    }
  }, [debouncedSearch, selectedFilter]);

  const openModal = (key, config = {}) => {
    setModals((prev) => ({ ...prev, [key]: { open: true, ...config } }));
  };

  const closeModal = (key) => {
    setModals((prev) => ({ ...prev, [key]: { ...prev[key], open: false } }));
  };

  const handleMenuClick = (e) => {
    setSelectedFilter(e.key);
    if (Object.keys(appliedAdvanceFilters).length > 0) {
      setAppliedAdvanceFilters({});
    }
    setSearchText('');
    setDebouncedSearch('');
    setFilter({});
  };

  const handleSearchChange = (e) => {
    const value = e.target.value;
    if (Object.keys(appliedAdvanceFilters).length > 0) {
      setAppliedAdvanceFilters({});
      setFilter({});
    }
    setSearchText(value);
    debouncedSearchHandler(value);
  };

  const handleAdvanceSearch = (showSearch) => {
    if (showSearch && searchText) {
      setSearchText('');
      setDebouncedSearch('');
      setSelectedFilter('');
    }
    setShowAdvanceSearch(showSearch);
  };

  const handleRecentSearch = (showSearch) => setShowRecentSearch(showSearch);

  const handleAdvanceFilterSubmit = (advancedFilters) => {
    setAppliedAdvanceFilters(advancedFilters);
    setFilter(advancedFilters);
    closeModal('advanceSearch');
  };

  const handleFilter = (filterValue) => {
    setFilter(filterValue);
  };

  const handleEdgeDevicesRowClick = (edgeDeviceRow) => {
    setSelectedEdgeDevice(edgeDeviceRow);
  };

  const handleEdgeDevicesView = (view) => {
    setEdgeDevicesView(view);
  };

  const handleRemoveFilter = (keyToRemove) => {
    const updatedFilters = { ...appliedAdvanceFilters };
    delete updatedFilters[keyToRemove];

    setAppliedAdvanceFilters(updatedFilters);
    setFilter(updatedFilters);
  };

  const contextValue = useMemo(
    () => ({
      selectedEdgeDevice,
      filter,
      selectedFilter,
      edgeDevicesView,
      searchText,
      debouncedSearch,
      appliedAdvanceFilters,
      recentAdvancedResults,
      modals,
      openModal,
      closeModal,
      selectedCompany,

      selectedNumber,
      setSelectedNumber,
      debouncedSearchHandler,
      setSearchText,
      setSelectedCompany,
      setRecentAdvancedResults,
      setAppliedAdvanceFilters,
      setEdgeDevicesView,
      setSelectedEdgeDevice,
      setFilter,
      handleSearchChange,
      handleMenuClick,
      showAdvanceSearch,
      handleAdvanceSearch,
      showRecentSearch,
      handleRecentSearch,
      handleAdvanceFilterSubmit,
      handleFilter,
      handleEdgeDevicesRowClick,
      handleEdgeDevicesView,
      handleRemoveFilter,
      setSelectedFilter,
    }),
    [
      selectedNumber,
      selectedCompany,
      modals,
      recentAdvancedResults,
      selectedEdgeDevice,
      filter,
      selectedFilter,
      edgeDevicesView,
      searchText,
      debouncedSearch,
      appliedAdvanceFilters,
      showAdvanceSearch,
      showRecentSearch,
    ],
  );

  return (
    <EdgeDevicesContext.Provider value={contextValue}>
      {children}
    </EdgeDevicesContext.Provider>
  );
};

EdgeDevicesProvider.propTypes = {
  children: PropTypes.node.isRequired,
};

export const useEdgeDevicesContext = () => {
  const context = useContext(EdgeDevicesContext);
  if (!context)
    throw new Error(
      'useEdgeDevicesContext must be used within EdgeDevicesProvider',
    );
  return context;
};
