import { useEffect, useRef } from 'react';
import { useListEpikBoxes } from '@/graphqlHooks/epikv2-api/useListEpikBoxes';
import { useStore } from '@/store';
import { useState } from 'react';
import { useEdgeDevicesContext } from '../context';
import AssignEdgeDevice from '../AssignEdgeDevice';
import DeviceNotes from '../DeviceNotes';
import { Flex } from '@/components';
import MainHeader from '../MainHeader';
import MainTableHeader from '../MainTableHeader';
import DeviceTable from '../DeviceTable';
import DeviceModals from '../DeviceModals';
import DeviceView from '@/pages/EdgeDeviceDetail/DeviceView';

const EdgeDevicesTableView = () => {
  const { filter, appliedAdvanceFilters, setRecentAdvancedResults, openModal } =
    useEdgeDevicesContext();

  const lastSavedRef = useRef('');

  const { openDrawer, closeDrawer } = useStore((state) => state.drawer);

  const [paginationData, setPaginationData] = useState({
    page: 1,
    pageSize: 10,
  });

  const { data, isLoading } = useListEpikBoxes({
    pagination: paginationData,
    filter: { ...filter, isAll: true }, // will review this change with backend.
  });

  useEffect(() => {
    const results = data?.ListEpikBoxes?.docs || [];
    const saveKey = JSON.stringify({
      filters: appliedAdvanceFilters,
      pagination: paginationData,
    });

    if (
      results.length > 0 &&
      Object.keys(appliedAdvanceFilters).length > 0 &&
      saveKey !== lastSavedRef.current
    ) {
      setRecentAdvancedResults((prev) => {
        const combined = [...prev, ...results];
        const unique = Array.from(
          new Map(combined.map((item) => [item._id, item])).values(),
        ).slice(0, 50);

        sessionStorage.setItem('recentAdvancedResults', JSON.stringify(unique));
        return unique;
      });

      lastSavedRef.current = saveKey;
    }

    if (Object.keys(appliedAdvanceFilters).length === 0) {
      lastSavedRef.current = '';
    }
  }, [data, appliedAdvanceFilters, paginationData]);

  const handleAssignDevice = (formData) => {
    console.log('Assigned Device:', formData);
    closeDrawer();
  };

  const handleDeviceActions = (key, edgeDevice) => {
    if (key === 'view' || key === 'edit') {
      if (!edgeDevice) return null;
    } else if (key === 'delete') {
      openModal('deleteDevice', { deviceId: edgeDevice?._id });
    }
    const { viewDevice, deviceNotes } = drawerConfig;
    switch (key) {
      case 'view':
      case 'edit':
        return viewDevice(key, edgeDevice);
      case 'notes':
        return deviceNotes();
      default:
        return null;
    }
  };

  const drawerConfig = {
    assignEdgeDevice: () =>
      openDrawer(AssignEdgeDevice, {}, (formData) =>
        handleAssignDevice(formData),
      ),
    viewDevice: (mode, edgeDevice) =>
      openDrawer(DeviceView, { mode, edgeDeviceDetail: edgeDevice }),
    deviceNotes: () =>
      openDrawer(DeviceNotes, { openModal: () => openModal('addNote') }),
  };

  return (
    <Flex vertical gap={12} style={{ height: '100%' }}>
      <MainHeader />
      <div style={{ padding: '0 12px' }} className="custom-scrollbar">
        <Flex
          vertical
          style={{
            background: 'white',
            borderRadius: '12px',
            boxShadow: '0px 1px 2px 0px #1018280F, 0px 1px 3px 0px #1018281A',
          }}
          flex={1}
        >
          <MainTableHeader
            totalDevices={String(data?.ListEpikBoxes?.pagination?.count) || 0}
          />
          <DeviceTable
            handleAction={handleDeviceActions}
            data={data?.ListEpikBoxes?.docs || []}
            paginationData={{
              ...paginationData,
              totalRecord: data?.ListEpikBoxes?.pagination?.count || 0,
            }}
            isLoading={isLoading}
            onPaginationChange={(page, pageSize) => {
              setPaginationData({ page, pageSize });
            }}
          />
        </Flex>
      </div>
      <DeviceModals />
    </Flex>
  );
};

export default EdgeDevicesTableView;
