import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Input, Flex, Typography, CloseIcon, Button } from '@/components';
import { useStore } from '@/store';
import { Divider, Row, Col } from 'antd';
import './styles.css';

const Field = ({ label, component }) => (
  <Flex vertical gap={8}>
    <Typography className="small-text" style={{ color: '#344054' }}>
      {label}
    </Typography>
    {component}
  </Flex>
);

Field.propTypes = {
  label: PropTypes.string.isRequired,
  component: PropTypes.node.isRequired,
};

// Main Component: AssignEdgeDevice
const AssignEdgeDevice = ({ handleOk }) => {
  const [formValues, setFormValues] = useState({
    displayName: '',
    serialNumber: '',
    staticIp: '',
    lteIp: '',
    wgPublicKey: '',
    edgeDeviceModal: '',
    apuType: '',
    modemType: '',
    imei: '',
    phone: '',
    sim1: '',
    sim2: '',
    accountNumber: '',
    company: '',
    notificationEmails: '',
    notificationNumbers: '',
    shipping: '',
    alertEmail: '',
    alertSms: '',
  });

  const [submittedEmails, setSubmittedEmails] = useState([]);
  const [submittedSmsNumbers, setSubmittedSmsNumbers] = useState([]);

  const { closeDrawer } = useStore((state) => state.drawer);

  const handleChange = (field, value) => {
    setFormValues((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = () => {
    if (handleOk) {
      handleOk({ ...formValues, submittedEmails, submittedSmsNumbers });
    }
  };

  const handleKeyDown = (field, value, event) => {
    if (event.key === 'Enter' && value.trim()) {
      if (field === 'alertEmail') {
        setSubmittedEmails((prev) => [...prev, value]);
        setFormValues((prev) => ({ ...prev, [field]: '' }));
      } else if (field === 'alertSms') {
        setSubmittedSmsNumbers((prev) => [...prev, value]);
        setFormValues((prev) => ({ ...prev, [field]: '' }));
      }
    }
  };

  const sections = [
    {
      fields: [
        {
          name: 'displayName',
          label: 'Display Name',
          placeholder: 'Enter Name',
          span: 8,
        },
        {
          name: 'serialNumber',
          label: 'Serial Number',
          placeholder: 'Choose/Type',
          span: 8,
        },
      ],
    },
    {
      fields: [
        {
          name: 'staticIp',
          label: 'Static IP Address',
          placeholder: 'Specify',
          span: 8,
        },
        { name: 'lteIp', label: 'LTE IP', placeholder: 'Specify', span: 8 },
        {
          name: 'wgPublicKey',
          label: 'WG Public Key',
          placeholder: 'Specify',
          span: 8,
        },
      ],
    },
    {
      fields: [
        {
          name: 'edgeDeviceModal',
          label: 'Edge Device Modal',
          placeholder: 'Enter Modal',
          span: 8,
        },
        { name: 'apuType', label: 'APU Type', placeholder: 'Specify', span: 8 },
      ],
    },
    {
      title: 'Modal Management',
      fields: [
        {
          name: 'modemType',
          label: 'Modem Type',
          placeholder: 'Filter Modem',
          span: 8,
        },
        { name: 'imei', label: 'IMEI', placeholder: 'Enter', span: 8 },
        { name: 'phone', label: 'Phone', placeholder: 'Enter', span: 8 },
      ],
    },
    {
      fields: [
        {
          name: 'sim1',
          label: 'SIM 1',
          placeholder: 'Set box Phone',
          span: 12,
        },
        {
          name: 'sim2',
          label: 'SIM 2',
          placeholder: 'Set box Phone',
          span: 12,
        },
      ],
    },
    {
      fields: [
        {
          name: 'accountNumber',
          label: 'Account Number',
          placeholder: 'Not Specified',
          disabled: true,
          span: 8,
        },
        { name: 'company', label: 'Company', placeholder: 'Company', span: 8 },
      ],
    },
    {
      fields: [
        {
          name: 'notificationEmails',
          label: 'Notification Email(s)',
          placeholder: 'Enter the email',
          span: 12,
        },
        {
          name: 'notificationNumbers',
          label: 'Notification Number(s)',
          placeholder: 'Enter the number',
          span: 12,
        },
      ],
    },
    {
      showDivider: true,
      fields: [
        {
          name: 'shipping',
          label: 'Shipping',
          placeholder: 'Specify',
          span: 8,
        },
      ],
    },
    {
      fields: [
        {
          name: 'alertEmail',
          label: '911 Notification Alert Email',
          placeholder: 'Enter the email',
          span: 12,
        },
        {
          name: 'alertSms',
          label: '911 Notification Alert SMS Text Number(s)',
          placeholder: 'Enter the number',
          span: 12,
        },
      ],
    },
  ];

  return (
    <Flex vertical>
      <Flex vertical>
        <Flex justify="space-between" align="center">
          <Typography className="heading-four-bold">
            Assign Edge Device
          </Typography>
          <CloseIcon onClick={closeDrawer} style={{ cursor: 'pointer' }} />
        </Flex>
        <Divider className="divider-sm" />
      </Flex>
      {sections.map((section, sectionIndex) => (
        <React.Fragment key={`section-${sectionIndex}`}>
          {(section?.title || section?.showDivider) && (
            <Flex vertical style={{ marginTop: '24px' }}>
              <Typography className="heading-five">{section.title}</Typography>
              <Divider className="divider-sm" />
            </Flex>
          )}
          <Row gutter={[16]} style={{ marginTop: '16px' }}>
            {section.fields.map((field, fieldIndex) => (
              <Col
                key={`field-${sectionIndex}-${fieldIndex}`}
                span={field.span}
                xs={24}
                sm={12}
                md={12}
                lg={field.span}
                xl={field.span}
                xxl={field.span}
              >
                <Field
                  label={field.label}
                  component={
                    <Input
                      placeholder={field.placeholder}
                      value={formValues[field.name]}
                      onChange={(e) => handleChange(field.name, e.target.value)}
                      onKeyDown={(e) =>
                        handleKeyDown(field.name, formValues[field.name], e)
                      }
                    />
                  }
                />
              </Col>
            ))}
          </Row>
        </React.Fragment>
      ))}
      <Flex vertical gap={8} align="flex-end" style={{ marginTop: '28px' }}>
        <Divider className="divider-sm" />
        <Flex>
          <Button onClick={handleSubmit} style={{ width: '100%' }}>
            Assign Device
          </Button>
        </Flex>
      </Flex>
    </Flex>
  );
};

AssignEdgeDevice.propTypes = {
  handleOk: PropTypes.func.isRequired,
};

export default AssignEdgeDevice;
