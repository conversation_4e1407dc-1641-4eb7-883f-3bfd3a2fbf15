import PropTypes from 'prop-types';
import { theme } from 'antd';
import {
  Flex,
  Typography,
  Tag,
  Button,
  ListIcon,
  StarIcon,
  MapIcon,
  CloseTwoIcon,
} from '@/components';
import { useEdgeDevicesContext } from '../context';

const { useToken } = theme;

const filterKeyLabelMap = {
  companyName: 'Company',
  displayName: 'Edge Device Name',
  status: 'Status',
  epiNumber: 'EPI Number',
  serialNumber: 'Serial Number',
  macAddress: 'MAC Address',
  shipingNumber: 'Shipping Tracking Number',
  vpnAddress: 'VPN Address',
  imei: 'IMEI',
  sim: 'SIM',
  createdOn: 'Created On',
};

const MainTableHeader = ({ totalDevices = 0 }) => {
  const {
    searchText,
    edgeDevicesView,
    handleEdgeDevicesView,
    appliedAdvanceFilters,
    handleRemoveFilter,
  } = useEdgeDevicesContext();
  const {
    token: { colorPrimary, iconBlackColor },
  } = useToken();

  const advancedFilters = Object.keys(appliedAdvanceFilters).length > 0;

  return (
    <Flex align="center" justify="space-between" style={{ padding: '12px' }}>
      <Flex align="center" gap={8}>
        <Typography className="text-inter-500-18-28-gray-900">
          {searchText
            ? 'Search Results: '
            : advancedFilters
              ? 'Results for: '
              : 'Total Edge Devices: '}
        </Typography>
        <Tag
          text={`${totalDevices === 'undefined' ? 0 : totalDevices} Devices`}
          size="small"
          classNames="text-inter-500-12-18"
        />
        <Flex align="center" gap={4} wrap="wrap">
          {Object.entries(appliedAdvanceFilters).map(([key, value]) => (
            <Tag
              key={key}
              text={`${filterKeyLabelMap[key] || key}: ${String(value)}`}
              closable
              size="small"
              onClose={() => handleRemoveFilter(key)}
              style={{ marginLeft: '6px' }}
              closeIcon={
                <CloseTwoIcon stroke={'black'} height={14} width={14} />
              }
              classNames="extra-small-text custom-tag-small"
            />
          ))}
        </Flex>
      </Flex>
      <Flex align="center">
        <Button
          variant="outlined"
          variantColorBgContainer={true}
          icon={<StarIcon stroke={colorPrimary} />}
          style={{
            marginRight: '6px',
          }}
          classNames="device-header-right-action-btns"
        />
        <Flex
          align="center"
          justify="space-between"
          className="device-header-right-action-btns-container"
        >
          <Button
            variant={edgeDevicesView !== 'map' ? 'outlined' : 'solid'}
            icon={
              <MapIcon
                stroke={edgeDevicesView === 'map' ? 'white' : iconBlackColor}
              />
            }
            onClick={() => handleEdgeDevicesView('map')}
            style={{
              marginRight: '4px',
            }}
            onlyIcon={true}
            classNames="device-header-right-action-btns"
          />
          <Button
            variant={edgeDevicesView !== 'table' ? 'outlined' : 'solid'}
            icon={
              <ListIcon
                stroke={edgeDevicesView === 'table' ? 'white' : iconBlackColor}
              />
            }
            onClick={() => handleEdgeDevicesView('table')}
            onlyIcon={true}
            classNames="device-header-right-action-btns"
          />
        </Flex>
      </Flex>
    </Flex>
  );
};

MainTableHeader.propTypes = {
  totalDevices: PropTypes.string,
};

export default MainTableHeader;
