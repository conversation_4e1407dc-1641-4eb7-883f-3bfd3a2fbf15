import {
  Flex,
  CustomDropDown,
  Input,
  Button,
  SearchIcon,
  Select,
  PageTitle,
  Typography,
  Tooltip,
} from '@/components';
import CompanySearchDropdown from '../CompanySearchDropdown';
import NumberSearchDropdown from '../NumberSearchDropdown';
import { useEdgeDevicesContext } from '../context';
import { useStore } from '@/store';

const menuItems = [
  { key: '', label: 'Select Filter' },
  { key: 'serialNumber', label: 'Serial Number' },
  { key: 'displayName', label: 'Edge Device Name' },
  { key: 'vpnAddress', label: 'EPIK Edge Address' },
  { key: 'assignedNumber', label: 'Assigned Numbers' },
  { key: 'companyName', label: 'Company' },
  // { key: 'macAddress', label: 'MAC Address' },
  { key: 'status', label: 'Status' },
  { key: 'imei', label: 'IMEI' },
  { key: 'sim', label: 'SIM' },
  // { key: 'epiNumber', label: 'EPI Number' },
  { key: 'shipingNumber', label: 'Shipping Tracking Number' },
  { key: 'createdOn', label: 'Created On' },
];

const statusOptions = [
  { label: 'Registered', value: 'registered' },
  { label: 'Unregistered', value: 'unregistered' },
];

const MainHeader = () => {
  const {
    selectedFilter,
    searchText,
    handleSearchChange,
    handleMenuClick,
    openModal,
    selectedCompany,
    setSelectedCompany,
    selectedNumber,
    setSelectedNumber,

    setFilter,
    setSearchText,
    debouncedSearchHandler,
    setSelectedFilter,
  } = useEdgeDevicesContext();

  const selectedFilterItem = menuItems?.find(
    (item) => selectedFilter === item?.key,
  );

  const listEpikboxesError = useStore(
    (s) => s.error.errorState['list-epikboxes'] || null,
  );

  return (
    <Flex
      align="center"
      wrap
      justify="space-between"
      style={{ padding: '0 12px' }}
    >
      <Flex gap={8} align="center">
        <PageTitle pageTitle="Edge Devices" />
        {listEpikboxesError && (
          <Typography
            className="small-text"
            style={{
              color: 'var(--error-600)',
              marginLeft: '16px',
              maxWidth: '150px',
            }}
            ellipsis={{
              tooltip: <Tooltip>{listEpikboxesError}</Tooltip>,
            }}
          >
            {listEpikboxesError}
          </Typography>
        )}
      </Flex>
      <Flex align="center" wrap gap={6}>
        <CustomDropDown
          filterLabel={
            selectedFilter ? `${selectedFilterItem?.label}` : 'Select Filter'
          }
          menuItems={menuItems}
          handleMenuClick={handleMenuClick}
          disabled={listEpikboxesError}
        />
        {selectedFilter === 'companyName' ? (
          <CompanySearchDropdown
            value={selectedCompany}
            onChange={setSelectedCompany}
            onSearchText={(companyName) => {
              setSelectedFilter('companyName');
              setSearchText(companyName);
              debouncedSearchHandler(companyName);
            }}
            disabled={!!listEpikboxesError}
          />
        ) : selectedFilter === 'status' ? (
          <Select
            style={{ width: 165 }}
            options={statusOptions}
            placeholder="Select Status"
            value={searchText}
            allowClear
            onChange={(value) => {
              setSearchText(value || '');
              debouncedSearchHandler(value || '');
            }}
            disabled={!!listEpikboxesError}
          />
        ) : selectedFilter === 'assignedNumber' ? (
          <NumberSearchDropdown
            value={selectedNumber}
            onChange={setSelectedNumber}
            onSearchText={({ query, linkedBox }) => {
              setSearchText(query);

              const uniqueValidIds = Array.isArray(linkedBox)
                ? [...new Set(linkedBox.filter((id) => !!id))]
                : linkedBox
                  ? [linkedBox]
                  : [];

              if (uniqueValidIds.length === 0) {
                setFilter({});
              } else {
                debouncedSearchHandler({
                  assignedNumber: uniqueValidIds,
                  isAll: true,
                });
              }
            }}
            disabled={listEpikboxesError}
          />
        ) : (
          <Input
            prefix={<SearchIcon />}
            placeholder={
              selectedFilter ? `Search ${selectedFilterItem?.label}` : 'Search'
            }
            value={searchText}
            onChange={handleSearchChange}
            style={{
              maxWidth: '165px',
              paddingTop: '5px',
              paddingBottom: '5px',
            }}
            disabled={!!listEpikboxesError}
          />
        )}

        <Button
          variant="outlined"
          variantColorBgContainer
          onClick={() => openModal('advanceSearch')}
          classNames="responsive-button"
          disabled={!!listEpikboxesError}
        >
          <Typography className="text-inter-600-12-18-granite">
            Advance Search
          </Typography>
        </Button>
        <Button
          variant="outlined"
          variantColorBgContainer
          onClick={() => openModal('recentSearch')}
          classNames="responsive-button"
          disabled={!!listEpikboxesError}
        >
          <Typography className="text-inter-600-12-18-granite">
            Recent Search
          </Typography>
        </Button>
        {/* <Button onClick={() => drawerConfig.assignEdgeDevice()}>
          <Typography className="text-inter-600-12-18-granite">
            Assign Edge Device
          </Typography>
        </Button> */}
      </Flex>
    </Flex>
  );
};

export default MainHeader;
