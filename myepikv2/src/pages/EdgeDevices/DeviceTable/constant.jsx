import {
  Button,
  <PERSON>lex,
  Typography,
  DropDown,
  Tag,
  Tooltip,
  EditIcon,
  EyeIcon,
  FileTextIcon,
  MoreHorizontalIcon,
  StarIcon,
  TrashIcon,
} from '@/components';

export function getColumns({ onMenuClick }) {
  const handleMenuClick = (e, record) => {
    onMenuClick(e?.key, record);
    e?.domEvent?.stopPropagation();
  };

  const menuItems = [
    { key: 'view', label: 'View', icon: <EyeIcon /> },
    { key: 'edit', label: 'Edit', icon: <EditIcon /> },
    { key: 'notes', label: 'Notes', icon: <FileTextIcon /> },
    { key: 'delete', label: 'Delete', icon: <TrashIcon /> },
  ];

  const renderRegistrationTag = (status) => {
    const labelText = status ? 'Registered' : 'UnRegistered';
    return (
      <span style={{ width: 'fit-content', display: 'inline-block' }}>
        <Tooltip title={'Last Updated on:24-Aug-2024'}>
          <Tag
            bordered={false}
            text={labelText}
            className="responsive-row-text"
            style={{
              color: `${labelText === 'Registered' ? '#458CBE' : '#D92D20'}`,
              backgroundColor: `${labelText === 'Registered' ? '#E4F0F8' : '#FECDCA'}`,
              borderRadius: '16px',
            }}
          />
        </Tooltip>
      </span>
    );
  };

  const renderMonitoringTag = (isMonitor) => (
    <span style={{ width: 'fit-content', display: 'inline-block' }}>
      <Tooltip title={'Last Updated on:24-Aug-2024'} placement="right">
        {isMonitor ? (
          <div
            style={{
              height: '20px',
              width: '20px',
              background: '#4D9CD3',
              borderRadius: '50%',
            }}
          ></div>
        ) : (
          <div
            style={{
              height: '20px',
              width: '20px',
              background: '#E4E7EC',
              borderRadius: '50%',
            }}
          ></div>
        )}
      </Tooltip>
    </span>
  );

  const renderActionButtons = (record) => {
    return (
      <Flex
        align="center"
        style={{ height: '16px' }}
        onClick={(e) => e?.stopPropagation()}
      >
        <Button
          color="default"
          variant="outlined"
          icon={<StarIcon width={16} height={16} />}
          style={{
            padding: '0px',
            marginRight: '12px',
            width: '16px',
            height: 'inherit',
          }}
        />
        <DropDown
          menuItems={menuItems}
          onClick={(e) => handleMenuClick(e, record)}
          trigger={['click']}
          dropDownStyles={{ height: 'inherit', display: 'flex' }}
          buttonLabel=""
          buttonProps={{
            onlyIcon: true,
            icon: <MoreHorizontalIcon width={16} height={16} />,
            type: 'default',
            variant: 'filled',
            style: { height: 'inherit', padding: 0 },
          }}
        />
      </Flex>
    );
  };

  return [
    {
      title: (
        <Typography className="responsive-column-text">Company</Typography>
      ),
      dataIndex: 'companyDoc',
      key: 'companyDoc',
      render: (companyDoc) => {
        return (
          <Typography
            ellipsis={{
              tooltip: <Tooltip>{companyDoc?.name}</Tooltip>,
            }}
            className="responsive-column-text"
            style={{ fontWeight: 600 }}
          >
            {companyDoc?.name || '--'}
          </Typography>
        );
      },
    },
    {
      title: (
        <Typography className="responsive-column-text">
          Edge Device Name
        </Typography>
      ),
      dataIndex: 'displayName',
      key: 'displayName',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="responsive-row-text"
        >
          {text || '--'}
        </Typography>
      ),
    },
    {
      title: (
        <Typography className="responsive-column-text">
          Serial Number
        </Typography>
      ),
      dataIndex: 'serialNumber',
      key: 'serialNumber',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="responsive-row-text"
        >
          {text || '--'}
        </Typography>
      ),
    },
    {
      title: (
        <Typography className="responsive-column-text">Location</Typography>
      ),
      dataIndex: 'locationDoc',
      key: 'locationDoc',
      render: (locationDoc) => {
        return (
          <Typography
            ellipsis={{
              tooltip: <Tooltip>{locationDoc?.locationName}</Tooltip>,
            }}
            className="responsive-row-text"
          >
            {locationDoc?.locationName || '--'}
          </Typography>
        );
      },
    },
    {
      title: (
        <Typography className="responsive-column-text">Registration</Typography>
      ),
      dataIndex: 'registered',
      render: renderRegistrationTag,
    },
    {
      title: <Typography className="responsive-column-text">Ports</Typography>,
      dataIndex: 'numPorts',
      key: 'numPorts',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="responsive-row-text"
        >
          {text || '--'}
        </Typography>
      ),
    },
    {
      title: (
        <Typography className="responsive-column-text">Monitor</Typography>
      ),
      dataIndex: 'monitor',
      render: renderMonitoringTag,
    },
    {
      title: '',
      key: 'action',
      render: (record, index) => renderActionButtons(record, index),
      width: 70,
    },
  ];
}
