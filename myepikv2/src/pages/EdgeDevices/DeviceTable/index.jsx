import { useNavigate } from 'react-router-dom';
import PropTypes from 'prop-types'; // Import PropTypes for validation
import { DataTable, Typography } from '@/components';
import { getColumns } from './constant';
import { useEdgeDevicesContext } from '../context';
import { useStore } from '@/store';
import { Empty } from 'antd';

const DeviceTable = ({
  data,
  isLoading,
  paginationData,
  onPaginationChange,
  handleAction,
}) => {
  const { handleEdgeDevicesRowClick } = useEdgeDevicesContext();

  const navigate = useNavigate();

  const onRowClick = (row) => {
    navigate(`/edge-devices/${row?._id}`);
    handleEdgeDevicesRowClick(row);
  };

  const handleToggleChange = (checked, record) => {
    console.log(
      `Monitor toggled to ${checked} for device:`,
      record.serialNumber,
    );
  };

  const columns = getColumns({
    onNameClick: (record, index) => console.log('Row clicked:', record, index),
    onMenuClick: (key, record) => handleAction(key, record),
    onToggleChange: handleToggleChange,
  });

  const listEpikboxesError = useStore(
    (s) => s.error.errorState['list-epikboxes'] || null,
  );

  return (
    <DataTable
      columns={columns}
      data={data}
      onRowClick={onRowClick}
      loading={isLoading}
      rowSelection={true}
      onRowsSelectionChange={(rowsKeys, rowsData) => {
        console.log('Rows selected:', rowsKeys, rowsData);
      }}
      onPaginationChange={onPaginationChange}
      paginationData={paginationData}
      pageSizeOptions={['10', '25', '50', '100']}
      paginationBtnOutside={true}
      paginationBtnText={['Previous']}
      customPaginationClass={'custom-pagination'}
      paginationAlign="end"
      headerClass="device-table-header-row"
      isPagination={true}
      rowClassName="device-table-content-row"
      locale={
        listEpikboxesError && {
          emptyText: (
            <Empty>
              <Typography
                className="small-text"
                style={{ color: 'var(--error-600)' }}
              >
                {listEpikboxesError}
              </Typography>
            </Empty>
          ),
        }
      }
    />
  );
};

// Add PropTypes validation
DeviceTable.propTypes = {
  handleAction: PropTypes.func.isRequired, // `handleAction` is required and must be a function
  data: PropTypes.array.isRequired,
  isLoading: PropTypes.bool.isRequired,
  onPaginationChange: PropTypes.func.isRequired,
  paginationData: PropTypes.object.isRequired,
};

export default DeviceTable;
