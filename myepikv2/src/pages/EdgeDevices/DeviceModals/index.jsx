import { DeleteModal } from '@/components';
import { useEdgeDevicesContext } from '../context';
import { AddNote, AdvanceSearch, RecentSearch } from '../Modals';

const DeviceModals = () => {
  const { modals, closeModal } = useEdgeDevicesContext();

  const modalMap = {
    advanceSearch: AdvanceSearch,
    recentSearch: RecentSearch,
    deleteDevice: DeleteModal,
    addNote: AddNote,
  };

  return (
    <>
      {Object.entries(modals).map(([key, modal]) => {
        const ModalComponent = modalMap[key];
        if (!modal.open) return null;
        return (
          <ModalComponent
            key={key}
            {...modal}
            onCancel={() => closeModal(key)}
            handleOk={() => closeModal(key)}
          />
        );
      })}
    </>
  );
};

export default DeviceModals;
