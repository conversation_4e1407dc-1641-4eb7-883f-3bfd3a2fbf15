import PropTypes from 'prop-types';
import { useEffect, useState } from 'react';
import { Select } from 'antd';
import { useListNumbers } from '@/graphqlHooks/epikv2-api/useListNumbers';

const pageSize = 10;

const NumberSearchDropdown = ({ value, onChange, onSearchText }) => {
  const [query, setQuery] = useState('');
  const [page, setPage] = useState(1);
  const [options, setOptions] = useState([]);
  const [hasMore, setHasMore] = useState(true);

  const { data } = useListNumbers(
    {
      filter: { query },
      pagination: { page, pageSize },
    },
    !!query,
  );

  useEffect(() => {
    const docs = data?.ListNumbers?.docs || [];
    const total = data?.ListNumbers?.pagination?.count || 0;

    const newOptions = docs.map((n, index) => ({
      label: n.number,
      value: n.linkedBox,
      key: `${n.linkedBox}-${index}`,
      numberObj: n,
    }));

    const mergedOptions = page === 1 ? newOptions : [...options, ...newOptions];
    setOptions(mergedOptions);
    setHasMore((page - 1) * pageSize + newOptions.length < total);

    if (query.length >= 3) {
      const matched = mergedOptions.filter((opt) =>
        opt.label.toLowerCase().includes(query.toLowerCase()),
      );

      if (matched.length > 0) {
        const linkedBoxIds = matched.map((opt) => opt.value);

        onSearchText?.({
          query,
          linkedBox: linkedBoxIds,
        });
      } else {
        onSearchText?.({
          query,
          linkedBox: [],
        });
      }
    }
  }, [data]);

  const handleSearch = (input) => {
    if (input.length >= 3) {
      setQuery(input);
      setPage(1);
      onSearchText?.({ query: input, linkedBox: [] });
    } else if (input === '') {
      setQuery('');
      setPage(1);
      onSearchText?.({ query: '', linkedBox: [] });
      setOptions([]);
    }
  };

  const handleScroll = (e) => {
    const bottomReached =
      e.target.scrollTop + e.target.offsetHeight >= e.target.scrollHeight - 50;
    if (bottomReached && hasMore) {
      setPage((prev) => prev + 1);
    }
  };

  const selectedOption = options.find((o) => o.value === value);

  return (
    <Select
      showSearch
      allowClear
      labelInValue
      placeholder="Search Number"
      style={{ width: 200 }}
      filterOption={false}
      virtual
      value={
        selectedOption
          ? { value: selectedOption.value, label: selectedOption.label }
          : undefined
      }
      onSearch={handleSearch}
      onPopupScroll={handleScroll}
      onChange={(selectedOption) => {
        if (!selectedOption) {
          onChange(null);
          setQuery('');
          onSearchText?.({ query: '', linkedBox: '' });
          return;
        }

        const selected = options.find(
          (o) => o.value === selectedOption.value,
        )?.numberObj;

        onChange(selected.linkedBox);
        setQuery(selected.number);
        onSearchText?.({
          query: selected.number,
          linkedBox: selected.linkedBox,
        });
      }}
    >
      {options.map((opt) => (
        <Select.Option key={opt.key} value={opt.value}>
          {opt.label}
        </Select.Option>
      ))}
    </Select>
  );
};

NumberSearchDropdown.propTypes = {
  value: PropTypes.string,
  onChange: PropTypes.func,
  onSearchText: PropTypes.func,
};

export default NumberSearchDropdown;
