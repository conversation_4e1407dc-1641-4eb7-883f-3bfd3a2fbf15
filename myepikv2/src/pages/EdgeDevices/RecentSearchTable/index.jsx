import PropTypes from 'prop-types';
import { useEffect, useState } from 'react';
import { DataTable } from '@/components';
import { getColumns } from './constant';

const DeviceTable = ({ recentAdvancedResults }) => {
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, 0);
  }, []);

  const columns = getColumns();

  return (
    <DataTable
      size="small"
      columns={columns}
      data={recentAdvancedResults}
      loading={loading}
      headerClass="device-table-header-row"
    />
  );
};

DeviceTable.propTypes = {
  recentAdvancedResults: PropTypes.object.isRequired,
};

export default DeviceTable;
