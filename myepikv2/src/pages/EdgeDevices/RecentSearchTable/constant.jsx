import { Typography, Tag, Tooltip } from '@/components';

export function getColumns() {
  const renderRegistrationTag = (status) => {
    const labelText = status ? 'Registered' : 'UnRegistered';
    return (
      <span style={{ width: 'fit-content', display: 'inline-block' }}>
        <Tooltip title={'Last Updated on:24-Aug-2024'}>
          <Tag
            bordered={false}
            text={labelText}
            className="small-text"
            style={{
              color: `${labelText === 'Registered' ? '#458CBE' : '#D92D20'}`,
              backgroundColor: `${labelText === 'Registered' ? '#E4F0F8' : '#FECDCA'}`,
              borderRadius: '16px',
            }}
          />
        </Tooltip>
      </span>
    );
  };

  const renderMonitoringTag = (isMonitor) => (
    <span style={{ width: 'fit-content', display: 'inline-block' }}>
      <Tooltip title={'Last Updated on:24-Aug-2024'} placement="right">
        {isMonitor ? (
          <div
            style={{
              height: '20px',
              width: '20px',
              background: '#4D9CD3',
              borderRadius: '50%',
            }}
          ></div>
        ) : (
          <div
            style={{
              height: '20px',
              width: '20px',
              background: '#E4E7EC',
              borderRadius: '50%',
            }}
          ></div>
        )}
      </Tooltip>
    </span>
  );

  return [
    {
      title: <Typography className="small-text">Company</Typography>,
      dataIndex: 'companyDoc',
      key: 'companyDoc',
      render: (companyDoc) => {
        return (
          <Typography
            ellipsis={{
              tooltip: <Tooltip>{companyDoc?.name}</Tooltip>,
            }}
            className="small-text-bold"
          >
            {companyDoc?.name}
          </Typography>
        );
      },
    },
    {
      title: <Typography className="small-text">Edge Device Name</Typography>,
      dataIndex: 'displayName',
      key: 'displayName',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Serial Number</Typography>,
      dataIndex: 'serialNumber',
      key: 'serialNumber',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Location</Typography>,
      dataIndex: 'locationDoc',
      key: 'locationDoc',
      render: (locationDoc) => {
        return (
          <Typography
            ellipsis={{
              tooltip: <Tooltip>{locationDoc?.locationName}</Tooltip>,
            }}
            className="small-text"
          >
            {locationDoc?.locationName}
          </Typography>
        );
      },
    },
    {
      title: <Typography className="small-text">Registration</Typography>,
      dataIndex: 'registration',
      render: renderRegistrationTag,
    },
    {
      title: <Typography className="small-text">Ports</Typography>,
      dataIndex: 'numPorts',
      key: 'numPorts',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Monitor</Typography>,
      dataIndex: 'monitor',
      render: renderMonitoringTag,
    },
  ];
}
