import PropTypes from 'prop-types';
import { useEffect, useState } from 'react';
import { Select } from 'antd';
import { useListCompanies } from '@/graphqlHooks/auth/useListCompanies';

const pageSize = 10;

const CompanySearchDropdown = ({ value, onChange, onSearchText }) => {
  const [query, setQuery] = useState('');
  const [page, setPage] = useState(1);
  const [options, setOptions] = useState([]);
  const [hasMore, setHasMore] = useState(true);

  const { data } = useListCompanies(
    {
      input: {
        pagination: { page, pageSize },
        query,
      },
    },
    'ListCompaniesWithIdNameQuery',
    !!query,
  );

  useEffect(() => {
    const docs = data?.ListCompanies?.docs || [];
    const total = data?.ListCompanies?.pagination?.count || 0;

    const newOptions = docs.map((company) => ({
      label: company.name,
      value: company._id,
      company,
    }));

    setOptions((prev) => (page === 1 ? newOptions : [...prev, ...newOptions]));
    setHasMore((page - 1) * pageSize + newOptions.length < total);
  }, [data]);

  const handleSearch = (input) => {
    if (input.length >= 3) {
      setQuery(input);
      setPage(1);
      onSearchText?.(input);
    } else if (input === '') {
      setQuery('');
      setPage(1);
      onSearchText?.('');
      setOptions([]);
    }
  };

  const handleScroll = (e) => {
    const bottomReached =
      e.target.scrollTop + e.target.offsetHeight >= e.target.scrollHeight - 50;
    if (bottomReached && hasMore) {
      setPage((prev) => prev + 1);
    }
  };

  return (
    <Select
      showSearch
      allowClear
      labelInValue
      placeholder="Search Company"
      style={{ width: 200 }}
      filterOption={false}
      value={value ? { value: value._id, label: value.name } : undefined}
      onSearch={handleSearch}
      onPopupScroll={handleScroll}
      onChange={(selectedOption) => {
        if (!selectedOption) {
          onChange(null);
          setQuery('');
          onSearchText?.('');
          return;
        }

        const selected = options.find(
          (o) => o.value === selectedOption.value,
        )?.company;

        onChange(selected || null);

        if (selected?.name) {
          setQuery(selected.name);
          onSearchText?.(selected.name);
        } else {
          setQuery('');
          onSearchText?.('');
        }
      }}
      options={options}
    />
  );
};

CompanySearchDropdown.propTypes = {
  value: PropTypes.object,
  onChange: PropTypes.func,
  onSearchText: PropTypes.func,
};

export default CompanySearchDropdown;
