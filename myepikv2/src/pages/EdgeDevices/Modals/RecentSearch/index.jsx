import { Modal, Button, Typography, Flex } from '@/components';
import { Divider } from 'antd';
import DeviceTable from '../../RecentSearchTable';
import { useEdgeDevicesContext } from '../../context';

const Title = () => {
  return (
    <Flex>
      <Typography className="heading-four-bold">Recent Search</Typography>
    </Flex>
  );
};

const RecentSearch = () => {
  const { recentAdvancedResults, closeModal } = useEdgeDevicesContext();

  return (
    <Modal
      title={<Title />}
      open={true}
      onCancel={() => closeModal('recentSearch')}
      width={'70%'}
      footer={
        <Button variant="outlined" onClick={() => closeModal('recentSearch')}>
          Close
        </Button>
      }
    >
      <Divider className="divider-sm " style={{ marginBottom: '16px' }} />
      <DeviceTable recentAdvancedResults={recentAdvancedResults} />
    </Modal>
  );
};

export default RecentSearch;
