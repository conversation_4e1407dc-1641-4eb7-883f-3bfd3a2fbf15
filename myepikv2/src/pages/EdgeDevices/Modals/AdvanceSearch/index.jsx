import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  Modal,
  Input,
  Flex,
  Button,
  DropDown,
  DownIcon,
  PlusIcon,
  CloseTwoIcon,
  Typography,
} from '@/components';
import { Divider, Row, Col, theme } from 'antd';
import { useEdgeDevicesContext } from '../../context';

const { useToken } = theme;

const Title = () => (
  <Flex>
    <Typography className="heading-four-bold">Advance Search</Typography>
  </Flex>
);

const convertToFilterObject = (rows) => {
  const filter = {};
  for (const row of rows) {
    if (row.dropdownValue && row.inputValue?.trim()) {
      filter[row.dropdownValue] = row.inputValue.trim();
    }
  }
  return filter;
};

const menuItems = [
  { key: 'companyName', label: 'Company' },
  { key: 'displayName', label: 'Edge Device Name' },
  { key: 'status', label: 'Status' },
  { key: 'epiNumber', label: 'EPI Number' },
  { key: 'serialNumber', label: 'Serial Number' },
  { key: 'macAddress', label: 'MAC Address' },
  { key: 'shipingNumber', label: 'Shipping Tracking Number' },
  { key: 'vpnAddress', label: 'VPN Address' },
  { key: 'imei', label: 'IMEI' },
  { key: 'sim', label: 'SIM' },
  { key: 'createdOn', label: 'Created On' },
];

const Field = ({ component }) => <Flex gap={8}>{component}</Flex>;

Field.propTypes = {
  component: PropTypes.node.isRequired, // Validate `component` as a React node
};

const AdvanceSearch = ({ onCancel }) => {
  const { appliedAdvanceFilters, handleAdvanceFilterSubmit } =
    useEdgeDevicesContext();
  const {
    token: { colorPrimary },
  } = useToken();

  const [rows, setRows] = useState([]);
  const [newRow, setNewRow] = useState({ dropdownValue: '', inputValue: '' });

  const convertFiltersToRows = (filters) => {
    return Object.entries(filters).map(([key, value]) => ({
      id: Date.now() + Math.random(),
      dropdownValue: key,
      inputValue: value,
    }));
  };

  useEffect(() => {
    if (Object.keys(appliedAdvanceFilters).length > 0) {
      setRows(convertFiltersToRows(appliedAdvanceFilters));
    }
  }, [appliedAdvanceFilters]);

  const addRow = () => {
    if (!newRow.dropdownValue || !newRow.inputValue.trim()) return;

    setRows((prevRows) => [{ id: Date.now(), ...newRow }, ...prevRows]);
    setNewRow({ dropdownValue: '', inputValue: '' });
  };

  const removeRow = (id) => {
    setRows((prevRows) => prevRows.filter((row) => row.id !== id));
  };

  const handleDropdownChange = (id, value) => {
    setRows((prevRows) =>
      prevRows.map((row) =>
        row.id === id ? { ...row, dropdownValue: value } : row,
      ),
    );
  };

  const handleInputChange = (id, value) => {
    setRows((prevRows) =>
      prevRows.map((row) =>
        row.id === id ? { ...row, inputValue: value } : row,
      ),
    );
  };

  const selectedFields = rows.map((row) => row.dropdownValue);

  return (
    <Modal
      title={<Title />}
      open
      onCancel={onCancel}
      width="50%"
      footer={
        <Flex
          gap={8}
          justify="center"
          style={{ width: '100%', marginTop: '32px' }}
        >
          <Button
            variant="outlined"
            onClick={onCancel}
            style={{ width: '50%' }}
          >
            Cancel
          </Button>
          <Button
            onClick={() => {
              const tempRows = [...rows];
              if (newRow.dropdownValue && newRow.inputValue.trim()) {
                tempRows.unshift({ id: Date.now(), ...newRow });
              }
              handleAdvanceFilterSubmit(convertToFilterObject(tempRows));
            }}
            style={{ width: '50%' }}
          >
            Submit
          </Button>
        </Flex>
      }
    >
      <Divider className="divider-sm" />

      <Flex vertical gap={16}>
        {rows.map((row) => (
          <Row key={row.id} gutter={[8]} style={{ marginTop: '16px' }}>
            <Col span={8}>
              <DropDown
                menuItems={menuItems.filter(
                  (item) =>
                    !selectedFields.includes(item.key) ||
                    item.key === row.dropdownValue,
                )}
                buttonLabel={
                  menuItems.find((m) => m.key === row.dropdownValue)?.label ||
                  'Select Field'
                }
                onClick={(e) => handleDropdownChange(row.id, e.key)}
                trigger={['click']}
                icon={<DownIcon stroke={colorPrimary} />}
                buttonProps={{
                  variant: 'outlined',
                  variantColorBgContainer: true,
                  style: {
                    border: 'none',
                    width: '100%',
                    display: 'flex',
                    justifyContent: 'space-between',
                  },
                }}
              />
            </Col>
            <Col span={12}>
              <Field
                component={
                  <Input
                    placeholder="Specify"
                    value={row.inputValue}
                    onChange={(e) => handleInputChange(row.id, e.target.value)}
                  />
                }
              />
            </Col>
            <Col span={4}>
              <Field
                component={
                  <Button
                    color="default"
                    variant="text"
                    style={{
                      padding: '0px',
                      margin: 'auto',
                      height: 'inherit',
                    }}
                    icon={<CloseTwoIcon height={24} width={24} />}
                    onClick={() => removeRow(row.id)}
                  />
                }
              />
            </Col>
          </Row>
        ))}
      </Flex>

      <Row gutter={[8]} style={{ marginTop: '16px' }}>
        <Col span={8}>
          <DropDown
            menuItems={menuItems.filter(
              (item) => !selectedFields.includes(item.key),
            )}
            buttonLabel={
              menuItems.find((m) => m.key === newRow.dropdownValue)?.label ||
              'Select Field'
            }
            onClick={(e) =>
              setNewRow((prev) => ({ ...prev, dropdownValue: e.key }))
            }
            trigger={['click']}
            icon={<DownIcon stroke={colorPrimary} />}
            buttonProps={{
              variant: 'outlined',
              variantColorBgContainer: true,
              style: {
                border: 'none',
                width: '100%',
                display: 'flex',
                justifyContent: 'space-between',
              },
            }}
          />
        </Col>
        <Col span={12}>
          <Field
            component={
              <Input
                placeholder="Specify"
                value={newRow.inputValue}
                onChange={(e) =>
                  setNewRow((prev) => ({ ...prev, inputValue: e.target.value }))
                }
              />
            }
          />
        </Col>
        <Col span={4}>
          <Field
            component={
              <Button
                variant="outlined"
                variantColorBgContainer
                icon={<PlusIcon stroke={colorPrimary} />}
                onClick={addRow}
              />
            }
          />
        </Col>
      </Row>
    </Modal>
  );
};

AdvanceSearch.propTypes = {
  onCancel: PropTypes.func.isRequired,
  appliedAdvanceFilters: PropTypes.object,
};

export default AdvanceSearch;
