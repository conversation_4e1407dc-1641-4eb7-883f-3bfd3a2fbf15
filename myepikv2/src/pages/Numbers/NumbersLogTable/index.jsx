import { useEffect, useState } from 'react';
import { Divider } from 'antd';
import PropTypes from 'prop-types'; // Import PropTypes for validation
import { DataTable, Typography, Flex, CloseIcon } from '@/components';
import { useStore } from '@/store';
import { getColumnsNumbersLog, dataSourceNumbersLog } from '../constant';

const NumbersLogTable = ({ handleAction }) => {
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState([]);
  const { closeDrawer } = useStore((state) => state.drawer);

  const handleToggleChange = (checked, record) => {
    console.log(
      `Monitor toggled to ${checked} for device:`,
      record.serialNumber,
    );
  };

  useEffect(() => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      setData(dataSourceNumbersLog);
    }, 0);
  }, []);

  const columns = getColumnsNumbersLog({
    onNameClick: (record, index) => console.log('Row clicked:', record, index),
    onMenuClick: (key) => handleAction(key),
    onToggleChange: handleToggleChange,
  });

  return (
    <Flex vertical>
      <Flex vertical>
        <Flex justify="space-between" align="center">
          <Typography className="heading-four-bold">
            Last 10 Assignment Change Log
          </Typography>
          <CloseIcon onClick={closeDrawer} style={{ cursor: 'pointer' }} />
        </Flex>
        <Divider className="divider-sm" />
      </Flex>
      <DataTable
        columns={columns}
        data={data}
        loading={loading}
        paginationBtnOutside={true}
        customPaginationClass={'custom-pagination'}
        headerClass="device-table-header-row"
        isPagination={false}
      />
    </Flex>
  );
};

// Add PropTypes validation
NumbersLogTable.propTypes = {
  handleAction: PropTypes.func.isRequired, // `handleAction` is required and must be a function
};

export default NumbersLogTable;
