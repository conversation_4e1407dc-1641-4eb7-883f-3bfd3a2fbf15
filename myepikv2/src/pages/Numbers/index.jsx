import { useState } from 'react';
import { Flex } from 'antd';
import {
  Button,
  SearchIcon,
  Input,
  AdvanceSearch,
  DeleteWithSecurityCode,
  PageTitle,
} from '@/components';
import { useStore } from '@/store';
import NewNumber from './NewNumber';
import NumbersLogTable from './NumbersLogTable';
import AllNumbersTable from './AllNumbersTable';

const Numbers = () => {
  const [numberActionModal, setNumberActionModal] = useState('');
  const [showAdvanceSearch, setShowAdvanceSearch] = useState(false);
  const { openDrawer } = useStore((state) => state.drawer);

  const modalConfig = {
    deleteNumber: {
      component: DeleteWithSecurityCode,
      props: {
        onCancel: () => setNumberActionModal(''),
        handleOk: () => setNumberActionModal(''),
      },
      condition: numberActionModal === 'deleteNumber',
    },
    advanceSearch: {
      component: AdvanceSearch,
      props: {
        onCancel: () => setShowAdvanceSearch(false),
        handleOk: () => setShowAdvanceSearch(false),
      },
      condition: showAdvanceSearch,
    },
  };

  const drawerConfig = {
    addNumber: (mode) => openDrawer(NewNumber, { mode }),
    numberLogs: () => openDrawer(NumbersLogTable, {}),
  };

  const handleActions = (key) => {
    const { addNumber, numberLogs } = drawerConfig;
    switch (key) {
      case 'delete':
        return setNumberActionModal('deleteNumber');
      case 'edit':
        return addNumber('edit');
      case 'log':
        return numberLogs();
      default:
        return null;
    }
  };

  const renderModals = () => {
    return Object.entries(modalConfig).map(([key, modal]) => {
      const { component: ModalComponent, props, condition } = modal;
      return condition ? <ModalComponent key={key} {...props} /> : null;
    });
  };

  return (
    <Flex vertical gap={8} style={{ height: '100%' }}>
      <Flex
        align="center"
        wrap
        justify="space-between"
        style={{ padding: '0 12px' }}
      >
        <PageTitle pageTitle={'Numbers'} />
        <Flex align="center" wrap gap={6}>
          <Button
            variantColorBgContainer
            onClick={() => drawerConfig.addNumber()}
          >
            New Number
          </Button>
          <Button
            variant="outlined"
            variantColorBgContainer
            onClick={() => setShowAdvanceSearch(true)}
          >
            Advance Search
          </Button>
          <Input
            prefix={<SearchIcon />}
            placeholder="Search"
            style={{ maxWidth: '165px' }}
          />
        </Flex>
      </Flex>
      <div style={{ padding: '0 12px' }} className="custom-scrollbar">
        <Flex
          vertical
          flex={1}
          style={{
            background: 'white',
            borderRadius: '12px',
            boxShadow: '0px 1px 2px 0px #1018280F, 0px 1px 3px 0px #1018281A',
          }}
        >
          <Flex vertical style={{ marginTop: '20px', height: '100%' }}>
            <AllNumbersTable handleAction={handleActions} />
          </Flex>

          {renderModals()}
        </Flex>
      </div>
    </Flex>
  );
};

export default Numbers;
