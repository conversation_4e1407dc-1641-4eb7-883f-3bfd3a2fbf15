import {
  Typo<PERSON>,
  EllipsisText,
  Flex,
  Button,
  StarIcon,
  DropDown,
  Tooltip,
  MoreHorizontalIcon,
  EditIcon,
  FileTextIcon,
  TrashIcon,
  Select,
  StatusTag,
} from '@/components';

export function getColumns({ onMenuClick }) {
  const renderRoute = (options) => {
    console.log('renderRoute', options);
    return (
      <Flex style={{ width: 'fit-content' }}>
        <Select defaultValue="LSNCARCGR6_3191" options={options} size="small" />
      </Flex>
    );
  };

  const handleMenuClick = (e, key) => {
    console.log('handleMenuClick', e, e?.target?.value, key);
    onMenuClick?.(e?.key);
    e?.domEvent?.stopPropagation();
  };

  const menuItems = [
    { key: 'edit', label: 'Edit', icon: <EditIcon /> },
    { key: 'delete', label: 'Delete', icon: <TrashIcon /> },
    { key: 'log', label: 'Log', icon: <FileTextIcon /> },
  ];

  const renderActionButtons = () => (
    <Flex
      align="center"
      justify="flex-end"
      style={{ height: '16px' }}
      onClick={(e) => e?.stopPropagation()}
    >
      <Button
        color="default"
        variant="outline"
        icon={<StarIcon width={16} height={16} />}
        style={{
          padding: '0px',
          marginRight: '12px',
          width: '16px',
          height: 'inherit',
        }}
      />
      <DropDown
        menuItems={menuItems}
        onClick={(e) => handleMenuClick(e)}
        trigger={['click']}
        dropDownStyles={{ height: 'inherit', display: 'flex' }}
        buttonLabel=""
        buttonProps={{
          onlyIcon: true,
          icon: <MoreHorizontalIcon width={16} height={16} />,
          type: 'default',
          variant: 'filled',
          style: { height: 'inherit', padding: 0 },
        }}
      />
    </Flex>
  );
  return [
    {
      title: <Typography className="small-text">Company</Typography>,
      dataIndex: 'company',
      key: 'company',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Device Name</Typography>,
      dataIndex: 'deviceName',
      key: 'deviceName',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Number</Typography>,
      dataIndex: 'number',
      key: 'number',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Temp DID</Typography>,
      dataIndex: 'tempDID',
      key: 'tempDID',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Carrier</Typography>,
      dataIndex: 'carrier',
      key: 'carrier',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Type</Typography>,
      dataIndex: 'type',
      render: (text) => <StatusTag text={text} />,
    },
    {
      title: <Typography className="small-text">Route</Typography>,
      dataIndex: 'route',
      key: 'route',
      render: (options) => renderRoute(options),
      width: 200,
    },
    {
      title: <Typography className="small-text">Date Added</Typography>,
      dataIndex: 'dateAdded',
      key: 'dateAdded',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: '',
      key: 'action',
      render: renderActionButtons,
    },
  ];
}

export function getColumnsNumbersLog() {
  return [
    {
      title: (
        <Typography className="small-text" style={{ paddingLeft: '8px' }}>
          Date
        </Typography>
      ),
      dataIndex: 'dateAdded',
      key: 'dateAdded',
      render: (text) => <span className="small-text">{text}</span>,
      width: 100,
    },
    {
      title: (
        <Typography className="small-text" style={{ paddingLeft: '8px' }}>
          Number Type
        </Typography>
      ),
      dataIndex: 'type',
      render: (text) => <StatusTag text={text} />,
      width: 130,
    },
    {
      title: (
        <Typography className="small-text" style={{ paddingLeft: '8px' }}>
          Assigned To
        </Typography>
      ),
      dataIndex: 'assignedTo',
      key: 'assignedTo',
      render: (text) => <span className="small-text">{text}</span>,
      width: 200,
    },
    {
      title: (
        <Typography className="small-text" style={{ marginLeft: '8px' }}>
          Company
        </Typography>
      ),
      dataIndex: 'company',
      key: 'company',
      render: (text) => (
        <EllipsisText text={text} minWidth={250} className="small-text" />
      ),
      width: 150,
    },
    {
      title: (
        <Typography className="small-text" style={{ paddingLeft: '8px' }}>
          EpikBox
        </Typography>
      ),
      dataIndex: 'epikBox',
      key: 'epikBox',
      render: (text) => <span className="small-text">{text}</span>,
      width: 150,
    },
    {
      title: (
        <Typography className="small-text" style={{ paddingLeft: '8px' }}>
          Carrier
        </Typography>
      ),
      dataIndex: 'carrier',
      key: 'carrier',
      render: (text) => <span className="small-text">{text}</span>,
      width: 90,
    },
    {
      title: (
        <Typography className="small-text" style={{ paddingLeft: '8px' }}>
          Box Port
        </Typography>
      ),
      dataIndex: 'boxPort',
      key: 'boxPort',
      render: (text) => <span className="small-text">{text}</span>,
      width: 90,
    },
  ];
}

const options = [
  {
    value: 'LSNCARCGR6_3191',
    label: (
      <Typography className="small-text" style={{ marginLeft: '8px' }}>
        LSNCARCGR6_3191
      </Typography>
    ),
  },
  {
    value: 'LSNCARCGR6_3192',
    label: (
      <Typography className="small-text" style={{ marginLeft: '8px' }}>
        LSNCARCGR6_3192
      </Typography>
    ),
  },
  {
    value: 'LSNCARCGR6_3193',
    label: (
      <Typography className="small-text" style={{ marginLeft: '8px' }}>
        LSNCARCGR6_3193
      </Typography>
    ),
  },
  {
    value: 'LSNCARCGR6_3194',
    label: (
      <Typography className="small-text" style={{ marginLeft: '8px' }}>
        LSNCARCGR6_3194
      </Typography>
    ),
  },
  {
    value: 'LSNCARCGR6_3195',
    label: (
      <Typography className="small-text" style={{ marginLeft: '8px' }}>
        LSNCARCGR6_3195
      </Typography>
    ),
  },
];

export const dataSource = [
  {
    key: '1',
    company: 'Epik Networks',
    deviceName: 'Device Alpha',
    number: '+16572078001',
    tempDID: 'Yes',
    carrier: 'IQ',
    type: 'Assigned',
    route: options,
    dateAdded: '09/01/2020',
  },
  {
    key: '2',
    company: 'Epik Telecom',
    deviceName: 'Device Beta',
    number: '+16572078002',
    tempDID: 'No',
    carrier: 'Twilio',
    type: 'Unassigned',
    route: options,
    dateAdded: '09/02/2020',
  },
  {
    key: '3',
    company: 'Epik Voice',
    deviceName: 'Device Gamma',
    number: '+16572078003',
    tempDID: 'No',
    carrier: 'Plivo',
    type: 'Unassigned',
    route: options,
    dateAdded: '09/03/2020',
  },
  {
    key: '4',
    company: 'Epik Cloud',
    deviceName: 'Device Delta',
    number: '+16572078004',
    tempDID: 'Yes',
    carrier: 'IQ',
    type: 'Assigned',
    route: options,
    dateAdded: '09/04/2020',
  },
  {
    key: '5',
    company: 'Epik Solutions',
    deviceName: 'Device Epsilon',
    number: '+16572078005',
    tempDID: 'No',
    carrier: 'Bandwidth',
    type: 'Unassigned',
    route: options,
    dateAdded: '09/05/2020',
  },
  {
    key: '6',
    company: 'Epik Systems',
    deviceName: 'Device Zeta',
    number: '+16572078006',
    tempDID: 'Yes',
    carrier: 'IQ',
    type: 'Assigned',
    route: options,
    dateAdded: '09/06/2020',
  },
  {
    key: '7',
    company: 'Epik Digital',
    deviceName: 'Device Eta',
    number: '+16572078007',
    tempDID: 'No',
    carrier: 'Plivo',
    type: 'Unassigned',
    route: options,
    dateAdded: '09/07/2020',
  },
  {
    key: '8',
    company: 'Epik Company Dummy',
    deviceName: 'Device Theta',
    number: '+16572078008',
    tempDID: 'No',
    carrier: 'Twilio',
    type: 'Assigned',
    route: options,
    dateAdded: '09/08/2020',
  },
  {
    key: '9',
    company: 'Epik Network',
    deviceName: 'Device Iota',
    number: '+16572078009',
    tempDID: 'Yes',
    carrier: 'IQ',
    type: 'Unassigned',
    route: options,
    dateAdded: '09/09/2020',
  },
  {
    key: '10',
    company: 'Epik Cloud',
    deviceName: 'Device Kappa',
    number: '+16572078010',
    tempDID: 'No',
    carrier: 'Bandwidth',
    type: 'Assigned',
    route: options,
    dateAdded: '09/10/2020',
  },
  {
    key: '11',
    company: 'Epik Telecom',
    deviceName: 'Device Lambda',
    number: '+16572078011',
    tempDID: 'Yes',
    carrier: 'Twilio',
    type: 'Unassigned',
    route: options,
    dateAdded: '09/11/2020',
  },
  {
    key: '12',
    company: 'Epik Secure',
    deviceName: 'Device Mu',
    number: '+16572078012',
    tempDID: 'No',
    carrier: 'Plivo',
    type: 'Unassigned',
    route: options,
    dateAdded: '09/12/2020',
  },
  {
    key: '13',
    company: 'Epik Logic',
    deviceName: 'Device Nu',
    number: '+16572078013',
    tempDID: 'No',
    carrier: 'IQ',
    type: 'Assigned',
    route: options,
    dateAdded: '09/13/2020',
  },
  {
    key: '14',
    company: 'Epik Edge',
    deviceName: 'Device Xi',
    number: '+16572078014',
    tempDID: 'Yes',
    carrier: 'Bandwidth',
    type: 'Unassigned',
    route: options,
    dateAdded: '09/14/2020',
  },
  {
    key: '15',
    company: 'Epik Team',
    deviceName: 'Device Omicron',
    number: '+16572078015',
    tempDID: 'No',
    carrier: 'Twilio',
    type: 'Assigned',
    route: options,
    dateAdded: '09/15/2020',
  },
  {
    key: '16',
    company: 'Epik Flow',
    deviceName: 'Device Pi',
    number: '+16572078016',
    tempDID: 'Yes',
    carrier: 'IQ',
    type: 'Unassigned',
    route: options,
    dateAdded: '09/16/2020',
  },
  {
    key: '17',
    company: 'Epik Company A',
    deviceName: 'Device Rho',
    number: '+16572078017',
    tempDID: 'No',
    carrier: 'Plivo',
    type: 'Assigned',
    route: options,
    dateAdded: '09/17/2020',
  },
  {
    key: '18',
    company: 'Epik Company B',
    deviceName: 'Device Sigma',
    number: '+16572078018',
    tempDID: 'Yes',
    carrier: 'Twilio',
    type: 'Unassigned',
    route: options,
    dateAdded: '09/18/2020',
  },
  {
    key: '19',
    company: 'Epik Core',
    deviceName: 'Device Tau',
    number: '+16572078019',
    tempDID: 'No',
    carrier: 'IQ',
    type: 'Assigned',
    route: options,
    dateAdded: '09/19/2020',
  },
  {
    key: '20',
    company: 'Epik Logic',
    deviceName: 'Device Upsilon',
    number: '+16572078020',
    tempDID: 'Yes',
    carrier: 'Bandwidth',
    type: 'Unassigned',
    route: options,
    dateAdded: '09/20/2020',
  },
  {
    key: '21',
    company: 'Epik Digital',
    deviceName: 'Device Phi',
    number: '+16572078021',
    tempDID: 'No',
    carrier: 'IQ',
    type: 'Assigned',
    route: options,
    dateAdded: '09/21/2020',
  },
  {
    key: '22',
    company: 'Epik Connect',
    deviceName: 'Device Chi',
    number: '+16572078022',
    tempDID: 'Yes',
    carrier: 'Twilio',
    type: 'Unassigned',
    route: options,
    dateAdded: '09/22/2020',
  },
  {
    key: '23',
    company: 'Epik Communications',
    deviceName: 'Device Psi',
    number: '+16572078023',
    tempDID: 'No',
    carrier: 'Bandwidth',
    type: 'Assigned',
    route: options,
    dateAdded: '09/23/2020',
  },
  {
    key: '24',
    company: 'Epik Bridge',
    deviceName: 'Device Omega',
    number: '+16572078024',
    tempDID: 'Yes',
    carrier: 'IQ',
    type: 'Unassigned',
    route: options,
    dateAdded: '09/24/2020',
  },
  {
    key: '25',
    company: 'Epik Secure',
    deviceName: 'Device Nova',
    number: '+16572078025',
    tempDID: 'No',
    carrier: 'Twilio',
    type: 'Assigned',
    route: options,
    dateAdded: '09/25/2020',
  },
  {
    key: '26',
    company: 'Epik Logic',
    deviceName: 'Device Orion',
    number: '+16572078026',
    tempDID: 'Yes',
    carrier: 'Plivo',
    type: 'Unassigned',
    route: options,
    dateAdded: '09/26/2020',
  },
  {
    key: '27',
    company: 'Epik One',
    deviceName: 'Device Vega',
    number: '+16572078027',
    tempDID: 'No',
    carrier: 'Bandwidth',
    type: 'Assigned',
    route: options,
    dateAdded: '09/27/2020',
  },
  {
    key: '28',
    company: 'Epik Ventures',
    deviceName: 'Device Altair',
    number: '+16572078028',
    tempDID: 'Yes',
    carrier: 'IQ',
    type: 'Unassigned',
    route: options,
    dateAdded: '09/28/2020',
  },
  {
    key: '29',
    company: 'Epik Comms',
    deviceName: 'Device Sirius',
    number: '+16572078029',
    tempDID: 'No',
    carrier: 'Plivo',
    type: 'Assigned',
    route: options,
    dateAdded: '09/29/2020',
  },
  {
    key: '30',
    company: 'Epik Company Name',
    deviceName: 'Epik Device Name',
    number: '+16572078030',
    tempDID: 'No',
    carrier: 'IQ',
    type: 'Unassigned',
    route: options,
    dateAdded: '09/30/2020',
  },
];

export const dataSourceNumbersLog = [
  {
    key: '1',
    dateAdded: '09/01/2020',
    type: 'Assigned',
    assignedTo: '<EMAIL>',
    company: 'Epik Networks',
    epikBox: 'Device Alpha',
    carrier: 'IQ',
    boxPort: 'Port 1',
  },
  {
    key: '2',
    dateAdded: '09/02/2020',
    type: 'Assigned',
    assignedTo: '<EMAIL>',
    company: 'Epik Telecom',
    epikBox: 'Device Beta',
    carrier: 'Verizon',
    boxPort: 'Port 2',
  },
  {
    key: '3',
    dateAdded: '09/03/2020',
    type: 'Unassigned',
    assignedTo: '',
    company: 'Epik Connect',
    epikBox: 'Device Gamma',
    carrier: 'AT&T',
    boxPort: 'Port 3',
  },
  {
    key: '4',
    dateAdded: '09/04/2020',
    type: 'Assigned',
    assignedTo: '<EMAIL>',
    company: 'Epik Networks',
    epikBox: 'Device Delta',
    carrier: 'IQ',
    boxPort: 'Port 4',
  },
  {
    key: '5',
    dateAdded: '09/05/2020',
    type: 'Assigned',
    assignedTo: '<EMAIL>',
    company: 'Epik Corp',
    epikBox: 'Device Epsilon',
    carrier: 'Rogers',
    boxPort: 'Port 5',
  },
  {
    key: '6',
    dateAdded: '09/06/2020',
    type: 'Unassigned',
    assignedTo: '',
    company: 'Epik Labs',
    epikBox: 'Device Zeta',
    carrier: 'IQ',
    boxPort: 'Port 6',
  },
  {
    key: '7',
    dateAdded: '09/07/2020',
    type: 'Assigned',
    assignedTo: '<EMAIL>',
    company: 'Epik Telecom',
    epikBox: 'Device Eta',
    carrier: 'T-Mobile',
    boxPort: 'Port 7',
  },
  {
    key: '8',
    dateAdded: '09/08/2020',
    type: 'Assigned',
    assignedTo: '<EMAIL>',
    company: 'Epik Connect',
    epikBox: 'Device Theta',
    carrier: 'IQ',
    boxPort: 'Port 8',
  },
  {
    key: '9',
    dateAdded: '09/09/2020',
    type: 'Unassigned',
    assignedTo: '',
    company: 'Epik Networks',
    epikBox: 'Device Iota',
    carrier: 'Telus',
    boxPort: 'Port 9',
  },
  {
    key: '10',
    dateAdded: '09/10/2020',
    type: 'Assigned',
    assignedTo: '<EMAIL>',
    company: 'Epik Labs',
    epikBox: 'Device Kappa',
    carrier: 'Bell',
    boxPort: 'Port 10',
  },
];
