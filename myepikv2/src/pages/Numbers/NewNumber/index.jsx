import React, { useState } from 'react';
import PropTypes from 'prop-types';
import {
  Input,
  Flex,
  Button,
  Typography,
  CloseIcon,
  Select,
  Toggle,
} from '@/components';
import { Divider, Row, Col } from 'antd';
import { useStore } from '@/store';

const Field = ({ label, component, bottomLabel }) => (
  <Flex vertical gap={8}>
    <Typography className="small-text" style={{ color: 'var(--primary-gray)' }}>
      {label}
    </Typography>
    {component}
    {bottomLabel && (
      <Typography className="extra-small-text">{bottomLabel}</Typography>
    )}
  </Flex>
);

// Add PropTypes for Field Component
Field.propTypes = {
  label: PropTypes.string.isRequired, // `label` is a required string
  component: PropTypes.node.isRequired, // `component` is a required React node
  bottomLabel: PropTypes.string.isRequired,
};

const typeOptions = [
  'Ring Down',
  'Secure Entry',
  'Elevator',
  'Modem',
  'Page',
  'Voice',
  'Custom',
  'Unassigned',
  'Alarm',
  'Fire Alarm',
  'Fax relay Protocol (T.37)',
  'Dial out',
  'Hunt',
  'Modem Hunt',
];

const carrierType = [
  'Ineloquent',
  'TDM',
  'Verizon TDM',
  'Verizon SIP',
  'Telnyx',
  'IQ Toll-Free',
  'Non Epik Line',
];

const routeOptions = [
  'NYCMNYBXGR1_4420',
  'NYCMNYBXGR1_4421',
  'NYCMNYBXGR1_4422',
  'NYCMNYBXGR1_4422',
  'NYCMNYBXGR1_4423',
];

const NewNumber = ({ mode }) => {
  const [formValues, setFormValues] = useState({});

  const { closeDrawer } = useStore((state) => state.drawer);

  const handleChange = (field, value) => {
    setFormValues((prev) => ({ ...prev, [field]: value }));
  };

  const handleKeyDown = (field, value, event) => {
    if (event.key === 'Enter' && value.trim()) {
      if (field === 'alertEmail') {
        handleChange(field, '');
      }
    }
  };

  const getOptions = (options) => {
    return options.map((opt) => {
      return {
        value: opt,
        label: (
          <Typography className="small-text" style={{ marginLeft: '8px' }}>
            {opt}
          </Typography>
        ),
      };
    });
  };

  const sections = [
    {
      title: '',
      fields: [
        {
          name: 'number',
          label: 'Number',
          placeholder: '',
          span: 12,
        },
        {
          name: 'type',
          label: 'Type',
          placeholder: 'Type',
          component: (
            <Select
              defaultValue="Unassigned"
              options={getOptions(typeOptions)}
              size="middle"
              variant="filled"
            />
          ),
          span: 6,
        },
        {
          name: 'carrier',
          label: 'Carrier Type',
          placeholder: 'Enter Phone',
          component: (
            <Select
              defaultValue="Ineloquent"
              options={getOptions(carrierType)}
              size="middle"
              variant="filled"
            />
          ),
          span: 6,
        },
        {
          name: 'company',
          label: 'Company',
          placeholder: '',
          span: 12,
        },
      ],
      editModeFields: [
        {
          name: 'user',
          label: 'User',
          placeholder: '<EMAIL>',
          value: '<EMAIL>',
          span: 12,
        },
        {
          name: 'route',
          label: 'Route',
          placeholder: 'Enter Phone',
          component: (
            <Select
              defaultValue="NYCMNYBXGR1_4420"
              options={getOptions(routeOptions)}
              size="middle"
            />
          ),
          span: 12,
        },
      ],
    },
  ];

  return (
    <Flex vertical>
      <Flex vertical>
        <Flex justify="space-between" align="center">
          <Typography className="heading-four-bold">
            {`${mode === 'edit' ? 'Edit Number +16572078001' : 'New Number'}`}
          </Typography>
          <Flex align="center" gap={8}>
            {mode === 'edit' ? (
              <Button size="small">Force Unlink</Button>
            ) : (
              <>
                <Typography className="heading-five">Bulk</Typography>
                <Toggle size="small" />
              </>
            )}

            <CloseIcon onClick={closeDrawer} style={{ cursor: 'pointer' }} />
          </Flex>
        </Flex>
        <Divider className="divider-sm" />
      </Flex>
      {sections.map((section, sectionIndex) => (
        <React.Fragment key={`section-${sectionIndex}`}>
          {(section?.title || section?.showDivider) && (
            <Flex vertical style={{ marginTop: '24px' }}>
              <Typography className="heading-five">{section.title}</Typography>
              <Divider className="divider-sm" />
            </Flex>
          )}
          <Row gutter={[8, 16]} style={{ marginTop: '16px' }}>
            {section.fields.map((field, fieldIndex) => (
              <Col
                key={`field-${sectionIndex}-${fieldIndex}`}
                span={field.span}
                xs={24}
                sm={12}
                md={12}
                lg={field.span}
                xl={field.span}
                xxl={field.span}
              >
                {Field({
                  label: field.label,
                  component: field.component || (
                    <Input
                      placeholder={field.placeholder}
                      value={formValues[field.name]}
                      onChange={(e) => handleChange(field.name, e.target.value)}
                      onKeyDown={(e) =>
                        field.list &&
                        e.key === 'Enter' &&
                        handleKeyDown(field.name, formValues[field.name], e)
                      }
                      size="middle"
                      className="text-medium-regular"
                    />
                  ),
                  bottomLabel: field?.bottomLabel,
                })}
              </Col>
            ))}
            {mode === 'edit' &&
              section.editModeFields.map((field, fieldIndex) => (
                <Col
                  key={`field-${sectionIndex}-${fieldIndex}`}
                  span={field.span}
                  xs={24}
                  sm={12}
                  md={12}
                  lg={field.span}
                  xl={field.span}
                  xxl={field.span}
                >
                  {Field({
                    label: field.label,
                    component: field.component || (
                      <Input
                        placeholder={field.placeholder}
                        value={formValues[field.name]}
                        onChange={(e) =>
                          handleChange(field.name, e.target.value)
                        }
                        onKeyDown={(e) =>
                          field.list &&
                          e.key === 'Enter' &&
                          handleKeyDown(field.name, formValues[field.name], e)
                        }
                        size="middle"
                        className="text-medium-regular"
                      />
                    ),
                    bottomLabel: field?.bottomLabel,
                  })}
                </Col>
              ))}
          </Row>
          {mode === 'edit' && (
            <Button style={{ width: '10%', marginTop: 16 }}>Edit CNAM</Button>
          )}
        </React.Fragment>
      ))}
      <Flex vertical gap={4} justify="end" style={{ marginTop: '8px' }}>
        <Divider className="divider-sm" />
        <Flex gap={8} justify="end">
          <Button
            variant="outlined"
            onClick={closeDrawer}
            style={{ width: '10%' }}
          >
            Cancel
          </Button>
          <Button
            onClick={() => console.log('Save Form:', formValues)}
            style={{ width: '10%' }}
          >
            Send
          </Button>
        </Flex>
      </Flex>
    </Flex>
  );
};

NewNumber.propTypes = {
  mode: PropTypes.bool.isRequired,
};

export default NewNumber;
