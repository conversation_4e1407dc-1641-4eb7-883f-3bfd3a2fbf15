import {
  Typography,
  Flex,
  Button,
  StarIcon,
  DropDown,
  Tooltip,
  MoreHorizontalIcon,
  TrashIcon,
  StatusTag,
} from '@/components';

export function getColumns({ onMenuClick }) {
  const handleMenuClick = (e, key) => {
    console.log('handleMenuClick', e, e?.target?.value, key);
    onMenuClick?.(e?.key);
    e?.domEvent?.stopPropagation();
  };

  const menuItems = [{ key: 'delete', label: 'Delete', icon: <TrashIcon /> }];

  const renderActionButtons = () => (
    <Flex
      align="center"
      justify="flex-end"
      style={{ height: '16px' }}
      onClick={(e) => e?.stopPropagation()}
    >
      <Button
        color="default"
        variant="outline"
        icon={<StarIcon width={16} height={16} />}
        style={{
          padding: '0px',
          marginRight: '12px',
          width: '16px',
          height: 'inherit',
        }}
      />
      <DropDown
        menuItems={menuItems}
        onClick={(e) => handleMenuClick(e)}
        trigger={['click']}
        dropDownStyles={{ height: 'inherit', display: 'flex' }}
        buttonLabel=""
        buttonProps={{
          onlyIcon: true,
          icon: <MoreHorizontalIcon width={16} height={16} />,
          type: 'default',
          variant: 'filled',
          style: { height: 'inherit', padding: 0 },
        }}
      />
    </Flex>
  );
  return [
    {
      title: <Typography className="small-text">Company</Typography>,
      dataIndex: 'company',
      key: 'company',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Description</Typography>,
      dataIndex: 'description',
      key: 'description',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">User</Typography>,
      dataIndex: 'user',
      key: 'user',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Page Count</Typography>,
      dataIndex: 'pageCount',
      key: 'pageCount',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Date/Time</Typography>,
      dataIndex: 'dateTime',
      key: 'dateTime',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">CID</Typography>,
      dataIndex: 'cid',
      key: 'cid',
      render: (text) => (
        <Typography
          className="small-text"
          underline={true}
          style={{ color: '#1154FD', cursor: 'pointer' }}
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Destination</Typography>,
      dataIndex: 'destination',
      key: 'destination',
      render: (text) => (
        <Typography
          className="small-text"
          underline={true}
          style={{ color: '#1154FD', cursor: 'pointer' }}
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Status</Typography>,
      dataIndex: 'status',
      render: (text) => <StatusTag text={text} />,
    },
    {
      title: '',
      key: 'action',
      render: renderActionButtons,
    },
  ];
}

export const dataSource = [
  {
    key: '1',
    company: 'Epik Company Dummy',
    description: 'Quarterly financial report.',
    user: '<EMAIL>',
    pageCount: '9',
    dateTime: '09/04/2020 10:30 AM',
    cid: '823018',
    destination: '739402',
    status: 'Delivered',
  },
  {
    key: '2',
    company: 'Epik Solutions',
    description: 'Project documentation upload.',
    user: '<EMAIL>',
    pageCount: '12',
    dateTime: '09/05/2020 11:00 AM',
    cid: '823019',
    destination: '739403',
    status: 'Failed',
  },
  {
    key: '3',
    company: 'Epik Group',
    description: 'Client contract submission.',
    user: '<EMAIL>',
    pageCount: '6',
    dateTime: '09/06/2020 02:15 PM',
    cid: '823020',
    destination: '739404',
    status: 'Delivered',
  },
  {
    key: '4',
    company: 'Epik Telecom',
    description: 'Call detail report export.',
    user: '<EMAIL>',
    pageCount: '15',
    dateTime: '09/07/2020 09:45 AM',
    cid: '823021',
    destination: '739405',
    status: 'Failed',
  },
  {
    key: '5',
    company: 'Epik Digital',
    description: 'Support ticket log.',
    user: '<EMAIL>',
    pageCount: '8',
    dateTime: '09/08/2020 01:30 PM',
    cid: '823022',
    destination: '739406',
    status: 'Delivered',
  },
  {
    key: '6',
    company: 'Epik Networks',
    description: 'System upgrade checklist.',
    user: '<EMAIL>',
    pageCount: '4',
    dateTime: '09/09/2020 11:20 AM',
    cid: '823023',
    destination: '739407',
    status: 'Failed',
  },
  {
    key: '7',
    company: 'Epik Services',
    description: 'Marketing materials.',
    user: '<EMAIL>',
    pageCount: '10',
    dateTime: '09/10/2020 03:10 PM',
    cid: '823024',
    destination: '739408',
    status: 'Delivered',
  },
  {
    key: '8',
    company: 'Epik Company Dummy',
    description: 'Fax of signed documents.',
    user: '<EMAIL>',
    pageCount: '5',
    dateTime: '09/11/2020 12:40 PM',
    cid: '823025',
    destination: '739409',
    status: 'Delivered',
  },
  {
    key: '9',
    company: 'Epik Tech',
    description: 'Internal meeting notes.',
    user: '<EMAIL>',
    pageCount: '7',
    dateTime: '09/12/2020 04:25 PM',
    cid: '823026',
    destination: '739410',
    status: 'Failed',
  },
  {
    key: '10',
    company: 'Epik Company Dummy',
    description: 'Legal document fax.',
    user: '<EMAIL>',
    pageCount: '11',
    dateTime: '09/13/2020 09:00 AM',
    cid: '823027',
    destination: '739411',
    status: 'Delivered',
  },
  {
    key: '11',
    company: 'Epik Cloud',
    description: 'Service invoice.',
    user: '<EMAIL>',
    pageCount: '3',
    dateTime: '09/14/2020 10:30 AM',
    cid: '823028',
    destination: '739412',
    status: 'Failed',
  },
  {
    key: '12',
    company: 'Epik Secure',
    description: 'Security alert summary.',
    user: '<EMAIL>',
    pageCount: '6',
    dateTime: '09/15/2020 02:00 PM',
    cid: '823029',
    destination: '739413',
    status: 'Delivered',
  },
  {
    key: '13',
    company: 'Epik Systems',
    description: 'IT escalation case.',
    user: '<EMAIL>',
    pageCount: '9',
    dateTime: '09/16/2020 12:15 PM',
    cid: '823030',
    destination: '739414',
    status: 'Failed',
  },
  {
    key: '14',
    company: 'Epik Connect',
    description: 'Customer inquiry summary.',
    user: '<EMAIL>',
    pageCount: '2',
    dateTime: '09/17/2020 03:45 PM',
    cid: '823031',
    destination: '739415',
    status: 'Delivered',
  },
  {
    key: '15',
    company: 'Epik Voice',
    description: 'Team feedback collection.',
    user: '<EMAIL>',
    pageCount: '10',
    dateTime: '09/18/2020 11:00 AM',
    cid: '823032',
    destination: '739416',
    status: 'Delivered',
  },
  {
    key: '16',
    company: 'Epik Flow',
    description: 'Change request documentation.',
    user: '<EMAIL>',
    pageCount: '6',
    dateTime: '09/19/2020 01:10 PM',
    cid: '823033',
    destination: '739417',
    status: 'Failed',
  },
  {
    key: '17',
    company: 'Epik Telecom',
    description: 'Client proposal package.',
    user: '<EMAIL>',
    pageCount: '13',
    dateTime: '09/20/2020 10:45 AM',
    cid: '823034',
    destination: '739418',
    status: 'Delivered',
  },
  {
    key: '18',
    company: 'Epik Logic',
    description: 'Daily audit logs.',
    user: '<EMAIL>',
    pageCount: '5',
    dateTime: '09/21/2020 09:15 AM',
    cid: '823035',
    destination: '739419',
    status: 'Delivered',
  },
  {
    key: '19',
    company: 'Epik Company Dummy',
    description: 'Partner onboarding form.',
    user: '<EMAIL>',
    pageCount: '4',
    dateTime: '09/22/2020 03:00 PM',
    cid: '823036',
    destination: '739420',
    status: 'Failed',
  },
  {
    key: '20',
    company: 'Epik Ventures',
    description: 'Weekly project update.',
    user: '<EMAIL>',
    pageCount: '7',
    dateTime: '09/23/2020 02:20 PM',
    cid: '823037',
    destination: '739421',
    status: 'Delivered',
  },
  {
    key: '21',
    company: 'Epik Company Dummy',
    description: 'Case summary notes.',
    user: '<EMAIL>',
    pageCount: '3',
    dateTime: '09/24/2020 01:00 PM',
    cid: '823038',
    destination: '739422',
    status: 'Failed',
  },
  {
    key: '22',
    company: 'Epik Systems',
    description: 'Monthly usage report.',
    user: '<EMAIL>',
    pageCount: '11',
    dateTime: '09/25/2020 04:30 PM',
    cid: '823039',
    destination: '739423',
    status: 'Delivered',
  },
  {
    key: '23',
    company: 'Epik Comms',
    description: 'Release checklist.',
    user: '<EMAIL>',
    pageCount: '5',
    dateTime: '09/26/2020 09:40 AM',
    cid: '823040',
    destination: '739424',
    status: 'Failed',
  },
  {
    key: '24',
    company: 'Epik Team',
    description: 'Onboarding welcome kit.',
    user: '<EMAIL>',
    pageCount: '8',
    dateTime: '09/27/2020 11:20 AM',
    cid: '823041',
    destination: '739425',
    status: 'Delivered',
  },
  {
    key: '25',
    company: 'Epik Bridge',
    description: 'Incident response record.',
    user: '<EMAIL>',
    pageCount: '6',
    dateTime: '09/28/2020 01:50 PM',
    cid: '823042',
    destination: '739426',
    status: 'Delivered',
  },
  {
    key: '26',
    company: 'Epik Cloud',
    description: 'IT policy update.',
    user: '<EMAIL>',
    pageCount: '9',
    dateTime: '09/29/2020 10:15 AM',
    cid: '823043',
    destination: '739427',
    status: 'Failed',
  },
  {
    key: '27',
    company: 'Epik Logic',
    description: 'Network test summary.',
    user: '<EMAIL>',
    pageCount: '7',
    dateTime: '09/30/2020 03:05 PM',
    cid: '823044',
    destination: '739428',
    status: 'Delivered',
  },
  {
    key: '28',
    company: 'Epik Com',
    description: 'Employee leave forms.',
    user: '<EMAIL>',
    pageCount: '5',
    dateTime: '10/01/2020 11:55 AM',
    cid: '823045',
    destination: '739429',
    status: 'Failed',
  },
  {
    key: '29',
    company: 'Epik Company Dummy',
    description: 'Job application packet.',
    user: '<EMAIL>',
    pageCount: '4',
    dateTime: '10/02/2020 09:25 AM',
    cid: '823046',
    destination: '739430',
    status: 'Delivered',
  },
  {
    key: '30',
    company: 'Epik HR',
    description: 'Internal HR update.',
    user: '<EMAIL>',
    pageCount: '6',
    dateTime: '10/03/2020 10:50 AM',
    cid: '823047',
    destination: '739431',
    status: 'Failed',
  },
];

export const dataSourceCallActivity = [
  {
    key: '1',
    dateAdded: '09/01/2020',
    totalFaxes: '15',
    success: '6',
    failure: '9',
  },
  {
    key: '2',
    dateAdded: '09/01/2020',
    totalFaxes: '15',
    success: '6',
    failure: '9',
  },
  {
    key: '3',
    dateAdded: '09/01/2020',
    totalFaxes: '15',
    success: '6',
    failure: '9',
  },
  {
    key: '4',
    dateAdded: '09/01/2020',
    totalFaxes: '15',
    success: '6',
    failure: '9',
  },
];

export const faxSuccessFailData = [
  { faxType: 'delivered', date: 'Jul 1', value: 19.1 },
  { faxType: 'fail', date: 'Jul 1', value: 36.1 },
  { faxType: 'pending', date: 'Jul 1', value: 35.3 },
  { faxType: 'delivered', date: 'Jul 2', value: 37.2 },
  { faxType: 'fail', date: 'Jul 2', value: 46.5 },
  { faxType: 'pending', date: 'Jul 2', value: 47.4 },
  { faxType: 'delivered', date: 'Jul 3', value: 31.2 },
  { faxType: 'fail', date: 'Jul 3', value: 46.0 },
  { faxType: 'pending', date: 'Jul 3', value: 25.2 },
  { faxType: 'delivered', date: 'Jul 4', value: 24.7 },
  { faxType: 'fail', date: 'Jul 4', value: 44.3 },
  { faxType: 'pending', date: 'Jul 4', value: 36.3 },
  { faxType: 'delivered', date: 'Jul 5', value: 40.0 },
  { faxType: 'fail', date: 'Jul 5', value: 21.5 },
  { faxType: 'pending', date: 'Jul 5', value: 38.2 },
  { faxType: 'delivered', date: 'Jul 6', value: 43.4 },
  { faxType: 'fail', date: 'Jul 6', value: 17.8 },
  { faxType: 'pending', date: 'Jul 6', value: 33.5 },
  { faxType: 'delivered', date: 'Jul 7', value: 33.8 },
  { faxType: 'fail', date: 'Jul 7', value: 25.0 },
  { faxType: 'pending', date: 'Jul 7', value: 19.9 },
  { faxType: 'delivered', date: 'Jul 8', value: 11.2 },
  { faxType: 'fail', date: 'Jul 8', value: 22.4 },
  { faxType: 'pending', date: 'Jul 8', value: 23.0 },
  { faxType: 'delivered', date: 'Jul 9', value: 14.1 },
  { faxType: 'fail', date: 'Jul 9', value: 27.6 },
  { faxType: 'pending', date: 'Jul 9', value: 41.3 },
  { faxType: 'delivered', date: 'Jul 10', value: 10.2 },
  { faxType: 'fail', date: 'Jul 10', value: 25.4 },
  { faxType: 'pending', date: 'Jul 10', value: 43.3 },
  { faxType: 'delivered', date: 'Jul 11', value: 22.8 },
  { faxType: 'fail', date: 'Jul 11', value: 28.4 },
  { faxType: 'pending', date: 'Jul 11', value: 44.9 },
  { faxType: 'delivered', date: 'Jul 12', value: 21.3 },
  { faxType: 'fail', date: 'Jul 12', value: 23.0 },
  { faxType: 'pending', date: 'Jul 12', value: 17.0 },
  { faxType: 'delivered', date: 'Jul 13', value: 44.8 },
  { faxType: 'fail', date: 'Jul 13', value: 39.7 },
  { faxType: 'pending', date: 'Jul 13', value: 40.7 },
  { faxType: 'delivered', date: 'Jul 14', value: 33.6 },
  { faxType: 'fail', date: 'Jul 14', value: 17.8 },
  { faxType: 'pending', date: 'Jul 14', value: 39.3 },
  { faxType: 'delivered', date: 'Jul 15', value: 35.8 },
  { faxType: 'fail', date: 'Jul 15', value: 19.3 },
  { faxType: 'pending', date: 'Jul 15', value: 46.0 },
  { faxType: 'delivered', date: 'Jul 16', value: 27.6 },
  { faxType: 'fail', date: 'Jul 16', value: 22.2 },
  { faxType: 'pending', date: 'Jul 16', value: 12.9 },
  { faxType: 'delivered', date: 'Jul 17', value: 17.0 },
  { faxType: 'fail', date: 'Jul 17', value: 45.3 },
  { faxType: 'pending', date: 'Jul 17', value: 48.3 },
  { faxType: 'delivered', date: 'Jul 18', value: 49.3 },
  { faxType: 'fail', date: 'Jul 18', value: 14.6 },
  { faxType: 'pending', date: 'Jul 18', value: 48.2 },
  { faxType: 'delivered', date: 'Jul 19', value: 14.2 },
  { faxType: 'fail', date: 'Jul 19', value: 45.4 },
  { faxType: 'pending', date: 'Jul 19', value: 26.4 },
  { faxType: 'delivered', date: 'Jul 20', value: 43.2 },
  { faxType: 'fail', date: 'Jul 20', value: 13.6 },
  { faxType: 'pending', date: 'Jul 20', value: 15.7 },
  { faxType: 'delivered', date: 'Jul 21', value: 20.8 },
  { faxType: 'fail', date: 'Jul 21', value: 20.2 },
  { faxType: 'pending', date: 'Jul 21', value: 32.1 },
];

export function getColumnsCallActivity() {
  return [
    {
      title: (
        <Typography className="small-text" style={{ paddingLeft: '8px' }}>
          Date
        </Typography>
      ),
      dataIndex: 'dateAdded',
      key: 'dateAdded',
      render: (text) => <span className="small-text">{text}</span>,
      width: 90,
    },
    {
      title: (
        <Typography className="small-text" style={{ paddingLeft: '8px' }}>
          Total Faxes
        </Typography>
      ),
      dataIndex: 'totalFaxes',
      key: 'totalFaxes',
      render: (text) => <span className="small-text">{text}</span>,
      width: 90,
    },
    {
      title: (
        <Typography className="small-text" style={{ paddingLeft: '8px' }}>
          Success
        </Typography>
      ),
      dataIndex: 'success',
      key: 'success',
      render: (text) => <span className="small-text">{text}</span>,
      width: 80,
    },
    {
      title: (
        <Typography className="small-text" style={{ paddingLeft: '8px' }}>
          Failure
        </Typography>
      ),
      dataIndex: 'failure',
      key: 'failure',
      render: (text) => <span className="small-text">{text}</span>,
      width: 70,
    },
  ];
}
