import { useEffect, useState } from 'react';
import { Row, Col } from 'antd';
import { Column } from '@ant-design/plots';
import { Flex, PieChartCard, Typography } from '@/components';
import {
  dataSourceCallActivity,
  faxSuccessFailData,
  getColumnsCallActivity,
} from '../constant';
import CallActivity from '../CallActivity';

const pieData = [
  { faxes: 'Failure Faxes', 'Processed Fax': 0.45 },
  { faxes: 'Delivered Faxes', 'Processed Fax': 0.65 },
];

const pieData2 = [
  { faxes: 'Sending Faxes', failPass: 0.45 },
  { faxes: 'Receiving Faxes', failPass: 0.65 },
];

const faxSuccessFailConfig = {
  data: faxSuccessFailData,
  xField: 'date',
  yField: 'value',
  colorField: 'faxType',
  group: true,
  legend: false,
  scale: { color: { palette: ['#458CBE', '#C8E0F1', '#2D9CDB'] } },
  style: { inset: 0, maxWidth: 200 },
};

const deliveredFailedConfig = {
  angleField: 'Processed Fax',
  colorField: 'faxes',
  colors: ['#5EB1EB', '#4D9CD3'],
  legend: false,
  radius: 0.9,
  label: {
    text: (d) => `${d.faxes}`,
    fontSize: 8,
    position: 'spider',
  },
};

const sendingReceivedConfig = {
  angleField: 'failPass',
  colorField: 'faxes',
  colors: ['#5EB1EB', '#336B92'],
  legend: false,
  radius: 0.9,
  label: {
    text: (d) => `${d.faxes}`,
    fill: '#000',
    fontSize: 8,
    position: 'spider',
  },
};

const LegacyFaxStats = () => {
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState([]);

  useEffect(() => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      setData(dataSourceCallActivity);
    }, 0);
  }, []);

  const columns = getColumnsCallActivity({
    onNameClick: (record, index) => console.log('Row clicked:', record, index),
    onMenuClick: (key) => console.log(key),
  });

  return (
    <div style={{ margin: 12 }}>
      <Row gutter={[8, 16]}>
        <Col xs={24} sm={12} lg={8} align="center" justifyContent="center">
          <PieChartCard
            title="Processed Fax"
            label1={'Delivered Faxes'}
            label2={'Failure Faxes'}
            pieData={pieData}
            config={deliveredFailedConfig}
          />
        </Col>
        <Col xs={24} sm={12} lg={8} align="center" justifyContent="center">
          <PieChartCard
            title="Pending Fax"
            label1={'Sending Faxes'}
            label2={'Receiving Faxes'}
            pieData={pieData2}
            config={sendingReceivedConfig}
          />
        </Col>
        <Col xs={24} sm={12} lg={8} align="center" justifyContent="center">
          <CallActivity data={data} columns={columns} loading={loading} />
        </Col>
      </Row>

      <Row style={{ marginTop: 16 }}>
        <Col span={24}>
          <div
            style={{
              border: '1px solid var(--border-grey)',
              borderRadius: '8px',
              paddingBottom: 16,
            }}
          >
            <Typography
              className="heading-four-bold"
              style={{ padding: '6px 16px' }}
            >
              Success/Failure Chart
            </Typography>
            <Flex
              style={{
                padding: '4px',
                height: 200,
              }}
            >
              <Column {...faxSuccessFailConfig} />
            </Flex>
          </div>
        </Col>
      </Row>
    </div>
  );
};

export default LegacyFaxStats;
