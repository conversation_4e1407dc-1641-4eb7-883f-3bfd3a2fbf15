import { useState } from 'react';
import {
  AdvanceSearch,
  AnimatedTab,
  Button,
  DeleteWithSecurityCode,
  Flex,
  Input,
  PageTitle,
  SearchIcon,
} from '@/components';
import { useStore } from '@/store';
import OutgoingLegacyFaxesTable from './OutgoingLegacyFaxesTable';
import IncommingLagacyFaxesTable from './IncommingLegacyFaxesTable';
import LegacyFaxStats from './LegacyFaxStats';
import NewFax from './NewFax';

const LegacyFax = () => {
  const [legacyFaxActionModal, setLegacyFaxActionModal] = useState('');
  const [legacyFaxTab, setLegacyFaxTab] = useState('Outgoing Faxes');
  const [showAdvanceSearch, setShowAdvanceSearch] = useState(false);
  const { openDrawer } = useStore((state) => state.drawer);

  const modalConfig = {
    deleteLegacyFax: {
      component: DeleteWithSecurityCode,
      props: {
        onCancel: () => setLegacyFaxActionModal(''),
        handleOk: () => setLegacyFaxActionModal(''),
      },
      condition: legacyFaxActionModal === 'deleteLegacyFax',
    },
    advanceSearch: {
      component: AdvanceSearch,
      props: {
        onCancel: () => setShowAdvanceSearch(false),
        handleOk: () => setShowAdvanceSearch(false),
      },
      condition: showAdvanceSearch,
    },
  };

  const drawerConfig = {
    addLegacyFax: () => openDrawer(NewFax, {}),
  };

  const handleActions = (key) => {
    switch (key) {
      case 'delete':
        return setLegacyFaxActionModal('deleteLegacyFax');
      default:
        return null;
    }
  };

  const renderContent = () => {
    switch (legacyFaxTab) {
      case 'Outgoing Faxes':
        return <OutgoingLegacyFaxesTable handleAction={handleActions} />;
      case 'Incomming Faxes':
        return <IncommingLagacyFaxesTable handleAction={handleActions} />;
      case 'Relay':
        return <LegacyFaxStats handleAction={handleActions} />;
      case 'Stats':
        return <LegacyFaxStats />;
      default:
        return null;
    }
  };

  const renderModals = () => {
    return Object.entries(modalConfig).map(([key, modal]) => {
      const { component: ModalComponent, props, condition } = modal;
      return condition ? <ModalComponent key={key} {...props} /> : null;
    });
  };

  return (
    <Flex vertical gap={8} style={{ height: '100%' }}>
      <Flex
        align="center"
        wrap
        justify="space-between"
        style={{ padding: '0 12px' }}
      >
        <PageTitle pageTitle={legacyFaxTab} />
        <Flex align="center" wrap gap={6}>
          <Button
            variantColorBgContainer
            onClick={() => drawerConfig.addLegacyFax()}
          >
            New Fax
          </Button>
          <Button
            variant="outlined"
            variantColorBgContainer
            onClick={() => setShowAdvanceSearch(true)}
          >
            Advance Search
          </Button>
          <Input
            prefix={<SearchIcon />}
            placeholder="Search"
            style={{ maxWidth: '165px' }}
          />
        </Flex>
      </Flex>
      <div style={{ padding: '0 12px' }} className="custom-scrollbar">
        <Flex
          vertical
          flex={1}
          style={{
            background: 'white',
            borderRadius: '12px',
            boxShadow: '0px 1px 2px 0px #1018280F, 0px 1px 3px 0px #1018281A',
            overflow: 'hidden',
          }}
        >
          <Flex
            align="center"
            justify="space-between"
            style={{ padding: '12px' }}
          >
            <Flex align="center">
              <AnimatedTab
                options={[
                  'Outgoing Faxes',
                  'Incomming Faxes',
                  'Relay',
                  'Stats',
                ]}
                value={legacyFaxTab}
                onChange={(value) => {
                  setLegacyFaxTab(value);
                }}
                size="default"
              />
            </Flex>
            <Flex align="center"></Flex>
          </Flex>
          {renderContent()}
          {renderModals()}
        </Flex>
      </div>
    </Flex>
  );
};

export default LegacyFax;
