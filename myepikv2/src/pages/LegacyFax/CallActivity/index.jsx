import PropTypes from 'prop-types';
import { DataTable, Flex, Typography } from '@/components';

const CallActivity = ({ columns, data, loading }) => (
  <div
    style={{
      border: '1px solid var(--border-grey)',
      borderRadius: '8px',
      marginBottom: 16,
      height: '100%',
    }}
  >
    <Flex vertical>
      <Flex vertical>
        <Typography
          className="heading-four-bold"
          style={{ padding: '6px 16px' }}
        >
          24 Hours Call Activity
        </Typography>
      </Flex>
      <DataTable
        columns={columns}
        data={data}
        loading={loading}
        paginationBtnOutside={true}
        customPaginationClass={'custom-pagination'}
        headerClass="device-table-header-row"
        isPagination={false}
      />
    </Flex>
  </div>
);

CallActivity.propTypes = {
  columns: PropTypes.array.isRequired,
  data: PropTypes.object.isRequired,
  loading: PropTypes.bool.isRequired,
};

export default CallActivity;
