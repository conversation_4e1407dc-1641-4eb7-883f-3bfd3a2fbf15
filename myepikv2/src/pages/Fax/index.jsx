import { useState } from 'react';
import {
  AdvanceSearch,
  AnimatedTab,
  Button,
  DeleteModal,
  Input,
  SearchIcon,
  Flex,
  PageTitle,
} from '@/components';
import { useStore } from '@/store';
import NewFax from './NewFax';
import PendingFaxTable from './PendingFaxTable';
import ProcessedFaxTable from './ProcessedFaxTable';
import FaxStats from './FaxStats';

const Fax = () => {
  const [faxActionModal, setFaxActionModal] = useState('');
  const [showAdvanceSearch, setShowAdvanceSearch] = useState(false);
  const [faxTab, setFaxTab] = useState('Pending');
  const { openDrawer } = useStore((state) => state.drawer);

  const modalConfig = {
    deleteFax: {
      component: DeleteModal,
      props: {
        onCancel: () => setFaxActionModal(''),
        handleOk: () => setFaxActionModal(''),
      },
      condition: faxActionModal === 'deleteFax',
    },
    advanceSearch: {
      component: AdvanceSearch,
      props: {
        onCancel: () => setShowAdvanceSearch(false),
        handleOk: () => setShowAdvanceSearch(false),
      },
      condition: showAdvanceSearch,
    },
  };

  const drawerConfig = {
    addFax: () => openDrawer(NewFax, {}),
  };

  const handleActions = (key) => {
    switch (key) {
      case 'delete':
        return setFaxActionModal('deleteFax');
      default:
        return null;
    }
  };

  const renderContent = () => {
    switch (faxTab) {
      case 'Pending':
        return <PendingFaxTable handleAction={handleActions} />;
      case 'Processed':
        return <ProcessedFaxTable handleAction={handleActions} />;
      case 'Stats':
        return <FaxStats />;
      default:
        return null;
    }
  };

  const renderModals = () => {
    return Object.entries(modalConfig).map(([key, modal]) => {
      const { component: ModalComponent, props, condition } = modal;
      return condition ? <ModalComponent key={key} {...props} /> : null;
    });
  };

  return (
    <Flex vertical gap={8} style={{ height: '100%' }}>
      <Flex
        align="center"
        wrap
        justify="space-between"
        style={{ padding: '0 12px' }}
      >
        <PageTitle pageTitle={faxTab} />
        <Flex align="center" wrap gap={6}>
          <Button variantColorBgContainer onClick={() => drawerConfig.addFax()}>
            New Fax
          </Button>
          <Button
            variant="outlined"
            variantColorBgContainer
            onClick={() => setShowAdvanceSearch(true)}
          >
            Advance Search
          </Button>
          <Input
            prefix={<SearchIcon />}
            placeholder="Search"
            style={{ maxWidth: '165px' }}
          />
        </Flex>
      </Flex>
      <div style={{ padding: '0 12px' }} className="custom-scrollbar">
        <Flex
          vertical
          flex={1}
          style={{
            background: 'white',
            borderRadius: '12px',
            boxShadow: '0px 1px 2px 0px #1018280F, 0px 1px 3px 0px #1018281A',
          }}
        >
          <Flex
            align="center"
            justify="space-between"
            style={{ padding: '12px' }}
          >
            <Flex align="center">
              <AnimatedTab
                options={['Pending', 'Processed', 'Stats']}
                value={faxTab}
                onChange={(value) => {
                  setFaxTab(value);
                }}
                size="default"
              />
            </Flex>
            <Flex align="center"></Flex>
          </Flex>
          {renderContent()}
          {renderModals()}
        </Flex>
      </div>
    </Flex>
  );
};

export default Fax;
