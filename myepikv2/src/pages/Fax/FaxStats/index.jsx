import { useEffect, useState } from 'react';
import { Typography, Divider, Row, Col } from 'antd';
import PropTypes from 'prop-types';
import { Chart, Flex } from '@/components';
import { pieConfig } from '@/components/common/Charts/chartConfigs';
import { Column } from '@ant-design/plots';
import {
  dataSourceCallActivity,
  faxSuccessFailData,
  getColumnsCallActivity,
} from '../constant';
import CallActivity from '../CallActivity';

const pieData = [
  { faxes: 'Failure Faxes', 'Pending Fax': 0.45 },
  { faxes: 'Delivered Faxes', 'Pending Fax': 0.65 },
];

const faxSuccessFailDataConfig = {
  data: faxSuccessFailData.slice(0, 42),
  xField: 'date',
  yField: 'value',
  colorField: 'faxType',
  group: true,
  legend: false,
  scale: { color: { palette: ['#458CBE', '#C8E0F1', '#2D9CDB'] } },
  style: { inset: 0 },
};

const config = {
  angleField: 'Pending Fax',
  colorField: 'faxes',
  colors: ['#5EB1EB', '#4D9CD3'],
  legend: false,
  radius: 0.8,
  label: {
    text: (d) => `${d.faxes}`,
    fill: '#000',
    fontSize: 8,
    position: 'spider',
  },
};

const Legend = ({ label, color }) => (
  <Flex gap={4} align="center">
    <div
      style={{
        width: 10,
        height: 10,
        backgroundColor: color,
        borderRadius: '50%',
        marginRight: 2,
      }}
    />
    <Typography className="extra-small-text">{label}</Typography>
  </Flex>
);

Legend.propTypes = {
  label: PropTypes.string.isRequired,
  color: PropTypes.string.isRequired,
};

const ChartCard = ({ title, pieData, config, label1, label2 }) => (
  <Col xs={24} sm={11} lg={11}>
    <Flex
      style={{
        border: '1px solid var(--border-grey)',
        borderRadius: '8px',
        marginBottom: 16,
        height: '100%',
      }}
      vertical
    >
      <Typography className="heading-four-bold" style={{ padding: '6px 16px' }}>
        {title}
      </Typography>
      <Divider style={{ margin: 0, borderColor: 'var(--border-grey)' }} />
      <Flex gap={8}>
        <Flex
          style={{ height: 250, width: '100%' }}
          justifyContent="center"
          align="center"
          flex={3}
        >
          <Chart type="Pie" data={pieData} config={pieConfig({ ...config })} />
        </Flex>
        <Flex gap={8} flex={1} vertical justify="center">
          <Flex
            style={{
              backgroundColor: 'var(--border-grey)',
              padding: '10px 8px',
            }}
          >
            <Typography className="heading-four-bold">755</Typography>
          </Flex>
          <Flex gap={2} vertical>
            <Legend label={label1} color="var(--granite-blue)" />
            <Legend label={label2} color="var(--button-light-blue-1)" />
          </Flex>
        </Flex>
      </Flex>
    </Flex>
  </Col>
);

ChartCard.propTypes = {
  title: PropTypes.string.isRequired,
  pieData: PropTypes.object.isRequired,
  config: PropTypes.object.isRequired,
  label1: PropTypes.string.isRequired,
  label2: PropTypes.string.isRequired,
};

const FaxStats = () => {
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState([]);

  useEffect(() => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      setData(dataSourceCallActivity);
    }, 0);
  }, []);

  const columns = getColumnsCallActivity({
    onNameClick: (record, index) => console.log('Row clicked:', record, index),
    onMenuClick: (key) => console.log(key),
  });

  return (
    <div style={{ margin: 12 }}>
      <Row gutter={[8, 16]}>
        <ChartCard
          title="Processed Fax"
          label1={'Delivered Faxes'}
          label2={'Failure Faxes'}
          pieData={pieData}
          config={config}
        />
        <Col xs={24} sm={13} lg={13} align="center" justifyContent="center">
          <Flex
            vertical
            style={{
              border: '1px solid var(--border-grey)',
              borderRadius: '8px',
              height: '100%',
            }}
          >
            <Typography
              className="heading-four-bold"
              style={{ padding: '6px 16px' }}
            >
              Success/Failure Chart
            </Typography>
            <Divider style={{ margin: 0, borderColor: 'var(--border-grey)' }} />
            <Flex
              style={{
                height: 300,
              }}
            >
              <Column {...faxSuccessFailDataConfig} />
            </Flex>
          </Flex>
        </Col>
      </Row>

      <Row style={{ marginTop: 16 }}>
        <Col xs={24} sm={24} lg={24} align="center" justifyContent="center">
          <CallActivity data={data} columns={columns} loading={loading} />
        </Col>
      </Row>
    </div>
  );
};

export default FaxStats;
