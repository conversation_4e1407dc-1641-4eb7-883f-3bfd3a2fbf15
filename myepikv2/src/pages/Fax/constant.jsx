import {
  Typography,
  Flex,
  Button,
  StarIcon,
  DropDown,
  MoreHorizontalIcon,
  TrashIcon,
  Tooltip,
} from '@/components';

export function getColumns({ onMenuClick }) {
  const handleMenuClick = (e, key) => {
    console.log('handleMenuClick', e, e?.target?.value, key);
    onMenuClick?.(e?.key);
    e?.domEvent?.stopPropagation();
  };

  const menuItems = [{ key: 'delete', label: 'Delete', icon: <TrashIcon /> }];

  const renderActionButtons = () => (
    <Flex
      align="center"
      justify="flex-end"
      style={{ height: '16px' }}
      onClick={(e) => e?.stopPropagation()}
    >
      <Button
        color="default"
        variant="outline"
        icon={<StarIcon width={16} height={16} />}
        style={{
          padding: '0px',
          marginRight: '12px',
          width: '16px',
          height: 'inherit',
        }}
      />
      <DropDown
        menuItems={menuItems}
        onClick={(e) => handleMenuClick(e)}
        trigger={['click']}
        dropDownStyles={{ height: 'inherit', display: 'flex' }}
        buttonLabel=""
        buttonProps={{
          onlyIcon: true,
          icon: <MoreHorizontalIcon width={16} height={16} />,
          type: 'default',
          variant: 'filled',
          style: { height: 'inherit', padding: 0 },
        }}
      />
    </Flex>
  );
  return [
    {
      title: (
        <Typography className="small-text" style={{ marginLeft: '8px' }}>
          Sender Company
        </Typography>
      ),
      dataIndex: 'senderCompany',
      key: 'senderCompany',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text-bold"
        >
          {text}
        </Typography>
      ),
      width: 150,
    },
    {
      title: (
        <Typography className="small-text" style={{ paddingLeft: '8px' }}>
          Receiver Company
        </Typography>
      ),
      dataIndex: 'receiverCompany',
      key: 'receiverCompany',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
      width: 150,
    },
    {
      title: (
        <Typography className="small-text" style={{ paddingLeft: '8px' }}>
          Sender Device #
        </Typography>
      ),
      dataIndex: 'senderDeviceSN',
      key: 'senderDeviceSN',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
      width: 150,
    },
    {
      title: (
        <Typography className="small-text" style={{ paddingLeft: '8px' }}>
          Receiver Device #
        </Typography>
      ),
      dataIndex: 'receiverDeviceSN',
      key: 'receiverDeviceSN',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
      width: 150,
    },
    {
      title: (
        <Typography className="small-text" style={{ paddingLeft: '8px' }}>
          Sender #
        </Typography>
      ),
      dataIndex: 'senderNo',
      key: 'senderNo',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
      width: 100,
    },
    {
      title: (
        <Typography className="small-text" style={{ paddingLeft: '8px' }}>
          Receiver #
        </Typography>
      ),
      dataIndex: 'receiveNo',
      key: 'receiveNo',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
      width: 100,
    },
    {
      title: (
        <Typography className="small-text" style={{ paddingLeft: '8px' }}>
          Date/Time
        </Typography>
      ),
      dataIndex: 'dateTime',
      key: 'dateTime',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
      width: 100,
    },
    {
      title: (
        <Typography className="small-text" style={{ paddingLeft: '8px' }}>
          Last Update
        </Typography>
      ),
      dataIndex: 'lastUpdate',
      key: 'lastUpdate',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
      width: 100,
    },
    {
      title: (
        <Typography className="small-text" style={{ paddingLeft: '8px' }}>
          Description
        </Typography>
      ),
      dataIndex: 'description',
      key: 'description',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
      width: 100,
    },
    {
      title: '',
      key: 'action',
      render: renderActionButtons,
      width: 70,
    },
  ];
}

export const dataSource = [
  {
    key: '1',
    senderCompany: 'Epik Networks',
    receiverCompany: 'Epik Digital',
    senderDeviceSN: '2024233301',
    receiverDeviceSN: '2024233311',
    senderNo: '2024233301',
    receiveNo: '2024233311',
    dateTime: '09/01/2020',
    lastUpdate: '06/06/2020 01:00 PM',
    description: 'ERR Timeout',
  },
  {
    key: '2',
    senderCompany: 'Epik Telecom',
    receiverCompany: 'Epik Cloud',
    senderDeviceSN: '2024233302',
    receiverDeviceSN: '2024233312',
    senderNo: '2024233302',
    receiveNo: '2024233312',
    dateTime: '09/02/2020',
    lastUpdate: '06/06/2020 01:30 PM',
    description: 'ERR Destination',
  },
  {
    key: '3',
    senderCompany: 'Epik Voice',
    receiverCompany: 'Epik Logic',
    senderDeviceSN: '2024233303',
    receiverDeviceSN: '2024233313',
    senderNo: '2024233303',
    receiveNo: '2024233313',
    dateTime: '09/03/2020',
    lastUpdate: '06/06/2020 02:00 PM',
    description: 'ERR Not Found',
  },
  {
    key: '4',
    senderCompany: 'Epik Connect',
    receiverCompany: 'Epik Switch',
    senderDeviceSN: '2024233304',
    receiverDeviceSN: '2024233314',
    senderNo: '2024233304',
    receiveNo: '2024233314',
    dateTime: '09/04/2020',
    lastUpdate: '06/06/2020 02:30 PM',
    description: 'ERR Device Busy',
  },
  {
    key: '5',
    senderCompany: 'Epik Company Dummy',
    receiverCompany: 'Epik Company Dummy',
    senderDeviceSN: '2024233305',
    receiverDeviceSN: '2024233315',
    senderNo: '2024233305',
    receiveNo: '2024233315',
    dateTime: '09/05/2020',
    lastUpdate: '06/06/2020 03:00 PM',
    description: 'ERR Destination',
  },
  {
    key: '6',
    senderCompany: 'Epik Labs',
    receiverCompany: 'Epik Flow',
    senderDeviceSN: '2024233306',
    receiverDeviceSN: '2024233316',
    senderNo: '2024233306',
    receiveNo: '2024233316',
    dateTime: '09/06/2020',
    lastUpdate: '06/06/2020 03:30 PM',
    description: 'ERR Timeout',
  },
  {
    key: '7',
    senderCompany: 'Epik Edge',
    receiverCompany: 'Epik Systems',
    senderDeviceSN: '2024233307',
    receiverDeviceSN: '2024233317',
    senderNo: '2024233307',
    receiveNo: '2024233317',
    dateTime: '09/07/2020',
    lastUpdate: '06/06/2020 04:00 PM',
    description: 'ERR Timeout',
  },
  {
    key: '8',
    senderCompany: 'Epik Connect',
    receiverCompany: 'Epik Network',
    senderDeviceSN: '2024233308',
    receiverDeviceSN: '2024233318',
    senderNo: '2024233308',
    receiveNo: '2024233318',
    dateTime: '09/08/2020',
    lastUpdate: '06/06/2020 04:30 PM',
    description: 'ERR Destination',
  },
  {
    key: '9',
    senderCompany: 'Epik Flow',
    receiverCompany: 'Epik Secure',
    senderDeviceSN: '2024233309',
    receiverDeviceSN: '2024233319',
    senderNo: '2024233309',
    receiveNo: '2024233319',
    dateTime: '09/09/2020',
    lastUpdate: '06/06/2020 05:00 PM',
    description: 'ERR Disconnected',
  },
  {
    key: '10',
    senderCompany: 'Epik Cloud',
    receiverCompany: 'Epik Voice',
    senderDeviceSN: '2024233310',
    receiverDeviceSN: '2024233320',
    senderNo: '2024233310',
    receiveNo: '2024233320',
    dateTime: '09/10/2020',
    lastUpdate: '06/06/2020 05:30 PM',
    description: 'ERR Not Found',
  },
  {
    key: '11',
    senderCompany: 'Epik Secure',
    receiverCompany: 'Epik Digital',
    senderDeviceSN: '2024233311',
    receiverDeviceSN: '2024233321',
    senderNo: '2024233311',
    receiveNo: '2024233321',
    dateTime: '09/11/2020',
    lastUpdate: '06/06/2020 06:00 PM',
    description: 'ERR Device Busy',
  },
  {
    key: '12',
    senderCompany: 'Epik Services',
    receiverCompany: 'Epik Networks',
    senderDeviceSN: '2024233312',
    receiverDeviceSN: '2024233322',
    senderNo: '2024233312',
    receiveNo: '2024233322',
    dateTime: '09/12/2020',
    lastUpdate: '06/06/2020 06:30 PM',
    description: 'ERR Timeout',
  },
  {
    key: '13',
    senderCompany: 'Epik Tel',
    receiverCompany: 'Epik HR',
    senderDeviceSN: '2024233313',
    receiverDeviceSN: '2024233323',
    senderNo: '2024233313',
    receiveNo: '2024233323',
    dateTime: '09/13/2020',
    lastUpdate: '06/06/2020 07:00 PM',
    description: 'ERR Rejected',
  },
  {
    key: '14',
    senderCompany: 'Epik IT',
    receiverCompany: 'Epik Admin',
    senderDeviceSN: '2024233314',
    receiverDeviceSN: '2024233324',
    senderNo: '2024233314',
    receiveNo: '2024233324',
    dateTime: '09/14/2020',
    lastUpdate: '06/06/2020 07:30 PM',
    description: 'ERR Unknown Host',
  },
  {
    key: '15',
    senderCompany: 'Epik HR',
    receiverCompany: 'Epik Labs',
    senderDeviceSN: '2024233315',
    receiverDeviceSN: '2024233325',
    senderNo: '2024233315',
    receiveNo: '2024233325',
    dateTime: '09/15/2020',
    lastUpdate: '06/06/2020 08:00 PM',
    description: 'ERR Destination',
  },
  {
    key: '16',
    senderCompany: 'Epik One',
    receiverCompany: 'Epik Two',
    senderDeviceSN: '2024233316',
    receiverDeviceSN: '2024233326',
    senderNo: '2024233316',
    receiveNo: '2024233326',
    dateTime: '09/16/2020',
    lastUpdate: '06/06/2020 08:30 PM',
    description: 'ERR Rejected',
  },
  {
    key: '17',
    senderCompany: 'Epik Systems',
    receiverCompany: 'Epik Team',
    senderDeviceSN: '2024233317',
    receiverDeviceSN: '2024233327',
    senderNo: '2024233317',
    receiveNo: '2024233327',
    dateTime: '09/17/2020',
    lastUpdate: '06/06/2020 09:00 PM',
    description: 'ERR Timeout',
  },
  {
    key: '18',
    senderCompany: 'Epik Company A',
    receiverCompany: 'Epik Company B',
    senderDeviceSN: '2024233318',
    receiverDeviceSN: '2024233328',
    senderNo: '2024233318',
    receiveNo: '2024233328',
    dateTime: '09/18/2020',
    lastUpdate: '06/06/2020 09:30 PM',
    description: 'ERR Not Found',
  },
  {
    key: '19',
    senderCompany: 'Epik Test',
    receiverCompany: 'Epik Prod',
    senderDeviceSN: '2024233319',
    receiverDeviceSN: '2024233329',
    senderNo: '2024233319',
    receiveNo: '2024233329',
    dateTime: '09/19/2020',
    lastUpdate: '06/06/2020 10:00 PM',
    description: 'ERR Busy Line',
  },
  {
    key: '20',
    senderCompany: 'Epik A',
    receiverCompany: 'Epik B',
    senderDeviceSN: '2024233320',
    receiverDeviceSN: '2024233330',
    senderNo: '2024233320',
    receiveNo: '2024233330',
    dateTime: '09/20/2020',
    lastUpdate: '06/06/2020 10:30 PM',
    description: 'ERR Destination',
  },
  {
    key: '21',
    senderCompany: 'Epik Cloud',
    receiverCompany: 'Epik Logic',
    senderDeviceSN: '2024233321',
    receiverDeviceSN: '2024233331',
    senderNo: '2024233321',
    receiveNo: '2024233331',
    dateTime: '09/21/2020',
    lastUpdate: '06/06/2020 11:00 PM',
    description: 'ERR Rejected',
  },
  {
    key: '22',
    senderCompany: 'Epik Services',
    receiverCompany: 'Epik Bridge',
    senderDeviceSN: '2024233322',
    receiverDeviceSN: '2024233332',
    senderNo: '2024233322',
    receiveNo: '2024233332',
    dateTime: '09/22/2020',
    lastUpdate: '06/06/2020 11:30 PM',
    description: 'ERR Disconnected',
  },
  {
    key: '23',
    senderCompany: 'Epik Edge',
    receiverCompany: 'Epik Core',
    senderDeviceSN: '2024233323',
    receiverDeviceSN: '2024233333',
    senderNo: '2024233323',
    receiveNo: '2024233333',
    dateTime: '09/23/2020',
    lastUpdate: '06/06/2020 11:45 PM',
    description: 'ERR Timeout',
  },
  {
    key: '24',
    senderCompany: 'Epik Dev',
    receiverCompany: 'Epik Ops',
    senderDeviceSN: '2024233324',
    receiverDeviceSN: '2024233334',
    senderNo: '2024233324',
    receiveNo: '2024233334',
    dateTime: '09/24/2020',
    lastUpdate: '06/07/2020 12:00 AM',
    description: 'ERR Destination',
  },
  {
    key: '25',
    senderCompany: 'Epik Admin',
    receiverCompany: 'Epik User',
    senderDeviceSN: '2024233325',
    receiverDeviceSN: '2024233335',
    senderNo: '2024233325',
    receiveNo: '2024233335',
    dateTime: '09/25/2020',
    lastUpdate: '06/07/2020 12:15 AM',
    description: 'ERR Timeout',
  },
  {
    key: '26',
    senderCompany: 'Epik Internal',
    receiverCompany: 'Epik External',
    senderDeviceSN: '2024233326',
    receiverDeviceSN: '2024233336',
    senderNo: '2024233326',
    receiveNo: '2024233336',
    dateTime: '09/26/2020',
    lastUpdate: '06/07/2020 12:30 AM',
    description: 'ERR Rejected',
  },
  {
    key: '27',
    senderCompany: 'Epik Trial',
    receiverCompany: 'Epik Production',
    senderDeviceSN: '2024233327',
    receiverDeviceSN: '2024233337',
    senderNo: '2024233327',
    receiveNo: '2024233337',
    dateTime: '09/27/2020',
    lastUpdate: '06/07/2020 12:45 AM',
    description: 'ERR Not Found',
  },
  {
    key: '28',
    senderCompany: 'Epik Frontend',
    receiverCompany: 'Epik Backend',
    senderDeviceSN: '2024233328',
    receiverDeviceSN: '2024233338',
    senderNo: '2024233328',
    receiveNo: '2024233338',
    dateTime: '09/28/2020',
    lastUpdate: '06/07/2020 01:00 AM',
    description: 'ERR Device Busy',
  },
  {
    key: '29',
    senderCompany: 'Epik Comms',
    receiverCompany: 'Epik Data',
    senderDeviceSN: '2024233329',
    receiverDeviceSN: '2024233339',
    senderNo: '2024233329',
    receiveNo: '2024233339',
    dateTime: '09/29/2020',
    lastUpdate: '06/07/2020 01:15 AM',
    description: 'ERR Rejected',
  },
  {
    key: '30',
    senderCompany: 'Epik Final',
    receiverCompany: 'Epik Start',
    senderDeviceSN: '2024233330',
    receiverDeviceSN: '2024233340',
    senderNo: '2024233330',
    receiveNo: '2024233340',
    dateTime: '09/30/2020',
    lastUpdate: '06/07/2020 01:30 AM',
    description: 'ERR Timeout',
  },
];

export const dataSourceCallActivity = [
  {
    key: '1',
    dateAdded: '09/01/2020',
    totalFaxes: '15',
    success: '6',
    failure: '9',
  },
  {
    key: '2',
    dateAdded: '09/01/2020',
    totalFaxes: '15',
    success: '6',
    failure: '9',
  },
  {
    key: '3',
    dateAdded: '09/01/2020',
    totalFaxes: '15',
    success: '6',
    failure: '9',
  },
  {
    key: '4',
    dateAdded: '09/01/2020',
    totalFaxes: '15',
    success: '6',
    failure: '9',
  },
];

export const faxSuccessFailData = [
  { faxType: 'delivered', date: 'Jul 1', value: 19.1 },
  { faxType: 'fail', date: 'Jul 1', value: 36.1 },
  { faxType: 'pending', date: 'Jul 1', value: 35.3 },
  { faxType: 'delivered', date: 'Jul 2', value: 37.2 },
  { faxType: 'fail', date: 'Jul 2', value: 46.5 },
  { faxType: 'pending', date: 'Jul 2', value: 47.4 },
  { faxType: 'delivered', date: 'Jul 3', value: 31.2 },
  { faxType: 'fail', date: 'Jul 3', value: 46.0 },
  { faxType: 'pending', date: 'Jul 3', value: 25.2 },
  { faxType: 'delivered', date: 'Jul 4', value: 24.7 },
  { faxType: 'fail', date: 'Jul 4', value: 44.3 },
  { faxType: 'pending', date: 'Jul 4', value: 36.3 },
  { faxType: 'delivered', date: 'Jul 5', value: 40.0 },
  { faxType: 'fail', date: 'Jul 5', value: 21.5 },
  { faxType: 'pending', date: 'Jul 5', value: 38.2 },
  { faxType: 'delivered', date: 'Jul 6', value: 43.4 },
  { faxType: 'fail', date: 'Jul 6', value: 17.8 },
  { faxType: 'pending', date: 'Jul 6', value: 33.5 },
  { faxType: 'delivered', date: 'Jul 7', value: 33.8 },
  { faxType: 'fail', date: 'Jul 7', value: 25.0 },
  { faxType: 'pending', date: 'Jul 7', value: 19.9 },
  { faxType: 'delivered', date: 'Jul 8', value: 11.2 },
  { faxType: 'fail', date: 'Jul 8', value: 22.4 },
  { faxType: 'pending', date: 'Jul 8', value: 23.0 },
  { faxType: 'delivered', date: 'Jul 9', value: 14.1 },
  { faxType: 'fail', date: 'Jul 9', value: 27.6 },
  { faxType: 'pending', date: 'Jul 9', value: 41.3 },
  { faxType: 'delivered', date: 'Jul 10', value: 10.2 },
  { faxType: 'fail', date: 'Jul 10', value: 25.4 },
  { faxType: 'pending', date: 'Jul 10', value: 43.3 },
  { faxType: 'delivered', date: 'Jul 11', value: 22.8 },
  { faxType: 'fail', date: 'Jul 11', value: 28.4 },
  { faxType: 'pending', date: 'Jul 11', value: 44.9 },
  { faxType: 'delivered', date: 'Jul 12', value: 21.3 },
  { faxType: 'fail', date: 'Jul 12', value: 23.0 },
  { faxType: 'pending', date: 'Jul 12', value: 17.0 },
  { faxType: 'delivered', date: 'Jul 13', value: 44.8 },
  { faxType: 'fail', date: 'Jul 13', value: 39.7 },
  { faxType: 'pending', date: 'Jul 13', value: 40.7 },
  { faxType: 'delivered', date: 'Jul 14', value: 33.6 },
  { faxType: 'fail', date: 'Jul 14', value: 17.8 },
  { faxType: 'pending', date: 'Jul 14', value: 39.3 },
  { faxType: 'delivered', date: 'Jul 15', value: 35.8 },
  { faxType: 'fail', date: 'Jul 15', value: 19.3 },
  { faxType: 'pending', date: 'Jul 15', value: 46.0 },
  { faxType: 'delivered', date: 'Jul 16', value: 27.6 },
  { faxType: 'fail', date: 'Jul 16', value: 22.2 },
  { faxType: 'pending', date: 'Jul 16', value: 12.9 },
  { faxType: 'delivered', date: 'Jul 17', value: 17.0 },
  { faxType: 'fail', date: 'Jul 17', value: 45.3 },
  { faxType: 'pending', date: 'Jul 17', value: 48.3 },
  { faxType: 'delivered', date: 'Jul 18', value: 49.3 },
  { faxType: 'fail', date: 'Jul 18', value: 14.6 },
  { faxType: 'pending', date: 'Jul 18', value: 48.2 },
  { faxType: 'delivered', date: 'Jul 19', value: 14.2 },
  { faxType: 'fail', date: 'Jul 19', value: 45.4 },
  { faxType: 'pending', date: 'Jul 19', value: 26.4 },
  { faxType: 'delivered', date: 'Jul 20', value: 43.2 },
  { faxType: 'fail', date: 'Jul 20', value: 13.6 },
  { faxType: 'pending', date: 'Jul 20', value: 15.7 },
  { faxType: 'delivered', date: 'Jul 21', value: 20.8 },
  { faxType: 'fail', date: 'Jul 21', value: 20.2 },
  { faxType: 'pending', date: 'Jul 21', value: 32.1 },
];

export function getColumnsCallActivity() {
  return [
    {
      title: (
        <Typography className="small-text" style={{ paddingLeft: '8px' }}>
          Date
        </Typography>
      ),
      dataIndex: 'dateAdded',
      key: 'dateAdded',
      render: (text) => <span className="small-text">{text}</span>,
      width: 90,
    },
    {
      title: (
        <Typography className="small-text" style={{ paddingLeft: '8px' }}>
          Total Faxes
        </Typography>
      ),
      dataIndex: 'totalFaxes',
      key: 'totalFaxes',
      render: (text) => <span className="small-text">{text}</span>,
      width: 90,
    },
    {
      title: (
        <Typography className="small-text" style={{ paddingLeft: '8px' }}>
          Success
        </Typography>
      ),
      dataIndex: 'success',
      key: 'success',
      render: (text) => <span className="small-text">{text}</span>,
      width: 80,
    },
    {
      title: (
        <Typography className="small-text" style={{ paddingLeft: '8px' }}>
          Failure
        </Typography>
      ),
      dataIndex: 'failure',
      key: 'failure',
      render: (text) => <span className="small-text">{text}</span>,
      width: 70,
    },
  ];
}
