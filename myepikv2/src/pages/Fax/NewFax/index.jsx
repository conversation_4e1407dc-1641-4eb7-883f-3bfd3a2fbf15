import React, { useState } from 'react';
import PropTypes from 'prop-types';
import {
  Input,
  Flex,
  Button,
  Typography,
  CloseIcon,
  Upload,
} from '@/components';
import { Divider, Row, Col } from 'antd';
import { useStore } from '@/store';

const Field = ({ label, component, bottomLabel }) => (
  <Flex vertical gap={8}>
    <Typography className="small-text" style={{ color: 'var(--primary-gray)' }}>
      {label}
    </Typography>
    {component}
    {bottomLabel && (
      <Typography className="extra-small-text">{bottomLabel}</Typography>
    )}
  </Flex>
);

// Add PropTypes for Field Component
Field.propTypes = {
  label: PropTypes.string.isRequired, // `label` is a required string
  component: PropTypes.node.isRequired, // `component` is a required React node
  bottomLabel: PropTypes.string.isRequired,
};

const NewFax = () => {
  const [formValues, setFormValues] = useState({});

  const { closeDrawer } = useStore((state) => state.drawer);

  const handleChange = (field, value) => {
    setFormValues((prev) => ({ ...prev, [field]: value }));
  };

  const handleKeyDown = (field, value, event) => {
    if (event.key === 'Enter' && value.trim()) {
      if (field === 'alertEmail') {
        handleChange(field, '');
      }
    }
  };

  const handleUploadChange = (info) => {
    console.log('Custom upload change:', info);
  };

  const handleDrop = (e) => {
    console.log('Custom drop event:', e.dataTransfer.files);
  };

  const sections = [
    {
      title: '',
      fields: [
        {
          name: 'receiver',
          label: 'Receiver',
          placeholder: 'Enter Phone',
          span: 16,
        },
        {
          name: 'upLoad',
          component: (
            <Upload
              action=""
              name="file"
              multiple
              onChange={handleUploadChange}
              onDrop={handleDrop}
            />
          ),
          span: 24,
        },
      ],
    },
  ];

  return (
    <Flex vertical>
      <Flex vertical>
        <Flex justify="space-between" align="center">
          <Typography className="heading-four-bold">New Fax</Typography>
          <CloseIcon onClick={closeDrawer} style={{ cursor: 'pointer' }} />
        </Flex>
        <Divider className="divider-sm" />
      </Flex>
      {sections.map((section, sectionIndex) => (
        <React.Fragment key={`section-${sectionIndex}`}>
          {(section?.title || section?.showDivider) && (
            <Flex vertical style={{ marginTop: '24px' }}>
              <Typography className="heading-five">{section.title}</Typography>
              <Divider className="divider-sm" />
            </Flex>
          )}
          <Row gutter={[8, 16]} style={{ marginTop: '16px' }}>
            {section.fields.map((field, fieldIndex) => (
              <Col
                key={`field-${sectionIndex}-${fieldIndex}`}
                span={field.span}
                xs={24}
                sm={12}
                md={12}
                lg={field.span}
                xl={field.span}
                xxl={field.span}
              >
                {Field({
                  label: field.label,
                  component: field.component || (
                    <Input
                      placeholder={field.placeholder}
                      value={formValues[field.name]}
                      onChange={(e) => handleChange(field.name, e.target.value)}
                      onKeyDown={(e) =>
                        field.list &&
                        e.key === 'Enter' &&
                        handleKeyDown(field.name, formValues[field.name], e)
                      }
                      className="text-medium-regular"
                    />
                  ),
                  bottomLabel: field?.bottomLabel,
                })}
              </Col>
            ))}
          </Row>
        </React.Fragment>
      ))}
      <Flex gap={4} justify="end" style={{ marginTop: '8px' }}>
        <Button
          variant="outlined"
          onClick={closeDrawer}
          style={{ width: '10%' }}
        >
          Cancel
        </Button>
        <Button
          onClick={() => console.log('Save Form:', formValues)}
          style={{ width: '10%' }}
        >
          Send
        </Button>
      </Flex>
    </Flex>
  );
};

export default NewFax;
