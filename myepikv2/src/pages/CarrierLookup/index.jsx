import { Flex } from 'antd';
import {
  SearchIcon,
  Input,
  CustomDropDown,
  Button,
  PageTitle,
} from '@/components';
import { useStore } from '@/store';
import QueryLRN from './QueryLRN';
import CarrierLookupTable from './CarrierLookupTable';

const menuItems = [
  { key: 'company', label: 'Company' },
  { key: 'edgeDeviceName', label: 'Edge Device Name' },
  { key: 'status', label: 'Status' },
  { key: 'assignedNumber', label: 'Assigned Number' },
  { key: 'epiNumber', label: 'EPI Number' },
  { key: 'serialNumber', label: 'Serial Number' },
  { key: 'macAddress', label: 'MAC Address' },
  { key: 'byGroup', label: 'By Group' },
  { key: 'shippingTrackingNumber', label: 'Shipping Tracking Number' },
  { key: 'apnAddress', label: 'VPN Address' },
  { key: 'imei', label: 'IMEI' },
  { key: 'sim', label: 'SIM' },
  { key: 'createdOn', label: 'Created On' },
];

const CarrierLookup = () => {
  const { openDrawer } = useStore((state) => state.drawer);

  const drawerConfig = {
    queryLRN: (mode) => openDrawer(QueryLRN, { mode }),
  };

  const handleMenuClick = (e) => {
    if (e.key) {
      console.log(`Click on menu item ${e.key}`);
    }
  };

  return (
    <Flex vertical gap={8} style={{ height: '100%' }}>
      <Flex
        align="center"
        wrap
        justify="space-between"
        style={{ padding: '0 12px' }}
      >
        <PageTitle pageTitle={'Carrier LookUp'} />
        <Flex align="center" wrap gap={6}>
          <Input
            prefix={<SearchIcon />}
            placeholder="Search"
            style={{ maxWidth: '165px' }}
          />
          <CustomDropDown
            menuItems={menuItems}
            handleMenuClick={handleMenuClick}
          />
          <Input
            // prefix={<SearchIcon />}
            placeholder="10 Digit Number"
            style={{ maxWidth: '165px' }}
          />
          <Button onClick={() => drawerConfig?.queryLRN()}>Query LRN</Button>
        </Flex>
      </Flex>
      <div style={{ padding: '0 12px' }} className="custom-scrollbar">
        <Flex
          vertical
          style={{
            background: 'white',
            borderRadius: '12px',
            boxShadow: '0px 1px 2px 0px #1018280F, 0px 1px 3px 0px #1018281A',
          }}
        >
          <Flex vertical style={{ marginTop: '20px', height: '100%' }}>
            <CarrierLookupTable handleAction={() => drawerConfig?.queryLRN()} />
          </Flex>
        </Flex>
      </div>
    </Flex>
  );
};

export default CarrierLookup;
