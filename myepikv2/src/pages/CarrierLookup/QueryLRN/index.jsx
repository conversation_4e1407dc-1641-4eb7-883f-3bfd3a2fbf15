import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Input, Flex, Button, Typography, CloseIcon } from '@/components';
import { Divider, Row, Col } from 'antd';
import { useStore } from '@/store';

const Field = ({ label, component, bottomLabel }) => (
  <Flex vertical gap={8}>
    <Typography className="small-text" style={{ color: 'var(--primary-gray)' }}>
      {label}
    </Typography>
    {component}
    {bottomLabel && (
      <Typography className="extra-small-text">{bottomLabel}</Typography>
    )}
  </Flex>
);

// Add PropTypes for Field Component
Field.propTypes = {
  label: PropTypes.string.isRequired, // `label` is a required string
  component: PropTypes.node.isRequired, // `component` is a required React node
  bottomLabel: PropTypes.string.isRequired,
};

const QueryLRN = ({ mode }) => {
  const [formValues, setFormValues] = useState({ lrn: '13613202126' });

  const { closeDrawer } = useStore((state) => state.drawer);

  const handleChange = (field, value) => {
    setFormValues((prev) => ({ ...prev, [field]: value }));
  };

  const handleKeyDown = (field, value, event) => {
    if (event.key === 'Enter' && value.trim()) {
      if (field === 'alertEmail') {
        handleChange(field, '');
      }
    }
  };

  const sections = [
    {
      title: '',
      fields: [
        {
          name: 'lrn',
          label: 'LRN',
          placeholder: '',
          value: '+13613202126',
          span: 12,
        },
        {
          name: 'ocn',
          label: 'OCN',
          placeholder: '--',
          span: 12,
        },
        {
          name: 'lata',
          label: 'LATA',
          placeholder: 'Enter Phone',
          span: 12,
        },
        {
          name: 'city',
          label: 'City',
          placeholder: '',
          span: 12,
        },
        {
          name: 'rc',
          label: 'RC',
          placeholder: '',
          span: 12,
        },
        {
          name: 'state',
          label: 'State',
          placeholder: '',
          span: 12,
        },
        {
          name: 'jurisdiction',
          label: 'JURISDICTION',
          placeholder: '',
          span: 12,
        },
        {
          name: 'local',
          label: 'LOCAL',
          placeholder: '',
          span: 12,
        },
        {
          name: 'lec',
          label: 'LEC',
          placeholder: '',
          span: 12,
        },
        {
          name: 'lecType',
          label: 'LECTYPE',
          placeholder: '',
          span: 12,
        },
      ],
    },
  ];

  return (
    <Flex vertical>
      <Flex vertical>
        <Flex justify="space-between" align="center">
          <Typography className="heading-four-bold">+13613202126</Typography>
          <CloseIcon onClick={closeDrawer} style={{ cursor: 'pointer' }} />
        </Flex>
        <Divider className="divider-sm" />
      </Flex>
      {sections.map((section, sectionIndex) => (
        <React.Fragment key={`section-${sectionIndex}`}>
          {(section?.title || section?.showDivider) && (
            <Flex vertical style={{ marginTop: '24px' }}>
              <Typography className="heading-five">{section.title}</Typography>
              <Divider className="divider-sm" />
            </Flex>
          )}
          <Row gutter={[8, 16]} style={{ marginTop: '16px' }}>
            {section.fields.map((field, fieldIndex) => (
              <Col
                key={`field-${sectionIndex}-${fieldIndex}`}
                span={field.span}
                xs={24}
                sm={12}
                md={12}
                lg={field.span}
                xl={field.span}
                xxl={field.span}
              >
                {Field({
                  label: field.label,
                  component: field.component || (
                    <Input
                      placeholder={field.placeholder}
                      value={formValues[field.name]}
                      onChange={(e) => handleChange(field.name, e.target.value)}
                      onKeyDown={(e) =>
                        field.list &&
                        e.key === 'Enter' &&
                        handleKeyDown(field.name, formValues[field.name], e)
                      }
                      size="middle"
                      className="text-medium-regular"
                    />
                  ),
                  bottomLabel: field?.bottomLabel,
                })}
              </Col>
            ))}
            {mode === 'edit' &&
              section.editModeFields.map((field, fieldIndex) => (
                <Col
                  key={`field-${sectionIndex}-${fieldIndex}`}
                  span={field.span}
                  xs={24}
                  sm={12}
                  md={12}
                  lg={field.span}
                  xl={field.span}
                  xxl={field.span}
                >
                  {Field({
                    label: field.label,
                    value: field?.value,
                    component: field.component || (
                      <Input
                        placeholder={field.placeholder}
                        value={formValues[field.name]}
                        onChange={(e) =>
                          handleChange(field.name, e.target.value)
                        }
                        onKeyDown={(e) =>
                          field.list &&
                          e.key === 'Enter' &&
                          handleKeyDown(field.name, formValues[field.name], e)
                        }
                        size="middle"
                        className="text-medium-regular"
                      />
                    ),
                    bottomLabel: field?.bottomLabel,
                  })}
                </Col>
              ))}
          </Row>
          {mode === 'edit' && (
            <Button style={{ width: '10%', marginTop: 16 }}>Edit CNAM</Button>
          )}
        </React.Fragment>
      ))}
    </Flex>
  );
};

QueryLRN.propTypes = {
  mode: PropTypes.bool.isRequired,
};

export default QueryLRN;
