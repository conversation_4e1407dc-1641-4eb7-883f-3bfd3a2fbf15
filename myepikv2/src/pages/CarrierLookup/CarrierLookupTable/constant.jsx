import { Button, Flex, Tooltip, Typography } from '@/components';

export function getColumns({ onQueryLRNClick }) {
  const renderActionButtons = () => (
    <Flex
      align="center"
      style={{ height: '16px' }}
      onClick={(e) => e?.stopPropagation()}
    >
      <Button
        size="small"
        onClick={onQueryLRNClick}
        style={{ backgroundColor: '#3B5CA9' }}
      >
        <Typography className="small-text" style={{ color: 'white' }}>
          Query LRN
        </Typography>
      </Button>
    </Flex>
  );

  return [
    {
      title: <Typography className="small-text">Serial Number</Typography>,
      dataIndex: 'serialNumber',
      key: 'serialNumber',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Company</Typography>,
      dataIndex: 'company',
      key: 'company',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Phone Number</Typography>,
      dataIndex: 'phoneNumber',
      key: 'phoneNumber',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Line Type</Typography>,
      dataIndex: 'lineType',
      key: 'lineType',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Route</Typography>,
      dataIndex: 'route',
      key: 'route',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Action</Typography>,
      key: 'action',
      render: renderActionButtons,
      width: 100,
    },
  ];
}

export const dataSource = [
  {
    key: '1',
    serialNumber: '*********',
    company: 'Epik Company Name',
    phoneNumber: '*********',
    lineType: 'Alarm',
    route: 'NYCMNYBXGR1_4402',
  },
  {
    key: '2',
    serialNumber: '*********',
    company: 'Epik Company Name',
    phoneNumber: '*********',
    lineType: 'Alarm',
    route: 'NYCMNYBXGR1_4402',
  },
  {
    key: '3',
    serialNumber: '*********',
    company: 'Epik Company Name',
    phoneNumber: '*********',
    lineType: 'Alarm',
    route: 'NYCMNYBXGR1_4402',
  },
  {
    key: '4',
    serialNumber: '*********',
    company: 'Epik Company Name',
    phoneNumber: '*********',
    lineType: 'Alarm',
    route: 'NYCMNYBXGR1_4402',
  },
  {
    key: '5',
    serialNumber: '*********',
    company: 'Epik Company Name',
    phoneNumber: '*********',
    lineType: 'Alarm',
    route: 'NYCMNYBXGR1_4402',
  },
  {
    key: '6',
    serialNumber: '*********',
    company: 'Epik Company Name',
    phoneNumber: '*********',
    lineType: 'Alarm',
    route: 'NYCMNYBXGR1_4402',
  },
  {
    key: '7',
    serialNumber: '*********',
    company: 'Epik Company Name',
    phoneNumber: '*********',
    lineType: 'Alarm',
    route: 'NYCMNYBXGR1_4402',
  },
  {
    key: '8',
    serialNumber: '*********',
    company: 'Epik Company Name',
    phoneNumber: '*********',
    lineType: 'Alarm',
    route: 'NYCMNYBXGR1_4402',
  },
  {
    key: '9',
    serialNumber: '*********',
    company: 'Epik Company Name',
    phoneNumber: '*********',
    lineType: 'Alarm',
    route: 'NYCMNYBXGR1_4402',
  },
  {
    key: '10',
    serialNumber: '*********',
    company: 'Epik Company Name',
    phoneNumber: '*********',
    lineType: 'Alarm',
    route: 'NYCMNYBXGR1_4402',
  },
  {
    key: '11',
    serialNumber: '*********',
    company: 'Epik Company Name',
    phoneNumber: '*********',
    lineType: 'Alarm',
    route: 'NYCMNYBXGR1_4402',
  },
  {
    key: '12',
    serialNumber: '*********',
    company: 'Epik Company Name',
    phoneNumber: '*********',
    lineType: 'Alarm',
    route: 'NYCMNYBXGR1_4402',
  },
  {
    key: '13',
    serialNumber: '*********',
    company: 'Epik Company Name',
    phoneNumber: '*********',
    lineType: 'Alarm',
    route: 'NYCMNYBXGR1_4402',
  },
  {
    key: '14',
    serialNumber: '*********',
    company: 'Epik Company Name',
    phoneNumber: '*********',
    lineType: 'Alarm',
    route: 'NYCMNYBXGR1_4402',
  },
  {
    key: '15',
    serialNumber: '*********',
    company: 'Epik Company Name',
    phoneNumber: '*********',
    lineType: 'Alarm',
    route: 'NYCMNYBXGR1_4402',
  },
  {
    key: '16',
    serialNumber: '*********',
    company: 'Epik Company Name',
    phoneNumber: '*********',
    lineType: 'Alarm',
    route: 'NYCMNYBXGR1_4402',
  },
  {
    key: '17',
    serialNumber: '*********',
    company: 'Epik Company Name',
    phoneNumber: '*********',
    lineType: 'Alarm',
    route: 'NYCMNYBXGR1_4402',
  },
  {
    key: '18',
    serialNumber: '*********',
    company: 'Epik Company Name',
    phoneNumber: '*********',
    lineType: 'Alarm',
    route: 'NYCMNYBXGR1_4402',
  },
  {
    key: '19',
    serialNumber: '*********',
    company: 'Epik Company Name',
    phoneNumber: '*********',
    lineType: 'Alarm',
    route: 'NYCMNYBXGR1_4402',
  },
  {
    key: '20',
    serialNumber: '*********',
    company: 'Epik Company Name',
    phoneNumber: '*********',
    lineType: 'Alarm',
    route: 'NYCMNYBXGR1_4402',
  },
  {
    key: '21',
    serialNumber: '*********',
    company: 'Epik Company Name',
    phoneNumber: '*********',
    lineType: 'Alarm',
    route: 'NYCMNYBXGR1_4402',
  },
];
