import React from 'react';
import PropTypes from 'prop-types';
import { Flex, Spin, Typography } from 'antd';
import './CardStyles.css';
import { Button } from '@/components';

const { Text } = Typography;

const Card = ({
  type = 'default',
  title,
  subtitle,
  icon,
  iconSize = { width: 24, height: 24 },
  buttonIcon,
  buttonText,
  buttonAction,
  text,
  customClass = '',
  style = {},
  loading = false,
}) => {
  switch (type) {
    case 'signal': // Signal Strength Card
      return (
        <Flex
          justify="space-between"
          wrap={false}
          className={`card card--signal ${customClass}`}
          style={{ ...style }}
        >
          <Flex vertical className="card__content">
            <Typography className="text-inter-500-12-18">{title}</Typography>
            {icon && React.cloneElement(icon, iconSize)}
          </Flex>
          {buttonIcon && buttonAction && (
            <Flex vertical align="center" className="card__button-container">
              <Button
                color="default"
                variant="outlined"
                style={{ margin: 0, padding: 0, height: '24px', width: '24px' }}
                icon={buttonIcon}
                onClick={buttonAction}
              />
            </Flex>
          )}
        </Flex>
      );

    case 'battery': // Battery Card
      return (
        <Flex
          justify="flex-start"
          align="center"
          className="card card--battery"
          gap={6}
          style={{ ...style }}
        >
          {icon && React.cloneElement(icon, iconSize)}
          <Typography className="text-inter-500-12-18">{text}</Typography>
        </Flex>
      );

    case 'info':
      return (
        <Flex
          justify="space-between"
          className={`card card--info ${customClass}`}
          key={title}
          style={{ ...style }}
        >
          <Flex vertical gap={6} className="card__content">
            <Typography className="text-montserrat-500-12-16-white">
              {title}
            </Typography>
            {subtitle && (
              <Flex
                align="center"
                gap={6}
                className="text-montserrat-600-18-16-white"
                // style={{ marginTop: '8px' }}
              >
                <Typography className="text-montserrat-600-18-16-white">
                  {subtitle}
                </Typography>
                {loading && <Spin size="small" className="card-spin" />}
              </Flex>
            )}
          </Flex>
          <Flex
            vertical
            align="end"
            className="card__button-container"
            style={{ padding: '4px 0' }}
          >
            {icon}
            {buttonText && buttonAction && (
              <Button
                color="default"
                variant="outlined"
                size="small"
                onClick={buttonAction}
                className="card__button"
              >
                {buttonText}
              </Button>
            )}
          </Flex>
        </Flex>
      );

    default: // Default Card Layout
      return (
        <Flex
          justify="space-between"
          align="center"
          className={`card card--default ${customClass}`}
          style={{ ...style }}
        >
          <Flex vertical className="card__content">
            {title && <Text className="card__title">{title}</Text>}
            {icon && React.cloneElement(icon, iconSize)}
          </Flex>
          {text && (
            <Text
              className="card__text"
              style={{ marginLeft: title ? '0' : '15px' }}
            >
              {text}
            </Text>
          )}
        </Flex>
      );
  }
};

// Add PropTypes validation
Card.propTypes = {
  type: PropTypes.oneOf(['default', 'signal', 'battery', 'info']),
  title: PropTypes.string,
  subtitle: PropTypes.string,
  icon: PropTypes.element,
  iconSize: PropTypes.shape({
    width: PropTypes.number,
    height: PropTypes.number,
  }),
  buttonIcon: PropTypes.element,
  buttonText: PropTypes.string,
  buttonAction: PropTypes.func,
  text: PropTypes.string,
  customClass: PropTypes.string,
  style: PropTypes.object,
  loading: PropTypes.bool,
};

export default Card;
