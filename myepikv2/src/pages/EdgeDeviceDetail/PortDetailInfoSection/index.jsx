import { Button, Flex, Typography } from '@/components';
import { Di<PERSON><PERSON>, Spin } from 'antd';
import { useEpiDetailById } from '@/graphqlHooks/epikv2-api/useEpiDetailById';
import { useEdgeDeviceDetailContext } from '../context';
import { useEpiUpdateHook } from '@/graphqlHooks/epikv2-api/updateEpiHook';
import { epiDetailByIdKey } from '@/utils/helpers';
import { useEpiStore } from '@/store/epiSlice';
import { useStore } from '@/store';

const PortDetailInfoSection = () => {
  const { selectedPort } = useEdgeDeviceDetailContext();
  const { loadingState } = useEpiStore();

  const registrationMetaError = useStore(
    (s) => s.error.errorState[`sub:${'registrationMeta'}`] || null,
  );

  const portPhysicalMetaError = useStore(
    (s) => s.error.errorState[`sub:${'portPhysicalMeta'}`] || null,
  );

  const { data, isLoading } = useEpiDetailById(
    { id: selectedPort?.obiId },
    false,
  );

  useEpiUpdateHook(
    selectedPort?.obiId,
    ['*'],
    epiDetailByIdKey(selectedPort?.obiId),
  );

  const portNumber = selectedPort?.boxPortNumber;

  const detail = data?.EpiDetailById ?? {};
  const registrationMeta = detail?.registrationMeta ?? {};
  const wanInfo = registrationMeta?.wanInfo ?? {};
  const sp1ServiceStatus = registrationMeta?.sp1ServiceStatus ?? {};
  const sp2ServiceStatus = registrationMeta?.sp2ServiceStatus ?? {};
  const obiTalkServiceStatus = registrationMeta?.obiTalkServiceStatus ?? {};

  const rawPortPhysicalMeta = detail?.portPhysicalMeta;
  const portPhysicalMeta = Array.isArray(rawPortPhysicalMeta)
    ? rawPortPhysicalMeta
    : [];
  const selectedServiceStatus =
    portNumber === 'Port 1'
      ? sp1ServiceStatus
      : portNumber === 'Port 2'
        ? sp2ServiceStatus
        : {};

  const selectedPhysicalMeta =
    portNumber === 'Port 1'
      ? (portPhysicalMeta.find((p) => p?.name === 'PHONE1 Port') ?? {})
      : portNumber === 'Port 2'
        ? (portPhysicalMeta.find((p) => p?.name === 'PHONE2 Port') ?? {})
        : {};

  return (
    <Flex
      vertical
      gap={4}
      style={{ padding: '16px', background: '#f5f5f5', borderRadius: '8px' }}
    >
      <Typography className="heading-five-bold">
        Original Provisioned Date
      </Typography>
      <Typography className="small-text">Date is not available.</Typography>
      <Divider className="divider-sm" />
      <Typography className="heading-five-bold">Last Updated</Typography>
      <Typography className="small-text">Date is not available.</Typography>
      <Divider className="divider-sm" />
      <Flex vertical>
        <Flex justify="space-between">
          <Flex align="center" gap={8}>
            <Typography className="heading-five-bold">Port Status</Typography>
            {(isLoading || loadingState?.portPhysicalMeta) && (
              <Spin size="small" />
            )}
          </Flex>
          <Button>Refresh</Button>
        </Flex>
        <Flex>
          <Flex flex={1} vertical>
            <Typography className="small-text">Name:</Typography>
            <Typography className="small-text">State:</Typography>
            <Typography className="small-text">Loop Current:</Typography>
            <Typography className="small-text">Vbat:</Typography>
            <Typography className="small-text">Tip Ring Voltage:</Typography>
            <Typography className="small-text">
              Last Inbound Caller Info:
            </Typography>
          </Flex>
          {portPhysicalMetaError ? (
            <Flex flex={1} align="center">
              <Typography
                className="small-text"
                style={{ color: 'var(--error-600)' }}
              >
                {portPhysicalMetaError}
              </Typography>
            </Flex>
          ) : (
            <Flex flex={1} vertical>
              <Typography className="small-text">
                {selectedPhysicalMeta?.name || '--'}
              </Typography>
              <Typography className="small-text">
                {selectedPhysicalMeta?.state || '--'}
              </Typography>
              <Typography className="small-text">
                {selectedPhysicalMeta?.loopCurrent || '--'}
              </Typography>
              <Typography className="small-text">
                {selectedPhysicalMeta?.Vbat || '--'}
              </Typography>
              <Typography className="small-text">
                {selectedPhysicalMeta?.tipRingVoltage || '--'}
              </Typography>
              <Typography className="small-text">
                {selectedPhysicalMeta?.lastCallerInfo || '--'}
              </Typography>
            </Flex>
          )}
        </Flex>
      </Flex>

      <Divider className="divider-sm" />

      <Flex align="center" gap={8}>
        <Typography className="heading-five-bold">EPI WAN INFO</Typography>
        {(isLoading || loadingState?.registrationMeta) && <Spin size="small" />}
      </Flex>
      {registrationMetaError ? (
        <Flex flex={1} align="center">
          <Typography
            className="small-text"
            style={{ color: 'var(--error-600)' }}
          >
            {registrationMetaError}
          </Typography>
        </Flex>
      ) : (
        <Flex vertical>
          <Typography className="small-text">{wanInfo?.ip || '--'}</Typography>
          <Typography className="small-text">
            {wanInfo?.subnet || '--'}
          </Typography>
          <Typography className="small-text">
            {wanInfo?.gateway || '--'}
          </Typography>
          <Typography className="small-text">
            {Array.isArray(wanInfo?.dns)
              ? wanInfo.dns.join(', ')
              : wanInfo?.dns || '--'}
          </Typography>
        </Flex>
      )}
      <Divider className="divider-sm" />
      <Flex align="center" gap={8}>
        <Typography className="heading-five-bold">
          {portNumber || ''}
        </Typography>
        {(isLoading || loadingState?.registrationMeta) && <Spin size="small" />}
      </Flex>
      {registrationMetaError ? (
        <Flex flex={1} align="center">
          <Typography
            className="small-text"
            style={{ color: 'var(--error-600)' }}
          >
            {registrationMetaError}
          </Typography>
        </Flex>
      ) : (
        <Typography className="small-text">
          {selectedServiceStatus?.status || '--'}
        </Typography>
      )}
      <Divider className="divider-sm" />
      <Flex align="center" gap={8}>
        <Typography className="heading-five-bold">
          EPI connection status
        </Typography>
        {(isLoading || loadingState?.registrationMeta) && <Spin size="small" />}
      </Flex>
      <Flex justify="space-between">
        {registrationMetaError ? (
          <Flex flex={1} align="center">
            <Typography
              className="small-text"
              style={{ color: 'var(--error-600)' }}
            >
              {registrationMetaError}
            </Typography>
          </Flex>
        ) : (
          <Flex>
            <Typography className="small-text">
              {obiTalkServiceStatus?.status || '--'}
            </Typography>
          </Flex>
        )}
        <Button>Refresh</Button>
      </Flex>
    </Flex>
  );
};

export default PortDetailInfoSection;
