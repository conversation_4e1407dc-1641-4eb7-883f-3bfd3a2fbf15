import PropTypes from 'prop-types';
import { Spin } from 'antd';
import { Button, Typography, Flex, Tooltip, InfoIcon } from '@/components';
import './styles.css';

const HeaderInfoCard = ({
  title,
  subtitle,
  icon,
  buttonText,
  buttonAction,
  style = {},
  loading = false,
  error = '',
}) => {
  console.log('HeaderInfoCard', error);
  return (
    <Flex
      justify="space-between"
      className={`header-info-card`}
      key={title}
      style={{ ...style }}
    >
      <Flex vertical gap={6}>
        <Flex gap={4} align="center">
          <Typography
            className="text-montserrat-500-12-16-white"
            ellipsis={{
              tooltip: <Tooltip>{title}</Tooltip>,
            }}
          >
            {title}
          </Typography>
          {error && (
            <Tooltip title={error} placement="top">
              <InfoIcon height={12} width={12} stroke={'var(--error-600)'} />
            </Tooltip>
          )}
        </Flex>
        {subtitle && (
          <Flex
            align="center"
            gap={6}
            className="text-montserrat-600-18-16-white"
          >
            <Typography className="text-montserrat-600-18-16-white">
              {subtitle}
            </Typography>
            {loading && <Spin size="small" className="header-info-card-spin" />}
          </Flex>
        )}
      </Flex>
      <Flex vertical align="end" style={{ padding: '4px 0' }}>
        {icon}
        {buttonText && buttonAction && (
          <Button
            color="default"
            variant="outlined"
            size="small"
            onClick={buttonAction}
          >
            {buttonText}
          </Button>
        )}
      </Flex>
    </Flex>
  );
};

// Add PropTypes validation
HeaderInfoCard.propTypes = {
  title: PropTypes.string,
  subtitle: PropTypes.string,
  icon: PropTypes.element,
  buttonText: PropTypes.string,
  buttonAction: PropTypes.func,
  style: PropTypes.object,
  loading: PropTypes.bool,
  error: PropTypes.string,
};

export default HeaderInfoCard;
