import {
  Flex,
  Typography,
  Tooltip,
  MoreHorizontalIcon,
  DropDown,
  EditIcon,
  FileTextIcon,
  TrashIcon,
  StatusTag,
} from '@/components';

const menuItems = [
  { key: 'edit', label: 'Edit', icon: <EditIcon /> },
  { key: 'notes', label: 'Notes', icon: <FileTextIcon /> },
  { key: 'delete', label: 'Delete', icon: <TrashIcon /> },
];

export function getColumns({ handleOOMBAction }) {
  const renderActionButtons = () => (
    <Flex
      align="center"
      justify="flex-end"
      style={{ height: '16px' }}
      onClick={(e) => e?.stopPropagation()}
    >
      <DropDown
        menuItems={menuItems}
        onClick={(e) => handleOOMBAction(e)}
        trigger={['click']}
        dropDownStyles={{ height: 'inherit', display: 'flex' }}
        buttonLabel=""
        buttonProps={{
          onlyIcon: true,
          icon: <MoreHorizontalIcon width={16} height={16} />,
          type: 'default',
          variant: 'filled',
          style: { height: 'inherit', padding: 0 },
        }}
      />
    </Flex>
  );

  return [
    {
      title: (
        <Typography className="small-text" color="var(--gray-600)">
          BTN
        </Typography>
      ),
      dataIndex: 'btn',
      key: 'btn',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },

    {
      title: (
        <Typography className="small-text" color="var(--gray-600)">
          Category
        </Typography>
      ),
      dataIndex: 'category',
      key: 'category',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },

    {
      title: (
        <Typography className="small-text" color="var(--gray-600)">
          Interface
        </Typography>
      ),
      dataIndex: 'interface',
      key: 'interface',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },

    {
      title: (
        <Typography
          className="small-text"
          color="var(--gray-600)"
          ellipsis={{
            tooltip: <Tooltip>Interface State</Tooltip>,
          }}
        >
          Interface State
        </Typography>
      ),
      dataIndex: 'interfaceState',
      key: 'interfaceState',
      render: (text) => <StatusTag text={text} />,
    },
    {
      title: (
        <Typography className="small-text" color="var(--gray-600)">
          TTY
        </Typography>
      ),
      dataIndex: 'tty',
      key: 'tty',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: (
        <Typography className="small-text" color="var(--gray-600)">
          Speed
        </Typography>
      ),
      dataIndex: 'speed',
      key: 'speed',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: (
        <Typography className="small-text" color="var(--gray-600)">
          Data Bits
        </Typography>
      ),
      dataIndex: 'dataBits',
      key: 'dataBits',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: (
        <Typography className="small-text" color="var(--gray-600)">
          Parity
        </Typography>
      ),
      dataIndex: 'parity',
      key: 'parity',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: (
        <Typography
          className="small-text"
          color="var(--gray-600)"
          ellipsis={{
            tooltip: <Tooltip>Flow Control</Tooltip>,
          }}
        >
          Flow Control
        </Typography>
      ),
      dataIndex: 'flowControl',
      key: 'flowControl',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: '',
      key: 'action',
      render: renderActionButtons,
      width: 50,
    },
  ];
}
