import React, { useEffect, useMemo, useState } from 'react';
import {
  Input,
  Flex,
  Button,
  Typography,
  Checkbox,
  Toggle,
  Select,
  CloseTwoIcon,
  Radio,
} from '@/components';
import { Divider, Row, Col, Upload } from 'antd';
import { useEdgeDeviceDetailContext } from '../context';
import './styles.css';
import { useGet } from '@/hooks';
import { APIS } from '@/constants';
import parsePhoneNumber from 'libphonenumber-js';
import AprStatusBadg from '../AprStatusBadg';

const normalizeEmail = (v) => v.trim().toLowerCase();
const isValidEmail = (v) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v.trim());
const isValidPhone = (v) => {
  try {
    const phone = parsePhoneNumber(v, 'US');
    return phone?.isValid() || false;
  } catch {
    return false;
  }
};
const addUniqueItem = (list, value) => {
  const incoming = (value || '').trim();
  if (!incoming) return list;
  if (list.some((x) => x.trim().toLowerCase() === incoming.toLowerCase()))
    return list;
  return [...list, incoming];
};

const options = [
  { value: 'sip', label: 'SIP' },
  { value: 'pri', label: 'PRI' },
  { value: 'vmail', label: 'Voicemail' },
  { value: 'ringGroup', label: 'Ring Group' },
  { value: 'fax', label: 'Fax' },
  { value: 'faxStandard', label: 'Fax Standard' },
  { value: 'did', label: 'DID' },
  { value: 'dialout', label: 'Dial Out' },
  { value: 'page', label: 'Page' },
  { value: 'ringDown', label: 'Ring Down' },
  { value: 'generic', label: 'Generic' },
  { value: 'voice', label: 'Voice' },
  { value: 'alarm', label: 'Alarm' },
  { value: 'elevator', label: 'Elevator' },
  { value: 'modem', label: 'Modem' },
  { value: 'modemHunt', label: 'Modem Hunt' },
  { value: 'fireAlarm', label: 'Fire Alarm' },
  { value: 'hunt', label: 'Hunt Group' },
  { value: 'forward', label: 'Forward' },
  { value: 'door', label: 'Door' },
  { value: 'gate', label: 'Gate' },
  { value: 'secureEntry', label: 'Secure Entry' },
  { value: 'oob', label: 'OOB' },
  { value: 'callIdMask', label: 'Caller ID Masking' },
  { value: 'unassigned', label: 'Unassigned' },
  { value: 'tollFree', label: 'Toll Free' },
];

const typeOptions = [
  { label: 'Contact ID', value: 'contactId' },
  { label: 'Modem 3', value: 'modem3', disabledItem: true },
];

const operationOptions = [
  { label: 'Test', value: 'Test' },
  { label: 'Live', value: 'Live', disabledItem: true },
];

const PortNumberDetail = () => {
  const { selectedPort, edgeDeviceDetail, handlePortNumberDetail } =
    useEdgeDeviceDetailContext();

  const portsOption =
    edgeDeviceDetail?.obiDocs
      ?.flatMap((obi) => [
        { value: obi?.port1?.boxPortNumber, label: obi?.port1?.boxPortNumber },
        { value: obi?.port2?.boxPortNumber, label: obi?.port2?.boxPortNumber },
      ])
      .filter((portOpt) => portOpt?.value !== selectedPort?.boxPortNumber) ||
    [];

  const [formValues, setFormValues] = useState({
    number: '',
    type: 'unassigned',
    label: '',
    company: 'Granite_Unassigned',
    forwardUris: '',
    emails: [],
    fallbackEmails: [],
    confirmationEmail: '',
    spoofType: '',
    spoofIncomingCid: false,
    numDigits: 1,
    cnam: false,
    callerIdName: '',
    callerIdMasking: false,
    callerIdMaskingNumber: '',
    blockInboundCalls: false,
    blockCallerId: false,
    inbandRoute: false,
    route: 'notSpecified',
    voicemailEmails: [],
    voicemailSMS: [],
    greetingFile: null,
    ringOrder: [],
    advancedModemConfiguration: false,
    alarmRelayStatus: false,
    customPortConfig: false,
    advancedRouting: false,
    callerIdMaskingAccess: false,
    savefaxes: false,
    onlyEfax: false,
    faxDeliveryConfirmation: false,
    callIdOverride: false,
    alarmProtocolSelection: '',
    operationMode: '',
    disableCompanionPort: false,
  });

  const [uriList, setUriList] = useState([]);
  const [emails, setEmails] = useState([]);
  const [fallbackEmails, setFallbackEmails] = useState([]);

  const [voiceEmailInput, setVoiceEmailInput] = useState('');
  const [voiceSmsInput, setVoiceSmsInput] = useState('');
  const [uploadedFile, setUploadedFile] = useState(null);
  const [errors, setErrors] = useState({});

  const handleChange = (field, value) => {
    setFormValues((prev) => ({ ...prev, [field]: value }));
  };

  useEffect(() => {
    if (!selectedPort) return;
    const assigned = selectedPort.assignedNumberDoc || {};
    const callerIdName = assigned.callerIdName || '';
    setFormValues((prev) => ({
      ...prev,
      number: assigned?.number || '',
      type: assigned?.type || 'unassigned',
      label: assigned?.portLabel || '',
      company: selectedPort?.companyDoc?.name || 'Granite_Unassigned',
      forwardUris: '',
      callerIdName,
      cnam: !!callerIdName,
      cid: assigned?.cid,
      blockCallerId: !!assigned?.blockCallerId,
      spoofIncomingCid: !!assigned?.spoofIncomingCid,
      spoofType: assigned?.spoofType || '',
      numDigits: assigned?.numDigits || 1,
      voicemailEmails: assigned?.emails || [],
      voicemailSMS: assigned?.forwardNumbers || [],
      emails: assigned?.emails || [],
      fallbackEmails: assigned?.fallbackEmails || [],
      confirmationEmail: assigned?.confirmationEmail || '',
      callerIdMasking: !!assigned?.callerIdMasking,
      callerIdMaskingNumber: assigned?.callerIdMaskingNumber || '',
      ringOrder: assigned?.huntPorts || [],
      advancedModemConfiguration: selectedPort?.advancedModemConfiguration,
      advancedRouting: selectedPort?.advancedRouting,
      callerIdMaskingAccess: selectedPort?.callerIdMaskingAccess,
      savefaxes: assigned?.savefaxes,
      onlyEfax: assigned?.onlyEfax,
      faxDeliveryConfirmation: assigned?.faxDeliveryConfirmation,
      callIdOverride: selectedPort?.callIdOverride,
    }));
    setUriList(assigned?.forwardUris || []);
    setEmails(assigned?.emails || []);
    setFallbackEmails(assigned?.fallbackEmails || []);
  }, [selectedPort]);

  const usedToType = [
    'alarm',
    'fireAlarm',
    'modem',
    'secureEntry',
    'elevator',
    'modemHunt',
  ];

  const numberId = selectedPort?.assignedNumberDoc?._id;

  const { data: vmailFileData, isLoading } = useGet({
    api: APIS.EPIKV2,
    endpoint: numberId ? `numbers/vmail-file/${numberId}` : '',
    responseType: 'blob',
    enabled: false, // need to create the endpoint for this
    retry: false,
    refetchOnWindowFocus: false,
  });

  const greetingUrl = useMemo(() => {
    if (uploadedFile) return URL.createObjectURL(uploadedFile);
    if (vmailFileData) return URL.createObjectURL(vmailFileData);
    return null;
  }, [uploadedFile, vmailFileData]);

  const addVoiceEmail = () => {
    const val = normalizeEmail(voiceEmailInput);
    if (!val) return;
    if (!isValidEmail(val)) {
      setErrors((e) => ({ ...e, email: 'Invalid email address' }));
      return;
    }
    setErrors((e) => ({ ...e, email: undefined }));
    const newList = addUniqueItem(formValues.voicemailEmails, val);
    handleChange('voicemailEmails', newList);
    setVoiceEmailInput('');
  };

  const addVoiceSms = () => {
    const val = voiceSmsInput.trim();
    if (!val) return;
    if (!isValidPhone(val)) {
      setErrors((e) => ({ ...e, sms: 'Invalid phone number' }));
      return;
    }
    setErrors((e) => ({ ...e, sms: undefined }));
    const newList = addUniqueItem(formValues.voicemailSMS, val);
    handleChange('voicemailSMS', newList);
    setVoiceSmsInput('');
  };

  const uploadProps = {
    beforeUpload: (file) => {
      setUploadedFile(file);
      handleChange('greetingFile', file);
      return false;
    },
    onRemove: () => {
      setUploadedFile(null);
      handleChange('greetingFile', null);
    },
    fileList: uploadedFile
      ? [{ uid: '1', name: uploadedFile.name, status: 'done' }]
      : [],
    accept: 'audio/*',
    maxCount: 1,
  };

  return (
    <Flex
      vertical
      style={{
        flex: 1,
        padding: '16px',
        borderRadius: '12px',
        background: '#fff',
        marginTop: '16px',
      }}
    >
      {/* Header */}
      <Flex justify="space-between" align="center" style={{ marginBottom: 16 }}>
        <Typography className="heading-four-bold">
          {formValues.number ? 'Edit Number' : 'Add New Number'}
        </Typography>
        <Flex gap={8} align="center">
          <Typography className="heading-five-bold">
            Assign New Number
          </Typography>
          <Toggle size="small" />
          <Typography className="heading-five-bold">Port Number</Typography>
        </Flex>
      </Flex>
      <Divider className="divider-sm" />

      {/* Row 1 */}
      <Row gutter={[16, 24]} style={{ marginTop: '24px' }}>
        <Col xs={24} sm={12} md={12} lg={8}>
          <Flex vertical gap={8}>
            <Typography
              className="small-text"
              style={{ color: 'var(--primary-gray)' }}
            >
              Number
            </Typography>
            <Input
              placeholder="Select Number"
              value={formValues.number}
              onChange={(e) => handleChange('number', e.target.value)}
              className="small-text"
            />
          </Flex>
        </Col>
        <Col xs={24} sm={12} md={12} lg={8}>
          <Flex vertical gap={8}>
            <Typography
              className="small-text"
              style={{ color: 'var(--primary-gray)' }}
            >
              Type
            </Typography>
            <Select
              value={formValues.type}
              options={options}
              onChange={(value) => handleChange('type', value)}
            />
          </Flex>
        </Col>
        <Col xs={24} sm={12} md={12} lg={8}>
          <Flex vertical gap={8}>
            <Typography
              className="small-text"
              style={{ color: 'var(--primary-gray)' }}
            >
              Label
            </Typography>
            <Input
              placeholder="--"
              value={formValues.label}
              onChange={(e) => handleChange('label', e.target.value)}
              className="text-medium-regular"
            />
          </Flex>
        </Col>
      </Row>

      {/* Row 2 */}
      <Row gutter={[16, 24]} style={{ marginTop: '24px' }}>
        <Col xs={24} sm={12} md={12} lg={8}>
          <Flex vertical gap={8}>
            <Typography className="small-text">Company</Typography>
            <Input
              placeholder="Company"
              value={formValues.company}
              onChange={(e) => handleChange('company', e.target.value)}
              className="text-small-regular"
              disabled
            />
          </Flex>
        </Col>
        <Col xs={24} sm={12} md={12} lg={8}>
          <Flex vertical gap={8}>
            <Typography
              className="small-text"
              style={{ color: 'var(--primary-gray)' }}
            >
              Route
            </Typography>
            <Select
              value={formValues.route}
              options={[{ value: 'notSpecified', label: 'Not Specified' }]}
              onChange={(value) => handleChange('route', value)}
            />
          </Flex>
        </Col>
        <Col xs={24} sm={12} md={12} lg={8}>
          <Flex vertical gap={8}>
            <Typography className="small-text">Allowed User(s)</Typography>
            <Input
              placeholder="Specify"
              value={
                formValues?.allowedUsers ? formValues?.allowedUsers[0] : ''
              }
              onChange={(e) => handleChange('allowedUsers', e.target.value)}
              className="text-medium-regular"
              disabled
            />
          </Flex>
          {formValues?.allowedUsers?.map((item, idx) => (
            <React.Fragment key={idx}>
              <Flex justify="space-between" align="center">
                <Typography className="small-text">{item?.email}</Typography>
              </Flex>
              {formValues?.forwardNumbers?.length !== idx + 1 && (
                <Divider className="divider-sm" />
              )}
            </React.Fragment>
          ))}
        </Col>
      </Row>

      {formValues.type === 'vmail' && (
        <Row gutter={[16, 24]} style={{ marginTop: '24px' }}>
          <Col span={8}>
            <Typography className="small-text">
              Voicemail Recording Notification email(s)
            </Typography>
            <Input
              placeholder="Add email and press Enter"
              value={voiceEmailInput}
              onChange={(e) => setVoiceEmailInput(e.target.value)}
              onPressEnter={addVoiceEmail}
              className="text-medium-regular"
              addonAfter={
                <Button
                  onClick={addVoiceEmail}
                  disabled={!voiceEmailInput.trim()}
                  size="small"
                >
                  Add
                </Button>
              }
            />
            {errors.email && (
              <Typography className="text-medium-regular" type="danger">
                {errors.email}
              </Typography>
            )}
            <Flex
              vertical
              style={{ marginTop: 8, background: '#f2f4f7', padding: 8 }}
            >
              {formValues.voicemailEmails.map((item, idx) => (
                <Flex key={idx} justify="space-between" align="center">
                  <Typography>{item}</Typography>
                  <CloseTwoIcon
                    onClick={() =>
                      handleChange(
                        'voicemailEmails',
                        formValues.voicemailEmails.filter((_, i) => i !== idx),
                      )
                    }
                  />
                </Flex>
              ))}
            </Flex>
          </Col>
          <Col span={8}>
            <Typography className="small-text">
              Voicemail Recording Notification SMS text number(s)
            </Typography>
            <Input
              placeholder="Add SMS and press Enter"
              value={voiceSmsInput}
              onChange={(e) => setVoiceSmsInput(e.target.value)}
              onPressEnter={addVoiceSms}
              className="text-medium-regular"
              addonAfter={
                <Button
                  onClick={addVoiceSms}
                  disabled={!voiceSmsInput.trim()}
                  size="small"
                >
                  Add
                </Button>
              }
            />
            {errors.sms && (
              <Typography className="text-medium-regular" type="danger">
                {errors.sms}
              </Typography>
            )}
            <Flex
              vertical
              style={{ marginTop: 8, background: '#f2f4f7', padding: 8 }}
            >
              {formValues.voicemailSMS.map((item, idx) => (
                <Flex key={idx} justify="space-between" align="center">
                  <Typography>{item}</Typography>
                  <CloseTwoIcon
                    onClick={() =>
                      handleChange(
                        'voicemailSMS',
                        formValues.voicemailSMS.filter((_, i) => i !== idx),
                      )
                    }
                  />
                </Flex>
              ))}
            </Flex>
          </Col>
          <Col span={8}>
            <Flex vertical>
              <Typography className="small-text">Greeting File</Typography>
              <Upload {...uploadProps}>
                <Button variant="outlined" size="small">
                  Upload Audio
                </Button>
              </Upload>

              {isLoading ? (
                <Typography style={{ marginTop: 8 }}>Loading…</Typography>
              ) : greetingUrl ? (
                <audio
                  controls
                  src={greetingUrl}
                  style={{ marginTop: 8, width: '100%', height: 35 }}
                />
              ) : (
                <Typography
                  className="text-medium-regular"
                  style={{ marginTop: 8, color: '#667085' }}
                >
                  No Greeting File Found
                </Typography>
              )}
            </Flex>
          </Col>
        </Row>
      )}

      {formValues?.type === 'fax' && (
        <Row gutter={[16, 24]} style={{ marginTop: '24px' }}>
          <Col xs={24} sm={12} md={12} lg={12}>
            <Flex vertical gap={8}>
              <Typography
                className="small-text"
                style={{ color: 'var(--primary-gray)' }}
              >
                Email(s)
              </Typography>
              <Input
                placeholder="Specify"
                value={formValues?.emails}
                onChange={(e) => handleChange('forwardUris', e.target.value)}
                className="text-medium-regular"
              />
              <Typography className="extra-small-text">
                Type a value and <strong>enter</strong> to add to the list
              </Typography>
            </Flex>
            <Flex
              vertical
              style={{
                background: '#F2F4F7',
                borderRadius: '8px',
                padding: '10px 14px 16px 14px',
                marginTop: '16px',
              }}
            >
              {emails?.map((item, idx) => (
                <React.Fragment key={idx}>
                  <Flex justify="space-between" align="center">
                    <Typography className="small-text">{item}</Typography>
                    <CloseTwoIcon
                      onClick={() =>
                        setEmails(uriList?.filter((_, i) => i !== idx))
                      }
                      style={{ cursor: 'pointer' }}
                    />
                  </Flex>
                  {emails?.length !== idx + 1 && (
                    <Divider className="divider-sm" />
                  )}
                </React.Fragment>
              ))}
            </Flex>
          </Col>

          <Col xs={24} sm={12} md={12} lg={12}>
            <Flex vertical gap={8}>
              <Typography
                className="small-text"
                style={{ color: 'var(--primary-gray)' }}
              >
                Fallback email(s)
              </Typography>
              <Input
                placeholder="Specify"
                value={formValues?.fallbackEmails}
                onChange={(e) => handleChange('forwardUris', e.target.value)}
                className="text-medium-regular"
              />
              <Typography className="extra-small-text">
                Type a value and <strong>enter</strong> to add to the list
              </Typography>
            </Flex>
            <Flex
              vertical
              style={{
                background: '#F2F4F7',
                borderRadius: '8px',
                padding: '10px 14px 16px 14px',
                marginTop: '16px',
              }}
            >
              {fallbackEmails?.map((item, idx) => (
                <React.Fragment key={idx}>
                  <Flex justify="space-between" align="center">
                    <Typography className="small-text">{item}</Typography>
                    <CloseTwoIcon
                      onClick={() =>
                        setFallbackEmails(uriList?.filter((_, i) => i !== idx))
                      }
                      style={{ cursor: 'pointer' }}
                    />
                  </Flex>
                  {fallbackEmails?.length !== idx + 1 && (
                    <Divider className="divider-sm" />
                  )}
                </React.Fragment>
              ))}
            </Flex>
          </Col>
        </Row>
      )}

      {formValues?.type === 'dialout' && (
        <>
          <Row gutter={[16, 24]} style={{ marginTop: '24px' }}>
            <Col xs={24} sm={12} md={12} lg={12}>
              <Flex vertical gap={8}>
                <Typography className="small-text">
                  Allowed number(s)
                </Typography>
                <Input
                  placeholder="Allowed number"
                  value={
                    formValues.allowedUsers ? formValues.allowedUsers[0] : ''
                  }
                  onChange={(e) => handleChange('allowedUsers', e.target.value)}
                  className="text-small-regular"
                />
              </Flex>
              <Flex
                vertical
                style={{
                  background: '#F2F4F7',
                  borderRadius: '8px',
                  padding: '10px 14px 16px 14px',
                  marginTop: '16px',
                }}
              >
                {formValues?.allowedUsers?.map((item, idx) => (
                  <React.Fragment key={idx}>
                    <Flex justify="space-between" align="center">
                      <Typography className="small-text">{item}</Typography>
                    </Flex>
                    {formValues?.allowedUsers?.length !== idx + 1 && (
                      <Divider className="divider-sm" />
                    )}
                  </React.Fragment>
                ))}
              </Flex>
            </Col>
            <Col xs={24} sm={12} md={12} lg={12}>
              <Flex vertical gap={8}>
                <Typography className="small-text">Forward number</Typography>
                <Input
                  placeholder="forwardNumbers"
                  value={
                    formValues?.forwardNumbers
                      ? formValues?.forwardNumbers[0]
                      : ''
                  }
                  onChange={(e) =>
                    handleChange('forwardNumbers', e.target.value)
                  }
                  className="text-small-regular"
                />
              </Flex>
            </Col>
          </Row>
          <Row gutter={[16, 24]} style={{ marginTop: '24px' }}>
            <Col xs={24} sm={12} md={12} lg={6}>
              <Flex vertical gap={8}>
                <Typography className="small-text">Caller ID</Typography>
                <Input
                  placeholder="Caller ID"
                  value={formValues?.cid}
                  onChange={(e) => handleChange('cid', e.target.value)}
                  className="text-small-regular"
                />
              </Flex>
            </Col>
            <Col xs={24} sm={12} md={12} lg={4}>
              <Flex justify="space-between" style={{ marginTop: '32px' }}>
                <Checkbox
                  checked={formValues?.spoofIncomingCid}
                  onChange={(e) =>
                    handleChange('spoofIncomingCid', e.target.checked)
                  }
                >
                  <Typography className="text-medium-regular">
                    Spoof cid
                  </Typography>
                </Checkbox>
              </Flex>
            </Col>
            <Col xs={24} sm={12} md={12} lg={8}>
              <Flex justify="space-between" style={{ marginTop: '32px' }}>
                <Radio.Group
                  options={[
                    {
                      label: (
                        <Typography className="text-medium-regular">
                          prepend
                        </Typography>
                      ),
                      value: 'prepend',
                    },
                    {
                      label: (
                        <Typography className="text-medium-regular">
                          append
                        </Typography>
                      ),
                      value: 'append',
                    },
                    {
                      label: (
                        <Typography className="text-medium-regular">
                          Same called number
                        </Typography>
                      ),
                      value: 'same',
                    },
                  ]}
                  // onChange={onChangeGroup}
                  value={formValues?.spoofType}
                  className="text-medium-regular"
                />
              </Flex>
            </Col>
            <Col xs={24} sm={12} md={12} lg={6}>
              <Flex vertical gap={8}>
                <Typography className="small-text">Number of digits</Typography>
                <Input
                  placeholder="Number of digits"
                  value={formValues?.numDigits}
                  onChange={(e) => handleChange('numDigits', e.target.value)}
                  className="text-small-regular"
                />
              </Flex>
            </Col>
          </Row>
        </>
      )}

      {formValues?.type === 'fax' && (
        <Row gutter={[16, 24]} style={{ marginTop: '24px' }}>
          <Col xs={24} sm={12} md={12} lg={8}>
            <Flex vertical gap={8}>
              <Typography className="small-text">Confirmation Email</Typography>
              <Input
                placeholder="Specify"
                value={formValues?.confirmationEmail}
                onChange={(e) =>
                  handleChange('confirmationEmail', e.target.value)
                }
                className="text-medium-regular"
                disabled
              />
            </Flex>
          </Col>
          <Col xs={24} sm={12} md={12} lg={8}>
            <Flex vertical gap={8}>
              <Typography
                className="small-text"
                style={{ color: 'var(--primary-gray)' }}
              >
                {formValues?.type === 'ringDown'
                  ? 'Ring Down Number'
                  : 'Forward Uri(s)'}
              </Typography>
              <Input
                placeholder="Specify"
                value={formValues.forwardUris}
                onChange={(e) => handleChange('forwardUris', e.target.value)}
                className="text-medium-regular"
              />
              <Typography className="extra-small-text">
                Type a value and <strong>enter</strong> to add to the list
              </Typography>
            </Flex>
          </Col>
          <Col xs={24} sm={12} md={12} lg={8}>
            <Flex
              vertical
              style={{
                background: '#F2F4F7',
                borderRadius: '8px',
                padding: '10px 14px 16px 14px',
                marginTop: '16px',
              }}
            >
              {uriList?.map((item, idx) => (
                <React.Fragment key={idx}>
                  <Flex justify="space-between" align="center">
                    <Typography className="small-text">{item}</Typography>
                    <CloseTwoIcon
                      onClick={() =>
                        setUriList(uriList?.filter((_, i) => i !== idx))
                      }
                      style={{ cursor: 'pointer' }}
                    />
                  </Flex>
                  {uriList?.length !== idx + 1 && (
                    <Divider className="divider-sm" />
                  )}
                </React.Fragment>
              ))}
            </Flex>
          </Col>
        </Row>
      )}

      {formValues?.type === 'forward' && (
        <Row gutter={[16, 24]} style={{ marginTop: '24px' }}>
          <Col xs={24} sm={12} md={12} lg={8}>
            <Flex vertical gap={8}>
              <Typography className="small-text">Forward Number</Typography>
              <Input
                placeholder="Specify"
                value={
                  formValues?.forwardNumbers
                    ? formValues?.forwardNumbers[0]
                    : ''
                }
                onChange={(e) => handleChange('forwardNumbers', e.target.value)}
                className="text-medium-regular"
                disabled
              />
            </Flex>
            {formValues?.forwardNumbers?.map((item, idx) => (
              <React.Fragment key={idx}>
                <Flex justify="space-between" align="center">
                  <Typography className="small-text">{item}</Typography>
                </Flex>
                {formValues?.forwardNumbers?.length !== idx + 1 && (
                  <Divider className="divider-sm" />
                )}
              </React.Fragment>
            ))}
          </Col>
        </Row>
      )}

      {/* Last rows for toggles and checkboxes */}
      <Row gutter={[32, 24]} style={{ marginTop: '24px' }}>
        {formValues?.advancedRouting &&
          selectedPort.assignedNumberDoc?.carrier == 'inteliquent' &&
          !['fax', 'faxStandard', 'vmail'].includes(formValues?.type) && (
            <Col xs={24} sm={12} md={12} lg={6}>
              <Flex gap={8}>
                <Typography className="text-medium-regular">
                  Inband Route
                </Typography>
                <Toggle
                  size="small"
                  checked={formValues.inbandRoute}
                  onChange={(checked) => handleChange('inbandRoute', checked)}
                />
              </Flex>
            </Col>
          )}
        <Col xs={24} sm={12} md={12} lg={6}>
          <Flex justify="space-between" align="center">
            <Typography className="text-medium-regular">
              Allow Caller ID Masking
            </Typography>
            <Toggle
              size="small"
              checked={formValues?.callerIdMaskingNumber}
              onChange={(checked) =>
                handleChange('callerIdMaskingNumber', checked)
              }
            />
          </Flex>
        </Col>
        <Col xs={24} sm={12} md={12} lg={6}>
          <Flex gap={16} justify="space-between" align="center">
            <Typography className="text-medium-regular">
              Block Inbound Calls
            </Typography>
            <Toggle
              size="small"
              checked={formValues?.inBoundCallBlocked}
              onChange={(checked) =>
                handleChange('inBoundCallBlocked', checked)
              }
            />
          </Flex>
        </Col>
        <Col xs={24} sm={12} md={12} lg={6}>
          <Flex justify="space-between" align="center">
            <Typography className="text-medium-regular">
              Block Caller ID
            </Typography>
            <Toggle
              size="small"
              checked={formValues?.blockCallerId}
              onChange={(checked) => handleChange('blockCallerId', checked)}
            />
          </Flex>
        </Col>
        <Col xs={24} sm={12} md={12} lg={6}>
          <Flex justify="space-between" align="center">
            <Typography className="text-medium-regular">
              Enable Caller ID Display
            </Typography>
            <Toggle
              size="small"
              checked={formValues?.callIdOverride}
              onChange={(checked) => handleChange('callIdOverride', checked)}
            />
          </Flex>
        </Col>

        {!selectedPort?.myEpik &&
          selectedPort.assignedNumberDoc?.linkedBox &&
          (formValues?.type === 'fireAlarm' ||
            formValues?.type === 'alarm') && (
            <Col xs={24} sm={12} md={12} lg={6}>
              <Flex align="center" justify="space-between">
                <Typography className="text-medium-regular">
                  Enable Alarm Protocol Relay
                </Typography>
                <Toggle
                  size="small"
                  checked={formValues?.alarmRelayStatus}
                  onChange={(checked) =>
                    handleChange('alarmRelayStatus', checked)
                  }
                />
              </Flex>
            </Col>
          )}

        {formValues?.type === 'fax' && (
          <>
            <Col xs={24} sm={12} md={12} lg={6}>
              <Flex align="center" justify="space-between">
                <Typography className="text-medium-regular">
                  Epik Cloud Based Fax Storages
                </Typography>
                <Toggle
                  size="small"
                  checked={formValues?.saveFaxes}
                  onChange={(checked) => handleChange('saveFaxes', checked)}
                />
              </Flex>
            </Col>
            <Col xs={24} sm={12} md={12} lg={6}>
              <Flex align="center" justify="space-between">
                <Typography className="text-medium-regular">
                  eFax Only
                </Typography>
                <Toggle
                  size="small"
                  checked={formValues?.onlyEfax}
                  onChange={(checked) => handleChange('onlyEfax', checked)}
                />
              </Flex>
            </Col>
            <Col xs={24} sm={12} md={12} lg={6}>
              <Flex align="center" justify="space-between">
                <Typography className="text-medium-regular">
                  Delivery Confirmations
                </Typography>
                <Toggle
                  size="small"
                  checked={formValues?.faxDeliveryConfirmation}
                  onChange={(checked) =>
                    handleChange('faxDeliveryConfirmation', checked)
                  }
                />
              </Flex>
            </Col>
          </>
        )}
      </Row>
      {formValues?.alarmRelayStatus && (
        <Row gutter={[16, 24]} style={{ marginTop: '24px' }}>
          <Col xs={24} sm={12} md={6} lg={6}>
            <Flex vertical gap={8}>
              <Typography
                className="small-text"
                style={{ color: 'var(--primary-gray)' }}
              >
                Alarm Protocol Selection
              </Typography>
              <Select
                value={formValues?.alarmProtocolSelection}
                options={typeOptions}
                onChange={(value) =>
                  handleChange('alarmProtocolSelection', value)
                }
                className="small-text"
              />
            </Flex>
          </Col>
          <Col xs={24} sm={12} md={6} lg={6}>
            <Flex vertical gap={8}>
              <Typography
                className="small-text"
                style={{ color: 'var(--primary-gray)' }}
              >
                Operation Mode
              </Typography>
              <Select
                value={formValues?.operationMode}
                options={operationOptions}
                onChange={(value) => handleChange('operationMode', value)}
                className="small-text"
              />
            </Flex>
          </Col>
          <Col xs={24} lg={6} style={{ marginTop: '30px' }}>
            <Checkbox
              checked={formValues.disableCompanionPort}
              onChange={(e) =>
                handleChange('disableCompanionPort', e.target.checked)
              }
            >
              <Typography className="text-medium-regular">
                Disable Companion Port
              </Typography>
            </Checkbox>
          </Col>
          <Col xs={24} sm={12} md={6} lg={6}>
            {formValues.disableCompanionPort && (
              <Flex vertical gap={8}>
                <Typography
                  className="small-text"
                  style={{ color: 'var(--primary-gray)' }}
                >
                  Select Port
                </Typography>
                <Select
                  value={formValues?.port}
                  options={portsOption}
                  onChange={(value) => handleChange('port', value)}
                />
              </Flex>
            )}
          </Col>

          <Col xs={24} lg={6}>
            <Button onClick={() => handlePortNumberDetail(false)}>
              Initiate Test
            </Button>
          </Col>

          <Col xs={24} lg={6}>
            <Flex vertical>
              <Typography
                className="small-text"
                style={{ color: 'var(--primary-gray)', fontWeight: 'bold' }}
              >
                Initiated Time:
              </Typography>
              <Typography
                className="small-text"
                style={{ color: 'var(--primary-gray)' }}
              >
                --- {/* Sep 03, 2025 10:17AM PDT */}
              </Typography>
            </Flex>
          </Col>

          <Col xs={24} lg={6} style={{ marginTop: 8 }}>
            <Checkbox
              checked={formValues.alarmTestPassed}
              onChange={(e) =>
                handleChange('alarmTestPassed', e.target.checked)
              }
            >
              <Typography className="text-medium-regular">
                Alarm Test Passed
              </Typography>
            </Checkbox>
          </Col>
          <Col xs={24} lg={6} style={{ marginTop: 8 }}>
            <Flex align="center" gap={4}>
              <Typography className="text-medium-regular">Time:</Typography>
              <AprStatusBadg type="time" value="04:15" />
            </Flex>
            {/* <AprStatusBadg type="error" label="Try Again" />
                <AprStatusBadg type="success" label="Live" /> */}
          </Col>
        </Row>
      )}
      <Row gutter={[4, 24]} style={{ marginTop: '24px' }}>
        <Col xs={24} lg={6}>
          <Checkbox
            checked={formValues.cnam}
            onChange={(e) => handleChange('cnam', e.target.checked)}
          >
            <Typography className="text-medium-regular">
              Enable Customer Caller ID Name (CNAM)
            </Typography>
          </Checkbox>
        </Col>
        <Col xs={24} lg={6}>
          <Flex vertical gap={8}>
            <Checkbox
              checked={formValues?.customPortConfig}
              onChange={(e) =>
                handleChange('customPortConfig', e.target.checked)
              }
            >
              <Typography className="text-medium-regular">
                Custom Port Config Settings
              </Typography>
            </Checkbox>
          </Flex>
        </Col>
        {!selectedPort?.myEpik &&
          selectedPort.assignedNumberDoc?.linkedBox &&
          usedToType.includes(formValues?.type) && (
            <Col xs={24} lg={6}>
              <Flex align="center" gap={8}>
                <Checkbox
                  checked={formValues?.advancedModemConfiguration}
                  onChange={(e) =>
                    handleChange('advancedModemConfiguration', e.target.checked)
                  }
                />
                <Typography className="text-medium-regular">
                  Advanced Modem Configuration
                </Typography>
              </Flex>
            </Col>
          )}
      </Row>

      {/* Footer */}
      <Flex justify="end" style={{ marginTop: '24px' }} gap={8}>
        <Button
          variant="outlined"
          onClick={() => handlePortNumberDetail(false)}
        >
          Cancel
        </Button>
        <Button onClick={() => console.log('Save Form:', formValues)}>
          Assign Device
        </Button>
      </Flex>
    </Flex>
  );
};

export default PortNumberDetail;
