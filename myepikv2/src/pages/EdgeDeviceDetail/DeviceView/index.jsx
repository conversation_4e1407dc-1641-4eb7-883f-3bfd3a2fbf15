import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  Input,
  Flex,
  Button,
  Typography,
  Checkbox,
  Toggle,
  EditTwoIcon,
  StarIcon,
  CloseTwoIcon,
  CloseIcon,
} from '@/components';
import { Divider, Row, Col } from 'antd';
import { useStore } from '@/store';
import './styles.css';
import { useEpikBoxById } from '@/graphqlHooks/epikv2-api/useEpikBoxById';
import { formatTimestamp } from '@/utils/date';

const Field = ({ label, component, error, isEditMode }) => (
  <Flex vertical gap={8}>
    <Typography className="small-text" style={{ color: '#344054' }}>
      {label}
    </Typography>
    {component}
    {error && !isEditMode && (
      <Typography className="small-text" style={{ color: 'var(--error-600)' }}>
        {error}
      </Typography>
    )}
  </Flex>
);

// Add PropTypes for Field Component
Field.propTypes = {
  label: PropTypes.string.isRequired, // `label` is a required string
  component: PropTypes.node.isRequired, // `component` is a required React node
  error: PropTypes.string || PropTypes.null || PropTypes.undefined,
  isEditMode: PropTypes.bool,
};

const dialPlanValue = (advancedRouting, myEpik, boxRegistrar) => {
  if (advancedRouting) {
    return 'Enhanced Routing';
  } else {
    if (myEpik) {
      return 'Standard';
    } else {
      if (boxRegistrar?.toLowerCase() === 'unified') {
        return 'Unified';
      } else if (boxRegistrar?.toLowerCase() === 'midregistrar') {
        return 'Load Balancer';
      }
    }
  }
};

const DeviceView = ({ mode, edgeDeviceDetail = {} }) => {
  const [isEditMode, setIsEditMode] = useState(mode === 'edit');
  const [formValues, setFormValues] = useState({});
  const [emailList, setEmailList] = useState([]);
  const [smsList, setSmsList] = useState([]);
  const [secondaryNotification, setSecondaryNotification] = useState(false);

  const modemInfoError = useStore(
    (s) => s.error.errorState[`sub:${'modemInfo'}`] || null,
  );

  const { closeDrawer } = useStore((state) => state.drawer);

  const { data } = useEpikBoxById({
    input: { id: edgeDeviceDetail?._id },
    keepPreviousData: false,
    queryRun: 'deviceView',
    enabled: !!edgeDeviceDetail?._id,
  });

  useEffect(() => {
    if (data) {
      const {
        modemInfo = {},
        modems = [],
        lteIp,
        lteIp2,
        activeLTE = {},
        advancedRouting = false,
        myEpik = false,
        boxRegistrar = false,
        creationDate = '',
        customerProvidedIp = '',
        apuType,
        ...rest
      } = data?.EpikBoxById || {};

      const formattedTime = formatTimestamp(activeLTE?.[1]?.timeStamp, {
        format: 'MMM D, YYYY hh:mm:ss A',
      });

      const formatedCreationDate = formatTimestamp(creationDate, {
        format: 'MMM D,YYYY hh:mm A',
      });

      const modem = modems[0] || {};
      const imei = modem.imeis || '';
      const sim1 = modem.sims?.[0] || '';
      const sim2 = modem.sims?.[1] || '';
      const mtn1 = modem.phones?.[0] || '';
      const mtn2 = modem.phones?.[1] || '';
      const activeSim = activeLTE?.[1]?.sim || '';
      const activeSimIpAddress = activeLTE?.[1]?.ip || '';
      const timeStamp = formattedTime || '';
      const dialPlan = dialPlanValue(advancedRouting, myEpik, boxRegistrar);

      setFormValues({
        ...rest,
        modemType: modemInfo?.model || '',
        imei: modemInfo?.imei || '',
        sim1,
        sim2,
        imei1: imei,
        imei2: imei,
        mtn1,
        mtn2,
        lteIp,
        lteIp2,
        activeSim,
        activeSimIpAddress,
        timeStamp,
        dialPlan,
        creationDate: formatedCreationDate,
        customerProvidedIp,
        apuType,
      });
    }
  }, [edgeDeviceDetail, data?.EpikBoxById]);

  const handleChange = (field, value) => {
    setFormValues((prev) => ({ ...prev, [field]: value }));
  };

  const handleKeyDown = (field, value, event) => {
    if (event.key === 'Enter' && value.trim()) {
      if (field === 'alertEmail') {
        setEmailList((prev) => [...prev, value]);
        handleChange(field, ''); // Clear input
      } else if (field === 'alertSms') {
        setSmsList((prev) => [...prev, value]);
        handleChange(field, ''); // Clear input
      }
    }
  };

  const handleToggleEditMode = () => setIsEditMode(true);

  const sections = [
    {
      fields: [
        {
          name: 'displayName',
          label: 'Display Name',
          placeholder: 'Enter Name',
          span: 8,
        },
        {
          name: 'serialNumber',
          label: 'Serial Number',
          placeholder: 'Choose/Type',
          span: 8,
        },
        {
          name: 'fwVersion',
          label: 'Firmware',
          placeholder: 'Specify',
          span: 4,
        },
        {
          name: 'dialPlan',
          label: 'Dial Plan',
          placeholder: 'Specify',
          span: 4,
        },
        {
          name: 'creationDate',
          label: 'Created On',
          placeholder: 'Specify',
          span: 8,
        },
        {
          name: 'customerProvidedIp',
          label: 'Static IP Address',
          placeholder: 'Specify',
          span: 8,
        },
        {
          name: 'edgeDeviceModel',
          label: 'Edge Device Model',
          placeholder: 'G3-8S20-A3e',
          span: 8,
        },
        {
          name: 'vpnAddress',
          label: 'EPIK Edge Address',
          placeholder: 'specify',
          span: 8,
        },
        { name: 'apuType', label: 'APU Type', placeholder: 'Specify', span: 4 },
      ],
    },
    {
      title: 'Modal Management',
      fields: [
        {
          name: 'modemType',
          label: 'Modem Type',
          placeholder: 'Filter Modem',
          error: modemInfoError,
          span: 8,
        },
        {
          name: 'imei',
          label: 'IMEI',
          placeholder: 'Enter',
          error: modemInfoError,
          span: 8,
        },
        { name: 'phone', label: 'Phone', placeholder: 'Enter', span: 8 },
      ],
    },
    {
      title: 'Active Sim',
      fields: [
        {
          name: 'activeSimStatus',
          label: 'Sim Status',
          placeholder: 'Active',
          span: 12,
        },
        { name: 'activeSim', label: 'SIM', placeholder: 'Specify', span: 12 },
        {
          name: 'activeSimIpAddress',
          label: 'IP Address',
          placeholder: 'Active',
          span: 12,
        },
        {
          name: 'timeStamp',
          label: 'Time stamp',
          placeholder: 'Jun 14, 2021 11:20:56 AM',
          span: 12,
        },
      ],
    },
    {
      title: 'SIM 1 Details',
      fields: [
        {
          name: 'sim1',
          label: 'SIM 1',
          placeholder: '',
          span: 12,
          extra: <Checkbox>Customer Provided Sim</Checkbox>,
        },
        {
          name: 'lteIp',
          label: 'LTE IP 1',
          placeholder: '',
          span: 12,
        },
        {
          name: 'imei1',
          label: 'IMEI',
          placeholder: '',
          error: modemInfoError,
          span: 12,
        },
        { name: 'mtn1', label: 'MTN', placeholder: '', span: 12 },
      ],
    },
    {
      title: 'SIM 2 Details',
      fields: [
        {
          name: 'sim2',
          label: 'SIM 2',
          placeholder: '',
          span: 12,
          extra: <Checkbox>Customer Provided Sim</Checkbox>,
        },
        {
          name: 'lteIp2',
          label: 'LTE IP 2',
          placeholder: '',
          span: 12,
        },
        {
          name: 'imei2',
          label: 'IMEI',
          placeholder: '',
          error: modemInfoError,
          span: 12,
        },
        { name: 'mtn2', label: 'MTN', placeholder: '', span: 12 },
      ],
    },
    {
      showDivider: true,
      fields: [
        {
          name: 'alertEmail',
          label: '911 Notification Alert Email',
          placeholder: 'Enter Email',
          span: 12,
          list: emailList,
        },
        {
          name: 'alertSms',
          label: '911 Notification Alert SMS Text Number(s)',
          placeholder: 'Enter Number',
          span: 12,
          list: smsList,
        },
      ],
    },
    {
      fields: [
        {
          name: 'secondaryNotification',
          span: 24,
          component: (
            <Flex gap={16} align="center">
              <Typography>Secondary Notification</Typography>
              <Toggle
                checked={secondaryNotification}
                onChange={() =>
                  isEditMode && setSecondaryNotification(!secondaryNotification)
                }
                size="small"
                disabled={!isEditMode}
              />
            </Flex>
          ),
        },
      ],
    },
  ];

  return (
    <Flex vertical>
      <Flex vertical>
        <Flex justify="space-between" align="center">
          <Typography className="heading-four-bold">
            {isEditMode && 'Edit'} Edge Device Details
          </Typography>
          <Flex gap={4}>
            <StarIcon />
            {!isEditMode && (
              <EditTwoIcon
                onClick={handleToggleEditMode}
                style={{ cursor: 'pointer' }}
              />
            )}
            <CloseIcon onClick={closeDrawer} style={{ cursor: 'pointer' }} />
          </Flex>
        </Flex>
        <Divider className="divider-sm" />
      </Flex>
      {sections.map((section, sectionIndex) => (
        <React.Fragment key={`section-${sectionIndex}`}>
          {(section?.title || section?.showDivider) && (
            <Flex vertical style={{ marginTop: '24px' }}>
              <Typography className="heading-five">{section.title}</Typography>
              <Divider className="divider-sm" />
            </Flex>
          )}
          <Row gutter={[8, 16]} style={{ marginTop: '16px' }}>
            {section.fields.map((field, fieldIndex) => (
              <Col
                key={`field-${sectionIndex}-${fieldIndex}`}
                span={field.span}
                xs={24}
                sm={12}
                md={12}
                lg={field.span}
                xl={field.span}
                xxl={field.span}
              >
                {Field({
                  label: field.label,
                  component: field.component || (
                    <Input
                      placeholder={field.placeholder}
                      value={formValues?.[field.name] || ''}
                      onChange={(e) => handleChange(field.name, e.target.value)}
                      disabled={!isEditMode}
                      onKeyDown={(e) =>
                        field.list &&
                        e.key === 'Enter' &&
                        handleKeyDown(field.name, formValues?.[field.name], e)
                      }
                      className="text-medium-regular"
                    />
                  ),
                  error: field?.error,
                  isEditMode: isEditMode,
                })}
                {field.list && (
                  <Flex
                    vertical
                    style={{
                      background: '#F2F4F7',
                      borderRadius: '8px',
                      padding: '10px 14px 16px 14px',
                      marginTop: '16px',
                    }}
                    justify="center"
                  >
                    {field.list.map((item, idx) => (
                      <>
                        <Flex
                          justify="space-between"
                          align="center"
                          key={idx}
                          style={{ width: '100%' }}
                        >
                          <Typography key={`${field.name}-${idx}`}>
                            {item}
                          </Typography>
                          <CloseTwoIcon
                            style={{ cursor: 'pointer' }}
                            onClick={() => {
                              // Update the list by removing the clicked item
                              if (field.name === 'alertEmail') {
                                setEmailList((prev) =>
                                  prev.filter((_, index) => index !== idx),
                                );
                              } else if (field.name === 'alertSms') {
                                setSmsList((prev) =>
                                  prev.filter((_, index) => index !== idx),
                                );
                              }
                            }}
                          />
                        </Flex>
                        {field.list.length !== idx + 1 && (
                          <Divider className="divider-sm" />
                        )}
                      </>
                    ))}
                  </Flex>
                )}

                {field.extra && (
                  <div style={{ marginTop: '8px' }}>{field.extra}</div>
                )}
              </Col>
            ))}
          </Row>
        </React.Fragment>
      ))}
      {isEditMode ? (
        <Flex vertical>
          <Divider className="divider-sm" />
          <Flex justify="flex-end" gap={8}>
            <Button onClick={() => console.log('Save Form:', formValues)}>
              Update
            </Button>
          </Flex>
        </Flex>
      ) : null}
    </Flex>
  );
};

DeviceView.propTypes = {
  mode: PropTypes.oneOf(['view', 'edit']).isRequired,
  edgeDeviceDetail: PropTypes.object.isRequired,
};

export default DeviceView;
