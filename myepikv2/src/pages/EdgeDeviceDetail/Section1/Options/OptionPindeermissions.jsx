import { useState, useEffect } from 'react';
import { Row, Col, Radio, Divider } from 'antd';
import { ReloadOutlined, CloudDownloadOutlined } from '@ant-design/icons';
import './styles.css';
import {
  Tooltip,
  Toggle,
  Input,
  Button,
  SaveIcon,
  PowerReload,
  WarningIcon,
  ReloadIcon,
  Select,
  Typography,
  Flex,
  InfoIcon,
} from '@/components';
import { useEdgeDeviceDetailContext } from '../../context';
import { formatTimestamp } from '@/utils/date';
import { timezoneOptions } from '@/constants';
import { useStore } from '@/store';

const formatLatency = (val) => {
  if (typeof val === 'string' && val.includes('Unable')) return 'Unavailable';
  const num = Number(val);
  return isNaN(num) ? 'Unavailable' : `${num}ms`;
};

const permissionsList = [
  {
    id: 1,
    name: 'ext_9override',
    label: 'Omit Leading 9',
    tooltip: 'Omit Leading 9',
  },
  { id: 2, name: 'monitor', label: 'Monitoring', tooltip: 'Monitoring' },
  {
    id: 3,
    name: 'enhancedFax',
    label: 'Enable Enhanced Fax',
    tooltip: 'Enable Enhanced Fax',
  },
  { id: 4, name: 'portAlerts', label: 'Ports Alert', tooltip: 'Ports Alert' },
  {
    id: 5,
    name: 'boxRegistrar',
    label: 'Enable Registration Offset',
    tooltip: 'Enable Registration Offset',
  },
  {
    id: 6,
    name: 'features?.dcAutoUpdate',
    label: 'Data Center Auto Select',
    tooltip: 'Data Center Auto Select',
  },
  { id: 7, name: 'starCodes', label: 'Star Codes', tooltip: 'Star Codes' },
  {
    id: 8,
    name: 'boxOfflineCallForward',
    label: 'Remote Call Overflow',
    tooltip: 'Remote Call Overflow',
  },
  {
    id: 9,
    name: 'features.showPriTab',
    label: 'Enable PRI | CAS:',
    tooltip: 'Enable PRI | CAS:',
  },
  {
    id: 10,
    name: 'recording',
    label: 'Enable Call Recording',
    tooltip: 'Enable Call Recording',
  },
  {
    id: 11,
    name: 'OBEnable',
    label: 'Enable Out of Band Management:',
    tooltip: 'Enable Out of Band Management',
  },
];

const OptionPermission = () => {
  const { edgeDeviceDetail } = useEdgeDeviceDetailContext();

  const [formState, setFormState] = useState({
    powerSaveOption: false,
    interfacePriority: '',
    permissions: {},
    APNFor: '',
    currentApn: '', // from box
    simPriority: '',
    timeZone: '',
    activeCarrier: '',
    selectedDataCenter: '',
    activeSim: '',
    epiTimezone: '',
  });

  const dcAvgPingError = useStore(
    (s) => s.error.errorState[`sub:${'dcAvgPing'}`] || null,
  );

  const simStatusError = useStore(
    (s) => s.error.errorState[`sub:${'simStatus'}`] || null,
  );

  useEffect(() => {
    if (edgeDeviceDetail) {
      const permissionsFromDevice = Object.fromEntries(
        permissionsList.map((p) => {
          const value = p.name.startsWith('features.')
            ? edgeDeviceDetail?.features?.[p.name.split('.')[1]]
            : edgeDeviceDetail?.[p.name];
          return [p.name, !!value];
        }),
      );

      const best = edgeDeviceDetail?.dcAvgPing?.bestDC?.toLowerCase();
      const selectedDC = dataCenterOptions?.find((opt) => opt?.value === best)
        ? best
        : '';

      setFormState((prev) => ({
        ...prev,
        permissions: permissionsFromDevice,
        selectedDataCenter: selectedDC,
        APNFor: edgeDeviceDetail?.APNFor || '',
        interfacePriority: edgeDeviceDetail?.priorityInterface || '',
        currentApn: edgeDeviceDetail?.currentApn || '',
        activeCarrier: edgeDeviceDetail?.activeCarrier || '',
        epiTimezone: edgeDeviceDetail?.epiTimezone || '',
        simPriority:
          edgeDeviceDetail?.primarySim === '1'
            ? '1'
            : edgeDeviceDetail?.primarySim === '2'
              ? '2'
              : '',
        activeSIM: edgeDeviceDetail?.simStatus
          ?.toLowerCase()
          ?.includes('backup')
          ? 'SIM 2'
          : 'SIM 1',
      }));
    }
  }, [edgeDeviceDetail]);

  const handleChange = (field, value) => {
    setFormState((prev) => ({ ...prev, [field]: value }));
  };

  const handlePermissionToggle = (name) => {
    setFormState((prev) => ({
      ...prev,
      permissions: {
        ...prev.permissions,
        [name]: !prev.permissions[name],
      },
    }));
  };

  const handleSaveInterface = () => {
    // Save logic can be implemented here
  };

  const dataCenterLatency = {
    CHICAGO: formatLatency(edgeDeviceDetail?.dcAvgPing?.chPingAvg),
    LOS_ANGELES: formatLatency(edgeDeviceDetail?.dcAvgPing?.laPingAvg),
    NEW_YORK: formatLatency(edgeDeviceDetail?.dcAvgPing?.nyPingAvg),
    ATLANTA: formatLatency(edgeDeviceDetail?.dcAvgPing?.atPingAvg),
    DALLAS: formatLatency(edgeDeviceDetail?.dcAvgPing?.dlPingAvg),
  };

  const dataCenterOptions = [
    { value: 'ny', label: `New York - ${dataCenterLatency.NEW_YORK}` },
    { value: 'dl', label: `Dallas - ${dataCenterLatency.DALLAS}` },
    { value: 'ch', label: `Chicago - ${dataCenterLatency.CHICAGO}` },
    { value: 'la', label: `Los Angeles - ${dataCenterLatency.LOS_ANGELES}` },
    { value: 'at', label: `Atlanta - ${dataCenterLatency.ATLANTA}` },
  ];

  const formattedTime = formatTimestamp(
    edgeDeviceDetail?.dcAvgPing?.timeUpdate,
    {
      format: 'hh:mmA | DD MMM YYYY',
    },
  );

  const nightlyUpdateTime = formatTimestamp(
    edgeDeviceDetail?.nightlyUpdateTime,
    {
      format: 'hh:mmA | DD MMM YYYY',
    },
  );

  const epiTimezoneTooltip = timezoneOptions?.find(
    (timezone) => formState?.epiTimezone === timezone?.value,
  );

  return (
    <div>
      <Row gutter={[16, 0]} align={'middle'}>
        <Col xs={24} sm={12} md={8} lg={6} style={{ marginTop: '12px' }}>
          <div className="toggle-wrapper">
            <Typography className="text-inter-400-14-20">
              Enable Power Save Mode
            </Typography>
            <Toggle
              checked={formState.powerSaveOption}
              onChange={() =>
                handleChange('powerSaveOption', !formState.powerSaveOption)
              }
              size="small"
            />
          </div>
        </Col>
        {formState.powerSaveOption && (
          <Col xs={24} sm={12} md={8} lg={6} style={{ marginTop: '12px' }}>
            <Select
              placeholder="Please Specify"
              className="full-width-select"
              options={[
                {
                  value: 'normalMode',
                  label: 'Normal Mode',
                },
              ]}
            />
          </Col>
        )}
      </Row>

      <Row gutter={[20, 16]} style={{ marginTop: '16px' }}>
        {permissionsList.map((perm) => (
          <Col key={perm.id} xs={24} sm={12} md={8} lg={6}>
            <div className="toggle-wrapper" style={{ marginTop: '16px' }}>
              <div className="label-with-tooltip">
                <Typography className="text-inter-400-14-20">
                  {perm.label}
                </Typography>
                <Tooltip title={perm.tooltip} placement="top">
                  <InfoIcon
                    height={14}
                    width={14}
                    style={{ marginLeft: '4px' }}
                  />
                </Tooltip>
              </div>
              <Toggle
                checked={formState.permissions[perm?.name]}
                onChange={() => handlePermissionToggle(perm?.name)}
                size="small"
              />
            </div>
          </Col>
        ))}
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: '24px' }}>
        <Col xs={24} sm={12} md={6}>
          <Flex vertical gap={6}>
            <Typography className="text-inter-400-14-20">Active SIM</Typography>
            <Input
              name="activeSIM"
              value={formState?.activeSIM}
              placeholder="SIM 1"
              disabled
            />
            {simStatusError && (
              <Typography
                className="small-text"
                style={{ color: 'var(--error-600)' }}
              >
                {simStatusError}
              </Typography>
            )}
          </Flex>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Flex vertical gap={6}>
            <Typography className="text-inter-400-14-20">
              Interface Priority
            </Typography>
            <div className="inline-container">
              <Radio.Group
                value={formState.interfacePriority}
                onChange={(e) =>
                  handleChange('interfacePriority', e.target.value)
                }
                style={{ display: 'flex', width: '100%' }}
              >
                <Radio.Button value="wwan0">wwan0</Radio.Button>
                <Radio.Button value="eth0">eth0</Radio.Button>
              </Radio.Group>
              <Button
                onClick={handleSaveInterface}
                type="primary"
                size="medium"
                style={{ marginLeft: '8px' }}
              >
                Save
              </Button>
            </div>
          </Flex>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Flex vertical gap={6}>
            <Typography className="text-inter-400-14-20">
              Active Carrier
            </Typography>
            <Input
              value={formState?.activeCarrier}
              placeholder="Please Specify"
              disabled
            />
          </Flex>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Flex vertical gap={6}>
            <Typography className="text-inter-400-14-20">
              Current APN
            </Typography>
            <Input
              value={formState?.currentApn}
              placeholder=""
              onChange={(val) => handleChange('currentApn', val)}
              disabled
            />
          </Flex>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: '32px' }}>
        <Col xs={24} sm={12} md={6}>
          <Flex vertical gap={6}>
            <Typography className="text-inter-400-14-20">
              Set APN For
            </Typography>
            <Select
              placeholder="Select APN For"
              value={formState.APNFor}
              onChange={(val) => handleChange('APNFor', val)}
              options={[
                { label: 'Select Carrier', value: '' },
                { label: 'Verizon', value: 'verizon' },
                { label: 'T-Mobile', value: 'tmobile' },
                { label: 'Telus', value: 'telus' },
                { label: 'AT&T', value: 'atnt' },
              ]}
              className="full-width-select"
            />
          </Flex>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Flex vertical gap={6}>
            <Typography className="text-inter-400-14-20">
              Sim Priority
            </Typography>
            <Select
              placeholder="Select Sim Priority"
              value={formState.simPriority}
              onChange={(val) => handleChange('simPriority', val)}
              options={[
                { label: 'Select SIM', value: '' },
                { label: 'SIM 1', value: '1' },
                { label: 'SIM2', value: '2' },
              ]}
              className="full-width-select"
            />
          </Flex>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Flex gap={6} style={{ width: 'fit-content', marginTop: '24px' }}>
            <Button type="primary">Save</Button>
            <Button
              icon={<ReloadOutlined style={{ color: '#000' }} />}
              style={{ backgroundColor: '#E8E8E8' }}
            />
          </Flex>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: '32px' }}>
        <Col xs={24} sm={12} md={6}>
          <Flex vertical gap={6}>
            <Typography className="text-inter-400-14-20">
              EPIs Time Zone
            </Typography>
            <div
              style={{ display: 'flex', alignItems: 'center' }}
              className="epi-timezone-container"
            >
              <Tooltip title={epiTimezoneTooltip?.toolTip} placement="top">
                <Select
                  placeholder="Select Time Zone"
                  value={formState.epiTimezone}
                  onChange={(val) => handleChange('epiTimezone', val)}
                  options={timezoneOptions}
                  className="full-width-select"
                />
              </Tooltip>
              <Button
                type="primary"
                size="medium"
                style={{ marginLeft: '8px' }}
              >
                Save
              </Button>
            </div>
          </Flex>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Flex vertical gap={6}>
            <Typography className="text-inter-400-14-20">
              Select Data Center
            </Typography>
            <Select
              placeholder="Select Data Center"
              value={formState.selectedDataCenter}
              onChange={(val) => handleChange('selectedDataCenter', val)}
              options={dataCenterOptions}
              className="full-width-select"
            />
            <div className="last-updated" style={{ marginTop: 0 }}>
              Last Updated: {formattedTime}
            </div>
            {dcAvgPingError && (
              <Flex flex={1} align="center">
                <Typography
                  className="small-text"
                  style={{ color: 'var(--error-600)' }}
                >
                  {dcAvgPingError}
                </Typography>
              </Flex>
            )}
          </Flex>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Flex gap={6} style={{ width: 'fit-content', marginTop: '24px' }}>
            <Button type="primary">Commit</Button>
            <Button type="primary">Update Latend</Button>
          </Flex>
        </Col>
      </Row>
      <Divider />
      <Row
        gutter={0}
        align="middle"
        justify="start"
        style={{ marginTop: '20px' }}
      >
        <Col>
          <Tooltip title="Power-Refresh">
            <Button
              color="default"
              variant="text"
              icon={<PowerReload style={{ width: '30px', height: '30px' }} />}
              style={{ padding: '5px' }}
            />
          </Tooltip>
        </Col>
        <Col>
          <Tooltip title="Save">
            <Button
              color="default"
              variant="text"
              icon={<SaveIcon style={{ width: '30px', height: '30px' }} />}
              style={{ padding: '5px' }}
            />
          </Tooltip>
        </Col>
        <Col>
          <Tooltip title="Warning">
            <Button
              color="default"
              variant="text"
              icon={<WarningIcon style={{ width: '30px', height: '30px' }} />}
              style={{ padding: '5px' }}
            />
          </Tooltip>
        </Col>
        <Col>
          <Tooltip title="Reload">
            <Button
              color="default"
              variant="text"
              icon={<ReloadIcon style={{ width: '30px', height: '30px' }} />}
              style={{ padding: '5px' }}
            />
          </Tooltip>
        </Col>
        <Col>
          <Button icon={<CloudDownloadOutlined />} type="primary" size="middle">
            Download
          </Button>
        </Col>
        <Col span={24}>
          <div className="last-updated">
            Nightly Updated: {nightlyUpdateTime}
          </div>
        </Col>
      </Row>
    </div>
  );
};

export default OptionPermission;
