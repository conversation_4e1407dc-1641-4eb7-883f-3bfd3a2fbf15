import { useState, useEffect } from 'react';
import { Row, Col, Select, message } from 'antd';
import { CheckCircleOutlined } from '@ant-design/icons';
import { Button, Flex, Typography } from '@/components';
import OptionPermission from './OptionPindeermissions';
import { useEdgeDeviceDetailContext } from '../../context';
import { useListNumbers } from '@/graphqlHooks/epikv2-api/useListNumbers';
import { APIS } from '@/constants';
import { useRestMutation } from '@/hooks';
import './styles.css';

const OptionTab = () => {
  const { edgeDeviceDetail, openModal, refetchFn } =
    useEdgeDeviceDetailContext();

  const [matchedAssignedNumbers, setMatchedAssignedNumbers] = useState([]);
  const [formState, setFormState] = useState({
    numberId: '',
    e911Number: '',
    isE911: false,
    isE911Verified: false,
  });

  const { data } = useListNumbers(
    {
      filter: { query: edgeDeviceDetail?.e911Number },
      pagination: { page: 1, pageSize: 1 },
    },
    false,
    !!edgeDeviceDetail?.e911Number,
  );

  useEffect(() => {
    const options = [];
    const docs = data?.ListNumbers?.docs || [];
    const total = data?.ListNumbers?.pagination?.count || 0;

    edgeDeviceDetail?.obiDocs?.forEach((obi) => {
      Object.values(obi).forEach((port) => {
        const assignedNumber =
          port?.assignedNumber || port?.assignedNumberDoc?.number;
        if (assignedNumber) {
          options.push({
            label: port?.assignedNumberDoc?.number,
            value: port?.assignedNumberDoc?._id,
            isE911: !!(port?.e911Number && port?.e911Enabled),
          });
        }
      });
    });

    if (
      total === 1 &&
      docs?.length === 1 &&
      docs?.[0]?.number === edgeDeviceDetail?.e911Number
    ) {
      options.push({
        label: docs[0]?.number,
        value: docs[0]?._id,
        isE911: !!edgeDeviceDetail?.e911Number,
      });
      setFormState((prev) => ({
        ...prev,
        numberId: docs[0]?._id,
        e911Number: docs[0]?.number,
        isE911: !!edgeDeviceDetail?.e911Number,
      }));
    }

    setMatchedAssignedNumbers(options);
  }, [edgeDeviceDetail?.obiDocs, data]);

  const { mutateAsync: deleteE911 } = useRestMutation({
    method: 'DELETE',
    api: APIS.EPIKV2,
    endpoint: formState?.numberId ? `/number/${formState?.numberId}/e911` : '',
  });

  const handleSelectChange = (value) => {
    const selectedOpt = matchedAssignedNumbers.find((n) => n?.value === value);
    setFormState((prev) => ({
      ...prev,
      e911Number: selectedOpt?.label,
      numberId: selectedOpt?.value,
      isE911: selectedOpt?.isE911,
    }));
  };

  const handleIsE911Verified = (isVerified) => {
    setFormState((prev) => ({ ...prev, isE911Verified: isVerified }));
  };

  const handleDelete = async () => {
    try {
      const resp = await deleteE911();
      if (!resp?.ok) {
        const text = await resp?.text?.().catch(() => '');
        throw new Error(text || `Delete failed: ${resp?.status}`);
      }
      if (resp?.status === 200 && refetchFn) {
        await refetchFn();
      }
    } catch (err) {
      message.error(err?.message || 'Unexpected error.');
    }
  };

  return (
    <div className="option-tab-container">
      <Row gutter={[16, 16]}>
        <Col>
          <Typography className="text-inter-500-14-20-gray-700">
            E911 Number
          </Typography>
          <Flex align="center" gap={6} style={{ marginTop: 8 }}>
            <Select
              showSearch
              placeholder="Select Number"
              options={matchedAssignedNumbers}
              value={formState?.e911Number}
              optionFilterProp="label"
              style={{ width: 200 }}
              onSelect={handleSelectChange}
            />
            <Button
              type="primary"
              size="middle"
              disabled={!formState?.numberId || !formState?.e911Number}
              onClick={() =>
                openModal('e911Modal', {
                  numberId: formState?.numberId,
                  e911Number: formState?.e911Number,
                  isE911: formState?.isE911,
                  hanldeIsE911Verified: handleIsE911Verified,
                })
              }
            >
              Update
            </Button>
            <Button
              type="primary"
              size="middle"
              disabled={!formState?.numberId || !formState?.e911Number}
              onClick={handleDelete}
            >
              Clear
            </Button>
          </Flex>
        </Col>
      </Row>

      {formState?.isE911Verified && (
        <Flex align="center" gap={6}>
          <CheckCircleOutlined className="verified-icon" />
          <span className="text-inter-700-14-20 verified-text">
            Address Verified
          </span>
        </Flex>
      )}

      <OptionPermission />
    </div>
  );
};

export default OptionTab;
