.input-label {
  margin-bottom: 4px;
}
.tooltip-icon {
  font-weight: 400;
  color: #8c8c8c;
}

.toggle-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.input-label {
  font-weight: 600;
  display: block;
  margin-bottom: 4px;
}

.tooltip-icon {
  color: #8c8c8c;
}

.inline-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

@media (max-width: 768px) {
  .toggle-wrapper {
    flex-direction: column;
    align-items: flex-start;
  }
  .inline-container {
    flex-direction: column;
    align-items: stretch;
  }
}
.input-disabled {
  background-color: #e8e8e8 !important;
  color: #000;
  width: '100%';
}

.button-style {
  margin-bottom: 1px;
  width: 152px; /* Hug width */
  height: 44px; /* Fixed height */
  padding: 10px 18px; /* Padding */
  gap: 8px; /* Spacing inside */
  font-size: 0.9rem; /* Font size */
  font-weight: 500; /* Medium font weight */
  border: 1px solid #d9d9d9; /* Subtle border color */
  color: white; /* Default text color */
  cursor: pointer; /* Pointer cursor */
  display: inline-flex; /* To hug content */
  align-items: center; /* Center align content */
  justify-content: center; /* Center align content */
  transition: all 0.2s ease-in-out; /* Smooth hover effect */
}

/* Button Hover State */
.button-style:hover {
  background-color: #f0f0f0; /* Light gray background on hover */
  border-color: #3b5aa9;
  color: #3b5aa9;
}
.input-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 400;
  font-size: 0.9rem;
  line-height: 24px;
}

.tooltip-icon {
  vertical-align: middle;
  margin-left: 4px;
}
.button-style {
  margin-top: 0;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.option-tab-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.e911-label {
  font-family: 'Inter';
  font-size: 0.9rem;
  font-weight: 500;
  line-height: 20px;
  text-align: left;
  text-underline-position: 'from-font';
  text-decoration-skip-ink: none;
  margin-bottom: 5px;
  display: block;
}

.e911-clear-btn:hover {
  background-color: #40a9ff;
}

.verified-icon {
  color: #52c41a;
}

.verified-text {
  color: #52c41a;
}

.app-container .dropdown-container {
  display: flex;
  flex-direction: column;
  align-items: start;
}

.app-container .input-label {
  margin-bottom: 8px;
}

.app-container .buttons-container {
  display: flex;
  align-items: center;
}

@media (max-width: 576px) {
  .app-container .dropdown-container,
  .app-container .buttons-container {
    width: 100%;
    justify-content: center;
  }
}
.app-container .dropdown-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  flex-grow: 1;
}

.app-container .input-label {
  margin-bottom: 8px;
}

@media (max-width: 576px) {
  .app-container .dropdown-container {
    width: 100%;
    flex-direction: column;
    align-items: flex-start;
  }
}

.last-updated {
  font-size: 0.8rem;
  color: #bfbfbf;
  margin-top: 4px;
  font-weight: bold;
}
.full-width-select {
  width: 100%;
}

.epi-timezone-container > span {
  width: 100%;
}
/* MyComponent.module.css */
.normalRadio {
  flex: 1;
  text-align: center;
  background-color: white;
  color: #595959;
}

.savedRadio {
  flex: 1;
  text-align: center;
  background-color: #e0e0e0;
  color: #595959;
  border-color: #d9d9d9;
}
