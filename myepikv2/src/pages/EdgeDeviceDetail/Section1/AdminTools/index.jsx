import { Row, Col } from 'antd';
import {
  Button,
  Input,
  Toggle,
  Typography,
  InfoIcon,
  Tooltip,
  IconWithTooltip,
  Flex,
} from '@/components';
import './styles.css';
import { useEdgeDeviceDetailContext } from '../../context';

// should be remove after adding typography variants
const labelStyles = {
  display: 'flex',
  alignItems: 'center',
};

const buttonStyles = {
  width: 'fit-content',
};

const AdminTools = () => {
  const { edgeDeviceDetail } = useEdgeDeviceDetailContext();
  return (
    <Row gutter={[32]} className="admin-tools">
      <Col xs={24} sm={12} md={12} lg={7}>
        <Flex vertical gap={8}>
          <Typography
            className="text-montserrat-600-14-14-gray-500"
            style={labelStyles}
          >
            Center Sim Acitvation check{' '}
            <IconWithTooltip icon={<InfoIcon height="12" width="12" />} />
          </Typography>
          <Button
            variant="outlined"
            variantColorBgContainer
            style={buttonStyles}
          >
            <Typography className="text-montserrat-600-14-14-granite-primary">
              Execute
            </Typography>
          </Button>
        </Flex>
        <Flex vertical gap={8} style={{ marginTop: '24px' }}>
          <Flex justify="space-between">
            <Typography
              className="text-montserrat-600-14-14-gray-500"
              style={labelStyles}
            >
              Device Swap{' '}
              <IconWithTooltip icon={<InfoIcon height="12" width="12" />} />
            </Typography>
            <Typography
              className="text-montserrat-600-14-14-gray-500"
              style={{ ...labelStyles, color: '#3E4042' }}
            >
              Destination Serial
            </Typography>
          </Flex>
          <Input
            placeholder="No Boxes found"
            variant="filled"
            disabled
            style={{ cursor: 'default' }}
          />
        </Flex>
        <Flex vertical gap={8} style={{ marginTop: '24px' }}>
          <Typography
            className="text-montserrat-600-14-14-gray-500"
            style={labelStyles}
          >
            Reset to Default{' '}
            <IconWithTooltip icon={<InfoIcon height="12" width="12" />} />
          </Typography>
          <Button
            color="danger"
            variant="solid"
            danger
            style={{ ...buttonStyles, fontWeight: 600 }}
          >
            Execute
          </Button>
        </Flex>
      </Col>
      <Col xs={24} sm={12} md={12} lg={7}>
        <Flex vertical gap={8}>
          <Typography
            className="text-montserrat-600-14-14-gray-500"
            style={labelStyles}
          >
            Sim Failover Check{' '}
            <IconWithTooltip icon={<InfoIcon height="12" width="12" />} />
          </Typography>
          <Flex gap={8}>
            <Button
              variant="outlined"
              variantColorBgContainer
              style={buttonStyles}
            >
              <Typography className="text-montserrat-600-14-14-granite-primary">
                Execute
              </Typography>
            </Button>
            <Button
              variant="outlined"
              variantColorBgContainer
              style={buttonStyles}
            >
              <Typography className="text-montserrat-600-14-14-granite-primary">
                Retrieve Results
              </Typography>
            </Button>
          </Flex>
        </Flex>
        <Flex vertical gap={8} style={{ marginTop: '24px' }}>
          <Typography
            className="text-montserrat-600-14-14-gray-500"
            style={labelStyles}
          >
            Demo Box{' '}
            <IconWithTooltip icon={<InfoIcon height="12" width="12" />} />
          </Typography>
          <Toggle
            value={edgeDeviceDetail?.isDemoBox}
            variant="secondary"
            style={{ width: 'fit-content' }}
          />
        </Flex>
        <Flex vertical gap={8} style={{ marginTop: '28px' }}>
          <Typography
            className="text-montserrat-600-14-14-gray-500"
            style={labelStyles}
          >
            Disable EPIK Updates
            <IconWithTooltip icon={<InfoIcon height="12" width="12" />} />
          </Typography>
          <Toggle
            value={edgeDeviceDetail?.epikUpdateStatus}
            variant="secondary"
            style={{ width: 'fit-content' }}
          />
        </Flex>
      </Col>
      <Col xs={24} sm={12} md={12} lg={5}>
        <Flex vertical gap={8}>
          <Typography
            className="text-montserrat-600-14-14-gray-500"
            style={labelStyles}
          >
            Admin Flags{' '}
            <IconWithTooltip icon={<InfoIcon height="12" width="12" />} />
          </Typography>
          <Button
            variant="outlined"
            variantColorBgContainer
            style={buttonStyles}
          >
            <Typography className="text-montserrat-600-14-14-granite-primary">
              Open
            </Typography>
          </Button>
        </Flex>
        <Flex vertical style={{ marginTop: '24px' }} gap={8}>
          <Typography
            className="text-montserrat-600-14-14-gray-500"
            style={labelStyles}
          >
            Engineering Lock{' '}
            <IconWithTooltip icon={<InfoIcon height="12" width="12" />} />
          </Typography>
          <Toggle variant="secondary" style={{ width: 'fit-content' }} />
        </Flex>
      </Col>
      <Col xs={24} sm={12} md={12} lg={5}>
        <Flex vertical gap={8}>
          <Typography
            className="text-montserrat-600-14-14-gray-500"
            style={labelStyles}
          >
            Send Tone{' '}
            <Tooltip title="Info tooltip">
              <span style={{ marginLeft: '4px' }}>
                <InfoIcon height={12} width={12} />
              </span>
            </Tooltip>
          </Typography>
          <Button
            variant="outlined"
            variantColorBgContainer
            style={buttonStyles}
          >
            Execute
          </Button>
        </Flex>
      </Col>
    </Row>
  );
};

export default AdminTools;
