import { Flex, Divider } from 'antd';
import PropTypes from 'prop-types';
import {
  PowerEpi,
  Button,
  ProvisionEpi,
  Typography,
  FirmwareEpi,
} from '@/components';
import './styles.css';

const CardRow = ({ label, value }) => (
  <Flex justify="space-between" style={{ marginTop: '8px' }}>
    <Typography className="text-inter-500-14-20-gray-600">{label}</Typography>
    <Typography className="text-inter-600-14-20-gray3">{value}</Typography>
  </Flex>
);
CardRow.propTypes = {
  label: PropTypes.string.isRequired,
  value: PropTypes.string.isRequired,
};
const EpiCard = ({ epi }) => {
  return (
    <Flex vertical className="epik-card">
      <Flex justify="space-between" align="center">
        <span
        // className={`status-dot ${epi?.online ? 'online' : 'offline'}`} handle later
        ></span>
        <Typography className="text-inter-400-14-20 remove-text">
          Remove
        </Typography>
      </Flex>
      <Typography className="text-inter-600-20-30 epi-card-title">
        EPI #{epi?.epiNumber}
      </Typography>
      <CardRow label="MAC Address" value={epi?.macAddress || '--'} />
      <Divider className="mtb-sm custom-divider" />
      <Flex justify="space-between" align="center" style={{ marginTop: '8px' }}>
        <Flex gap={8}>
          <Button icon={<PowerEpi />} className="port-card-footer-btn" />
          <Button icon={<ProvisionEpi />} className="port-card-footer-btn" />
        </Flex>
        <Button icon={<FirmwareEpi />} className="port-card-footer-btn" />
      </Flex>
    </Flex>
  );
};

EpiCard.propTypes = {
  epi: PropTypes.object,
};

export default EpiCard;
