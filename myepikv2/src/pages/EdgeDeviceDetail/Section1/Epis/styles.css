.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  background-color: #edf5fb;
  width: 100%;
  margin: -16px -16px 8px -16px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}
.epik-card {
  background-color: #edf5fb;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 12px 16px;
  display: flex;
  width: 100%;
}

.epi-card-title {
  margin-top: 8px !important;
  margin-bottom: 8px !important;
}

.card-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}
.card-header {
  margin-bottom: 8px;
}
.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}
.status-dot.online {
  background-color: #ffc107;
}
.status-dot.offline {
  background-color: #ff4d4f;
}
.remove-text {
  color: #ff4d4f;
  cursor: pointer;
}
.epik-card-title {
  margin-top: 8px;
}
.mtb-sm {
  margin: 8px 0;
}
.bold-text {
  font-weight: bold;
}
.epik-button-container {
  margin-top: 20px;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}
.icon-button {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: #e6f7ff;
  border: none;
}
.icon-button:hover {
  background-color: #bae7ff;
}
.custom-divider {
  background-color: #3e404280;
}

.epik-card-buttons .ant-btn:first-child {
  margin-right: 1px;
}
.epik-card-buttons .ant-btn:nth-child(2) {
  margin-right: -5px;
  margin-left: -5px;
}
.epik-card-buttons .ant-btn:last-child {
  margin-left: 40px;
}
