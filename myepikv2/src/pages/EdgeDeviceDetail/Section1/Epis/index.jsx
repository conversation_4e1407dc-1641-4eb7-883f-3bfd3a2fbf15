import { useState } from 'react';
import EpiCard from './card';
import './styles.css';
import { Button, Flex } from '@/components';
import AdvancedEpiConfiguration from './Modal/AssignEpi/index';
import ExternalPortConfiguration from './Modal/ExternalPorts/index';
import RearrangePortsModal from './Modal/RearrangePort/index';
import AdvancedConfiguration from './Modal/Asc/index';
import FirmwareUpdateModal from './Modal/EpikFirmwareUpdate/index';
import RemoteExecutionModal from './Modal/RemoteExecution/index';
import { Col, Empty, Row } from 'antd';
import { useEdgeDeviceDetailContext } from '../../context';

const EPIS = () => {
  const { edgeDeviceDetail } = useEdgeDeviceDetailContext();
  // State to manage the visibility of the modal
  const [showAssignEPI, setShowAssignEPI] = useState(false);
  const [externalPort, setExternalPort] = useState(false);
  const [rearrangePort, setRearrangePort] = useState(false);
  const [ascModel, setAsc] = useState(false);
  const [firmwareUpdate, setFirmwareUpdate] = useState(false);
  const [epiRec, setEpiRec] = useState(false);

  const epis =
    edgeDeviceDetail?.obiDocs?.map((obi, index) => ({
      _id: obi._id,
      epiNumber: ++index,
      deviceId: obi.deviceId,
      obiNumber: obi.obiNumber,
      macAddress: obi.macAddress,
    })) || [];

  console.log('EPIS01', epis);

  const handleOpenAssignEPI = () => {
    setShowAssignEPI(true);
  };
  const handleEpiRec = () => {
    setEpiRec(true);
  };

  const handleExternalPort = () => {
    setExternalPort(true);
  };
  const RearrangePort = () => {
    setRearrangePort(true);
  };
  const handleFirmwareUpdate = () => {
    setFirmwareUpdate(true);
  };
  const AscendingData = () => {
    setAsc(true);
  };
  const modalConfig = {
    AdvancedEpiConfiguration: {
      component: AdvancedEpiConfiguration,
      props: {
        onCancel: () => setShowAssignEPI(false),
        handleOk: () => setShowAssignEPI(false),
      },
      condition: showAssignEPI,
    },
    ExternalPorts: {
      component: ExternalPortConfiguration,
      props: {
        onCancel: () => setExternalPort(false),
        onSubmit: () => setExternalPort(false),
        handleOk: () => setExternalPort(false),
      },
      condition: externalPort,
    },
    RearrangePortsModal: {
      component: RearrangePortsModal,
      props: {
        onCancel: () => setRearrangePort(false),
        onSubmit: () => setRearrangePort(false),
        handleOk: () => setRearrangePort(false),
      },
      condition: rearrangePort,
    },
    AdvancedConfiguration: {
      component: AdvancedConfiguration,
      props: {
        onCancel: () => setAsc(false),
        onSubmit: () => setAsc(false),
      },
      condition: ascModel,
    },
    FirmwareUpdateModal: {
      component: FirmwareUpdateModal,
      props: {
        onCancel: () => setFirmwareUpdate(false),
        onProceed: () => setFirmwareUpdate(false),
      },
      condition: firmwareUpdate,
    },
    RemoteExecutionModal: {
      component: RemoteExecutionModal,
      props: {
        onCancel: () => setEpiRec(false),
        onExecute: () => setEpiRec(false),
      },
      condition: epiRec,
    },
  };
  // Render modals dynamically
  const renderModals = () => {
    return Object.entries(modalConfig).map(([key, modal]) => {
      const { component: ModalComponent, props, condition } = modal;
      return condition ? <ModalComponent key={key} {...props} /> : null;
    });
  };

  return (
    <>
      {epis?.length ? (
        <>
          <Row gutter={[16, 16]}>
            {epis?.map((epi) => {
              return (
                <Col key={epi.id} xs={24} sm={12} md={12} lg={8} xl={8}>
                  <EpiCard epi={epi} key={epi?.epiNumber} />
                </Col>
              );
            })}
          </Row>
          <div className="epik-button-container">
            <Button onClick={handleOpenAssignEPI}>Assign EPI</Button>
            <Button onClick={handleExternalPort}>Add External Ports</Button>
            <Button onClick={RearrangePort}>Rearrange Ports</Button>
            <Button onClick={AscendingData}>AEC</Button>
            <Button onClick={handleFirmwareUpdate}>Hard Firmware Update</Button>
            <Button onClick={handleEpiRec}>EPI REC</Button>
          </div>
          {renderModals()}
        </>
      ) : (
        <Flex
          justify="center"
          align="center"
          style={{ width: '100%', minHeight: '200px' }}
        >
          <Empty />
        </Flex>
      )}
    </>
  );
};

export default EPIS;
