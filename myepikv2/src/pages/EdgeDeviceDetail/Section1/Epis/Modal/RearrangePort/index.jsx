import { useState, useEffect } from 'react';
import { Modal, Table, Select, Divider } from 'antd';
import PropTypes from 'prop-types';
import { Button, Typography } from '@/components';
const RearrangePortsModal = ({ onCancel, onSubmit }) => {
  const [data, setData] = useState([
    { key: '1', position: 1, macAddress: '00268B5018E6', newPosition: null },
    { key: '2', position: 2, macAddress: '00268B5018E6', newPosition: null },
    { key: '3', position: 3, macAddress: '00268B5018E6', newPosition: null },
    { key: '4', position: 4, macAddress: '00268B5018E6', newPosition: null },
  ]);

  const Title = () => (
    <div className="modal-title">
      <Typography level={4}>Rearrange Ports</Typography>
      <Divider className="divider" />
    </div>
  );

  // State to manage the disablement of the submit button
  const [isSubmitDisabled, setIsSubmitDisabled] = useState(true);

  useEffect(() => {
    // Check if all positions have been selected
    const allSelected = data.every((item) => item.newPosition !== null);
    setIsSubmitDisabled(!allSelected);
  }, [data]); // Update when data changes

  const handleSelectChange = (selectedValue, key) => {
    const newData = data.map((item) => {
      if (item.key === key) {
        return { ...item, newPosition: selectedValue };
      }
      return item;
    });
    setData(newData);
  };

  const getAvailableOptions = (index) => {
    let usedPositions = data
      .filter((item, idx) => idx !== index && item.newPosition !== null)
      .map((item) => item.newPosition);
    return [null, ...data.map((item) => item.position)].filter(
      (pos) => !usedPositions.includes(pos),
    );
  };

  const columns = [
    {
      title: 'Current EPI Position',
      dataIndex: 'position',
      key: 'position',
    },
    {
      title: 'MAC Address',
      dataIndex: 'macAddress',
      key: 'macAddress',
    },
    {
      title: 'New EPI Position',
      key: 'newPosition',
      render: (text, record, index) => (
        <Select
          placeholder="Select Number"
          style={{ width: 180 }}
          value={record.newPosition}
          onChange={(value) => handleSelectChange(value, record.key)}
          allowClear={true}
        >
          {getAvailableOptions(index).map((option) =>
            option === null ? (
              <Select.Option key="unselect" value={option}>
                Unselect
              </Select.Option>
            ) : (
              <Select.Option key={option} value={option}>
                {option}
              </Select.Option>
            ),
          )}
        </Select>
      ),
    },
  ];

  return (
    <Modal
      title={<Title />}
      open={true}
      maskClosable={false}
      onCancel={onCancel}
      onOk={() => onSubmit(data)}
      footer={[
        <Button
          key="cancel"
          color="default"
          variant="outlined"
          onClick={onCancel}
        >
          Cancel
        </Button>,
        <Button
          key="submit"
          type="primary"
          onClick={() => onSubmit(data)}
          disabled={isSubmitDisabled}
        >
          Submit
        </Button>,
      ]}
      width="800px"
    >
      <Table dataSource={data} columns={columns} pagination={false} />
    </Modal>
  );
};
RearrangePortsModal.propTypes = {
  onCancel: PropTypes.func,
  onSubmit: PropTypes.func,
};
export default RearrangePortsModal;
