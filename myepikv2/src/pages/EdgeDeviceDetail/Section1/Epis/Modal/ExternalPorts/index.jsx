import { Modal, Form, Input, InputNumber, Divider } from 'antd';
import { Button, Typography } from '@/components';
import PropTypes from 'prop-types';
import './styles.css';

const ExternalPortConfiguration = ({ onCancel, onSubmit }) => {
  const [form] = Form.useForm();

  const handleSubmit = (values) => {
    console.log('Received values of form:', values);
  };
  const Title = () => (
    <div className="modal-title">
      <Typography level={4}>External Port Configuration</Typography>
      <Divider className="divider" />
    </div>
  );
  const handleNumericInputKeyDown = (event) => {
    if (event.ctrlKey || event.metaKey) {
      if (['c', 'v', 'x', 'a'].includes(event.key.toLowerCase())) {
        return;
      }
    }
    if (
      [
        'Backspace',
        'Tab',
        'Enter',
        'Escape',
        'ArrowLeft',
        'ArrowRight',
        'Delete',
        'Home',
        'End',
      ].includes(event.key)
    ) {
      return;
    }
    if (!/[0-9]/.test(event.key)) {
      event.preventDefault();
    }
  };
  return (
    <Modal
      title={<Title />}
      open={true}
      maskClosable={false}
      onCancel={onCancel}
      onSubmit={onCancel}
      footer={[
        <Button
          key="cancel"
          color="default"
          variant="outlined"
          onClick={onCancel}
        >
          Cancel
        </Button>,
        <Button key="submit" type="primary" onClick={onSubmit}>
          Submit
        </Button>,
      ]}
      width={545}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        className="custom-label-color" // This class is added to the form
      >
        <Form.Item
          name="macAddress"
          label="MAC Address"
          rules={[{ required: true, message: 'Please enter MAC address' }]}
        >
          <Input placeholder="Enter MAC address" />
        </Form.Item>
        <Form.Item
          name="numberOfPorts"
          label="Number of Ports"
          rules={[
            { required: true, message: 'Please enter the number of ports' },
          ]}
        >
          <InputNumber
            placeholder="Enter Number of Ports"
            min={1}
            style={{ width: '100%' }}
            onKeyDown={handleNumericInputKeyDown}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};
ExternalPortConfiguration.propTypes = {
  onCancel: PropTypes.func.isRequired,
  onSubmit: PropTypes.func.isRequired,
};
export default ExternalPortConfiguration;
