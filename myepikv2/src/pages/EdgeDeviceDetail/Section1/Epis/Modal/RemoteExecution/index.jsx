import { useState } from 'react';
import { Modal, Checkbox, Divider } from 'antd';
import './styles.css';
import { Button, Typography } from '@/components';
import PropTypes from 'prop-types';

const RemoteExecutionModal = ({ onCancel, onExecute }) => {
  const [selected, setSelected] = useState([]);
  const epiList = ['EP1', 'EP2', 'EP3', 'EP4'];

  const handleSelectAll = (e) => {
    if (e.target.checked) {
      setSelected(epiList);
    } else {
      setSelected([]);
    }
  };

  const handleSelect = (epi) => {
    setSelected((prev) =>
      prev.includes(epi) ? prev.filter((item) => item !== epi) : [...prev, epi],
    );
  };

  return (
    <Modal
      open={true}
      maskClosable={false}
      onCancel={onCancel}
      footer={null}
      width={440}
      className="execution-modal"
    >
      <Typography level={4}>EPI Remote Execution Center</Typography>
      <Divider />
      <div className="modal-body">
        <Checkbox
          onChange={handleSelectAll}
          checked={selected.length === epiList.length}
        >
          Select All
        </Checkbox>
        {epiList.map((epi) => (
          <div key={epi} className="epi-item">
            <Checkbox
              className="gray-text"
              checked={selected.includes(epi)}
              onChange={() => handleSelect(epi)}
            >
              {epi}
            </Checkbox>
          </div>
        ))}
      </div>
      <div className="footer-text">Please wait, fetching commands</div>
      <div className="modal-footer">
        <Button color="default" variant="outlined" onClick={onCancel}>
          Cancel
        </Button>
        <Button
          type="primary"
          onClick={onExecute}
          disabled={selected.length === 0}
        >
          Execute
        </Button>
      </div>
    </Modal>
  );
};
RemoteExecutionModal.propTypes = {
  onCancel: PropTypes.func.isRequired,
  onExecute: PropTypes.func.isRequired,
};
export default RemoteExecutionModal;
