import { Modal } from 'antd';
import PropTypes from 'prop-types';
import './styles.css';
import { Warning, Typography, Button } from '@/components';

const FirmwareUpdateModal = ({ onCancel, onProceed }) => {
  return (
    <Modal
      open={true}
      onCancel={onCancel}
      maskClosable={false}
      footer={null}
      width={600}
      className="firmware-modal"
    >
      <div className="modal-content">
        <div className="icon-container">
          <Warning />
        </div>
        <Typography level={4}>All EPI’s Firmware Update</Typography>
        <Typography className="description">
          This process will take approximately 15 minutes to complete and
          require the ports on this device to reboot.
        </Typography>
        <Typography className="description">
          Would you like to proceed with this update now?
        </Typography>
        <div className="modal-footer">
          <Button color="default" variant="outlined" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="primary" onClick={onProceed}>
            Proceed
          </Button>
        </div>
      </div>
    </Modal>
  );
};

FirmwareUpdateModal.propTypes = {
  onCancel: PropTypes.func.isRequired,
  onProceed: PropTypes.func.isRequired,
};

export default FirmwareUpdateModal;
