import { useState } from 'react';
import PropTypes from 'prop-types';
import { Modal, Input, List, Divider, Row, Col, Form } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { Button, Typography, ReloadEpi } from '@/components';

const Title = () => (
  <div className="modal-title">
    <Typography level={4}>Advanced EPI Configuration</Typography>
    <Divider className="divider" />
  </div>
);

const AdvancedEPIConfiguration = ({ onCancel }) => {
  const [macAddresses, setMacAddresses] = useState(
    new Array(20).fill('9CADFESAJUHU31'),
  );
  const [visible, setVisible] = useState(false);
  const [form] = Form.useForm();

  const handleSave = (values) => {
    setMacAddresses([...macAddresses, values.macAddress]);
    setVisible(false);
  };

  return (
    <>
      <Modal
        title={<Title />}
        open={true}
        maskClosable={false}
        onCancel={onCancel}
        footer={
          <Button color="default" variant="outlined" onClick={onCancel}>
            Cancel
          </Button>
        }
        width={545}
      >
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={15}>
            <Input.Search placeholder="Search" />
          </Col>
          <Col span={3}>
            <Button
              type="primary"
              icon={<ReloadEpi />}
              onClick={() => console.log('Reload EPI')}
            ></Button>
          </Col>
          <Col span={2}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setVisible(true)}
            >
              Add EPI
            </Button>
          </Col>
        </Row>
        <List
          dataSource={macAddresses}
          renderItem={(item) => <List.Item>{item}</List.Item>}
          bordered
          style={{ overflowY: 'auto', height: '300px' }}
        />
      </Modal>
      <Modal
        title="Add New EPI"
        open={visible}
        onCancel={() => setVisible(false)}
        maskClosable={false}
        onOk={() => form.submit()}
        footer={[
          <Button
            key="back"
            color="default"
            variant="outlined"
            onClick={() => setVisible(false)}
          >
            Cancel
          </Button>,
          <Button key="submit" type="primary" onClick={() => form.submit()}>
            Save
          </Button>,
        ]}
        width={545}
      >
        <Divider className="divider" />
        <Form form={form} layout="vertical" onFinish={handleSave}>
          <Form.Item
            name="macAddress"
            label="Enter New Mac Address"
            className="custom-label-color"
            rules={[
              { required: true, message: 'Please specify the MAC Address' },
            ]}
          >
            <Input placeholder="Please Specify" />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

AdvancedEPIConfiguration.propTypes = {
  onCancel: PropTypes.func.isRequired,
};
export default AdvancedEPIConfiguration;
