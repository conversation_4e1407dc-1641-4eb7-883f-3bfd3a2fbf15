import { useState } from 'react';
import './styles.css';
import PropTypes from 'prop-types';
import { Modal, Form, Input, Select, Row, Col, Divider } from 'antd';
import {
  Button,
  Typography,
  DeleteIcon,
  AddIcon,
  DowwnloadEpiIcon,
} from '@/components';

const AdvancedConfiguration = ({ onCancel }) => {
  const [fields, setFields] = useState([{ key: '', value: '' }]);

  const handleAddField = () => {
    setFields([...fields, { key: '', value: '' }]);
  };

  const handleRemoveField = (index) => {
    if (fields.length > 1) {
      const updatedFields = fields.filter((_, i) => i !== index);
      setFields(updatedFields);
    }
  };

  const handleFieldChange = (index, field, value) => {
    const updatedFields = [...fields];
    updatedFields[index][field] = value;
    setFields(updatedFields);
  };

  const handleSubmit = () => {
    console.log('Form Data:', fields);
  };

  return (
    <Modal
      title="Advanced EPI Configuration"
      open={true}
      maskClosable={false}
      onCancel={onCancel}
      footer={[
        <Button
          key="cancel"
          color="default"
          variant="outlined"
          onClick={onCancel}
        >
          Cancel
        </Button>,
        <Button key="submit" type="primary" onClick={handleSubmit}>
          Save
        </Button>,
      ]}
      width={720}
    >
      <Form layout="vertical">
        <Form.Item
          name="epi"
          label={<span className="label-color">Select EPI</span>}
          rules={[{ required: true, message: 'Please select an EPI!' }]}
        >
          <Row gutter={16}>
            <Col span={16}>
              <Select placeholder="Choose one">
                <Select.Option value="epi1">EPI 1</Select.Option>
                <Select.Option value="epi2">EPI 2</Select.Option>
              </Select>
            </Col>
            <Col span={8}>
              <Button
                type="primary"
                icon={<DowwnloadEpiIcon />}
                className="download-btn"
              >
                Download Config
              </Button>
            </Col>
          </Row>
        </Form.Item>
        <Divider />
        <Typography level={4} className="label-color">
          Add Configuration
        </Typography>
        {fields.map((field, index) => (
          <Row key={index} gutter={16} align="middle" className="config-row">
            <Col span={8}>
              <Input
                placeholder="Sample Key1"
                value={field.key}
                onChange={(e) =>
                  handleFieldChange(index, 'key', e.target.value)
                }
              />
            </Col>
            <Col span={8}>
              <Input
                placeholder="Sample Value1"
                value={field.value}
                onChange={(e) =>
                  handleFieldChange(index, 'value', e.target.value)
                }
              />
            </Col>
            <Col span={6} className="add-remove-btns">
              {fields.length > 1 && (
                <Button
                  variant="text"
                  size="small"
                  color="default"
                  icon={<DeleteIcon />}
                  onClick={() => handleRemoveField(index)}
                />
              )}
              {index === fields.length - 1 && (
                <Button
                  variant="text"
                  color="default"
                  size="small"
                  icon={<AddIcon />}
                  onClick={handleAddField}
                />
              )}
            </Col>
          </Row>
        ))}
      </Form>
    </Modal>
  );
};

AdvancedConfiguration.propTypes = {
  onCancel: PropTypes.func.isRequired,
  onSubmit: PropTypes.func.isRequired,
};
export default AdvancedConfiguration;
