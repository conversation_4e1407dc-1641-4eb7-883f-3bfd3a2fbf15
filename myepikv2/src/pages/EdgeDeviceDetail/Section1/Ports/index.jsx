import PropTypes from 'prop-types';
import { Divider, Row, Col, Empty } from 'antd';
import {
  Button,
  Typography,
  Flex,
  PortForwardIcon,
  VoiceMailPhoneIcon,
  PhoneTwoIcon,
  FireIcon,
  EthernetPortIcon,
  CheckCircleIcon,
  DangerTriangleIcon,
} from '@/components';
import './styles.css';
import { useEdgeDeviceDetailContext } from '../../context';

const CardRow = ({ label, value }) => (
  <Row className="port-card-row">
    <Col span={12}>
      <Typography className="text-inter-500-14-20-gray-600">{label}</Typography>
    </Col>
    <Col span={12}>
      <Typography className="text-inter-600-14-20-gray3">{value}</Typography>
    </Col>
  </Row>
);

CardRow.propTypes = {
  label: PropTypes.string.isRequired, // Label must be a string and is required
  value: PropTypes.string.isRequired, // Value must be a string and is required
};

const PortCard = ({ openPortDetailsModal, port, handleSelectPort }) => {
  const portNumber = parseInt(
    port?.boxPortNumber?.match(/\d+/)?.[0] || '0',
    10,
  );

  return (
    <Flex
      vertical
      className="port-card"
      onClick={() => {
        openPortDetailsModal(portNumber);
        handleSelectPort(port);
      }}
    >
      <Typography className="text-inter-600-20-30 port-card-title">
        {port?.boxPortNumber}
      </Typography>
      <CardRow
        label={'Port Label'}
        value={port?.assignedNumberDoc?.type || '--'}
      />
      <Divider className="mtb-sm" />
      <CardRow
        label={'Assigned #'}
        value={port?.assignedNumberDoc?.number || '--'}
      />
      <Divider className="mtb-sm" />
      <Flex
        style={{ marginTop: '8px', padding: '0 16px' }}
        justify="space-between"
      >
        <Flex gap={4}>
          <Button
            color="default"
            variant="outlined"
            className="port-card-footer-btn"
            icon={<PortForwardIcon />}
          />
          <Button
            color="default"
            variant="outlined"
            className="port-card-footer-btn"
            icon={<VoiceMailPhoneIcon />}
          />
          <Button
            color="default"
            variant="outlined"
            className="port-card-footer-btn"
            icon={<PhoneTwoIcon />}
          />
          <Button
            color="default"
            variant="outlined"
            className="port-card-footer-btn"
            icon={<FireIcon />}
          />
          <Button
            color="default"
            variant="outlined"
            className="port-card-footer-btn"
            icon={<EthernetPortIcon />}
          />
        </Flex>
        <Flex gap={4}>
          <Button
            color="default"
            variant="outlined"
            className="port-card-footer-btn"
            icon={<CheckCircleIcon />}
          />
          <Button
            color="default"
            variant="outlined"
            className="port-card-footer-btn"
            icon={<DangerTriangleIcon />}
          />
        </Flex>
      </Flex>
    </Flex>
  );
};

PortCard.propTypes = {
  openPortDetailsModal: PropTypes.func.isRequired,
  port: PropTypes.object.isRequired,
  handleSelectPort: PropTypes.func.isRequired,
};

const PortsTab = ({ openPortDetailsModal }) => {
  const { edgeDeviceDetail, handleSelectPort } = useEdgeDeviceDetailContext();
  const ports =
    edgeDeviceDetail?.obiDocs?.flatMap((obi) => [
      { ...obi.port1, obiId: obi._id },
      { ...obi.port2, obiId: obi._id },
    ]) || [];

  return (
    <>
      <Row gap={'middle'} gutter={[10, 10]} wrap>
        {ports.length ? (
          ports?.map((port, index) => {
            return (
              <Col
                key={`${port}${index}`}
                xs={24}
                sm={12}
                md={8}
                lg={8}
                xl={8}
                xll={6}
              >
                <PortCard
                  port={port}
                  openPortDetailsModal={openPortDetailsModal}
                  handleSelectPort={handleSelectPort}
                />
              </Col>
            );
          })
        ) : (
          <Flex
            justify="center"
            align="center"
            style={{ width: '100%', minHeight: '200px' }}
          >
            <Empty />
          </Flex>
        )}
      </Row>

      {/* <div className="mt-15">
        <Button>Push Configuration</Button>
      </div> */}
    </>
  );
};

PortsTab.propTypes = {
  tabName: PropTypes.string.isRequired, // Tab name must be a string and is required
  openPortDetailsModal: PropTypes.func.isRequired,
};

export default PortsTab;
