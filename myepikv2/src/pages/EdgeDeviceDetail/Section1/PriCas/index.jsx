import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Row, Col, Divider } from 'antd';
import { ReactSortable } from 'react-sortablejs';
import {
  Button,
  Input,
  Toggle,
  Select,
  Typography,
  Flex,
  CloseTwoIcon,
  DragIcon,
} from '@/components';
import './styles.css';
import { useEdgeDeviceDetailContext } from '../../context';

const SectionTitle = ({
  title,
  toggleLabel,
  toggleVariant = 'secondary',
  toggled = false,
  toggleName,
  handleToggleChange,
}) => (
  <Flex align="center">
    <Typography className="text-montserrat-600-16-16-gray-500">
      {title}
    </Typography>
    {toggleLabel && (
      <Toggle
        name={toggleName}
        checked={toggled}
        variant={toggleVariant}
        style={{ marginLeft: '16px' }}
        className="toggle-44-24"
        onChange={(e) => handleToggleChange({ toggleName, value: e })}
      />
    )}
  </Flex>
);

// PropTypes for SectionTitle
SectionTitle.propTypes = {
  title: PropTypes.string.isRequired,
  toggleLabel: PropTypes.bool,
  toggleVariant: PropTypes.string,
  toggled: PropTypes.bool,
  toggleName: PropTypes.string,
  handleToggleChange: PropTypes.func,
};

const Field = ({ label, component }) => (
  <Flex vertical gap={8}>
    <Typography className="text-inter-500-14-20-gray-700">{label}</Typography>
    {component}
  </Flex>
);

// PropTypes for Field
Field.propTypes = {
  label: PropTypes.string.isRequired,
  component: PropTypes.element.isRequired,
};

const FieldGroup = ({ fields }) => (
  <Col span={8} xs={24} sm={12} md={12} lg={12} xl={8} xxl={8}>
    {fields.map((field, index) => (
      <div key={index} style={{ marginTop: index === 0 ? 0 : '16px' }}>
        <Field label={field.label} component={field.component} />
      </div>
    ))}
  </Col>
);

// PropTypes for FieldGroup
FieldGroup.propTypes = {
  fields: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      component: PropTypes.element.isRequired,
    }),
  ).isRequired,
};

const allNumbers = [];

const PriCas = () => {
  const { edgeDeviceDetail } = useEdgeDeviceDetailContext();

  const [casFailOver, setCasFailOver] = useState(false);
  const [priFailOver, setPriFailOver] = useState(false);
  const [priCaseToggle, setPriCasToggle] = useState({
    priToggle: true,
    casToggle: true,
  });
  const [priFailOverNumbers, setPriFailOverNumbers] = useState([]);
  const [casFailOverNumbers, setCasFailOverNumbers] = useState([]);

  const handleToggleChange = (key) => {
    const toggleName = key.toggleName;
    setPriCasToggle({ ...priCaseToggle, [toggleName]: key?.value });
  };

  const priSettings = edgeDeviceDetail?.priSettings || {};
  const casSettings = edgeDeviceDetail?.sipSettings || {};

  useEffect(() => {
    setCasFailOver(casSettings?.casFailovers?.length);
    setPriFailOver(priSettings?.priFailovers?.length);
    setPriFailOverNumbers(priSettings?.priFailovers || []);
    setCasFailOverNumbers(casSettings?.casFailovers || []);
  }, [priSettings, casSettings]);

  const priFields = [
    [
      {
        label: 'Number of Channels',
        component: (
          <Input
            placeholder="24"
            variant="filled"
            value={priSettings?.channels || ''}
            disabled
            style={{ cursor: 'default', color: '#667085' }}
          />
        ),
      },
      {
        label: 'Timing Source',
        component: (
          <Select
            value={priSettings?.timeAndSource || undefined}
            options={[{ value: 'internal', label: 'Internal' }]}
            disabled
          />
        ),
      },
      {
        label: 'Switch Type',
        component: (
          <Select
            value={priSettings?.switchType || undefined}
            options={[{ value: 'national', label: 'National' }]}
            disabled
          />
        ),
      },
    ],
    [
      {
        label: 'T1 Framing',
        component: (
          <Select
            value={priSettings?.framing || undefined}
            options={[{ value: 'd4', label: 'd4' }]}
            disabled
          />
        ),
      },
      {
        label: 'D Channel',
        component: (
          <Input
            variant="filled"
            value={priSettings?.dChannel || ''}
            disabled
            style={{ cursor: 'default' }}
          />
        ),
      },
      {
        label: 'Channel Range',
        component: (
          <Input
            placeholder="1-24"
            variant="filled"
            value={priSettings?.echoChannels || ''}
            disabled
            style={{ cursor: 'default' }}
          />
        ),
      },
    ],
    [
      {
        label: 'Line Code',
        component: (
          <Select
            value={priSettings?.lineCode || undefined}
            options={[{ value: 'b8zs', label: 'b8zs' }]}
            disabled
          />
        ),
      },
      {
        label: 'B Channels',
        component: (
          <Input
            placeholder="1-24"
            variant="filled"
            value={priSettings?.bChannels || ''}
            disabled
            style={{ cursor: 'default' }}
          />
        ),
      },
      {
        label: 'Echo Cancelation Type',
        component: (
          <Input
            placeholder="mg2"
            variant="filled"
            value={priSettings?.echoCancellationType || ''}
            disabled
            style={{ cursor: 'default' }}
          />
        ),
      },
    ],
  ];

  const casFields = [
    [
      {
        label: 'Number of Channels',
        component: (
          <Input
            placeholder="24"
            variant="filled"
            value={casSettings?.channels || ''}
            disabled
            style={{ cursor: 'default', color: '#667085' }}
          />
        ),
      },
      {
        label: 'Timing Source',
        component: (
          <Select
            value={casSettings?.timeAndSource || undefined}
            options={[{ value: 'internal', label: 'Internal' }]}
            disabled
          />
        ),
      },
      {
        label: 'Switch Type',
        component: (
          <Select
            value={casSettings?.switchType || undefined}
            options={[{ value: 'national', label: 'National' }]}
            disabled
          />
        ),
      },
    ],
    [
      {
        label: 'T1 Framing',
        component: (
          <Select
            value={casSettings?.framing || undefined}
            options={[{ value: 'd4', label: 'd4' }]}
            disabled
          />
        ),
      },
      {
        label: 'B Channel',
        component: (
          <Input
            variant="filled"
            value={casSettings?.bChannel || ''}
            disabled
            style={{ cursor: 'default' }}
          />
        ),
      },
      {
        label: 'Channel Range',
        component: (
          <Input
            placeholder="1-24"
            variant="filled"
            value={casSettings?.channelRange || ''}
            disabled
            style={{ cursor: 'default' }}
          />
        ),
      },
    ],
    [
      {
        label: 'Line Code',
        component: (
          <Select
            value={casSettings?.lineCode || undefined}
            options={[{ value: 'b8zs', label: 'b8zs' }]}
            disabled
          />
        ),
      },
      {
        label: 'Auth Type',
        component: (
          <Input
            variant="filled"
            value={casSettings?.authType || ''}
            disabled
            style={{ cursor: 'default' }}
          />
        ),
      },
      {
        label: 'Echo Cancelation Type',
        component: (
          <Input
            variant="filled"
            value={casSettings?.echoCancellationType || ''}
            disabled
            style={{ cursor: 'default' }}
          />
        ),
      },
    ],
  ];

  const handleAddNumber = (value, setFailOverNumbers) => {
    const selected = allNumbers.find((num) => num === value);
    if (selected) setFailOverNumbers((prev) => [...prev, selected]);
  };

  const renderSortableList = (numbers, setFailOverNumbers) => (
    <ReactSortable
      list={numbers}
      setList={setFailOverNumbers}
      animation={150}
      handle=".drag-handle"
      className="sortable-numbers-list"
    >
      {numbers.map((item) => (
        <div key={item.id} style={{ width: '100%' }}>
          <Flex
            justify="space-between"
            align="center"
            style={{ width: '100%' }}
          >
            <Flex align="center" gap={4}>
              <DragIcon
                className="drag-handle"
                width={18}
                height={17}
                style={{ cursor: 'grab' }}
              />
              <Typography>{item.name}</Typography>
            </Flex>
            <CloseTwoIcon
              style={{ cursor: 'pointer' }}
              onClick={() =>
                setFailOverNumbers((prev) =>
                  prev.filter((num) => num.id !== item.id),
                )
              }
            />
          </Flex>
          {numbers.length !== numbers.indexOf(item) + 1 && (
            <Divider className="divider-sm" />
          )}
        </div>
      ))}
    </ReactSortable>
  );

  const renderSelectField = (label, numbers, setFailOverNumbers) => (
    <Flex vertical>
      <Typography
        style={{ marginBottom: '8px' }}
        className="text-inter-500-14-20-gray-700"
      >
        {label}
      </Typography>
      <Select
        placeholder="Select Box Serial Number"
        options={numbers.map((num) => ({
          value: num,
          label: num,
        }))}
        onChange={(value) => handleAddNumber(value, setFailOverNumbers)}
        style={{ width: '100%' }}
      />
    </Flex>
  );

  const renderFields = (fields) =>
    fields.map((group, index) => <FieldGroup key={index} fields={group} />);

  return (
    <Flex vertical className="admin-tools">
      <Flex vertical gap={24}>
        <SectionTitle
          title="PRI"
          toggleName={'priToggle'}
          toggleLabel
          toggled={priCaseToggle['priToggle']}
          handleToggleChange={handleToggleChange}
        />
        {priCaseToggle?.priToggle && (
          <>
            <Row gutter={[16]}>{renderFields(priFields)}</Row>
            <Flex align="center">
              <Typography className="text-montserrat-600-16-16-gray-500">
                Echo Cancelation
              </Typography>
              <Toggle
                value={priSettings?.echoCancellation}
                variant="secondary"
                style={{ marginLeft: '16px' }}
                className="toggle-44-24"
              />
              <Typography
                className="text-montserrat-600-16-16-gray-500"
                style={{ marginLeft: '16px' }}
              >
                PRI Failover
              </Typography>
              <Toggle
                variant="secondary"
                style={{ marginLeft: '26px' }}
                value={priFailOver}
                onChange={() => setPriFailOver(!priFailOver)}
                className="toggle-44-24"
              />
            </Flex>
            {priFailOver && (
              <Row gutter={[16]}>
                <Col span={8} xs={24} sm={12} md={12} lg={12} xl={8} xxl={8}>
                  {renderSelectField(
                    'Select Backup PRI',
                    priFailOverNumbers,
                    setPriFailOverNumbers,
                  )}
                </Col>
                <Col span={8} xs={24} sm={12} md={12} lg={12} xl={8} xxl={8}>
                  {priFailOverNumbers?.length > 0 &&
                    renderSortableList(
                      priFailOverNumbers,
                      setPriFailOverNumbers,
                    )}
                </Col>
              </Row>
            )}
          </>
        )}
      </Flex>

      <Divider style={{ borderBlockStart: '1px solid #D0D5DD' }} />

      <Flex vertical gap={24}>
        <SectionTitle
          title="CAS"
          toggleLabel
          toggleName={'casToggle'}
          toggled={priCaseToggle['casToggle']}
          handleToggleChange={handleToggleChange}
        />
        {priCaseToggle?.casToggle && (
          <>
            <Row gutter={[16]}>{renderFields(casFields)}</Row>
            <Flex align="center">
              <Typography className="text-montserrat-600-16-16-gray-500">
                Echo Cancelation
              </Typography>
              <Toggle
                value={casSettings?.echoCancellation}
                variant="secondary"
                style={{ marginLeft: '16px' }}
                className="toggle-44-24"
              />
              <Typography
                className="text-montserrat-600-16-16-gray-500"
                style={{ marginLeft: '16px' }}
              >
                CAS Failover
              </Typography>
              <Toggle
                variant="secondary"
                style={{ marginLeft: '16px' }}
                value={casFailOver}
                onChange={() => setCasFailOver(!casFailOver)}
                className="toggle-44-24"
              />
            </Flex>
            {casFailOver && (
              <Row gutter={[16]}>
                <Col span={8} xs={24} sm={12} md={12} lg={12} xl={8} xxl={8}>
                  {renderSelectField(
                    'Select Backup CAS',
                    casFailOverNumbers,
                    setCasFailOverNumbers,
                  )}
                </Col>
                <Col span={8} xs={24} sm={12} md={12} lg={12} xl={8} xxl={8}>
                  {casFailOverNumbers?.length > 0 &&
                    renderSortableList(
                      casFailOverNumbers,
                      setCasFailOverNumbers,
                    )}
                </Col>
              </Row>
            )}
          </>
        )}
      </Flex>

      <Divider style={{ borderBlockStart: '1px solid #D0D5DD' }} />

      <Flex align="center" justify="flex-end">
        <Button>Reset Settings</Button>
        <Button style={{ marginLeft: '8px' }}>Save Details</Button>
      </Flex>
    </Flex>
  );
};

export default PriCas;
