.network-interface-row {
  padding: 16px;
  max-width: 1200px;
  margin: 0 auto;
}

.interface-card,
.feature-card,
.interface-form-card {
  background-color: var(--bg-blue);
  border-radius: 8px;
  padding: 16px 0;
}

.section-title,
.feature-title {
  font-size: 1rem;
  color: #000000;
}

.info-row,
.feature-row,
.form-row {
  border-bottom: 1px solid #3e404280;
  padding: 8px 16px;
}

.infoTab {
  background-color: var(--primary-white);
  border-radius: 8px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.button {
  margin-top: 16px;
  background-color: var(--button-light-blue);
  border-color: var(--button-light-blue);
}

/* table css */
.table-cell-center {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.sync-button {
  width: 40px;
  height: 35px;
  margin-left: 8px;
  background-color: var(--bg-gray);
}
input[disabled],
textarea[disabled],
select[disabled] {
  background-color: var(--disabled-field-color) !important;
}

/* Table custom border and style */
.custom-table .ant-table {
  border-radius: 4px 0px 0px 0px;
  overflow: hidden;
}

.custom-table .ant-table-thead > tr > th {
  border: 1px solid #5e6278;
  text-align: center;
  background-color: #f5f5f5;
  color: #000000;
}

.custom-table .ant-table-tbody > tr > td {
  border: 1px solid #5e6278;
  text-align: center;
  color: #a1a5b7;
}
