import { useState, useEffect } from 'react';
import { Row, Col, Table, Flex, Spin, Empty } from 'antd';
import PropTypes from 'prop-types';
import {
  Button,
  Toggle,
  Input,
  Typography,
  ReloadSync,
  CircleTrue,
  CircleFalse,
  Tooltip,
} from '@/components';
import './styles.css';
import { useEdgeDeviceDetailContext } from '../../context';
import { useEdgeDeviceStore } from '@/store/edgeDeviceSlice';
import { useStore } from '@/store';

const NetworkInterfaceInfo = () => {
  const { edgeDeviceDetail } = useEdgeDeviceDetailContext();
  const [networkData, setNetworkData] = useState([]);
  const { loadingState } = useEdgeDeviceStore();

  const networkInfoError = useStore(
    (s) => s.error.errorState[`sub:${'networkInfo'}`] || null,
  );

  const wifiStatusError = useStore(
    (s) => s.error.errorState[`sub:${'wifiStatus'}`] || null,
  );

  const { wifiStatus = {}, networkInfo = {} } = edgeDeviceDetail || {};

  const StatusIcon = ({ status }) => (
    <div className="table-cell-center">
      {status === 'true' ? <CircleTrue /> : <CircleFalse />}
    </div>
  );

  useEffect(() => {
    if (networkInfo) {
      const formattedNetworkData = networkInfo?.interfaces?.map(
        (item, index) => ({
          key: index + 1,
          name: item.interface,
          internet: item.internet,
          icmp: item.icmp,
          wireguard: item.wg,
          dns: index === 0 ? networkInfo.dns : null,
          dnsRowSpan: index === 0 ? networkInfo.interfaces.length : 0,
        }),
      );

      setNetworkData(formattedNetworkData);
    }
  }, [networkInfo]);

  const columnsNetwork = [
    {
      title: <Typography className="text-inter-700-14-20">NAME</Typography>,
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: <Typography className="text-inter-700-14-20">Internet</Typography>,
      dataIndex: 'internet',
      key: 'internet',
      render: (status) => <StatusIcon status={status} />,
    },
    {
      title: <Typography className="text-inter-700-14-20">ICMP</Typography>,
      dataIndex: 'icmp',
      key: 'icmp',
      render: (status) => <StatusIcon status={status} />,
    },
    {
      title: (
        <Typography className="text-inter-700-14-20">Wireguard</Typography>
      ),
      dataIndex: 'wireguard',
      key: 'wireguard',
      render: (status) => <StatusIcon status={status} />,
    },
    {
      title: <Typography className="text-inter-700-14-20">DNS</Typography>,
      dataIndex: 'dns',
      key: 'dns',
      render: (status) =>
        status !== null ? <StatusIcon status={status} /> : null,
      onCell: (record) => ({
        rowSpan: record.dnsRowSpan || 0,
      }),
    },
  ];

  return (
    <div>
      <div className="network-interface-info-container">
        <Row gutter={[16, 16]} align="top">
          <Col xs={24} md={16} lg={16}>
            <Flex vertical className="interface-card">
              <Typography
                className="text-montserrat-500-16-20-black"
                style={{ marginLeft: '16px' }}
              >
                Network Interface Info
              </Typography>
              <Row
                style={{
                  marginTop: '16px',
                  borderBottom: '1px solid #3E404280',
                  padding: '8px 16px',
                }}
              >
                <Col xs={24} md={12} lg={4}>
                  <Typography className="text-inter-400-14-20">Eth0</Typography>
                </Col>
                <Col xs={24} md={12} lg={4}>
                  <Typography className="text-inter-400-14-20">IP</Typography>
                </Col>
                <Col xs={24} md={12} lg={4}>
                  <Typography className="text-inter-400-14-20">
                    Mac Address
                  </Typography>
                </Col>
                <Col xs={24} md={12} lg={4}>
                  <Typography className="text-inter-400-14-20">
                    Enable Eth0/3
                  </Typography>
                </Col>
                <Col xs={24} md={12} lg={4}>
                  <Typography className="text-inter-400-14-20">E1</Typography>
                </Col>
                <Col xs={24} md={12} lg={4}>
                  <Typography className="text-inter-400-14-20">
                    IP Type
                  </Typography>
                </Col>
              </Row>
              <Row
                style={{
                  borderBottom: '1px solid #3E404280',
                  padding: '8px 12px',
                }}
              >
                <Col xs={24} md={12} lg={4}>
                  <Typography className="text-inter-600-14-20-gray-900"></Typography>
                </Col>
                <Col xs={24} md={12} lg={4}>
                  <Typography className="text-inter-600-14-20-gray-900">
                    ************
                  </Typography>
                </Col>
                <Col xs={24} md={12} lg={4}>
                  <Typography className="text-inter-600-14-20-gray-900">
                    9cadef44a39
                  </Typography>
                </Col>
                <Col xs={24} md={12} lg={4}>
                  <Toggle
                    checked={edgeDeviceDetail?.eth3Disable}
                    defaultChecked
                    size="small"
                    variant="secondary"
                  />
                </Col>
                <Col xs={24} md={12} lg={4}>
                  <Flex gap={4} align="center">
                    <Typography className="text-inter-600-14-20-gray-900">
                      dhcp
                    </Typography>
                    <Toggle defaultChecked size="small" variant="secondary" />
                  </Flex>
                </Col>
                <Col xs={24} md={12} lg={4}>
                  <Typography className="text-inter-600-14-20-gray-900">
                    Static
                  </Typography>
                </Col>
              </Row>
              <Row style={{ marginTop: '16px' }}>
                <Button
                  type="primary"
                  size="middle"
                  style={{ marginLeft: '16px' }}
                >
                  Result Info
                </Button>
              </Row>
            </Flex>
          </Col>
          <Col xs={24} md={16} lg={4}>
            <Flex vertical className="interface-card">
              <Typography
                style={{ marginTop: '40px', marginLeft: '16px' }}
              ></Typography>
              <Row>
                <Col span={24}>
                  <Typography
                    className="text-inter-400-14-20"
                    style={{ marginLeft: '16px' }}
                  >
                    E2 DHCP
                  </Typography>
                </Col>
              </Row>
              <Row
                className="info-row"
                style={{ marginTop: '8px', borderTop: '1px solid #3E404280' }}
              >
                <Col span={6}>
                  <Typography className="text-inter-600-14-20-gray-900">
                    OFF
                  </Typography>
                </Col>
                <Col span={12}>
                  <Toggle defaultChecked size="small" variant="secondary" />
                </Col>
                <Col span={6}>
                  <Typography className="text-inter-600-14-20-gray-900">
                    ON
                  </Typography>
                </Col>
              </Row>
              <Row style={{ marginTop: '18px' }}>
                <Button type="primary" style={{ marginLeft: '16px' }}>
                  Refresh DHCP
                </Button>
              </Row>
            </Flex>
          </Col>
          <Col xs={24} md={16} lg={4}>
            <Flex vertical className="interface-card" justify="end">
              <Typography
                className="text-inter-400-14-20"
                style={{ marginTop: '40px', marginLeft: '16px' }}
              ></Typography>
              <Row>
                <Col span={24}>
                  <Typography style={{ marginLeft: '16px' }}>
                    Port Forwarding
                  </Typography>
                </Col>
              </Row>
              <Row
                className="info-row"
                style={{ marginTop: '8px', borderTop: '1px solid #3E404280' }}
              >
                <Col span={6}>
                  <Typography className="text-inter-600-14-20-gray-900">
                    --
                  </Typography>
                </Col>
                <Col span={12}></Col>
                <Col span={6}>
                  <Typography className="text-inter-600-14-20-gray-900"></Typography>
                </Col>
              </Row>
              <Row style={{ marginTop: '18px' }}>
                <Button
                  type="primary"
                  size="middle"
                  style={{ marginLeft: '16px' }}
                >
                  Refresh DHCP
                </Button>
              </Row>
            </Flex>
          </Col>
        </Row>
      </div>

      <Flex vertical gap={8} style={{ marginTop: '24px' }}>
        <Flex align="center" gap={4}>
          <Flex gap={8}>
            <Typography className="text-montserrat-600-13-100-black">
              WIFI Info
            </Typography>

            {loadingState?.wifiStatus && <Spin size="small" />}
          </Flex>
          {wifiStatusError && (
            <Typography
              className="small-text"
              style={{
                color: 'var(--error-600)',
                maxWidth: '150px',
              }}
              ellipsis={{
                tooltip: <Tooltip>{wifiStatusError}</Tooltip>,
              }}
            >
              {wifiStatusError}
            </Typography>
          )}
        </Flex>
        <Row gutter={16}>
          <Col xs={24} sm={12} md={6}>
            <Flex vertical gap={8}>
              <Typography className="text-inter-500-14-20-gray-700">
                Status
              </Typography>
              <Input
                value={wifiStatus?.status}
                placeholder="Disabled"
                disabled
              />
            </Flex>
            <Flex vertical gap={8} style={{ marginTop: '16px' }}>
              <Typography className="text-inter-500-14-20-gray-700">
                IP
              </Typography>
              <Input value={wifiStatus?.IP} placeholder="Please specify" />
            </Flex>
          </Col>

          <Col xs={24} sm={12} md={6}>
            <Flex vertical gap={8}>
              <Typography className="text-inter-500-14-20-gray-700">
                SSID
              </Typography>
              <Input value={wifiStatus?.SSID} placeholder="Please specify" />
            </Flex>

            <Flex vertical gap={8} style={{ marginTop: '16px' }}>
              <Typography className="text-inter-500-14-20-gray-700">
                Subnet
              </Typography>
              <Input
                value={wifiStatus?.Subnet}
                placeholder="Please Specify"
                disabled
              />
            </Flex>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Flex vertical gap={8}>
              <Typography className="text-inter-500-14-20-gray-700">
                Mode
              </Typography>
              <Input value={wifiStatus?.Mode} placeholder="Please specify" />
            </Flex>
            <Flex vertical gap={8} style={{ marginTop: '16px' }}>
              <Typography className="text-inter-500-14-20-gray-700">
                Gateway
              </Typography>
              <Input
                value={wifiStatus?.Gateway}
                placeholder="Please Specify"
                disabled
              />
            </Flex>
          </Col>

          <Col xs={24} sm={12} md={6}>
            <Flex vertical gap={8}>
              <Typography className="text-inter-500-14-20-gray-700">
                Security Options
              </Typography>
              <Input
                value={wifiStatus?.Sec_Mode}
                placeholder="Please specify"
              />
            </Flex>
            <Flex vertical gap={8} style={{ marginTop: '16px' }}>
              <Typography className="text-inter-500-14-20-gray-700">
                Password
              </Typography>
              <Input
                value={wifiStatus?.Password}
                type="password"
                placeholder="Please Specify"
                disabled
              />
            </Flex>
          </Col>
        </Row>
        <Flex style={{ marginTop: '8px' }}>
          <Button type="primary">Connect</Button>
        </Flex>
      </Flex>
      <div style={{ width: '100%', marginTop: '24px' }}>
        <Row style={{ marginBottom: '20px', alignItems: 'center' }}>
          <Button>Fetch Result</Button>
          <Button
            type="primary"
            classNames="sync-button"
            icon={<ReloadSync />}
          />
        </Row>
        <Row style={{ display: 'flex', gap: '16px' }}>
          <Col flex="1">
            <Table
              className="custom-table"
              columns={columnsNetwork}
              dataSource={networkData}
              locale={
                networkInfoError && {
                  emptyText: (
                    <Empty>
                      <Typography
                        className="small-text"
                        style={{ color: 'var(--error-600)' }}
                      >
                        {networkInfoError}
                      </Typography>
                    </Empty>
                  ),
                }
              }
              pagination={false}
            />
          </Col>
        </Row>
      </div>
    </div>
  );
};
NetworkInterfaceInfo.propTypes = {
  status: PropTypes.string,
};

export default NetworkInterfaceInfo;
