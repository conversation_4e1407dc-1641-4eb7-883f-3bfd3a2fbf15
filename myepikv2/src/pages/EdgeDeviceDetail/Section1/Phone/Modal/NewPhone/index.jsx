import { useState } from 'react';
import { Modal, Form, Input, Select, Flex, Divider } from 'antd';
import { Button, Typography } from '@/components';
import PropTypes from 'prop-types';
import EditNumberModal from './EditNumberModal';
import './styles.css';

const AddNewPhoneModal = ({ onCancel, onSubmit }) => {
  const [form] = Form.useForm();
  const [isEditModalVisible, setEditModalVisible] = useState(false);
  const [selectedNumber, setSelectedNumber] = useState(''); // Tracks the selected number

  const handleSubmit = (values) => {
    console.log('Form values:', values);
    onSubmit(values);
  };

  const handleEdit = () => {
    setEditModalVisible(true); // Open the Edit modal
  };

  const closeEditModal = () => {
    setEditModalVisible(false); // Close the Edit modal
  };

  const renderLabel = (label) => (
    <span>
      {label} <span style={{ color: 'red' }}>*</span>
    </span>
  );

  return (
    <>
      <Modal
        title={
          <Typography className="heading-four-bold">Add New Phone</Typography>
        }
        open={true}
        maskClosable={false}
        onCancel={onCancel}
        footer={[
          <Button
            key="cancel"
            variant="outlined"
            color="default"
            onClick={onCancel}
          >
            Cancel
          </Button>,
          <Button key="submit" type="primary" onClick={() => form.submit()}>
            Save
          </Button>,
        ]}
        width={450}
      >
        <Divider className="divider-color" />
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          className="custom-form"
          requiredMark={false}
        >
          <Form.Item
            name="displayName"
            label={renderLabel('Display Name')}
            rules={[
              { required: true, message: 'Please enter the display name' },
            ]}
          >
            <Input placeholder="Specify" />
          </Form.Item>
          <Form.Item
            name="extension"
            label={renderLabel('Extension')}
            rules={[
              { required: true, message: 'Please enter the extension' },
              {
                pattern: /^[+-]?\d+(-\d+)*$/,
                message: 'Please enter a valid number',
              },
            ]}
          >
            <Input placeholder="Specify" />
          </Form.Item>
          <Form.Item
            name="number"
            label={renderLabel('Number')}
            rules={[{ required: true, message: 'Please select a number' }]}
          >
            <Flex justify="space-between" align="center">
              <Select
                placeholder="Please select number"
                options={[
                  { value: '+12345679237', label: '+12345679237' },
                  { value: '+98765432101', label: '+98765432101' },
                ]}
                onChange={(value) => setSelectedNumber(value)}
                value={selectedNumber || undefined} // Fix to show placeholder
                style={{ width: '85%' }}
              />
              {selectedNumber && ( // Only show the Edit button if a number is selected
                <Button
                  onClick={handleEdit}
                  style={{
                    backgroundColor: '#4d9cd3',
                  }}
                >
                  Edit
                </Button>
              )}
            </Flex>
          </Form.Item>
          <Form.Item
            name="modal"
            label={renderLabel('Modal')}
            rules={[{ required: true, message: 'Please specify the modal' }]}
          >
            <Input placeholder="Specify" />
          </Form.Item>
        </Form>
        <Divider className="divider-color" />
      </Modal>
      {/* Edit Modal */}
      {isEditModalVisible && (
        <EditNumberModal
          visible={isEditModalVisible}
          onCancel={closeEditModal}
          onSubmit={(values) => {
            console.log('Edited values:', values);
            closeEditModal();
          }}
        />
      )}
    </>
  );
};

AddNewPhoneModal.propTypes = {
  onCancel: PropTypes.func.isRequired,
  onSubmit: PropTypes.func.isRequired,
};

export default AddNewPhoneModal;
