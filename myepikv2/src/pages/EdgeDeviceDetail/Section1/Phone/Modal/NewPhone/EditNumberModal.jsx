import { <PERSON>, Row, Col, Divider } from 'antd';
import { Modal, Input, Typography, Button, Select } from '@/components';
import PropTypes from 'prop-types';

const EditNumberModal = ({ visible, onCancel, onSubmit }) => {
  const [form] = Form.useForm();

  const handleSubmit = (values) => {
    console.log('Form values:', values);
    onSubmit(values);
  };
  return (
    <Modal
      title={<Typography className="heading-four-bold">Edit Number</Typography>}
      open={visible}
      onCancel={onCancel}
      maskClosable={false}
      footer={[
        <Row key="footer-row" justify="space-between">
          <Col>
            <Button onClick={onCancel} variant="outlined" color="default">
              Cancel
            </Button>
          </Col>
          <Col>
            <Button onClick={() => form.submit()} type="primary">
              Save
            </Button>
          </Col>
        </Row>,
      ]}
      width={600}
    >
      <Divider className="divider-color" />
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        requiredMark={false}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="number"
              label={<Typography className="label-bold">Number</Typography>}
            >
              <Input placeholder="+12059470876" disabled />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="type"
              label={<Typography className="label-bold">Type</Typography>}
              rules={[{ required: true, message: 'Please select the type' }]}
            >
              <Select
                placeholder="Unassigned"
                options={[
                  { value: 'Assigned', label: 'Assigned' },
                  { value: 'Unassigned', label: 'Unassigned' },
                ]}
              />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="company"
              label={<Typography className="label-bold">Company</Typography>}
            >
              <Input value="EPIK" disabled />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="user"
              label={<Typography className="label-bold">User</Typography>}
            >
              <Input
                value="<EMAIL>"
                disabled
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <Divider className="divider-color" />
    </Modal>
  );
};

EditNumberModal.propTypes = {
  visible: PropTypes.bool.isRequired,
  onCancel: PropTypes.func.isRequired,
  onSubmit: PropTypes.func.isRequired,
};

export default EditNumberModal;
