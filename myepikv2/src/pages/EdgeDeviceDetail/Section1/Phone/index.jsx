import { useState } from 'react';
import PropTypes from 'prop-types';
import { Divider, Row, Col, Empty } from 'antd';
import { Button, Typography, Flex } from '@/components';
import './styles.css';
import AddNewPhoneModal from './Modal/NewPhone/index'; // Import the modal component
import { useEdgeDeviceDetailContext } from '../../context';

const CardRow = ({ label, value }) => (
  <Row className="phone-card-row">
    <Col span={12}>
      <Typography className="text-inter-500-14-20-gray-600">{label}</Typography>
    </Col>
    <Col span={12}>
      <Typography className="text-inter-600-14-20-gray3">{value}</Typography>
    </Col>
  </Row>
);

CardRow.propTypes = {
  label: PropTypes.string.isRequired, // Label must be a string and is required
  value: PropTypes.string.isRequired, // Value must be a string and is required
};

const PhoneCard = ({ phone = {} }) => {
  return (
    <Flex vertical className="phone-card">
      <Typography className="text-montserrat-600-20-100-black phone-card-title">
        {phone?.numberDoc?.number || '--'}
      </Typography>
      <CardRow label={'Display Name'} value={phone?.displayName || '--'} />
      <Divider className="mtb-sm" />
      <CardRow label={'#Extension'} value={phone?.extension || '--'} />
      <Divider className="mtb-sm" />
      <CardRow label={'Model'} value={phone?.model || '--'} />
      <Divider className="mtb-sm" />
    </Flex>
  );
};

PhoneCard.propTypes = {
  phone: PropTypes.object,
};

const PhonesTab = () => {
  const [isModalVisible, setModalVisible] = useState(false);

  const { edgeDeviceDetail } = useEdgeDeviceDetailContext();

  const handleAssignPhoneClick = () => {
    setModalVisible(true);
  };

  const handleModalCancel = () => {
    setModalVisible(false);
  };

  const handleModalSubmit = (values) => {
    console.log('Modal submitted values:', values);
    setModalVisible(false);
  };

  return (
    <>
      {edgeDeviceDetail?.phoneDocs?.length ? (
        <>
          <Row gap={'middle'} gutter={[10, 10]} wrap>
            {edgeDeviceDetail?.phoneDocs?.map((phone, index) => {
              return (
                <Col
                  key={`${phone?._id}${index}`}
                  xs={24}
                  sm={12}
                  md={8}
                  lg={8}
                  xl={8}
                  xll={6}
                >
                  <PhoneCard phone={phone} />
                </Col>
              );
            })}
          </Row>
          <Flex gap={4} className="mt-15">
            <Button>Push Configuration</Button>
            <Button onClick={handleAssignPhoneClick}>Assign Phone</Button>
          </Flex>

          {/* Modal Component */}
          {isModalVisible && (
            <AddNewPhoneModal
              onCancel={handleModalCancel}
              onSubmit={handleModalSubmit}
            />
          )}
        </>
      ) : (
        <Flex
          justify="center"
          align="center"
          style={{ width: '100%', minHeight: '200px' }}
        >
          <Empty />
        </Flex>
      )}
    </>
  );
};

export default PhonesTab;
