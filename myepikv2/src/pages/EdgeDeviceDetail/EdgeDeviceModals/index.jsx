import { DeleteModal } from '@/components';
import {
  AddOOBMCategory,
  CDRAnalytics,
  CDRLogs,
  EditOOBM,
  PortConfiguration,
  PortDetails,
  AddNote,
  EdgeDevicePing,
  AssignE911,
  VoiceMail,
  PortCallHistory,
} from '../Modals';
import { useEdgeDeviceDetailContext } from '../context';

const EdgeDeviceModals = () => {
  const { modals, closeModal } = useEdgeDeviceDetailContext();

  const modalMap = {
    deleteDevice: DeleteModal,
    addNote: AddNote,
    portDetails: PortDetails,
    portConfiguration: PortConfiguration,
    cdrLogs: CDRLogs,
    cdrAnalytics: CDRAnalytics,
    editOOBM: EditOOBM,
    addOOBMCategory: AddOOBMCategory,
    edgeDevicePing: EdgeDevicePing,
    e911Modal: AssignE911,
    voiceMail: VoiceMail,
    portCallHistory: PortCallHistory,
  };

  return (
    <>
      {Object.entries(modals).map(([key, modal]) => {
        const ModalComponent = modalMap[key];
        if (!modal.open) return null;
        return (
          <ModalComponent
            key={key}
            {...modal}
            onCancel={() => closeModal(key)}
            handleOk={() => closeModal(key)}
          />
        );
      })}
    </>
  );
};

export default EdgeDeviceModals;
