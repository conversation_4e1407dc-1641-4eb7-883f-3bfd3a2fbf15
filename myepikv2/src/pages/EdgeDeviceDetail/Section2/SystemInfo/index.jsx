import { useState, useMemo, lazy, Suspense } from 'react';
import { Typography, Flex, Select } from '@/components';
import './styles.css';
import { useEdgeDeviceDetailContext } from '../../context';
import { formatTimestamp } from '@/utils/date';
import { useStore } from '@/store';

const componentsMap = {
  NetworkInfo: lazy(() => import('../NetworkInfo')),
  BoxFirmware: lazy(() => import('../BoxFirmware')),
  Cpu: lazy(() => import('../Cpu')),
  DiskI0: lazy(() => import('../DiskIO')),
  DiskUsage: lazy(() => import('../DiskUsage')),
  LoadAvg: lazy(() => import('../LoadAvg')),
  MemoryNetwork: lazy(() => import('../MemoryNetwork')),
  Network: lazy(() => import('../Network')),
  PortStatuses: lazy(() => import('../PortStatuses')),
  Uptime: lazy(() => import('../Uptime')),
  WifiInfo: lazy(() => import('../WifiInfo')),
};

const options = Object.keys(componentsMap).map((key) => ({
  value: key,
  label: key.replace(/([A-Z])/g, ' $1').trim(),
}));

const getComponentData = (selected, edgeDeviceDetail = {}) => {
  const { sysInfo = {}, modemInfo } = edgeDeviceDetail || {};

  switch (selected) {
    case 'NetworkInfo': {
      const epiArray = sysInfo?.networkInfoDetail?.epiInfo || [];
      const arpData = Object.entries(sysInfo?.networkInfoDetail?.arp || {}).map(
        ([ip, mac]) => ({ title: ip, description: mac }),
      );

      const eth0Config = sysInfo?.networkInfoDetail?.eth0Config || {};
      const networkConfig = Object.entries(eth0Config).map(([key, value]) => ({
        title: key,
        description: value || 'N/A',
      }));

      const eth0Stats = Object.entries(
        sysInfo?.networkInfoDetail?.eth0Stats || {},
      ).map(([key, value]) => ({
        title: key,
        description: value || 'N/A',
      }));

      const linkStatus = Object.entries(
        sysInfo?.networkInfoDetail?.linkStatus || {},
      ).map(([key, value]) => ({
        title: key,
        description: value || 'N/A',
      }));

      const icmp = Object.entries(sysInfo?.networkInfoDetail?.icmp || {}).map(
        ([key, value]) => ({
          title: key,
          description: value || 'N/A',
        }),
      );

      const tunnelAccess = Object.entries(
        sysInfo?.networkInfoDetail?.tunnelAccess || {},
      ).map(([key, value]) => ({
        title: key,
        description: value || 'N/A',
      }));

      const cdmBoxStatus = Object.entries(
        sysInfo?.networkInfoDetail?.cdmBoxStatus || {},
      ).map(([key, value]) => ({
        title: key,
        description: value || 'N/A',
      }));

      const modemInfoData = Object.entries(modemInfo || {})
        .filter(([key]) => !key.endsWith('_placeholder'))
        .map(([key, value]) => ({
          title: key,
          description: value?.trim() || 'N/A',
        }));

      const dcAvgPing = Object.entries(
        sysInfo?.networkInfoDetail?.dcAvgPing || {},
      )
        .filter(([key]) => key !== 'timeUpdated')
        .map(([key, value]) => ({
          title: key,
          description: value?.trim() || 'N/A',
        }));

      const epiInfoRows = Array.isArray(epiArray)
        ? epiArray.map((item) => {
            const ip = String(item?.ip ?? 'N/A').trim();
            const mac = String(item?.mac ?? 'N/A').trim();

            const description = Object.entries(item)
              .filter(([k]) => !['ip', 'mac'].includes(k))
              .map(([k, v]) => `${k}: ${String(v ?? '').trim()}`)
              .join('\n');

            return {
              title: `${ip} : ${mac}`,
              description: description || 'N/A',
            };
          })
        : [];

      const lastUpdated = formatTimestamp(
        sysInfo?.networkInfoDetail?.dcAvgPing?.timeUpdated,
        {
          format: 'MMM D, YYYY hh:mm:ss a z',
        },
      );

      return {
        arp: {
          title: 'ARP Table',
          data: arpData,
          span: 4,
        },
        networkConfiguration: {
          title: 'Network Configuration',
          data: networkConfig,
          span: 4,
        },
        eth0Stats: {
          title: 'Eth0 Stats',
          data: eth0Stats,
          span: 4,
        },
        LTESignalStrength: {
          title: 'LTE Signal Strength',
          data: sysInfo?.networkInfoDetail?.lteSgnalStrength
            ? [
                {
                  title: 'Signal Strength',
                  description:
                    sysInfo?.networkInfoDetail?.lteSgnalStrength || 'N/A',
                },
              ]
            : [],
          span: 4,
        },

        IP4G: {
          title: 'IP4G',
          data: sysInfo?.ip4G
            ? [
                {
                  title: '4GIP',
                  description: sysInfo?.ip4G || 'N/A',
                },
              ]
            : [],
          span: 4,
        },

        LinkStatus: {
          title: 'Link Status',
          data: linkStatus,
          span: 4,
        },

        tunnelIP: {
          title: 'Tunnel IP',
          data: sysInfo?.networkInfoDetail?.tunnelIP
            ? [
                {
                  title: 'Tunnel IP',
                  description: sysInfo?.networkInfoDetail?.tunnelIP || 'N/A',
                },
              ]
            : [],
          span: 4,
        },

        ICMP: {
          title: 'ICMP',
          data: icmp,
          span: 4,
        },

        edgeIP: {
          title: 'Edge IP',
          data: sysInfo?.networkInfoDetail?.edgeIP
            ? [
                {
                  title: 'Edge IP',
                  description: sysInfo?.networkInfoDetail?.edgeIP || 'N/A',
                },
              ]
            : [],
          span: 4,
        },

        modemCount: {
          title: 'Modem Count',
          data: sysInfo?.networkInfoDetail?.modemCount
            ? [
                {
                  title: 'Modem Count',
                  description: sysInfo?.networkInfoDetail?.modemCount || 'N/A',
                },
              ]
            : [],
          span: 4,
        },

        priCardStatus: {
          title: ' PRI Card Status',
          data: sysInfo?.networkInfoDetail?.priCardStatus?.priCardStatus
            ? [
                {
                  title: ' PRI Card Status',
                  description:
                    sysInfo?.networkInfoDetail?.priCardStatus?.priCardStatus ||
                    'N/A',
                },
              ]
            : [],
          span: 4,
        },

        cdmBoxStatus: {
          title: 'CDM Check',
          data: cdmBoxStatus,
          span: 4,
        },

        model: {
          title: 'EPIK Edge Model',
          data: edgeDeviceDetail?.model
            ? [
                {
                  title: 'EPIK Edge Model',
                  description: edgeDeviceDetail?.model || 'N/A',
                },
              ]
            : [],
          span: 4,
        },

        cdmStatus: {
          title: 'Management Agent Status',
          data: sysInfo?.cdmStatus
            ? [
                {
                  title: 'Management Agent Status',
                  description: sysInfo?.cdmStatus || 'N/A',
                },
              ]
            : [],
          span: 4,
        },

        activeInterface: {
          title: 'Active Interface',
          data: sysInfo?.networkInfoDetail?.activeInterface
            ? [
                {
                  title: 'Active Interface',
                  description:
                    sysInfo?.networkInfoDetail?.activeInterface || 'N/A',
                },
              ]
            : [],
          span: 4,
        },

        registrationStatus: {
          title: 'Registration Status',
          data: sysInfo?.networkInfoDetail?.registrationStatus
            ? [
                {
                  title: 'Registration Status',
                  description:
                    sysInfo?.networkInfoDetail?.registrationStatus || 'N/A',
                },
              ]
            : [],
          span: 4,
        },

        tunnelAccess: {
          title: 'Tunnel Access',
          data: tunnelAccess,
          span: 4,
        },

        dcAvgPing: {
          title: `Data Center Latency (${lastUpdated || '-'})`,
          data: dcAvgPing,
          span: 4,
        },

        powerSource: {
          title: 'Power Source',
          data: sysInfo?.networkInfoDetail?.powerSource
            ? [
                {
                  title: 'Power Source',
                  description: sysInfo?.networkInfoDetail?.powerSource || 'N/A',
                },
              ]
            : [],
          span: 4,
        },

        ePIKFailoverUpdateStatus: {
          title: 'EPIK Failover Update Status',
          data: sysInfo?.failoverUpdateStatus
            ? [
                {
                  title: 'Status',
                  description: sysInfo?.failoverUpdateStatus || 'N/A',
                },
              ]
            : [],
          span: 4,
        },

        restbinStatus: {
          title: 'EPIK Rest Status',
          data: sysInfo?.restbinStatus
            ? [
                {
                  title: 'Status',
                  description: sysInfo?.restbinStatus || 'N/A',
                },
              ]
            : [],
          span: 4,
        },

        ePIKRestVersion: {
          title: 'EPIK Rest Version',
          data: sysInfo?.restbinVersion
            ? [
                {
                  title: 'Version',
                  description: sysInfo?.restbinVersion || 'N/A',
                },
              ]
            : [],
          span: 4,
        },

        modemInfo: {
          title: 'Modem Info',
          data: modemInfoData,
          span: 4,
        },

        epiInfo: {
          title: 'EPI Info',
          data: epiInfoRows,
          span: 12,
        },
      };
    }

    case 'BoxFirmware': {
      const firmware = sysInfo?.networkInfoDetail?.fwVersion;
      const data = {
        title: 'Version',
        description: firmware || 'N/A',
      };
      return data;
    }

    case 'PortStatuses': {
      const ports = sysInfo?.portStatuses || {};
      const data = Object.entries(ports).map(([key, val]) => ({
        title: key,
        description: val.charAt(0).toUpperCase() + val.slice(1),
      }));

      return {
        firstSection: {
          title: '',
          data,
          span: 4,
        },
      };
    }

    case 'Uptime': {
      return {
        firstSection: {
          title: '',
          data: [
            {
              title: 'uptime:',
              description: sysInfo?.uptime?.uptime || 'N/A',
            },
          ],
          span: 4,
        },
      };
    }

    case 'WifiInfo': {
      const wifi = sysInfo?.wifiInfo || {};
      return {
        firstSection: {
          title: '',
          data: [
            { title: 'ap :', description: wifi.ap || '--' },
            { title: 'mode :', description: wifi.mode || '--' },
            { title: 'ssid :', description: wifi.ssid?.trim() || '--' },
          ],
          span: 4,
        },
      };
    }

    case 'Cpu': {
      const cpu = sysInfo?.cpu || {};

      const total = Number(cpu.Total) || 0;

      const idle = Number(cpu.Idle) || 0;
      const system = Number(cpu.System) || 0;
      const user = Number(cpu.User) || 0;

      const others = Object.entries(cpu)
        .filter(
          ([key, val]) =>
            !['CPUCount', 'Total', 'Idle', 'System', 'User'].includes(key) &&
            !isNaN(Number(val)),
        )
        .reduce((sum, [, val]) => sum + Number(val), 0);

      const formatValue = (val) => ({
        value: val,
        pct: total ? ((val / total) * 100).toFixed(1) : 0,
      });

      const data = [
        { type: 'Idle', ...formatValue(idle) },
        { type: 'System', ...formatValue(system) },
        { type: 'User', ...formatValue(user) },
        { type: 'Others', ...formatValue(others) },
      ];

      return data;
    }

    case 'DiskI0': {
      const diskIo = sysInfo?.diskIo || [];

      const data = diskIo.flatMap((d) => {
        const name = d.Name ?? 'unknown';
        const reads = Number(d.ReadsCompleted) || 0;
        const writes = Number(d.WritesCompleted) || 0;

        return [
          { type: 'ReadsCompleted', name, value: reads },
          { type: 'WritesCompleted', name, value: writes },
        ];
      });

      return data;
    }

    case 'DiskUsage': {
      const usage = sysInfo?.diskUsage || {};

      const avail = Number(usage.avail) || 0;
      const free = Number(usage.free) || 0;
      const used = Number(usage.used) || 0;

      const sum = avail + free + used;

      const formatValue = (val) => ({
        value: val,
        pct: sum ? Number(((val / sum) * 100).toFixed(1)) : 0,
      });

      const data = [
        { type: 'Avail', ...formatValue(avail) },
        { type: 'Free', ...formatValue(free) },
        { type: 'Used', ...formatValue(used) },
      ];

      return data;
    }

    case 'LoadAvg': {
      const load = sysInfo?.loadavg || {};

      const data = Object.entries(load)
        .filter(
          ([key, val]) => /^Loadavg\d+$/i.test(key) && !isNaN(Number(val)),
        )
        .map(([key, val]) => {
          const minutes = key.replace(/^\D+/, '');
          return {
            type: `${minutes}min`,
            name: key,
            value: Number(val),
          };
        })
        .sort((a, b) => parseFloat(a.type) - parseFloat(b.type));

      return data;
    }

    case 'MemoryNetwork': {
      const memory = sysInfo?.memory || {};

      const active = Number(memory.Active) || 0;
      const available = Number(memory.Available) || 0;
      const cached = Number(memory.Cached) || 0;
      const free = Number(memory.Free) || 0;
      const inactive = Number(memory.Inactive) || 0;
      const used = Number(memory.Used) || 0;

      const sum = active + available + cached + free + inactive + used;

      const formatValue = (val) => ({
        value: val,
        pct: sum ? Number(((val / sum) * 100).toFixed(1)) : 0,
      });

      const data = [
        { type: 'Active', ...formatValue(active) },
        { type: 'Available', ...formatValue(available) },
        { type: 'Cached', ...formatValue(cached) },
        { type: 'Free', ...formatValue(free) },
        { type: 'Inactive', ...formatValue(inactive) },
        { type: 'Used', ...formatValue(used) },
      ];

      return data;
    }

    case 'Network': {
      const network = sysInfo?.network || [];

      const data = network.flatMap((d) => {
        const name = d.Name ?? 'unknown';
        const RxBytes = Number(d.RxBytes) || 0;
        const TxBytes = Number(d.TxBytes) || 0;

        return [
          { type: 'RxBytes', name, value: RxBytes },
          { type: 'TxBytes', name, value: TxBytes },
        ];
      });

      return data;
    }

    default:
      return {};
  }
};

const SystemInfo = () => {
  const { edgeDeviceDetail } = useEdgeDeviceDetailContext();
  const [selectedOption, setSelectedOption] = useState('NetworkInfo');
  const SelectedComponent = componentsMap[selectedOption];

  const sysInfoError = useStore(
    (s) => s.error.errorState[`sub:${'sysInfo'}`] || null,
  );

  const componentData = useMemo(
    () => getComponentData(selectedOption, edgeDeviceDetail),
    [selectedOption, edgeDeviceDetail],
  );

  const lastUpdated = formatTimestamp(edgeDeviceDetail?.sysInfo?.time, {
    format: 'MMM D, YYYY hh:mm:ss a z',
  });

  const dataVersion = edgeDeviceDetail?.sysInfo?.time || 'no-time';
  const componentKey = `${selectedOption}-${dataVersion}`;

  return (
    <div
      style={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        flex: 1,
      }}
    >
      <Flex justify="space-between" align="center">
        <Select
          value={selectedOption}
          onChange={setSelectedOption}
          options={options}
          size="middle"
          style={{ minWidth: '150px' }}
        />
        {sysInfoError && (
          <Typography
            className="small-text"
            style={{ color: 'var(--error-600)' }}
          >
            {sysInfoError}
          </Typography>
        )}
        <Typography className="small-text-bold">
          Last Updated : {lastUpdated || '—'}
        </Typography>
      </Flex>
      <Suspense fallback={<div>Loading...</div>}>
        {SelectedComponent && (
          <SelectedComponent key={componentKey} data={componentData} />
        )}
      </Suspense>
    </div>
  );
};

export default SystemInfo;
