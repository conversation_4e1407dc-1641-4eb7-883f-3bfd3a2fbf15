import { Flex, <PERSON><PERSON>ip, Typography } from '@/components';
import { Col, Divider, Row } from 'antd';
import PropTypes from 'prop-types';

const ConnectionCard = ({ title, meta, right, children }) => (
  <Flex vertical className="dashboard-card">
    <Flex
      justify="space-between"
      align="center"
      style={{ padding: '4px 12px' }}
    >
      <Typography
        wrap="true"
        className="heading-four-bold"
        style={{ padding: 0, margin: 0 }}
      >
        {title}
      </Typography>
      {right}
    </Flex>

    <Flex
      justify="space-between"
      align="center"
      style={{ padding: '4px 12px' }}
    >
      {meta ? (
        typeof meta === 'string' ? (
          <Typography className="small-text" style={{ opacity: 0.8 }}>
            {meta}
          </Typography>
        ) : (
          meta
        )
      ) : null}
    </Flex>

    <Divider className="mtb-sm" style={{ borderColor: '#F0F0F0' }} />

    {children}
  </Flex>
);

ConnectionCard.propTypes = {
  title: PropTypes.oneOfType([PropTypes.string, PropTypes.node]).isRequired,
  meta: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  right: PropTypes.node,
  children: PropTypes.node,
};

ConnectionCard.defaultProps = {
  meta: null,
  right: null,
  children: null,
};

export default ConnectionCard;

export const Rows = ({ rows }) => (
  <>
    {rows.map((item, idx) => (
      <Row key={idx} style={{ margin: '8px 12px' }}>
        <Col span={12}>
          <Typography>{item.label}</Typography>
        </Col>
        <Col span={12} style={{ textAlign: 'left' }}>
          {item.label === 'Error' ? (
            <Typography
              className="small-text"
              style={{
                color: 'var(--error-600)',
                maxWidth: '150px',
              }}
              ellipsis={{
                tooltip: <Tooltip>{item.value}</Tooltip>,
              }}
            >
              {item.value}
            </Typography>
          ) : (
            <Typography strong>{item.value}</Typography>
          )}
        </Col>
      </Row>
    ))}
  </>
);

Rows.propTypes = {
  rows: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.oneOfType([PropTypes.string, PropTypes.node]).isRequired,
      value: PropTypes.oneOfType([
        PropTypes.string,
        PropTypes.number,
        PropTypes.node,
      ]),
    }),
  ).isRequired,
};

Rows.defaultProps = {
  rows: [],
};
