import { useEffect, useRef, useState } from 'react';
import { Button } from '@/components';
import ConnectionCard, { Rows } from '../ConnectionCard';
import { useGet } from '@/hooks';
import { APIS } from '@/constants';
import { useEdgeDeviceDetailContext } from '../../context';

const LteCheck = () => {
  const { edgeDeviceDetail } = useEdgeDeviceDetailContext();
  const deviceId = edgeDeviceDetail?._id;

  const ctxLTE = edgeDeviceDetail?.lteAnalyzer ?? null;

  const EMPTY = useRef({}).current;

  const [lteLocal, setLteLocal] = useState(() => ctxLTE ?? EMPTY);
  const isRunningRef = useRef(false);

  useEffect(() => {
    if (isRunningRef.current) return;

    if (ctxLTE == null) {
      setLteLocal((prev) => (prev === EMPTY ? prev : EMPTY));
    } else {
      setLteLocal((prev) => (prev === ctxLTE ? prev : ctxLTE));
    }
  }, [ctxLTE, EMPTY]);

  const run = useGet({
    api: APIS.EPIKV2,
    endpoint: `/epikbox/${deviceId}/lteanalyzer`,
    enabled: false,
    retry: false,
    refetchOnWindowFocus: false,
  });

  const handleRun = async () => {
    if (!deviceId || run.isFetching) return;
    isRunningRef.current = true;
    try {
      const res = await run.refetch({ throwOnError: false });
      const payload = res?.data?.data ?? res?.data ?? res;

      const next = payload?.lteAnalyzer ?? payload ?? EMPTY;
      setLteLocal((prev) => (prev === next ? prev : next));
    } finally {
      isRunningRef.current = false;
    }
  };

  const fmt = (v) => (typeof v === 'number' ? v.toFixed(2) : (v ?? '—'));

  return (
    <ConnectionCard
      title="4G LTE Check"
      right={
        <Button
          size="small"
          style={{ whiteSpace: 'nowrap' }}
          loading={run.isFetching}
          disabled={!deviceId}
          onClick={handleRun}
        >
          Run
        </Button>
      }
    >
      <Rows
        rows={[
          { label: 'DATE', value: lteLocal?.date ?? '—' },
          { label: 'IMEI', value: lteLocal?.imei ?? '—' },
          { label: 'Active SIM', value: lteLocal?.activeSim ?? '—' },
          { label: 'MTM', value: lteLocal?.mtm ?? '—' },
          { label: 'LTE Max Latency', value: fmt(lteLocal?.lteMaxLatency) },
          { label: 'LTE Avg Latency', value: fmt(lteLocal?.lteAvgLatency) },
          { label: 'LTE Min Latency', value: fmt(lteLocal?.lteMinLatency) },
          ...(lteLocal?.Error
            ? [{ label: 'Error', value: lteLocal.Error }]
            : []),
        ]}
      />
    </ConnectionCard>
  );
};

export default LteCheck;
