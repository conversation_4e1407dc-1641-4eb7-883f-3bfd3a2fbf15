import PropTypes from 'prop-types';
import Typography from '@/components/common/Typography';

const BoxFirmware = ({ data }) => {
  return (
    <div
      style={{
        marginTop: '20px',
        flex: 1,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      <Typography className="heading-four-bold">
        {data?.title ? `${data?.title} :` : 'N/A'}
        <Typography className="heading-four">{data?.description}</Typography>
      </Typography>
    </div>
  );
};

BoxFirmware.propTypes = {
  data: PropTypes.shape({
    title: PropTypes.string,
    description: PropTypes.string,
  }),
};

export default BoxFirmware;
