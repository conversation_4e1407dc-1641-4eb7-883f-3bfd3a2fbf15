import PropTypes from 'prop-types';
import Flex from '@/components/common/Flex';
import Typography from '@/components/common/Typography';
import { Col, Row } from 'antd';

const PortStatuses = ({ data }) => {
  if (!data) return null;

  return (
    <Flex vertical style={{ marginTop: '20px' }}>
      {Object.entries(data).map(([sectionKey, section]) => (
        <div key={sectionKey} style={{ marginBottom: '24px' }}>
          {section.title && (
            <Typography
              className="heading-four-bold"
              style={{ marginTop: '12px' }}
            >
              {section.title}
            </Typography>
          )}
          <Row gutter={[8, 16]} style={{ marginTop: '8px' }}>
            {section.data.map((item, index) => (
              <Col span={section.span || 4} key={index}>
                <Flex
                  vertical
                  align="center"
                  style={{
                    background: '#F2F4F7',
                    boxShadow: '0px 1px 2px 0px #1018280D',
                    borderRadius: '8px',
                    border: '1px solid #F9FAFB',
                    padding: '8px 12px',
                  }}
                >
                  <Typography className="text-medium-semibold">
                    {item.title}
                  </Typography>
                  <Typography className="small-text" wrap>
                    {item.description}
                  </Typography>
                </Flex>
              </Col>
            ))}
          </Row>
        </div>
      ))}
    </Flex>
  );
};

PortStatuses.propTypes = {
  data: PropTypes.objectOf(
    PropTypes.shape({
      title: PropTypes.string.isRequired,
      span: PropTypes.number,
      data: PropTypes.arrayOf(
        PropTypes.shape({
          title: PropTypes.string.isRequired,
          description: PropTypes.string.isRequired,
        }),
      ).isRequired,
    }),
  ),
};

export default PortStatuses;
