import { useEffect, useRef, useState } from 'react';
import { Button } from '@/components';
import ConnectionCard, { Rows } from '../ConnectionCard';
import { useGet } from '@/hooks';
import { APIS } from '@/constants';
import { useEdgeDeviceDetailContext } from '../../context';

const VoiceSpeedCheck = () => {
  const { edgeDeviceDetail } = useEdgeDeviceDetailContext();
  const deviceId = edgeDeviceDetail?._id;

  const ctxVoice = edgeDeviceDetail?.speedTestVoice;

  const [voiceLocal, setVoiceLocal] = useState(ctxVoice ?? {});
  const isRunningRef = useRef(false);

  useEffect(() => {
    if (!isRunningRef.current) {
      setVoiceLocal(ctxVoice ?? {});
    }
  }, [ctxVoice]);

  const run = useGet({
    api: APIS.EPIKV2,
    endpoint: `/epikbox/${deviceId}/speedtest/voice`,
    enabled: false,
    retry: false,
    refetchOnWindowFocus: false,
  });

  const handleRun = async () => {
    if (!deviceId || run.isFetching) return;
    isRunningRef.current = true;
    try {
      const res = await run.refetch({ throwOnError: false });
      const payload = res?.data?.data ?? res?.data ?? res;
      const next = payload?.speedTestVoice ?? payload ?? {};
      if (next && typeof next === 'object') {
        setVoiceLocal(next);
      }
    } finally {
      isRunningRef.current = false;
    }
  };

  const latency =
    typeof voiceLocal?.latency === 'number'
      ? voiceLocal.latency.toFixed(2)
      : (voiceLocal?.latency ?? '—');

  const jitter =
    typeof voiceLocal?.jitter === 'number'
      ? voiceLocal.jitter.toFixed(2)
      : (voiceLocal?.jitter ?? '—');

  const up =
    typeof voiceLocal?.uploadSpeed === 'number'
      ? voiceLocal.uploadSpeed.toFixed(2)
      : (voiceLocal?.uploadSpeed ?? '—');

  const down =
    typeof voiceLocal?.downloadSpeed === 'number'
      ? voiceLocal.downloadSpeed.toFixed(2)
      : (voiceLocal?.downloadSpeed ?? '—');

  return (
    <ConnectionCard
      title="Voice Speed Check"
      right={
        <Button
          size="small"
          style={{ whiteSpace: 'nowrap' }}
          loading={run.isFetching}
          disabled={!deviceId}
          onClick={handleRun}
        >
          Run
        </Button>
      }
    >
      <Rows
        rows={[
          { label: 'Latency', value: latency },
          { label: 'Jitter', value: jitter },
          { label: 'Upload Speed', value: up },
          { label: 'Download Speed', value: down },
          ...(voiceLocal?.Error
            ? [{ label: 'Error', value: voiceLocal.Error }]
            : []),
        ]}
      />
    </ConnectionCard>
  );
};

export default VoiceSpeedCheck;
