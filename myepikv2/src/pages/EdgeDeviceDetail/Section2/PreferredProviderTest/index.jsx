import { useEffect, useMemo, useRef, useState } from 'react';
import { Divider } from 'antd';
import { Button, Flex, Typography } from '@/components';
import ConnectionCard, { Rows } from '../ConnectionCard';
import { useGet } from '@/hooks';
import { APIS } from '@/constants';
import { useEdgeDeviceDetailContext } from '../../context';
import { formatTimestamp } from '@/utils/date';
import { useStore } from '@/store';

const useElapsedTimer = (startIsoOrDateString) => {
  const [tick, setTick] = useState(0);

  const display = useMemo(() => {
    if (!startIsoOrDateString) return null;
    const startedAt = new Date(startIsoOrDateString);
    const startMs = startedAt.getTime();
    if (Number.isNaN(startMs)) return null;

    const now = Date.now();
    const effectiveStart = Math.min(startMs, now);
    const diff = Math.max(0, now - effectiveStart);

    const sec = Math.floor(diff / 1000);
    const mm = String(Math.floor((sec % 3600) / 60)).padStart(2, '0');
    const ss = String(sec % 60).padStart(2, '0');
    return `${mm}:${ss}`;
  }, [startIsoOrDateString, tick]);

  useEffect(() => {
    if (!startIsoOrDateString) return;
    const id = setInterval(() => setTick((t) => t + 1), 1000);
    return () => clearInterval(id);
  }, [startIsoOrDateString]);

  return display;
};

const PreferredProviderTest = () => {
  const { edgeDeviceDetail } = useEdgeDeviceDetailContext();

  const deviceId = edgeDeviceDetail?._id;

  const ctxPreferred = edgeDeviceDetail?.preferredProviderTest ?? null;
  const ctxInitiated =
    edgeDeviceDetail?.preferredProviderTest?.initiatedTimeStamp ?? null;

  const [preferredLocal, setPreferredLocal] = useState(ctxPreferred);
  const [initiatedLocal, setInitiatedLocal] = useState(ctxInitiated);
  const isRunningRef = useRef(false);

  useEffect(() => {
    if (!isRunningRef.current) {
      setPreferredLocal(ctxPreferred || null);
      setInitiatedLocal(ctxInitiated || null);
    }
  }, [ctxPreferred, ctxInitiated]);

  const preferredProviderTestError = useStore(
    (s) => s.error.errorState[`sub:preferredProviderTest`] || null,
  );

  const initPrefCheck = useGet({
    api: APIS.EPIKV2,
    endpoint: `/epikbox/${deviceId}/initlteperfcheck`,
    enabled: false,
    retry: false,
    refetchOnWindowFocus: false,
  });

  const preferred = preferredLocal || ctxPreferred || null;
  const sims = preferred?.SimsInfo || [];

  const lastTime = formatTimestamp(preferred?.TimeStamp, {
    format: 'MM/DD/YYYY hh:mm A',
  });

  const startForTimer = initiatedLocal || ctxInitiated || null;
  const elapsed = useElapsedTimer(startForTimer);

  const handleRun = async () => {
    if (!deviceId || initPrefCheck.isFetching) return;

    isRunningRef.current = true;
    setInitiatedLocal(new Date().toISOString());

    try {
      const res = await initPrefCheck.refetch({ throwOnError: false });
      const payload = res?.data?.data ?? res?.data ?? res;

      const nextPreferred = payload?.preferredProviderTest ?? null;
      const initiated =
        payload?.preferredProviderTest?.initiatedTimeStamp ?? null;

      if (nextPreferred) setPreferredLocal(nextPreferred);
      if (initiated) setInitiatedLocal(initiated);
    } finally {
      isRunningRef.current = false;
    }
  };

  return (
    <ConnectionCard
      title="Preferred LTE/Cell Provider Test"
      meta={
        <Flex style={{ width: '100%' }} align="center" justify="space-between">
          {lastTime ? (
            <Typography className="small-text">Last: {lastTime}</Typography>
          ) : null}

          {startForTimer ? (
            <Typography className="small-text" style={{ fontWeight: 600 }}>
              Time: {elapsed || '00:00'}
            </Typography>
          ) : null}
        </Flex>
      }
      right={
        <Button
          size="small"
          style={{ whiteSpace: 'nowrap' }}
          loading={initPrefCheck.isFetching}
          disabled={!deviceId}
          onClick={handleRun}
        >
          Run
        </Button>
      }
    >
      <Flex vertical>
        {sims.map((s, idx) => (
          <Flex vertical key={s.SIM ?? idx}>
            <Rows
              rows={[
                { label: 'SIM', value: s.SIM ?? '—' },
                {
                  label: 'PingAvg',
                  value:
                    typeof s?.PingAvg === 'number' ? s.PingAvg.toFixed(2) : '0',
                },
                {
                  label: 'PacketLoss',
                  value:
                    typeof s?.PacketLoss === 'number'
                      ? s.PacketLoss.toFixed(2)
                      : '0',
                },
                {
                  label: 'Jitter',
                  value:
                    typeof s?.Jitter === 'number' ? s.Jitter.toFixed(2) : '0',
                },
              ]}
            />
            {idx < sims.length - 1 ? (
              <Divider className="mtb-sm" style={{ borderColor: '#F0F0F0' }} />
            ) : null}
          </Flex>
        ))}

        {!sims.length && !preferredProviderTestError ? (
          <Typography style={{ padding: '0 12px', opacity: 0.7 }}>
            No results yet.
          </Typography>
        ) : null}
        {preferredProviderTestError && (
          <Flex align="center" style={{ padding: '0 12px' }}>
            <Typography
              className="small-text"
              style={{ color: 'var(--error-600)' }}
            >
              {preferredProviderTestError}
            </Typography>
          </Flex>
        )}
      </Flex>
    </ConnectionCard>
  );
};

export default PreferredProviderTest;
