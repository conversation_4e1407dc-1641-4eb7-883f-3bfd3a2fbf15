import { useState } from 'react';
import { Mo<PERSON>, Select, Table, Alert, Row, Col, Divider } from 'antd';
import './styles.css';
import { AlertIcon, Button, Typography } from '@/components';
import PropTypes from 'prop-types';
const Title = () => (
  <div className="modal-title">
    <Typography level={4}>Command</Typography>
    <Divider className="divider" />
  </div>
);
const RemoteExecutionControl = ({ visible, onCancel }) => {
  const [data] = useState([
    {
      key: '1',
      arp: '172.16.16.250,172.16.16.250,172.16.16.250,172.16.16.250,172.16.16.250,172.16.16.250,172.16.16.250,172.16.16.250,172.16.16.250,172.16.16.250',
    },
  ]);
  const columns = [
    {
      title: 'ARP',
      dataIndex: 'arp',
      key: 'arp',
    },
  ];
  return (
    <Modal
      title={<Title />}
      open={visible}
      onCancel={onCancel}
      maskClosable={false}
      footer={[
        <div key="footer-container" className="footer-container">
          <Button key="clear" type="primary">
            Clear Data
          </Button>
          ,
          <Button
            key="cancel"
            color="default"
            variant="outlined"
            onClick={onCancel}
          >
            Cancel
          </Button>
        </div>,
      ]}
      width={1100}
    >
      <Row gutter={16} align="middle" justify="space-between">
        <Col span={11}>
          <Select defaultValue="ARP" style={{ width: '100%' }}>
            <Select.Option value="arp">ARP</Select.Option>
            <Select.Option value="icmp">ICMP</Select.Option>
          </Select>
        </Col>
        <Col span={11}>
          <Select defaultValue="Arp" style={{ width: '100%' }}>
            <Select.Option value="arp">Arp</Select.Option>
            <Select.Option value="ping">Ping</Select.Option>
          </Select>
        </Col>
        <Col span={2}>
          <Button type="primary">Execute</Button>
        </Col>
      </Row>
      <Alert
        style={{ margin: '24px 0' }}
        message={
          <div className="pci-message">
            Strictly for PCI card, says PCI was damaged in transit. This control
            will verify if PCI card is seated correctly. Most likely knocked
            loose during shipping and transit.
          </div>
        }
        type="info"
        showIcon
        icon={<AlertIcon />}
      />
      <Table dataSource={data} columns={columns} pagination={false} />
    </Modal>
  );
};
RemoteExecutionControl.propTypes = {
  visible: PropTypes.bool.isRequired, // Validate 'visible' as required boolean
  onCancel: PropTypes.func.isRequired, // Validate 'onCancel' as required function
};

export default RemoteExecutionControl;
