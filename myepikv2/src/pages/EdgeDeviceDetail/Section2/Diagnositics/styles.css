/* Container */
.diagnostics-container {
  width: 100%;
  max-width: 1345px;
  margin: auto;
  padding: 20px;
  background-color: #f4f6f8; /* Light gray background */
  border-radius: 8px;
}

/* Top Header Section */
.top-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  background-color: #f4f6f8; /* Matches the container background */
  border-bottom: 1px solid #e8e8e8; /* Light border for separation */
  margin-bottom: 15px; /* Space below the top header */
}

.left-icons .icon-button,
.right-icons .icon-button {
  background-color: #e8e8e8; /* Light gray button background */
  border: none;
  border-radius: 4px;
  color: black; /* Icon color */
  font-size: 1rem;
  margin-right: 5px; /* Space between buttons */
}

.right-icons .icon-button:last-child {
  margin-right: 0;
}

/* Timestamp Section */
.timestamp-header {
  background-color: #4c9cd3; /* Blue background */
  padding: 10px 20px;
  text-align: left;
  border-radius: 4px;
  margin-bottom: 20px;
}

.timestamp-text {
  color: white;
  font-size: 1rem;
}

.diagnostics-content {
  padding: 10px 0;
}

.diagnostics-card {
  background-color: #d4f3cf; /* Light green background */
  border: 1px solid #b7eb8f; /* Green border */
  border-radius: 4px;
  padding: 10px;
  height: 41px; /* Fixed height */
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: auto; /* Centers the card horizontally */
}

.diagnostics-text {
  font-size: 0.9rem;
  color: #000; /* Black text for the title */
}

.diagnostics-result {
  font-weight: bold;
  color: #0b9341; /* Dark green color for "PASS" */
}

/* Left icons container */
.left-icons {
  display: flex;
  gap: 10px; /* Adds space between buttons */
}

/* Right icons container */
.right-icons {
  display: flex;
  gap: 10px; /* Adds space between buttons */
}
.top-header .icon-button {
  padding: 0;
  border: none;
  background: none;
}

.icon-button.ant-btn {
  border-color: transparent;
  background: transparent;
  box-shadow: none;
}
