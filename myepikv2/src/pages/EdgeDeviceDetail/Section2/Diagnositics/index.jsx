import { useState } from 'react';
import { Row, Col, Card } from 'antd';
import {
  RemoteExecutionCenter,
  VswitchOutput,
  DownloadIcon,
  Reload,
  Button,
  Typography,
} from '@/components';
import './styles.css';
import RemoteExecutionControl from './Modal/REC/index';

const diagnosticsData = [
  { title: 'Device Disk Space Test', result: 'PASS' },
  { title: 'DHCPD Container Running Test', result: 'PASS' },
  { title: 'Time Sync Test', result: 'PASS' },
  { title: 'DHCPD Disk Space Test', result: 'PASS' },
  { title: 'Device RAM Test', result: 'PASS' },
  { title: 'DHCPD Server Running Test', result: 'PASS' },
  { title: 'DNS Config', result: 'PASS' },
  { title: 'vSwitch Container Test', result: 'PASS' },
  { title: 'PRI Symlink', result: 'PASS' },
  { title: 'vSwitch Symlink Test', result: 'PASS' },
];

const Diagnostics = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);

  const handleshowModal = () => {
    setIsModalVisible(true);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };
  return (
    <div className="">
      <div className="top-header">
        <div className="left-icons">
          <Button
            icon={<RemoteExecutionCenter />}
            onClick={handleshowModal}
            classNames="icon-button"
          />
          <Button icon={<VswitchOutput />} classNames="icon-button" />
        </div>
        <div className="right-icons">
          <Button icon={<DownloadIcon />} classNames="icon-button" />
          <Button icon={<Reload />} classNames="icon-button" />
        </div>
      </div>
      <div className="timestamp-header">
        <Typography className="timestamp-text">
          TIME STAMP: WED DEC 20 16:39:46 UTC 2024
        </Typography>
      </div>
      <Row gutter={[16, 16]} className="diagnostics-content">
        {diagnosticsData.map((item, index) => (
          <Col xs={24} sm={12} key={index}>
            <Card className="diagnostics-card">
              <Typography className="diagnostics-text">
                {item.title}:{' '}
                <span className="diagnostics-result">{item.result}</span>
              </Typography>
            </Card>
          </Col>
        ))}
      </Row>
      {isModalVisible && (
        <RemoteExecutionControl
          visible={isModalVisible}
          onCancel={handleCancel}
        />
      )}
    </div>
  );
};

export default Diagnostics;
