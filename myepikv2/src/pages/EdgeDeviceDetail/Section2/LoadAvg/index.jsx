import { Typography, Row, Col, theme } from 'antd';
import PropTypes from 'prop-types';
import { Flex } from '@/components';
import { Column } from '@ant-design/plots';
const { useToken } = theme;

const Legend = ({ label, color }) => (
  <Flex gap={4} align="center">
    <div
      style={{
        width: 10,
        height: 10,
        backgroundColor: color,
        borderRadius: '50%',
        marginRight: 2,
      }}
    />
    <Typography className="extra-small-text">{label}</Typography>
  </Flex>
);

Legend.propTypes = {
  label: PropTypes.string.isRequired,
  color: PropTypes.string.isRequired,
};

const LoadAvg = ({ data = [] }) => {
  const {
    token: { barChartColor1, switchSecondaryVariant },
  } = useToken();

  const colors = [barChartColor1, switchSecondaryVariant];

  const uniqueTypes = Array.from(new Set(data.map((d) => d.type)));
  const orderedTypes = uniqueTypes
    .slice()
    .sort((a, b) => parseFloat(a) - parseFloat(b));

  const typeRank = new Map(orderedTypes.map((t, i) => [t, i]));
  const sortedData = data.slice().sort((a, b) => {
    const ra = typeRank.get(a.type) ?? 9999;
    const rb = typeRank.get(b.type) ?? 9999;
    if (ra !== rb) return ra - rb;
    return String(a.name).localeCompare(String(b.name));
  });

  const config = {
    data: sortedData,
    xField: 'name',
    yField: 'value',
    colorField: 'type',
    legend: false,
    scale: {
      color: {
        range: colors,
      },
    },
    style: { inset: 0 },
  };

  return (
    <div style={{ marginTop: 16 }}>
      <Row gutter={[8, 16]}>
        <Col xs={24} sm={24} lg={24} align="center" justify="center">
          <Flex vertical style={{ borderRadius: '8px', height: '100%' }}>
            <Flex style={{ height: 300 }}>
              <Column {...config} />
            </Flex>
            <Flex gap={16} justify="center">
              {orderedTypes.map((t, i) => (
                <Legend key={t} label={t} color={colors[i % colors.length]} />
              ))}
            </Flex>
          </Flex>
        </Col>
      </Row>
    </div>
  );
};

LoadAvg.propTypes = {
  data: PropTypes.arrayOf(
    PropTypes.shape({
      type: PropTypes.string.isRequired,
      name: PropTypes.string.isRequired,
      value: PropTypes.number.isRequired,
    }),
  ),
};

export default LoadAvg;
