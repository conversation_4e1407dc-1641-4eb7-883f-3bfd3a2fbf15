import { useEffect, useRef, useState } from 'react';
import { Button } from '@/components';
import ConnectionCard, { Rows } from '../ConnectionCard';
import { useGet } from '@/hooks';
import { APIS } from '@/constants';
import { useEdgeDeviceDetailContext } from '../../context';

const DataSpeedCheck = () => {
  const { edgeDeviceDetail } = useEdgeDeviceDetailContext();
  const deviceId = edgeDeviceDetail?._id;

  const ctxData = edgeDeviceDetail?.speedTestData;

  const [dataLocal, setDataLocal] = useState(() => ctxData ?? {});
  const isRunningRef = useRef(false);

  useEffect(() => {
    if (!isRunningRef.current) {
      setDataLocal(ctxData ?? {});
    }
  }, [ctxData]);

  const run = useGet({
    api: APIS.EPIKV2,
    endpoint: `/epikbox/${deviceId}/speedtest/data`,
    enabled: false,
    retry: false,
    refetchOnWindowFocus: false,
  });

  const handleRun = async () => {
    if (!deviceId || run.isFetching) return;
    isRunningRef.current = true;
    try {
      const res = await run.refetch({ throwOnError: false });
      const payload = res?.data?.data ?? res?.data ?? res;
      const next = payload?.speedTestData ?? payload ?? {};
      if (next && typeof next === 'object') setDataLocal(next);
    } finally {
      isRunningRef.current = false;
    }
  };

  const up =
    typeof dataLocal?.uploadSpeed === 'number'
      ? dataLocal.uploadSpeed.toFixed(2)
      : (dataLocal?.uploadSpeed ?? '—');

  const down =
    typeof dataLocal?.downloadSpeed === 'number'
      ? dataLocal.downloadSpeed.toFixed(2)
      : (dataLocal?.downloadSpeed ?? '—');

  return (
    <ConnectionCard
      title="Data Speed Check"
      right={
        <Button
          size="small"
          style={{ whiteSpace: 'nowrap' }}
          loading={run.isFetching}
          disabled={!deviceId}
          onClick={handleRun}
        >
          Run
        </Button>
      }
    >
      <Rows
        rows={[
          { label: 'Upload Speed', value: up },
          { label: 'Download Speed', value: down },
          ...(dataLocal?.Error
            ? [{ label: 'Error', value: dataLocal.Error }]
            : []),
        ]}
      />
    </ConnectionCard>
  );
};

export default DataSpeedCheck;
