import { Typography, Row, Col, theme } from 'antd';
import PropTypes from 'prop-types';
import { Flex } from '@/components';
import { Column } from '@ant-design/plots';
const { useToken } = theme;

const Legend = ({ label, color }) => (
  <Flex gap={4} align="center">
    <div
      style={{
        width: 10,
        height: 10,
        backgroundColor: color,
        borderRadius: '50%',
        marginRight: 2,
      }}
    />
    <Typography className="extra-small-text">{label}</Typography>
  </Flex>
);

Legend.propTypes = {
  label: PropTypes.string.isRequired,
  color: PropTypes.string.isRequired,
};

const Network = ({ data = [] }) => {
  const {
    token: { barChartColor1, switchSecondaryVariant },
  } = useToken();

  const colors = [barChartColor1, switchSecondaryVariant];

  const dataConfig = {
    data,
    xField: 'name',
    yField: 'value',
    colorField: 'type',
    group: true,
    legend: false,
    scale: {
      color: {
        range: colors,
      },
    },
    style: { inset: 0 },
  };

  return (
    <div style={{ marginTop: 16 }}>
      <Row gutter={[8, 16]}>
        <Col xs={24} sm={24} lg={24} align="center" justify="center">
          <Flex
            vertical
            style={{
              borderRadius: '8px',
              height: '100%',
            }}
          >
            <Flex
              style={{
                height: 300,
              }}
            >
              <Column {...dataConfig} />
            </Flex>
            <Flex gap={16} justify="center">
              <Legend label={'RxBytes'} color={barChartColor1} />
              <Legend label={'TxBytes'} color={switchSecondaryVariant} />
            </Flex>
          </Flex>
        </Col>
      </Row>
    </div>
  );
};

Network.propTypes = {
  data: PropTypes.arrayOf(
    PropTypes.shape({
      type: PropTypes.oneOf(['ReadsCompleted', 'WritesCompleted']).isRequired,
      name: PropTypes.string.isRequired,
      value: PropTypes.number.isRequired,
    }),
  ),
};

export default Network;
