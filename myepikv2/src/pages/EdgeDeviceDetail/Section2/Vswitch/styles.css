.vswitch-epi-panel .ant-collapse-header {
  background-color: #4c9cd3;
  color: #333;
  font-size: 1rem;
  padding: 10px 16px;
  border-bottom: 1px solid #ccc;
}

.vswitch-port-panel .ant-collapse-header {
  background-color: #ffffff;
  color: #333;
  padding-left: 32px;
}

.vswitch-table .ant-table-thead > tr > th {
  background-color: #e8e8e8;
  font-weight: normal;
}

.vswitch-table .ant-table-tbody > tr > td {
  background-color: #ffffff;
  font-weight: bold;
}
.vswitch-port-panel.active .ant-collapse-header {
  background-color: #c8e0f1;
  color: white;
}
.epi-header .ant-collapse-header {
  color: white !important;
  align-items: center !important;
}

.ant-collapse-item.vswitch-port-panel > .ant-collapse-header {
  color: #333 !important;
}

.ant-collapse-item.vswitch-port-panel.active > .ant-collapse-header {
  background-color: #c8e0f1;
}

.vswitch-table {
  width: 100%;
  overflow-x: auto;
}

.vswitch-table .ant-table {
  width: 100%;
  table-layout: auto;
}

.vswitch-table .ant-table-tbody > tr > td {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.vswitch-buttons-row {
  display: flex;
  flex-wrap: wrap;
  margin-top: 16px;
}

.vswitch-buttons-row .ant-col {
  flex: 1 1 50%;
  max-width: 50%;
}

@media (max-width: 768px) {
  .vswitch-buttons-row .ant-col {
    flex: 1 1 100%;
    max-width: 100%;
  }
}
