import { useState, useMemo } from 'react';
import { Table, Collapse, Row, Col } from 'antd';
import { Flex, Tooltip, Typography } from '@/components';
import './styles.css';
import { useEdgeDeviceDetailContext } from '../../context';
import { useStore } from '@/store';

const columnsDebugOutput = [
  {
    title: 'EPIK EDGE ADDRESS',
    dataIndex: 'epikEdgeAddress',
    key: 'epikEdgeAddress',
  },
  { title: 'DEVICE ONLINE', dataIndex: 'deviceOnline', key: 'deviceOnline' },
  { title: 'REGISTERED', dataIndex: 'registered', key: 'registered' },
  {
    title: 'REGISTERED CONFIG CREATED',
    dataIndex: 'configCreated',
    key: 'configCreated',
  },
];

const columnsProvisioning = [
  {
    title: 'REGISTERED CONFIG CREATED',
    dataIndex: 'configCreated',
    key: 'configCreated',
  },
  {
    title: 'FIRMWARE VERSION',
    dataIndex: 'firmwareVersion',
    key: 'firmwareVersion',
  },
];

const portColumns = [
  { title: 'CALLER ID', dataIndex: 'calledId', key: 'calledId' },
  { title: 'RECORDING', dataIndex: 'recording', key: 'recording' },
  { title: 'TRUNK TYPE', dataIndex: 'trunkType', key: 'trunkType' },
];

const VswitchOutput = () => {
  const { edgeDeviceDetail } = useEdgeDeviceDetailContext();
  const { vSwitchTab = {}, sysInfo = {} } = edgeDeviceDetail || {};

  const vSwitchTabError = useStore(
    (s) => s.error.errorState[`sub:${'vSwitchTab'}`] || null,
  );

  const epiButtons = useMemo(() => {
    const byEpi = new Map();
    (vSwitchTab?.portsInfo ?? [])
      .sort((a, b) => {
        const na = parseInt(String(a?.port || '').replace(/\D/g, ''), 10) || 0;
        const nb = parseInt(String(b?.port || '').replace(/\D/g, ''), 10) || 0;
        return na - nb;
      })
      .forEach((p) => {
        const num = parseInt(String(p?.port || '').replace(/\D/g, ''), 10) || 0;
        const epiIndex = Math.ceil(num / 2) || 1;
        const epiId = `EPI ${epiIndex}`;
        if (!byEpi.has(epiId)) byEpi.set(epiId, []);
        byEpi.get(epiId).push(p);
      });

    return Array.from(byEpi.entries())
      .sort(
        (a, b) =>
          parseInt(a[0].replace(/\D/g, ''), 10) -
          parseInt(b[0].replace(/\D/g, ''), 10),
      )
      .map(([id, ports]) => ({ id, ports: ports.slice(0, 2) }));
  }, [vSwitchTab?.portsInfo]);

  const [activePorts, setActivePorts] = useState({});

  const outerItems = useMemo(
    () =>
      epiButtons.map((epi) => {
        const innerItems = (epi.ports || []).map((port) => {
          const isActive = activePorts[epi.id] === port?.port;
          return {
            key: port?.port,
            label: port?.port,
            className: `vswitch-port-panel ${isActive ? 'active' : ''}`,
            children: (
              <Table
                dataSource={[port]}
                columns={portColumns}
                pagination={false}
                bordered
                className="vswitch-table"
              />
            ),
          };
        });

        return {
          key: epi.id,
          label: epi.id,
          className: 'vswitch-epi-panel epi-header',
          children: (
            <Collapse
              size="small"
              bordered={false}
              accordion
              activeKey={activePorts[epi.id] || undefined}
              onChange={(key) => {
                const nextKey = Array.isArray(key) ? key[0] : key;
                setActivePorts((s) => ({ ...s, [epi.id]: nextKey || null }));
              }}
              items={innerItems}
            />
          ),
        };
      }),
    [epiButtons, portColumns, activePorts],
  );

  const dataProvisioning = [
    {
      key: '1',
      configCreated: vSwitchTab?.registered ? 'True' : 'False',
      firmwareVersion: sysInfo?.boxFirmware || 'N/A',
    },
  ];

  const dataDebugOutput = [
    {
      key: '1',
      epikEdgeAddress: edgeDeviceDetail?.vpnAddress || 'N/A',
      deviceOnline: edgeDeviceDetail?.deviceOnline ? 'True' : 'False',
      registered: edgeDeviceDetail?.registered ? 'True' : 'False',
      configCreated: edgeDeviceDetail?.registerationConfigCreated
        ? 'True'
        : 'False',
    },
  ];

  return (
    <div>
      <Flex
        align="center"
        justify="space-between"
        style={{ marginBottom: '12px' }}
      >
        <Typography className="heading-four-bold">
          EPIK edge Virtual Switch Debug Output
        </Typography>
      </Flex>
      <Table
        dataSource={dataDebugOutput}
        columns={columnsDebugOutput}
        pagination={false}
        bordered
        className="vswitch-table"
      />

      <Flex align="center" justify="space-between" style={{ margin: '12px 0' }}>
        <Typography className="heading-four-bold">EPIK Provisioning</Typography>
        {vSwitchTabError && (
          <Typography
            className="small-text"
            style={{
              color: 'var(--error-600)',
              marginLeft: '16px',
              maxWidth: '150px',
            }}
            ellipsis={{
              tooltip: <Tooltip>{vSwitchTabError}</Tooltip>,
            }}
          >
            {vSwitchTabError}
          </Typography>
        )}
      </Flex>
      <Table
        dataSource={dataProvisioning}
        columns={columnsProvisioning}
        pagination={false}
        bordered
        className="vswitch-table"
      />

      <Row gutter={[16, 16]} className="vswitch-buttons-row">
        {epiButtons.map((epi) => (
          <Col xs={24} sm={24} md={12} lg={12} xl={12} key={epi.id}>
            <Collapse
              size="small"
              items={outerItems.filter((item) => item.key === epi.id)}
            />
          </Col>
        ))}
      </Row>
    </div>
  );
};

export default VswitchOutput;
