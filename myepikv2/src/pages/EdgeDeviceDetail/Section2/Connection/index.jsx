import { Row, Col } from 'antd';
import './styles.css';
import PreferredProviderTest from '../PreferredProviderTest';
import VoiceSpeedCheck from '../VoiceSpeedCheck';
import DataSpeedCheck from '../DataSpeedCheck';
import LteCheck from '../LteCheck';

const Connection = () => {
  return (
    <Row gutter={[16, 16]}>
      <Col xs={24} sm={12} md={8} lg={6}>
        <LteCheck />
      </Col>

      <Col xs={24} sm={12} md={8} lg={6}>
        <VoiceSpeedCheck />
      </Col>

      <Col xs={24} sm={12} md={8} lg={6}>
        <DataSpeedCheck />
      </Col>

      <Col xs={24} sm={12} md={8} lg={6}>
        <PreferredProviderTest />
      </Col>
    </Row>
  );
};

export default Connection;
