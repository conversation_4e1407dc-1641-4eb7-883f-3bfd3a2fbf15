import PropTypes from 'prop-types';
import {
  Flex,
  Typography,
  ClockIcon,
  ReloadErrorIcon,
  SuccessLiveIcon,
} from '@/components';

const styles = {
  base: {
    alignItems: 'center',
    gap: 4,
    height: '28px',
    padding: '10px',
    borderRadius: '8px',
    width: 'fit-content',
    boxShadow: '0px 1px 2px 0px #1018280D',
  },

  time: {
    border: '1px solid #81BCE5',
    background: '#4D9CD340',
  },
  error: {
    border: '1px solid var(--error-icon)',
    background: '#ED2B2E1F',
  },
  success: {
    border: '1px solid #2FD214',
    background: '#2FD2141F',
  },
};

const AprStatusBadg = ({ type, label, value }) => {
  let content;

  switch (type) {
    case 'time':
      content = (
        <>
          <ClockIcon stroke="var(--granite-blue)" height={14} width={14} />
          <Typography className="small-text-bold">{value}</Typography>
        </>
      );
      break;
    case 'error':
      content = (
        <>
          <ReloadErrorIcon height={14} width={14} />
          <Typography className="small-text">{label || 'Try Again'}</Typography>
        </>
      );
      break;
    case 'success':
      content = (
        <>
          <SuccessLiveIcon />
          <Typography className="small-text">{label || 'Success'}</Typography>
        </>
      );
      break;
    default:
      content = null;
  }

  return (
    <Flex style={{ ...styles.base, ...styles[type] }} gap={4}>
      {content}
    </Flex>
  );
};

AprStatusBadg.propTypes = {
  type: PropTypes.oneOf(['time', 'error', 'success']).isRequired,
  label: PropTypes.string,
  value: PropTypes.string,
};

AprStatusBadg.defaultProps = {
  label: '',
  value: '',
};

export default AprStatusBadg;
