import { lazy, Suspense, useState } from 'react';
import {
  AnimatedTab,
  Button,
  DeviceLockIcon,
  DeviceUnLockIcon,
  Flex,
  OOBMIcon,
  PhoneActivity,
  Timer,
  TimerAdd,
  Typography,
} from '@/components';
import { Col, Row, Spin } from 'antd';
import PropTypes from 'prop-types';

const portSectionTabsList = [
  { label: 'Ports', value: 'ports' },
  { label: 'EPIs', value: 'epis' },
  { label: 'PRI | CAS', value: 'pri_cas' },
  { label: 'Networking', value: 'networking', disabled: false },
  { label: 'Options', value: 'options' },
  { label: 'Admin Tools', value: 'admin_tools' },
  { label: 'Phones', value: 'phones' },
];

const PortSectionTabs = {
  ports: lazy(() => import('../Section1/Ports')),
  phones: lazy(() => import('../Section1/Phone')),
  epis: lazy(() => import('../Section1/Epis')),
  pri_cas: lazy(() => import('../Section1/PriCas')),
  networking: lazy(() => import('../Section1/Networking')),
  admin_tools: lazy(() => import('../Section1/AdminTools')),
  options: lazy(() => import('../Section1/Options')),
};

const PortsMainSection = ({
  openPortDetailsModal,
  openRealTimePortActivityModal,
  openOOBMModal,
  selectedMainSectionTab = 'ports',
  onMainSectionTabChange,
}) => {
  const SelectedComponent = PortSectionTabs[selectedMainSectionTab];
  const [deviceLockToggle, setDeviceLockToggle] = useState(false);

  return (
    <Flex
      vertical
      style={{
        flex: 1,
        padding: '16px',
        borderRadius: '12px',
        background: '#fff',
        marginTop: '16px',
      }}
    >
      <Row gutter={[10, 10]} align="middle">
        <Col>
          <Button
            icon={deviceLockToggle ? <DeviceLockIcon /> : <DeviceUnLockIcon />}
            className={`ports-section-header-button ${deviceLockToggle && 'locked-device'}`}
            onClick={() => setDeviceLockToggle(true)}
          />
        </Col>
        {deviceLockToggle && (
          <>
            <Col>
              <Flex vertical>
                <Typography
                  className="text-inter-700-11-15"
                  style={{ margin: 0 }}
                >
                  Time Remaining
                </Typography>
                <Typography className="small-text" style={{ margin: 0 }}>
                  14min 56sec
                </Typography>
              </Flex>
            </Col>
            <Col>
              <Button
                icon={<TimerAdd />}
                className={`ports-section-header-button`}
              />
            </Col>
            <Col>
              <Button
                icon={<Timer />}
                className={`ports-section-header-button`}
                onClick={() => setDeviceLockToggle(false)}
              />
            </Col>
          </>
        )}
        <Col>
          <Button
            icon={<OOBMIcon />}
            onClick={openOOBMModal}
            className="ports-section-header-button"
          />
        </Col>
        <Col>
          <AnimatedTab
            value={selectedMainSectionTab}
            onChange={(tabKey) => onMainSectionTabChange(tabKey)}
            options={portSectionTabsList}
            size="middle"
          />
        </Col>
        <Col>
          <Button
            icon={<PhoneActivity />}
            onClick={openRealTimePortActivityModal}
            className="ports-section-header-button"
          />
        </Col>
      </Row>
      <Flex vertical style={{ flex: 1, marginTop: '24px', minHeight: '200px' }}>
        <Suspense
          fallback={
            <Flex
              justify="center"
              align="center"
              style={{ width: '100%', minHeight: '200px' }}
            >
              <Spin tip="Loading" size="middle" className="app-spin" />
            </Flex>
          }
        >
          {SelectedComponent && (
            <SelectedComponent
              tabName={selectedMainSectionTab}
              openPortDetailsModal={openPortDetailsModal}
            />
          )}
        </Suspense>
      </Flex>
    </Flex>
  );
};

export default PortsMainSection;

PortsMainSection.propTypes = {
  openRealTimePortActivityModal: PropTypes.func,
  openPortDetailsModal: PropTypes.func.isRequired,
  openOOBMModal: PropTypes.func.isRequired,
  selectedMainSectionTab: PropTypes.string.isRequired,
  onMainSectionTabChange: PropTypes.func.isRequired,
};
