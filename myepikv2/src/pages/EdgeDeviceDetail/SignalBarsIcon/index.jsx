import PropTypes from 'prop-types';
import { Flex } from 'antd';

const barHeightsPx = [6, 10, 14, 18, 22];

const SignalBarsIcon = ({ strength = 0 }) => {
  return (
    <Flex align="end" style={{ height: 24, gap: 4 }}>
      {barHeightsPx.map((height, index) => (
        <div
          key={index}
          style={{
            width: 5,
            height,
            backgroundColor:
              index < strength ? 'var(--granite-blue)' : '#D9D9D9',
            borderRadius: 1,
            transition: 'background-color 0.3s ease',
          }}
        />
      ))}
    </Flex>
  );
};

SignalBarsIcon.propTypes = {
  strength: PropTypes.number,
};

export default SignalBarsIcon;
