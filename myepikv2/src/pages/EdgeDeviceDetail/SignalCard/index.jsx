import PropTypes from 'prop-types';
import { Flex, Spin } from 'antd';
import { Button, InfoIcon, Tooltip, Typography } from '@/components';
import SignalBarsIcon from '../SignalBarsIcon';
import './styles.css';

const SignalStrengthCalculator = (value) => {
  value = value ? Number(value) : value;
  if (value === null) return 0;
  if (value > -61) return 5;
  if (value === -61 || value > -71) return 4;
  if (value === -71 || value > -81) return 3;
  if (value === -81 || value > -91) return 2;
  if (value >= -91) return 0;
  return 0;
};

const SignalCard = ({
  signalStrength,
  buttonIcon,
  buttonAction,
  loading,
  error = '',
}) => {
  const strength = SignalStrengthCalculator(signalStrength?.split(',')[0]);
  const title = strength === 0 ? 'No Signal' : 'Signal Strength';

  return (
    <Flex
      justify="space-between"
      align="center"
      wrap={false}
      className="signal-card"
    >
      <Flex vertical gap={4}>
        <Flex align="center" gap={4}>
          <Typography className="text-inter-500-12-18">{title}</Typography>
          {loading && <Spin size="small" />}
          {error && (
            <Tooltip title={error} placement="top">
              <InfoIcon height={12} width={12} stroke={'var(--error-600)'} />
            </Tooltip>
          )}
        </Flex>
        <SignalBarsIcon strength={strength} />
      </Flex>
      {buttonIcon && buttonAction && (
        <Flex vertical align="center">
          <Button
            color="default"
            variant="outlined"
            style={{ margin: 0, padding: 0, height: 24, width: 24 }}
            icon={buttonIcon}
            onClick={buttonAction}
          />
        </Flex>
      )}
    </Flex>
  );
};

SignalCard.propTypes = {
  signalStrength: PropTypes.string,
  buttonIcon: PropTypes.element,
  buttonAction: PropTypes.func,
  loading: PropTypes.bool,
  error: PropTypes.string,
};

export default SignalCard;
