import {
  Button,
  Flex,
  Typography,
  EllipsisText,
  DocumentAddIcon,
} from '@/components';

export function getColumns({ handleActivityClick }) {
  const renderActionButtons = ({ id }) => (
    <Flex
      align="center"
      style={{ height: '16px' }}
      onClick={(e) => e?.stopPropagation()}
    >
      <Button
        color="default"
        variant="outline"
        icon={<DocumentAddIcon width={16} height={16} />}
        style={{
          padding: '0px',
          marginRight: '12px',
          width: '16px',
          height: 'inherit',
        }}
        onClick={() => handleActivityClick(id)}
      />
    </Flex>
  );

  return [
    {
      title: (
        <Typography
          className="small-text"
          color="var(--gray-600)"
          style={{ marginLeft: '8px' }}
        >
          Caller ID
        </Typography>
      ),
      dataIndex: 'callerId',
      key: 'callerId',
      render: (text) => (
        <EllipsisText text={text} minWidth={170} className="heading-five" />
      ),
      width: 120,
    },

    {
      title: (
        <Typography
          className="small-text"
          color="var(--gray-600)"
          style={{ paddingLeft: '8px' }}
        >
          Destination #
        </Typography>
      ),
      dataIndex: 'destination',
      key: 'destination',
      render: (text) => (
        <EllipsisText
          text={text}
          className="text-medium-regular"
          minWidth={170}
        />
      ),
      width: 120,
    },

    {
      title: (
        <Typography
          className="small-text"
          color="var(--gray-600)"
          style={{ paddingLeft: '8px' }}
        >
          Type
        </Typography>
      ),
      dataIndex: 'type',
      key: 'type',
      render: (text) => <span className="text-medium-regular">{text}</span>,
      width: 100,
    },

    {
      title: (
        <Typography
          className="small-text"
          color="var(--gray-600)"
          style={{ paddingLeft: '8px' }}
        >
          Date & Time
        </Typography>
      ),
      dataIndex: 'dateTime',
      key: 'dateTime',
      render: (text) => <span className="text-medium-regular">{text}</span>,
      width: 140,
    },
    {
      title: (
        <Typography
          className="small-text"
          color="var(--gray-600)"
          style={{ paddingLeft: '8px' }}
        >
          Duration
        </Typography>
      ),
      dataIndex: 'duration',
      key: 'duration',
      render: (text) => <span className="text-medium-regular">{text}</span>,
      width: 75,
    },
    {
      title: (
        <Typography
          className="small-text"
          color="var(--gray-600)"
          style={{ paddingLeft: '8px' }}
        >
          MOS Score
        </Typography>
      ),
      dataIndex: 'mosScore',
      key: 'mosScore',
      render: (text) => <span className="text-medium-regular">{text}</span>,
      width: 75,
    },
    {
      title: '',
      key: 'action',
      render: renderActionButtons,
      width: 30,
    },
  ];
}
