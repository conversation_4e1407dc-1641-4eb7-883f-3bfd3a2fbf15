import { useEffect, useState } from 'react';
import { DataTable } from '@/components';
import { getColumns } from './constant';
import PropTypes from 'prop-types';

const RealTimePortActivityTable = ({ openActivityDetailsModal }) => {
  const dataSource = [
    {
      key: '1',
      callerId: '--',
      destination: 'Default',
      type: '--',
      dateTime: 'Feb 21, 2023, 03:05 pm',
      duration: '--',
      mosScore: '--',
      unmonitored: '--',
    },
    {
      key: '2',
      callerId: '--',
      destination: 'Public',
      type: '--',
      dateTime: 'Mar 15, 2023, 01:15 pm',
      duration: '--',
      mosScore: '--',
      unmonitored: '--',
    },
    {
      key: '3',
      callerId: '--',
      destination: 'Default',
      type: '--',
      dateTime: 'Apr 10, 2023, 11:30 am',
      duration: '--',
      mosScore: '--',
      unmonitored: '--',
    },
    {
      key: '4',
      callerId: '--',
      destination: 'Default',
      type: '--',
      dateTime: 'May 12, 2023, 02:45 pm',
      duration: '--',
      mosScore: '--',
      unmonitored: '--',
    },
    {
      key: '5',
      callerId: '--',
      destination: 'Public',
      type: '--',
      dateTime: 'Jun 18, 2023, 04:20 pm',
      duration: '--',
      mosScore: '--',
      unmonitored: '--',
    },
    {
      key: '6',
      callerId: '--',
      destination: 'Public',
      type: '--',
      dateTime: 'Jul 22, 2023, 09:10 am',
      duration: '--',
      mosScore: '--',
      unmonitored: '--',
    },
    {
      key: '7',
      callerId: '--',
      destination: 'Public',
      type: '--',
      dateTime: 'Aug 8, 2023, 06:50 pm',
      duration: '--',
      mosScore: '--',
      unmonitored: '--',
    },
    {
      key: '8',
      callerId: '--',
      destination: 'Public',
      type: '--',
      dateTime: 'Sep 14, 2023, 08:40 am',
      duration: '--',
      mosScore: '--',
      unmonitored: '--',
    },
    {
      key: '9',
      callerId: '--',
      destination: 'Public',
      type: '--',
      dateTime: 'Oct 3, 2023, 03:15 pm',
      duration: '--',
      mosScore: '--',
      unmonitored: '--',
    },
    {
      key: '10',
      callerId: '--',
      destination: 'Public',
      type: '--',
      dateTime: 'Nov 19, 2023, 07:00 pm',
      duration: '--',
      mosScore: '--',
      unmonitored: '--',
    },
  ];

  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, 0);
  }, []);

  const columns = getColumns({
    handleActivityClick: openActivityDetailsModal,
  });

  return (
    <DataTable
      columns={columns}
      data={dataSource}
      loading={loading}
      headerClass="device-table-header-row"
    />
  );
};

export default RealTimePortActivityTable;

RealTimePortActivityTable.propTypes = {
  openActivityDetailsModal: PropTypes.func.isRequired,
};
