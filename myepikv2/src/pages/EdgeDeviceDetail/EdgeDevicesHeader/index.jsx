import {
  BatteryIcon,
  Button,
  DataCenterIcon,
  DataIcon,
  DeviceHubIcon,
  DeviceMeetingIcon,
  DevicePowerIcon,
  ExpandIcon,
  Flex,
  MdiLocationIcon,
  MiningDeviceIcon,
  NetworkIcon,
  NotesPlusIcon,
  PowerIcon,
  SignalIcon,
  Tag,
  TemperatureIcon,
  Tooltip,
  Typography,
  VmActiveIcon,
} from '@/components';
import { Col, Row, Spin } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import './styles.css';
import { useEdgeDeviceStore } from '@/store/edgeDeviceSlice';
import { useEdgeDeviceDetailContext } from '../context';
import Card from '../Card';
import HeaderInfoCard from '../HeaderInfoCard';
import SignalCard from '../SignalCard';
import { useStore } from '@/store';

const getIcon = {
  'Active Interface': <VmActiveIcon height={24} width={24} />,
  'Location Name': <MdiLocationIcon height={24} width={24} />,
  'Epik Edge Address': <DeviceHubIcon height={24} width={25} />,
  'Public IP Address': <NetworkIcon height={24} width={24} />,
  'Lan IP Address': <SignalIcon height={24} width={24} />,
  'LTE IP': <SignalIcon height={24} width={24} />,
  'LTE IP 2': <SignalIcon height={24} width={24} />,
  'Power State': <PowerIcon height={24} width={24} />,
  vSwitch: <MiningDeviceIcon height={24} width={24} />,
  'Monitor State': <DeviceMeetingIcon height={24} width={24} />,
  'Active Carrier': <DataIcon height={24} width={24} />,
  'Active Data Center': <DataCenterIcon height={24} width={24} />,
  'Device Power': <DevicePowerIcon height={28} width={27} />,
  'CPU Temperature': <TemperatureIcon height={24} width={24} />,
};

const baseCardData = [
  'Active Interface',
  'Location Name',
  'Epik Edge Address',
  'Public IP Address',
  'Lan IP Address',
  'LTE IP',
  'LTE IP 2',
  'Power State',
  'vSwitch',
  'Monitor State',
  'Active Carrier',
  'Active Data Center',
  'Device Power',
  'CPU Temperature',
];

const handleAbbreviation = (key = '') => {
  const map = {
    ch: 'CHICAGO',
    la: 'LOS ANGELES',
    ny: 'NEW YORK',
    at: 'ATLANTA',
    nypingavg: 'NY',
    dl: 'DALLAS',
  };
  return map[key.toLowerCase()] || key.toUpperCase();
};

const ipFieldMap = {
  pingVpnAddress: 'vpnAddress',
  pingLteIp: 'lteIp',
  pingLteIp2: 'lteIp2',
};

/**
 *
 * @param {import('@/graphql/generated/epikv2-api/graphql').EdgeDeviceDetails} device
 * @param {import('@/store/edgeDeviceSlice').EdgeDeviceStore['loadingState']} loadingState
 * @returns
 */
const generateHeaderData = (device, loadingState, errorState = {}) => {
  return baseCardData.map((name, index) => {
    const valueMap = {
      'Active Interface': {
        loading: loadingState.activeInterface,
        value: device?.activeInterface || 'No Data',
        error: errorState['sub:activeInterface'] || '',
      },
      'Location Name': {
        value: device?.locationDoc?.locationName || 'No Data',
      },
      'Epik Edge Address': {
        value: device?.vpnAddress || 'No Data',
        actionKey: 'pingVpnAddress',
      },
      'Public IP Address': {
        loading: loadingState?.publicIp,
        value: device?.publicIp || 'No Data',
        error: errorState['sub:publicIp'] || '',
      },
      'Lan IP Address': {
        loading: loadingState.lanIp,
        value:
          device?.lanIp === 'error' || !device?.lanIp
            ? 'No Data'
            : device?.lanIp,
        error: errorState['sub:lanIp'] || '',
      },
      'LTE IP': {
        value: device?.lteIp || 'No Data',
        actionKey: 'pingLteIp',
      },
      'LTE IP 2': {
        value: device?.lteIp2 || 'No Data',
        actionKey: 'pingLteIp2',
      },
      'Power State': {
        loading: loadingState.powerState,
        value: device?.powerState || 'No Data',
        error: errorState['sub:powerState'] || '',
      },
      vSwitch: {
        value: device?.registered ? 'Registered' : 'Unregistered',
      },
      'Monitor State': {
        value: device?.monitor ? 'Monitored' : 'Unmonitored',
      },
      'Active Carrier': {
        loading: loadingState.modemInfo,
        value: device?.modemInfo?.carrier || 'N/A',
        error: errorState['sub:modemInfo'] || '',
      },
      'Active Data Center': {
        value: handleAbbreviation(device?.datacenter) || 'N/A',
      },
      'Device Power': {
        loading: loadingState.sensorData,
        value: device?.sensorData?.power || 'N/A',
        error: errorState['sub:sensorData'] || '',
      },
      'CPU Temperature': {
        loading: loadingState.sensorData,
        value: device?.sensorData?.temp || 'N/A',
        error: errorState['sub:sensorData'] || '',
      },
    };

    return {
      key: index + 1,
      name,
      error: valueMap[name]?.error,
      value: valueMap[name],
      icon: getIcon[name],
      actionKey: valueMap[name]?.actionKey || null,
    };
  });
};

const EdgeDevicesHeader = () => {
  const { edgeDeviceDetail, openModal } = useEdgeDeviceDetailContext();
  const { loadingState, resetLoadingState } = useEdgeDeviceStore();
  const [expandedView, setExpandedView] = useState(true);

  useEffect(() => {
    resetLoadingState();
  }, [edgeDeviceDetail?._id]);

  const deviceOnlineError = useStore(
    (s) => s.error.errorState[`sub:${'deviceOnline'}`] || null,
  );

  const errorState = useStore((s) => s.error.errorState || null);

  const isESeries =
    edgeDeviceDetail?.serialNumber &&
    edgeDeviceDetail?.serialNumber?.startsWith('E-');
  const showBattery =
    edgeDeviceDetail?.model === 'G4-4100' &&
    edgeDeviceDetail?.powerState === 'Battery';

  const headerData = useMemo(() => {
    let data =
      generateHeaderData(edgeDeviceDetail, loadingState, errorState) || [];

    if (isESeries) {
      return data.filter((card) => ['Location Name'].includes(card.name));
    }

    if (!edgeDeviceDetail?.lteIp2) {
      data = data.filter((card) => card.name !== 'LTE IP 2');
    }

    return data;
  }, [edgeDeviceDetail, loadingState, isESeries, errorState]);

  const handlePing = (ip, key) => {
    if (!ip || !edgeDeviceDetail?.serialNumber) return;
    if (key === 'pingVpnAddress') {
      ip = '';
    }
    openModal('edgeDevicePing', {
      serialNumber: edgeDeviceDetail?.serialNumber,
      ip,
    });
  };

  const getPingTarget = (key) => {
    return edgeDeviceDetail?.[ipFieldMap[key]] || null;
  };

  return (
    <Flex vertical className="edge-device-header">
      <Flex
        wrap
        justify="space-between"
        align="flex-start"
        className="edge-device-header__main"
      >
        <Flex vertical className="edge-device-header__info">
          <Typography className="text-inter-600-20-30">
            {edgeDeviceDetail?.displayName || '--'}
            {/* <Divider type="vertical" className="edge-device-header__divider" />
            Primary: AT&T
            <Divider type="vertical" className="edge-device-header__divider" />
            Secondary: T-Mobile - 03855442 */}
          </Typography>
          <Flex align="center" className="edge-device-header__serial">
            <Typography className="text-inter-600-16-24">
              <span className="text-inter-600-16-24-gray-500">SN#</span>{' '}
              {edgeDeviceDetail?.serialNumber || '--'}
            </Typography>
            <Tag
              text={edgeDeviceDetail?.deviceOnline ? 'Online' : 'Offline'}
              color={edgeDeviceDetail?.deviceOnline ? 'success' : 'warning'}
              bordered={false}
              className="edge-device-header__status-tag"
            />
            {loadingState?.deviceOnline && <Spin size="small" />}
            {deviceOnlineError && (
              <Typography
                className="small-text"
                style={{
                  color: 'var(--error-600)',
                  marginLeft: '16px',
                  maxWidth: '150px',
                }}
                ellipsis={{
                  tooltip: <Tooltip>{deviceOnlineError}</Tooltip>,
                }}
              >
                {deviceOnlineError}
              </Typography>
            )}
          </Flex>
        </Flex>
        <Flex wrap align="center" className="edge-device-header__cards">
          <SignalCard
            signalStrength={edgeDeviceDetail?.signalStrength}
            buttonIcon={<NotesPlusIcon width={20} height={20} />}
            buttonAction={() => console.log('refreshIcon')}
            loading={loadingState?.signalStrength}
            error={errorState?.['sub:signalStrength'] || ''}
          />
          {showBattery && (
            <Card
              type="battery"
              icon={<BatteryIcon />}
              text="Battery 59%"
              customClass="small-text edge-device-header__battery-card"
            />
          )}

          <Button
            color="default"
            variant="outlined"
            onlyIcon
            icon={<ExpandIcon height={26} width={26} />}
            onClick={() => setExpandedView((prev) => !prev)}
            className="edge-device-header__expand-btn"
          />
        </Flex>
      </Flex>

      <div
        className={`edge-device-header__expanded-section ${
          expandedView ? 'edge-device-header__expanded-section--expanded' : ''
        }`}
      >
        <Row gutter={[4, 4]} className="edge-device-header__card-grid">
          {headerData.map((card) => (
            <Col
              key={card.key}
              span={6}
              xs={24}
              sm={12}
              md={12}
              lg={12}
              xl={6}
              xll={4}
              className="edge-device-header__card-col"
            >
              <HeaderInfoCard
                icon={card.icon}
                title={card.name}
                subtitle={card.value.value}
                error={card?.error || ''}
                buttonText={
                  card?.value?.value &&
                  card?.value?.value !== 'N/A' &&
                  ipFieldMap[card.actionKey] &&
                  getPingTarget(card.actionKey)
                    ? 'Ping'
                    : null
                }
                buttonAction={
                  ipFieldMap[card?.actionKey]
                    ? () =>
                        handlePing(
                          getPingTarget(card.actionKey),
                          card.actionKey,
                        )
                    : null
                }
                loading={card.value.loading}
              />
            </Col>
          ))}
        </Row>
      </div>
    </Flex>
  );
};

export default EdgeDevicesHeader;
