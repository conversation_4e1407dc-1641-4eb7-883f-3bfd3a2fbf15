.edge-device-header {
  padding: 16px;
  background: #ffffff;
  border-radius: 12px;
}

.edge-device-header__info-text {
  color: #181c32;
}

.edge-device-header__divider {
  border: 1px solid #bdbdbd;
  padding: 0;
  height: 18px;
}

.edge-device-header__serial {
  margin-top: 12px;
}

.edge-device-header__serial-text {
  color: #414245;
}

.edge-device-header__serial-label {
  color: #a1a5b7;
}

.edge-device-header__status-tag {
  font-size: 0.95rem;
  margin-left: 4px;
}

.edge-device-header__cards {
  margin: 4px 0;
}

.edge-device-header__expand-btn {
  padding: 0;
  margin-left: 8px;
  height: fit-content;
  width: fit-content;
  border-color: rgb(77, 156, 211);
  border: none;
  border-radius: 5px;
}

.edge-device-header__expanded-section {
  overflow: hidden;
  max-height: 0;
  visibility: hidden;
  transition:
    max-height 0.8s ease,
    opacity 0.8s ease,
    visibility 0s 0.9s;
}

.edge-device-header__expanded-section--expanded {
  max-height: 500px;
  opacity: 1;
  visibility: visible;
  transition:
    max-height 0.8s ease,
    opacity 0.8s ease;
}

.edge-device-header__card-grid {
  margin-top: 16px;
}
