import React, { useState } from 'react';
import PropTypes from 'prop-types';
import {
  Input,
  Flex,
  Typography,
  Toggle,
  CloseIcon,
  Checkbox,
  Select,
  CloseTwoIcon,
  InfoIcon,
  Tooltip,
} from '@/components';
import { Divider, Row, Col } from 'antd';
import { useStore } from '@/store';
import './styles.css';

const Field = ({ label, component }) => (
  <Flex vertical gap={8}>
    <Typography className="small-text" style={{ color: '#344054' }}>
      {label}
    </Typography>
    {component}
  </Flex>
);

// Add PropTypes for Field Component
Field.propTypes = {
  label: PropTypes.string.isRequired, // `label` is a required string
  component: PropTypes.node.isRequired, // `component` is a required React node
};

const EnableDisableCodeRow = ({ enableValue = '', disableValue = '' }) => {
  return (
    <Flex align="center" gap={16}>
      <Typography className="heading-four">{`Enable  ${enableValue}`}</Typography>
      <Typography className="heading-four">{`Disable ${disableValue}`}</Typography>
    </Flex>
  );
};

EnableDisableCodeRow.propTypes = {
  enableValue: PropTypes.string.isRequired,
  disableValue: PropTypes.node.isRequired,
};

const containerStyles = {
  padding: '10px 14px 16px 14px',
  border: '1px solid #F9FAFB',
  borderRadius: '8px',
  marginTop: '16px',
  background: '#F2F4F7',
};

const CallFeatures = ({ selectedPort = '' }) => {
  const [formValues, setFormValues] = useState({});
  const [numbersList, setNumbersList] = useState(['(208) 555-0112']);

  const { closeDrawer } = useStore((state) => state.drawer);

  const handleChange = (field, value) => {
    setFormValues((prev) => ({ ...prev, [field]: value }));
  };

  const handleKeyDown = (field, value, event) => {
    if (event.key === 'Enter' && value.trim()) {
      setNumbersList((prev) => [...prev, value]);
      handleChange(field, ''); // Clear input
    }
  };

  const sections = [
    {
      title: `Port ${selectedPort} Call Forwarding`,
      secondTitle: `Port ${selectedPort} Call Forwarding`,
      showDivider: true,
      fields: [
        {
          name: 'callForwardBusy',
          span: 12,
          component: (
            <Flex vertical>
              <Flex gap={16} align="center" justify="space-between">
                <Flex align="center" gap={4}>
                  <Typography className="heading-four">
                    Call Forward busy
                  </Typography>
                  <Tooltip title="Call Forward busy" placement="top">
                    <InfoIcon width={14} height={14} />
                  </Tooltip>
                </Flex>
                <Toggle
                  checked={formValues['callForwardBusy']}
                  onChange={() =>
                    handleChange(
                      'callForwardBusy',
                      !formValues['callForwardBusy'],
                    )
                  }
                  size="small"
                />
              </Flex>
              {formValues['callForwardBusy'] && (
                <Flex style={containerStyles} vertical gap={8}>
                  <Typography className="heading-four">
                    If the line is busy, forward call to:
                  </Typography>
                  <Flex gap={8}>
                    <Checkbox
                      checked={formValues['voicMailCheckbox']}
                      onChange={() =>
                        handleChange(
                          'voicMailCheckbox',
                          !formValues['voicMailCheckbox'],
                        )
                      }
                    />
                    <Typography className="heading-four">Voicemail</Typography>
                  </Flex>
                  <Flex gap={8}>
                    <Checkbox
                      checked={formValues['phoneNumber']}
                      onChange={() =>
                        handleChange('phoneNumber', !formValues['phoneNumber'])
                      }
                    />
                    <Typography className="heading-four">
                      Phone Number
                    </Typography>
                  </Flex>
                  <Input
                    size="medium"
                    style={{ width: '60%' }}
                    placeholder="--"
                  />
                </Flex>
              )}
            </Flex>
          ),
        },
        {
          name: 'serialNumber',
          component: (
            <EnableDisableCodeRow enableValue="*90" disableValue="*90" />
          ),
          span: 12,
        },
        {
          name: 'callForwardNoAnswer1',
          span: 12,
          component: (
            <Flex vertical>
              <Flex gap={16} align="center" justify="space-between">
                <Flex align="center" gap={4}>
                  <Typography className="heading-four">
                    Call Forward No-Answer
                  </Typography>
                  <Tooltip title="Call Forward No-Answer" placement="top">
                    <InfoIcon width={14} height={14} />
                  </Tooltip>
                </Flex>
                <Toggle
                  checked={formValues['callForwardNoAnswer1']}
                  onChange={() =>
                    handleChange(
                      'callForwardNoAnswer1',
                      !formValues['callForwardNoAnswer1'],
                    )
                  }
                  size="small"
                />
              </Flex>
              {formValues['callForwardNoAnswer1'] && (
                <Flex style={containerStyles} vertical gap={8}>
                  <Typography className="heading-four">
                    If the line is busy, forward call to:
                  </Typography>
                  <Typography className="heading-four">
                    Select Call Forwarding time
                  </Typography>
                  <Select
                    defaultValue="30"
                    options={[{ value: '30', label: '30 Sec' }]}
                    style={{ width: 120 }}
                    size="medium"
                  />
                  <Flex gap={8}>
                    <Checkbox
                      checked={formValues['callForwardVoicemail']}
                      onChange={() =>
                        handleChange(
                          'callForwardVoicemail',
                          !formValues['callForwardVoicemail'],
                        )
                      }
                    />
                    <Typography className="heading-four">Voicemail</Typography>
                  </Flex>
                  <Flex gap={8}>
                    <Checkbox
                      checked={formValues['callForwardPhoneNumber']}
                      onChange={() =>
                        handleChange(
                          'callForwardPhoneNumber',
                          !formValues['callForwardPhoneNumber'],
                        )
                      }
                    />
                    <Typography className="heading-four">
                      Phone Number
                    </Typography>
                  </Flex>
                  <Input
                    size="medium"
                    style={{ width: '60%' }}
                    placeholder="--"
                  />
                </Flex>
              )}
            </Flex>
          ),
        },
        {
          name: 'serialNumber',
          component: (
            <EnableDisableCodeRow enableValue="*92" disableValue="*90" />
          ),
          span: 12,
        },
        {
          name: 'callForwardNoAnswer2',
          span: 12,
          component: (
            <Flex vertical>
              <Flex gap={16} align="center" justify="space-between">
                <Flex align="center" gap={4}>
                  <Typography className="heading-four">
                    Call Forward No-Answer
                  </Typography>
                  <Tooltip title="Call Forward No-Answer" placement="top">
                    <InfoIcon width={14} height={14} />
                  </Tooltip>
                </Flex>
                <Toggle
                  checked={formValues['callForwardNoAnswer2']}
                  onChange={() =>
                    handleChange(
                      'callForwardNoAnswer2',
                      !formValues['callForwardNoAnswer2'],
                    )
                  }
                  size="small"
                />
              </Flex>
              {formValues['callForwardNoAnswer2'] && (
                <Flex style={containerStyles} vertical>
                  <Typography className="heading-four">
                    If the line is busy, forward call to:
                  </Typography>
                  <Typography className="heading-four">
                    Select Call Forwarding time
                  </Typography>
                  <Select
                    defaultValue="30"
                    options={[{ value: '30', label: '30 Sec' }]}
                    style={{ width: 120 }}
                  />
                  <Flex gap={4}>
                    <Checkbox
                      checked={formValues['callForwardVoicemail2']}
                      onChange={() =>
                        handleChange(
                          'callForwardVoicemail2',
                          !formValues['callForwardVoicemail2'],
                        )
                      }
                    />
                    <Typography className="heading-four">Voicemail</Typography>
                  </Flex>
                  <Flex gap={4}>
                    <Checkbox
                      checked={formValues['callForwardPhoneNumber2']}
                      onChange={() =>
                        handleChange(
                          'callForwardPhoneNumber2',
                          !formValues['callForwardPhoneNumber2'],
                        )
                      }
                    />
                    <Typography className="heading-four">
                      Phone Number
                    </Typography>
                  </Flex>
                  <Input
                    size="medium"
                    style={{ width: '60%' }}
                    placeholder="--"
                  />
                </Flex>
              )}
            </Flex>
          ),
        },
        {
          name: 'serialNumber',
          component: (
            <EnableDisableCodeRow enableValue="*92" disableValue="*90" />
          ),
          span: 12,
        },
      ],
    },
    {
      title: `Port ${selectedPort} Call Features`,
      showDivider: true,
      fields: [
        {
          name: 'doNotDistrub',
          span: 12,
          component: (
            <Flex gap={16} align="center" justify="space-between">
              <Flex align="center" gap={4}>
                <Typography className="heading-four">Do Not Distrub</Typography>
                <Tooltip title="Do Not Distrub" placement="top">
                  <InfoIcon width={14} height={14} />
                </Tooltip>
              </Flex>
              <Toggle
                checked={formValues['doNotDistrub']}
                onChange={() =>
                  handleChange('doNotDistrub', !formValues['doNotDistrub'])
                }
                size="small"
              />
            </Flex>
          ),
        },
        {
          name: 'serialNumber',
          component: (
            <EnableDisableCodeRow enableValue="*78" disableValue="*79" />
          ),
          span: 12,
        },
      ],
    },
    {
      title: `Port ${selectedPort} Star Code`,
      showDivider: true,
      fields: [
        {
          name: '3WayCalling',
          span: 12,
          component: (
            <Flex gap={16} align="center" justify="space-between">
              <Typography className="heading-four">3 way calling</Typography>
              <Toggle
                checked={formValues['3WayCalling']}
                onChange={() =>
                  handleChange('3WayCalling', !formValues['3WayCalling'])
                }
                size="small"
              />
            </Flex>
          ),
        },
      ],
    },
    {
      title: 'Device Level Call Features',
      showDivider: true,
      fields: [
        {
          name: 'displayName',
          span: 12,
          component: (
            <Flex gap={16} align="center" justify="space-between">
              <Flex align="center" gap={4}>
                <Typography className="heading-four">
                  Anonymous Call Rejection
                </Typography>
                <Tooltip title="Anonymous Call Rejection" placement="top">
                  <InfoIcon width={14} height={14} />
                </Tooltip>
              </Flex>
              <Toggle
                checked={formValues['anonymousCallRejection']}
                onChange={() =>
                  handleChange(
                    'anonymousCallRejection',
                    !formValues['anonymousCallRejection'],
                  )
                }
                size="small"
              />
            </Flex>
          ),
        },
        {
          name: 'serialNumber',
          component: (
            <EnableDisableCodeRow enableValue="*77" disableValue="*87" />
          ),
          span: 12,
        },
        {
          name: 'remoteCallOverflow',
          span: 12,
          component: (
            <Flex vertical gap={4}>
              <Flex gap={16} align="center" justify="space-between">
                <Flex align="center" gap={4}>
                  <Typography className="heading-four">
                    Remote Call Overflow
                  </Typography>
                  <Tooltip title="Remote Call Overflow" placement="top">
                    <InfoIcon width={14} height={14} />
                  </Tooltip>
                </Flex>
                <Toggle
                  checked={formValues['remoteCallOverflow']}
                  onChange={() =>
                    handleChange(
                      'remoteCallOverflow',
                      !formValues['remoteCallOverflow'],
                    )
                  }
                  size="small"
                />
              </Flex>
              {formValues['remoteCallOverflow'] && (
                <Input
                  placeholder={'Specify'}
                  value={formValues['numbersList']}
                  onChange={(e) => handleChange('numbersList', e.target.value)}
                  onKeyDown={(e) =>
                    e.key === 'Enter' &&
                    handleKeyDown('numbersList', e.target.value, e)
                  }
                  className="text-medium-regular"
                  size="medium"
                />
              )}
            </Flex>
          ),
          list: numbersList,
        },
      ],
    },
  ];

  return (
    <Flex vertical>
      <Flex vertical>
        <Flex justify="space-between" align="center">
          <Typography className="heading-four-bold">Call Feature</Typography>
          <CloseIcon onClick={closeDrawer} style={{ cursor: 'pointer' }} />
        </Flex>
        <Divider className="divider-sm" />
      </Flex>
      {sections.map((section, sectionIndex) => (
        <React.Fragment key={`section-${sectionIndex}`}>
          {section?.title && section?.secondTitle && (
            <Row gutter={[32, 8]} style={{ marginTop: '16px' }}>
              <Col key={`field`} span={12}>
                <Typography className="heading-four-bold">
                  {section.title}
                </Typography>
              </Col>
              <Col key={`field`} span={12}>
                <Typography className="heading-four-bold">
                  {section.secondTitle}
                </Typography>
              </Col>
            </Row>
          )}
          {section?.title && !section?.secondTitle && (
            <Flex vertical style={{ marginTop: '16px' }}>
              <Typography className="heading-four-bold">
                {section.title}
              </Typography>
            </Flex>
          )}
          <Row gutter={[32, 8]} style={{ marginTop: '8px' }}>
            {section.fields.map((field, fieldIndex) => (
              <Col
                key={`field-${sectionIndex}-${fieldIndex}`}
                span={field.span}
                xs={24}
                sm={12}
                md={12}
                lg={field.span}
                xl={field.span}
                xxl={field.span}
              >
                {Field({
                  label: field.label,
                  component: field.component || (
                    <Input
                      placeholder={field.placeholder}
                      value={formValues[field.name]}
                      onChange={(e) => handleChange(field.name, e.target.value)}
                      onKeyDown={(e) =>
                        field.list &&
                        e.key === 'Enter' &&
                        handleKeyDown(field.name, formValues[field.name], e)
                      }
                      className="text-medium-regular"
                    />
                  ),
                })}
                {formValues['remoteCallOverflow'] && field.list && (
                  <Flex vertical style={containerStyles} justify="center">
                    {field.list.map((item, idx) => (
                      <>
                        <Flex
                          justify="space-between"
                          align="center"
                          key={idx}
                          style={{ width: '100%' }}
                        >
                          <Typography key={`${field.name}-${idx}`}>
                            {item}
                          </Typography>
                          <CloseTwoIcon
                            style={{ cursor: 'pointer' }}
                            onClick={() => {
                              setNumbersList((prev) =>
                                prev.filter((_, index) => index !== idx),
                              );
                            }}
                          />
                        </Flex>
                        {field.list.length !== idx + 1 && (
                          <Divider className="divider-sm" />
                        )}
                      </>
                    ))}
                  </Flex>
                )}
              </Col>
            ))}
            <Flex
              style={{
                width: '97%',
                marginLeft: 'auto',
                marginRight: 'auto',
                marginTop: '16px',
              }}
            >
              {section?.showDivider && <Divider className="divider-sm" />}
            </Flex>
          </Row>
        </React.Fragment>
      ))}
    </Flex>
  );
};

CallFeatures.propTypes = {
  selectedPort: PropTypes.string.isRequired,
};

export default CallFeatures;
