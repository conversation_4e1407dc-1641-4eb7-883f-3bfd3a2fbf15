import { useRef } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  AnimatedTab,
  ArrowLeftCircleIcon,
  Button,
  CloseTwoIcon,
} from '@/components';
import { useEpikBoxUpdateHook } from '@/graphqlHooks/epikv2-api/updateEpikBoxHook';
import { useEpikBoxById } from '@/graphqlHooks/epikv2-api/useEpikBoxById';
import { useStore } from '@/store';
import { Flex } from 'antd';
import PropTypes from 'prop-types';
import { useEffect, useState } from 'react';
import './styles.css';
import { epikBoxByIdKey } from '@/utils/helpers';
import ConnectionMainSection from '../ConnectionMainSection';
import RealTimePortActivity from '../Modals/RealTimePortActivity';
import OOBM from '../Modals/OOBM';
import PortsMainSection from '../PortsMainSection';
import PortDetails from '../Modals/PortDetails';
import EdgeDevicesHeader from '../EdgeDevicesHeader';
import { useEdgeDeviceDetailContext } from '../context';
import EdgeDeviceModals from '../EdgeDeviceModals';
import CallFeatures from '../CallFeatures';
import DeviceView from '../DeviceView';
import PortNumberDetail from '../PortNumberDetail';

const TabLabel = ({ portNumber, isSelected, onRemove }) => {
  return (
    <Flex align="center" gap="small">
      <div>Port {portNumber}</div>
      <CloseTwoIcon
        onClick={(e) => {
          e.stopPropagation();
          onRemove(portNumber);
        }}
        stroke={isSelected ? 'white' : 'black'}
      />
    </Flex>
  );
};

TabLabel.propTypes = {
  portNumber: PropTypes.string.isRequired,
  isSelected: PropTypes.string.isRequired,
  onRemove: PropTypes.func.isRequired,
};

const EdgeDeviceDetailView = () => {
  const {
    edgeDeviceDetail,
    showPortNumberDetail,
    handleSelectEdgeDeviceDetail,
    handlePortNumberDetail,
    handleSelectPort,
    openModal,
    handleSetRefetch,
  } = useEdgeDeviceDetailContext();
  const navigate = useNavigate();
  const { deviceId } = useParams();
  const [activePortsTab, setActivePortsTab] = useState([
    { label: 'Edge Device', value: 0 },
  ]);
  const [selectedTab, setSelectedTab] = useState(0);
  const [selectedMainSectionTab, setSelectedMainSectionTab] = useState('ports');
  const { openDrawer } = useStore((state) => state.drawer);

  const portNumberDetailRef = useRef(null);
  const scrollContainerRef = useRef(null);

  const isDeviceTabActive = selectedTab === 0;

  const { data, isLoading, refetch } = useEpikBoxById({
    input: { id: deviceId },
    keepPreviousData: false,
    enabled: isDeviceTabActive,
  });

  useEpikBoxUpdateHook(
    deviceId,
    ['*'],
    epikBoxByIdKey(deviceId),
    isDeviceTabActive,
  );

  useEffect(() => {
    const updated = data?.EpikBoxById;
    if (updated) {
      handleSelectEdgeDeviceDetail({ ...updated });
      handleSetRefetch(refetch);
    }
  }, [data?.EpikBoxById]);

  useEffect(() => {
    setActivePortsTab((prevTabs) =>
      prevTabs.map((tab) => ({
        ...tab,
        label:
          tab.value === 0 ? (
            'Edge Device'
          ) : (
            <TabLabel
              portNumber={tab.value}
              isSelected={tab.value === selectedTab}
              onRemove={removeTab}
            />
          ),
      })),
    );
  }, [selectedTab]);

  useEffect(() => {
    if (!edgeDeviceDetail?.obiDocs) return;

    const allPorts = edgeDeviceDetail.obiDocs.flatMap((obi) => [
      {
        ...obi.port1,
        obiId: obi._id,
        macAddress: obi?.macAddress,
      },
      {
        ...obi.port2,
        obiId: obi._id,
        macAddress: obi?.macAddress,
      },
    ]);

    const selectedPort = allPorts.find((port) => {
      const portNum = parseInt(
        port?.boxPortNumber?.match(/\d+/)?.[0] || '',
        10,
      );
      return portNum === selectedTab;
    });

    if (selectedTab !== 0 && selectedPort) {
      handleSelectPort({
        ...selectedPort,
        myEpik: edgeDeviceDetail?.myEpik,
        advancedModemConfiguration:
          edgeDeviceDetail?.advancedModemConfiguration,
        advancedRouting: edgeDeviceDetail?.advancedRouting,
        callerIdMaskingAccess: edgeDeviceDetail?.callerIdMaskingAccess,
        callIdOverride: edgeDeviceDetail?.callIdOverride,
        inbandRouting: edgeDeviceDetail?.inbandRouting,
        companyDoc: edgeDeviceDetail?.companyDoc,
        modemInfo: edgeDeviceDetail?.modemInfo,
      });
    }
  }, [selectedTab, edgeDeviceDetail]);

  useEffect(() => {
    const timeout = setTimeout(() => {
      if (scrollContainerRef.current && !showPortNumberDetail) {
        scrollContainerRef.current.scrollTo({ top: 0, behavior: 'smooth' });
      }
    }, 0);

    return () => clearTimeout(timeout);
  }, [selectedTab, showPortNumberDetail]);

  useEffect(() => {
    if (showPortNumberDetail && portNumberDetailRef.current) {
      portNumberDetailRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
  }, [showPortNumberDetail]);

  const openPortDetailsModal = (portNumber = null) => {
    if (!portNumber) {
      console.warn('Invalid or missing port number.');
      return;
    }

    const exists = activePortsTab.some((seg) => seg.value === portNumber);

    if (!exists) {
      const updatedPorts = [
        ...activePortsTab,
        {
          label: (
            <TabLabel
              portNumber={portNumber}
              isSelected={true}
              onRemove={removeTab}
            />
          ),
          value: portNumber,
        },
      ];
      setActivePortsTab(updatedPorts);
    }
    setSelectedTab(portNumber);
  };

  const removeTab = (targetKey) => {
    setActivePortsTab((prevTabs) => {
      const indexOfRemovedTab = prevTabs.findIndex(
        (tab) => Number(tab.value) === Number(targetKey),
      );

      if (indexOfRemovedTab === -1) {
        return prevTabs;
      }

      const filteredPortsTabs = prevTabs.filter(
        (tab) => Number(tab.value) !== Number(targetKey),
      );

      if (
        filteredPortsTabs.length === 0 ||
        (filteredPortsTabs.length === 1 && filteredPortsTabs[0].value === 0)
      ) {
        setSelectedTab(0);
        return filteredPortsTabs;
      }

      let newIndex;
      if (indexOfRemovedTab === 0) {
        newIndex = 0;
      } else if (indexOfRemovedTab >= filteredPortsTabs.length) {
        newIndex = filteredPortsTabs.length - 1;
      } else {
        newIndex = indexOfRemovedTab - 1 >= 0 ? indexOfRemovedTab - 1 : 0;
      }

      const newSelected = filteredPortsTabs[newIndex];
      setSelectedTab(newSelected ? newSelected.value : 0);

      return filteredPortsTabs;
    });
  };

  const handleOOMBAction = (e) => {
    if (e.key === 'edit') {
      openModal('editOOBM');
    }
  };

  const drawerConfig = {
    realTimePortActivity: () =>
      openDrawer(RealTimePortActivity, {
        openActivityDetailsModal: () => openModal('cdrLogs'),
      }),
    viewDevice: () =>
      openDrawer(DeviceView, {
        mode: 'view',
        edgeDeviceDetail,
      }),
    viewCallFeatures: () =>
      openDrawer(CallFeatures, { selectedPort: selectedTab }),
    openOOBMModal: () =>
      openDrawer(OOBM, {
        handleOOMBAction: (e) => handleOOMBAction(e),
      }),
  };

  const onChangeTab = (key) => {
    handlePortNumberDetail(false);
    setSelectedTab(key);
  };

  return (
    <Flex vertical className="edge-device-detail" style={{ height: '100%' }}>
      <Flex
        align="center"
        justify="space-between"
        style={{ padding: '0 12px 12px 12px' }}
      >
        <Flex align="center">
          <ArrowLeftCircleIcon
            className="edge-device-detail__back-icon"
            onClick={() => {
              handlePortNumberDetail(false);
              navigate(-1);
            }}
          />
          <AnimatedTab
            value={selectedTab}
            onChange={onChangeTab}
            options={activePortsTab}
            size="large"
          />
        </Flex>
        <Button
          onClick={() => drawerConfig.viewDevice()}
          className="responsive-button"
        >
          View Device Details
        </Button>
      </Flex>
      <div
        ref={scrollContainerRef}
        style={{ padding: '0 12px' }}
        className="custom-scrollbar"
      >
        {selectedTab === 0 || !selectedTab ? (
          <>
            <EdgeDevicesHeader />
            <PortsMainSection
              openPortDetailsModal={openPortDetailsModal}
              openRealTimePortActivityModal={() =>
                drawerConfig.realTimePortActivity()
              }
              openOOBMModal={() => drawerConfig.openOOBMModal()}
              selectedMainSectionTab={selectedMainSectionTab}
              onMainSectionTabChange={(tabKey) =>
                setSelectedMainSectionTab(tabKey)
              }
              portsTabProps={{
                isLoading,
              }}
            />
          </>
        ) : (
          <>
            <PortDetails
              openCallFeaturesDrawer={() => drawerConfig.viewCallFeatures()}
            />
            {showPortNumberDetail && (
              <div ref={portNumberDetailRef}>
                <PortNumberDetail />
              </div>
            )}
          </>
        )}
        {selectedTab === 0 && <ConnectionMainSection />}
      </div>
      <EdgeDeviceModals />
    </Flex>
  );
};

export default EdgeDeviceDetailView;
