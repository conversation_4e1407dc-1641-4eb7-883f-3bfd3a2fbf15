import PropTypes from 'prop-types';
import { Modal, Input, Flex, Button, Upload, Typography } from '@/components';
import { Divider } from 'antd';

const Title = () => (
  <Flex vertical>
    <Typography className="heading-four-bold">Add Notes</Typography>
    <Divider className="divider-sm" />
  </Flex>
);

const AddNote = ({ onCancel, handleOk }) => {
  const handleUploadChange = (info) => {
    console.log('Custom upload change:', info);
  };

  const handleDrop = (e) => {
    console.log('Custom drop event:', e.dataTransfer.files);
  };

  return (
    <Modal
      title={<Title />}
      open={true}
      onCancel={onCancel}
      width={'50%'}
      footer={
        <Flex justify="end" style={{ width: '100%' }}>
          <Button onClick={handleOk}>Add Note</Button>
        </Flex>
      }
    >
      <Flex vertical gap={6} style={{ marginTop: '16px' }}>
        <Input
          type="textarea"
          placeholder="Enter a Note ..."
          autoSize={{ minRows: 3 }}
        />

        <Upload
          action=""
          name="file"
          multiple
          onChange={handleUploadChange}
          onDrop={handleDrop}
        />
      </Flex>
    </Modal>
  );
};

// Define PropTypes
AddNote.propTypes = {
  onCancel: PropTypes.func.isRequired,
  handleOk: PropTypes.func.isRequired,
};

export default AddNote;
