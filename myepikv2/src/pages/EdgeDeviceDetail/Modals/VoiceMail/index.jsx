// VoiceMail.jsx
import { useEffect, useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import {
  UploadOutlined,
  AudioOutlined,
  CustomerServiceOutlined,
} from '@ant-design/icons';
import { useReactMediaRecorder } from 'react-media-recorder';
import { Divider, Col, message, Row, Upload, Switch, Space } from 'antd';
import {
  Modal,
  Alert,
  Button,
  Input,
  Typography,
  Flex,
  CloseTwoIcon,
} from '@/components';
import logger from '@/utils/logger';
import { useEdgeDeviceDetailContext } from '../../context';
import { APIS } from '@/constants';
import { useGet, useRestMutation } from '@/hooks';
import parsePhoneNumber from 'libphonenumber-js';

const normalizePortLabel = (input) => {
  const s = String(input || '').trim();
  const m = /port\s*([0-9]+)/i.exec(s);
  if (m) return `port${m[1]}`;
  return s.toLowerCase().replace(/\s+/g, '');
};

const normalizeEmail = (v) => v.trim().toLowerCase();
const isValidEmail = (v) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v.trim());
const isValidPhone = (v) => {
  try {
    const phone = parsePhoneNumber(v, 'US');
    return phone?.isValid() || false;
  } catch {
    return false;
  }
};
const addUniqueItem = (list, value) => {
  const incoming = (value || '').trim();
  if (!incoming) return list;
  if (list.some((x) => x.trim().toLowerCase() === incoming.toLowerCase()))
    return list;
  return [...list, incoming];
};

const isSecureOk =
  typeof window !== 'undefined'
    ? window.isSecureContext || window.location.hostname === 'localhost'
    : false;

async function enableMicrophone() {
  if (!navigator.mediaDevices?.getUserMedia) {
    const err = new Error('unsupported');
    err.name = 'NotSupportedError';
    throw err;
  }
  const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
  try {
    stream.getTracks().forEach((t) => t.stop());
  } catch (err) {
    logger.error(err);
  }
}

export default function VoiceMail({ onCancel, handleOk }) {
  const { selectedPort, refetchFn } = useEdgeDeviceDetailContext();
  const port = normalizePortLabel(selectedPort?.boxPortNumber);
  const obiId = selectedPort?.obiId;

  const [emailInput, setEmailInput] = useState('');
  const [smsInput, setSmsInput] = useState('');
  const [emailList, setEmailList] = useState([]);
  const [smsList, setSmsList] = useState([]);
  const [errors, setErrors] = useState({});

  const [greetingMode, setGreetingMode] = useState('none');
  const [uploadedFile, setUploadedFile] = useState(null);
  const [removeGreeting, setRemoveGreeting] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  const [fileVersion, setFileVersion] = useState(0);

  const [micState, setMicState] = useState(
    !isSecureOk
      ? 'insecure'
      : !navigator.mediaDevices?.getUserMedia
        ? 'unsupported'
        : 'prompt',
  );

  const {
    data: meta,
    refetch: refetchVmailBoxFn,
    error,
  } = useGet({
    api: APIS.EPIKV2,
    endpoint:
      selectedPort?.vmBox && obiId && port
        ? `/voicemails/${obiId}/${port}/vmailbox`
        : '',
    enabled: Boolean(obiId && port),
    retry: false,
    refetchOnWindowFocus: false,
    keepPreviousData: false,
  });

  useEffect(() => {
    if (meta?.status === 'success' && meta.data && !error) {
      setEmailList(meta.data.notificationEmails || []);
      setSmsList(meta.data.notificationNumbers || []);
      setRemoveGreeting(false);
      setGreetingMode('none');
      setUploadedFile(null);
    } else if (error) {
      setEmailList([]);
      setSmsList([]);
      setRemoveGreeting(false);
      setGreetingMode('none');
      setUploadedFile(null);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [meta?.status, error]);

  const { data: greetingBlob } = useGet({
    api: APIS.EPIKV2,
    endpoint:
      obiId && port && meta?.data?.greeting
        ? `/voicemails/${obiId}/${port}/vmailbox/file?v=${fileVersion}`
        : '',
    responseType: 'blob',
    enabled: Boolean(obiId && port && meta?.data?.greeting),
    retry: false,
    refetchOnWindowFocus: false,
  });
  const greetingUrl = useMemo(
    () => (greetingBlob ? URL.createObjectURL(greetingBlob) : null),
    [greetingBlob],
  );
  useEffect(
    () => () => {
      if (greetingUrl) URL.revokeObjectURL(greetingUrl);
    },
    [greetingUrl],
  );

  useEffect(() => {
    let unlisten;
    if (navigator.permissions?.query) {
      navigator.permissions
        .query({ name: 'microphone' })
        .then((p) => {
          const apply = () =>
            setMicState(
              p.state === 'granted'
                ? 'granted'
                : isSecureOk
                  ? 'prompt'
                  : 'insecure',
            );
          apply();
          p.onchange = apply;
          unlisten = () => {
            p.onchange = null;
          };
        })
        .catch(() => {});
    }
    return () => {
      if (unlisten) unlisten();
    };
  }, []);

  const {
    status,
    startRecording,
    stopRecording,
    mediaBlobUrl,
    clearBlobUrl,
    error: recError,
  } = useReactMediaRecorder({
    audio: true,
    askPermissionOnMount: false,
    onStop: () => setGreetingMode('record'),
  });
  const isRecording = status === 'recording';

  const uploadProps = {
    name: 'file',
    accept: 'audio/*',
    maxCount: 1,
    beforeUpload: (file) => {
      if (!file.type?.startsWith?.('audio/')) {
        message.error('You can only upload audio files.');
        return Upload.LIST_IGNORE;
      }
      if (isRecording) {
        message.warning('Stop recording before uploading.');
        return Upload.LIST_IGNORE;
      }
      setUploadedFile(file);
      setGreetingMode('upload');
      setRemoveGreeting(false);
      return false;
    },
    onRemove: () => {
      setUploadedFile(null);
      if (greetingMode === 'upload') setGreetingMode('none');
    },
    fileList: uploadedFile
      ? [{ uid: '1', name: uploadedFile.name, status: 'done' }]
      : [],
    showUploadList: { showPreviewIcon: false },
  };

  const addEmail = () => {
    const val = normalizeEmail(emailInput);
    if (!val) return;
    if (!isValidEmail(val))
      return setErrors((e) => ({ ...e, emailInput: 'Invalid email address' }));
    setErrors((e) => ({ ...e, emailInput: undefined }));
    setEmailList((prev) => addUniqueItem(prev, val));
    setEmailInput('');
  };

  const addSms = () => {
    const val = smsInput.trim();
    if (!val) return;
    if (!isValidPhone(val))
      return setErrors((e) => ({ ...e, smsInput: 'Invalid phone number' }));
    setErrors((e) => ({ ...e, smsInput: undefined }));
    setSmsList((prev) => addUniqueItem(prev, val));
    setSmsInput('');
  };

  const audioPreviewUrl = useMemo(() => {
    if (greetingMode === 'upload' && uploadedFile)
      return URL.createObjectURL(uploadedFile);
    if (greetingMode === 'record' && mediaBlobUrl) return mediaBlobUrl;
    return null;
  }, [greetingMode, uploadedFile, mediaBlobUrl]);

  useEffect(() => {
    return () => {
      if (audioPreviewUrl && greetingMode === 'upload')
        URL.revokeObjectURL(audioPreviewUrl);
      if (greetingMode === 'record') clearBlobUrl();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [audioPreviewUrl]);

  const recordedFile = async () => {
    if (!mediaBlobUrl) return null;
    const blob = await fetch(mediaBlobUrl).then((r) => r.blob());
    return new File([blob], 'recording.mp3', {
      type: blob.type || 'audio/mpeg',
    });
  };

  const { mutateAsync: saveVm } = useRestMutation({
    method: 'PUT',
    api: APIS.EPIKV2,
    endpoint: obiId && port ? `/voicemails/${obiId}/${port}/vmailbox` : '',
    onSuccess: async (resp) => {
      if (resp?.ok) message.success('Voicemail settings updated.');
    },
    onError: (err) => {
      message.error(err?.message || 'Update failed.');
    },
  });

  const onSubmit = async () => {
    if (emailList.length === 0 && smsList.length === 0) {
      setErrors((e) => ({
        ...e,
        submit: 'Add at least one email or SMS number.',
      }));
      return;
    }
    setErrors((e) => ({ ...e, submit: undefined }));

    let fileForUpload = null;
    if (greetingMode === 'upload' && uploadedFile) fileForUpload = uploadedFile;
    else if (greetingMode === 'record' && mediaBlobUrl)
      fileForUpload = await recordedFile();

    try {
      setSubmitting(true);
      const fd = new FormData();
      if (fileForUpload) fd.append('greeting', fileForUpload);

      const emailsCsv = emailList.join(',');
      const numbersCsv = smsList.join(',');

      fd.append('notificationEmails', emailsCsv);
      fd.append('notificationNumbers', numbersCsv);

      if (removeGreeting) fd.append('removeGreeting', '1');

      const resp = await saveVm(fd);
      if (!resp?.ok) {
        const text = await resp?.text?.().catch(() => '');
        throw new Error(text || `Upload failed: ${resp?.status}`);
      }

      await refetchVmailBoxFn();
      refetchFn && (await refetchFn());
      setFileVersion((v) => v + 1);
      handleOk();
    } catch (e) {
      message.error(e?.message || 'Update failed.');
    } finally {
      setSubmitting(false);
    }
  };

  const handleOnCancel = () => {
    setEmailList([]);
    setSmsList([]);
    setRemoveGreeting(false);
    setGreetingMode('none');
    setUploadedFile(null);

    onCancel();
  };

  const showBanner = micState !== 'granted';

  const hasServerGreeting =
    !!greetingUrl &&
    !uploadedFile &&
    greetingMode !== 'record' &&
    !removeGreeting;

  return (
    <Modal
      title={<Typography className="heading-four-bold">Voicemail</Typography>}
      open
      onCancel={() => {
        if (isRecording) stopRecording();
        onCancel();
      }}
      footer={
        <div style={{ marginTop: 16 }}>
          <Divider className="divider-sm" />
          {errors.submit && (
            <Typography className="small-text" type="danger">
              {errors.submit}
            </Typography>
          )}
          <div
            style={{
              display: 'flex',
              gap: 8,
              justifyContent: greetingUrl ? 'space-between' : 'flex-end',
              marginTop: 8,
            }}
          >
            {greetingUrl && (
              <Space size={8}>
                <Typography className="small-text" style={{ color: '#667085' }}>
                  Remove existing greeting
                </Typography>
                <Switch
                  checked={removeGreeting}
                  onChange={(v) => {
                    setRemoveGreeting(v);
                    if (v) {
                      setUploadedFile(null);
                      setGreetingMode('none');
                      clearBlobUrl();
                    }
                  }}
                  size="small"
                  disabled={!greetingUrl}
                />
              </Space>
            )}

            <div style={{ display: 'flex', gap: 8 }}>
              <Button
                onClick={() => {
                  if (isRecording) stopRecording();
                  handleOnCancel();
                }}
              >
                Cancel
              </Button>
              <Button
                type="primary"
                onClick={onSubmit}
                disabled={isRecording || submitting}
              >
                {submitting ? 'Uploading…' : 'Update'}
              </Button>
            </div>
          </div>
        </div>
      }
      width="48%"
    >
      {showBanner && (
        <Alert
          type={micState === 'insecure' ? 'warning' : 'info'}
          showIcon
          style={{ marginBottom: 12 }}
          message={
            micState === 'insecure'
              ? 'Recording requires HTTPS (or running on localhost).'
              : micState === 'unsupported'
                ? 'This browser does not support microphone recording here.'
                : 'Click “Enable Microphone” to allow recording.'
          }
          action={
            <Button
              size="small"
              onClick={async () => {
                try {
                  await enableMicrophone();
                  setMicState('granted');
                  message.success('Microphone enabled!');
                } catch (err) {
                  const name = err?.name || '';
                  if (name === 'NotAllowedError' || name === 'SecurityError') {
                    setMicState('denied');
                    message.error('Microphone access denied.');
                  } else if (
                    name === 'NotFoundError' ||
                    name === 'OverconstrainedError'
                  ) {
                    setMicState('nodevice');
                    message.error('No microphone found.');
                  } else if (name === 'NotReadableError') {
                    setMicState('inuse');
                    message.error('Microphone is in use by another app.');
                  } else if (name === 'NotSupportedError') {
                    setMicState('unsupported');
                  } else {
                    message.error('Unable to enable microphone.');
                  }
                }
              }}
              disabled={micState === 'insecure' || micState === 'unsupported'}
            >
              Enable Microphone
            </Button>
          }
        />
      )}

      <Row gutter={[12, 16]} style={{ marginTop: 8 }}>
        <Col xs={24} sm={12}>
          <Typography className="small-text" style={{ color: '#344054' }}>
            Voicemail Recording Notification email(s)
          </Typography>
          <Input
            placeholder="Add email and press Enter"
            value={emailInput}
            onChange={(e) => setEmailInput(e.target.value)}
            onPressEnter={addEmail}
            style={{ marginTop: 6 }}
            className="text-medium-regular"
            addonAfter={
              <Button
                size="small"
                type="primary"
                onClick={addEmail}
                disabled={!emailInput.trim()}
              >
                Add
              </Button>
            }
          />
          {errors.emailInput && (
            <div style={{ color: '#ff4d4f', marginTop: 6 }}>
              {errors.emailInput}
            </div>
          )}

          {emailList.length > 0 && (
            <Flex
              vertical
              style={{
                background: '#F2F4F7',
                borderRadius: '8px',
                padding: '10px 14px 16px 14px',
                marginTop: '16px',
              }}
              justify="center"
            >
              {emailList.map((item, idx) => (
                <>
                  <Flex
                    justify="space-between"
                    align="center"
                    key={idx}
                    style={{ width: '100%' }}
                  >
                    <span>{item}</span>
                    <CloseTwoIcon
                      onClick={() =>
                        setEmailList((prev) => prev.filter((_, i) => i !== idx))
                      }
                      style={{ cursor: 'pointer' }}
                    />
                  </Flex>

                  {emailList.length !== idx + 1 && (
                    <Divider className="divider-sm" />
                  )}
                </>
              ))}
            </Flex>
          )}
        </Col>

        <Col xs={24} sm={12}>
          <Typography className="small-text" style={{ color: '#344054' }}>
            Voicemail Recording Notification SMS text number(s)
          </Typography>
          <Input
            placeholder="Add number and press Enter"
            value={smsInput}
            onChange={(e) => setSmsInput(e.target.value)}
            onPressEnter={addSms}
            style={{ marginTop: 6 }}
            className="text-medium-regular"
            addonAfter={
              <Button
                size="small"
                type="primary"
                onClick={addSms}
                disabled={!smsInput.trim()}
              >
                Add
              </Button>
            }
          />
          {errors.smsInput && (
            <div style={{ color: '#ff4d4f', marginTop: 6 }}>
              {errors.smsInput}
            </div>
          )}
          {smsList.length > 0 && (
            <Flex
              vertical
              style={{
                background: '#F2F4F7',
                borderRadius: '8px',
                padding: '10px 14px 16px 14px',
                marginTop: '16px',
              }}
              justify="center"
            >
              {smsList.map((item, idx) => (
                <>
                  <Flex
                    justify="space-between"
                    align="center"
                    key={idx}
                    style={{ width: '100%' }}
                  >
                    <span>{item}</span>
                    <CloseTwoIcon
                      onClick={() =>
                        setSmsList((prev) => prev.filter((_, i) => i !== idx))
                      }
                      style={{ cursor: 'pointer' }}
                    />
                  </Flex>

                  {smsList.length !== idx + 1 && (
                    <Divider className="divider-sm" />
                  )}
                </>
              ))}
            </Flex>
          )}
        </Col>
      </Row>

      <Divider style={{ margin: '16px 0' }} />

      <Typography level={5} className="small-text" style={{ margin: 0 }}>
        Greeting File
      </Typography>
      <Typography className="small-text" style={{ color: '#667085' }}>
        Choose one: upload an audio file or record a new greeting.
      </Typography>

      <div style={{ display: 'flex', gap: 8, flexWrap: 'wrap', marginTop: 8 }}>
        <Upload {...uploadProps} disabled={isRecording || removeGreeting}>
          <Button
            variant="outlined"
            icon={<UploadOutlined />}
            type={greetingMode === 'upload' ? 'outlined' : 'default'}
          >
            Upload Audio
          </Button>
        </Upload>

        {!isRecording ? (
          <Button
            variant="outlined"
            icon={<AudioOutlined />}
            onClick={() => {
              setUploadedFile(null);
              clearBlobUrl();
              setGreetingMode('record');
              setRemoveGreeting(false);
              startRecording();
            }}
            disabled={
              !!uploadedFile || micState !== 'granted' || removeGreeting
            }
            type={greetingMode === 'record' ? 'primary' : 'default'}
          >
            Record
          </Button>
        ) : (
          <Button
            variant="outlined"
            icon={<CustomerServiceOutlined />}
            onClick={stopRecording}
            danger
          >
            Stop
          </Button>
        )}
      </div>

      {hasServerGreeting && (
        <Flex align="center" gap={8} style={{ marginTop: 8 }}>
          <Typography
            className="small-text"
            style={{ color: '#667085', marginBottom: 4 }}
          >
            Current greeting:
          </Typography>
          <audio controls src={greetingUrl} style={{ height: '35px' }} />
        </Flex>
      )}

      {audioPreviewUrl && (
        <Flex align="center" gap={8} style={{ marginTop: 8 }}>
          <Typography
            className="small-text"
            style={{ color: '#667085', marginBottom: 4 }}
          >
            New greeting preview:
          </Typography>
          <audio controls src={audioPreviewUrl} style={{ height: '35px' }} />
        </Flex>
      )}

      {!!recError && (
        <div style={{ color: '#ff4d4f', marginTop: 8 }}>{String(recError)}</div>
      )}
      {status && greetingMode === 'record' && (
        <div style={{ color: '#667085', marginTop: 4 }}>
          {status === 'recording' ? 'Recording…' : status}
        </div>
      )}
    </Modal>
  );
}

VoiceMail.propTypes = {
  onCancel: PropTypes.func.isRequired,
  handleOk: PropTypes.func.isRequired,
};
