import {
  AnimatedTab,
  Button,
  CodeDownloadIcon,
  Flex,
  Modal,
  SwapCallsIcon,
  Typography,
} from '@/components';
import { Divider } from 'antd';
import PropTypes from 'prop-types';
import { useState } from 'react';
import { useEdgeDeviceDetailContext } from '../../context';

const Title = ({ openCdrAnalyticsModal }) => (
  <Flex vertical justify={'center'} style={{ padding: '0', margin: '0px' }}>
    <Flex justify="space-between" style={{ marginTop: '-3px' }}>
      <Typography className="heading-four-bold">CDR log Analysis</Typography>
      <Flex gap={4}>
        <Button
          variant="filled"
          icon={<CodeDownloadIcon />}
          style={{ background: 'var(--granite-blue-light-active)' }}
          size="small"
        />
        <Button
          variant="filled"
          icon={<SwapCallsIcon />}
          style={{
            marginRight: '25px',
            background: 'var(--granite-blue-light-active)',
          }}
          size="small"
          onClick={openCdrAnalyticsModal}
        />
      </Flex>
    </Flex>

    <Divider className="divider-sm" />
  </Flex>
);

Title.propTypes = {
  openCdrAnalyticsModal: PropTypes.func.isRequired,
};

const notesTabs = [
  { label: 'Call 1', value: 'call_1' },
  { label: 'Call 2', value: 'call_2' },
  { label: 'Call 3', value: 'call_3' },
  { label: 'Call 4', value: 'call_4' },
  { label: 'Call 5', value: 'call_5' },
  { label: 'Call 6', value: 'call_6' },
  { label: 'Call 7', value: 'call_7' },
  { label: 'Call 8', value: 'call_8' },
  { label: 'Call 9', value: 'call_9' },
  { label: 'Call 10', value: 'call_10' },
  { label: 'Call 11', value: 'call_11' },
];

const fields = [
  'ID',
  'calldate',
  'callend',
  'duration',
  'connect_duration',
  'progress_time',
  'first_rtp_time',
  'caller',
  'caller_domain',
  'caller_reverse',
  'callername',
  'callername_reverse',
  'called',
  'called_domain',
  'called_reverse',
  'sipcallerip',
  'sipcallerport',
  'sipcalledip',
  'sipcalledport',
  'whohanged',
  'lastSIPresponse_id',
  'lastSIPresponseNum',
  'reason_sip_cause',
  'reason_sip_text_id',
];

const values = [
  '76368511',
  '2024-06-24 00:00:32',
  '2024-06-24 00:01:25',
  '00:53',
  '00:46',
  '0',
  '1',
  'sn000241190003',
  '************',
  '300091142000ns',
  '7575519316',
  '6139155757',
  '18003977431',
  '************',
  '13477930081',
  '*************',
  '5080',
  '*************',
  '5080',
  '3',
  '2',
  '200',
  'null',
  'null',
];

const CDRLogs = ({ onCancel }) => {
  const { openModal } = useEdgeDeviceDetailContext();
  const [selectedTab, setSelectedTab] = useState('call_1');

  const onChangeTab = (value) => {
    setSelectedTab(value);
  };

  return (
    <Modal
      title={<Title openCdrAnalyticsModal={() => openModal('cdrAnalytics')} />}
      open={true}
      onCancel={onCancel}
      width={'80%'}
      footer={
        <Button variant="outlined" onClick={onCancel} style={{ width: '20%' }}>
          Close
        </Button>
      }
      style={{
        top: 20,
      }}
    >
      <Flex gap={12} vertical>
        <AnimatedTab
          value={selectedTab}
          onChange={onChangeTab}
          options={notesTabs}
          size="large"
          style={{ maxWidth: 'fit-content' }}
        />
        <Flex
          style={{
            background: 'var(--secondary-gray)',
            padding: '10px 14px 16px 14px',
            border: '1px solid #F9FAFB',
            boxShadow: '0px 1px 2px 0px #1018280D',
            borderRadius: '8px',
          }}
          gap={12}
          vertical
        >
          <Typography className="small-text-bold">ARP</Typography>
          <Divider className="divider-sm" />
          <Flex gap={42}>
            <Flex gap={4} vertical>
              {fields.map((field) => (
                <Typography className="small-text" key={field}>
                  {field}
                </Typography>
              ))}
            </Flex>
            <Flex gap={4} vertical>
              {values.map((field) => (
                <Typography className="small-text-bold" key={field}>
                  {field}
                </Typography>
              ))}
            </Flex>
          </Flex>
        </Flex>
      </Flex>
    </Modal>
  );
};

// Add PropTypes for CDRLogs Component
CDRLogs.propTypes = {
  onCancel: PropTypes.func.isRequired, // `onCancel` is a required function
};

export default CDRLogs;
