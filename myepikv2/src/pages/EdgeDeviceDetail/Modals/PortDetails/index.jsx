import React, { useState } from 'react';
import PropTypes from 'prop-types';
import {
  Input,
  Flex,
  Typography,
  Button,
  Toggle,
  EditThreeIcon,
} from '@/components';
import { Divider, Row, Col, message, Spin } from 'antd';
import './styles.css';
import { useEffect } from 'react';
import dayjs from 'dayjs';
import { useEdgeDeviceDetailContext } from '../../context';
import PortDetailInfoSection from '../../PortDetailInfoSection';
import { useRestMutation } from '@/hooks';
import { APIS } from '@/constants';

const Field = ({ label, component, bottomComponent, inline = false }) => {
  if (inline) {
    return (
      <Flex justify="space-between" align="center" style={{ width: '100%' }}>
        <Typography
          className="small-text"
          style={{ color: 'var(--primary-gray)' }}
        >
          {label}
        </Typography>
        {component}
      </Flex>
    );
  }

  return (
    <Flex vertical gap={8}>
      <Typography
        className="small-text"
        style={{ color: 'var(--primary-gray)' }}
      >
        {label}
      </Typography>
      {component}
      {bottomComponent}
    </Flex>
  );
};

Field.propTypes = {
  label: PropTypes.string.isRequired,
  component: PropTypes.node.isRequired,
  bottomComponent: PropTypes.node.isRequired,
  inline: PropTypes.bool,
};

const normalizePortLabel = (input) => {
  const s = String(input || '').trim();
  const m = /port\s*([0-9]+)/i.exec(s);
  if (m) return `port${m[1]}`;
  return s.toLowerCase().replace(/\s+/g, '');
};

// Main Component: PortDetails
const PortDetails = ({ openCallFeaturesDrawer }) => {
  const {
    selectedPort,
    showPortNumberDetail,
    openModal,
    handlePortNumberDetail,
    edgeDeviceDetail,
    refetchFn,
  } = useEdgeDeviceDetailContext();

  const [formValues, setFormValues] = useState({
    portNumber: ``,
    portLabel: '',
    lastUpdated: '',
    assignedNumber: '',
    serviceName: '',
    callerIdName: '',
    remoteCallForwarding: false,
    monitorPort: false,
    usePortAssignedNumberForE911: false,
  });

  const formatedlastUpdated = selectedPort
    ? dayjs(selectedPort?.updatedOn).format('DD/MM/YYYY')
    : '';

  useEffect(() => {
    setFormValues({
      ...formValues,
      portNumber: selectedPort?.boxPortNumber,
      portLabel: selectedPort?.assignedNumberDoc?.type,
      assignedNumber: selectedPort?.assignedNumberDoc?.number,
      serviceName: selectedPort?.serviceName,
      callerIdName: selectedPort?.assignedNumberDoc?.callerIdName,
      lastUpdated: formatedlastUpdated,
      remoteCallForwarding: selectedPort?.assignedNumberDoc?.isForwarded,
      monitorPort: selectedPort?.monitored,
      enableVoiceMail: !!selectedPort?.vmNumber || !!selectedPort?.vmBox,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedPort]);

  const port = normalizePortLabel(selectedPort?.boxPortNumber);

  const {
    mutateAsync: disableVM,
    isPending,
    isSuccess,
  } = useRestMutation({
    method: 'PUT',
    api: APIS.EPIKV2,
    endpoint:
      selectedPort?.obiId && port
        ? `/voicemails/${selectedPort?.obiId}/disableVM`
        : '',
    onSuccess: async (resp) => {
      if (resp?.ok) message.success('Voicemail settings updated.');
    },
    onError: (err) => {
      message.error(err?.message || 'Update failed.');
    },
  });

  useEffect(() => {
    if (isSuccess) {
      setFormValues((prev) => ({ ...prev, ['enableVoiceMail']: false }));
    }
  }, [isSuccess]);

  const handleChange = async (field, value) => {
    if (field === `enableVoiceMail`) {
      if (value === false && edgeDeviceDetail?._id && port) {
        const response = await disableVM({
          number: selectedPort?.vmNumber,
          boxId: edgeDeviceDetail?._id,
          port,
        });
        if (response?.status === 200 && refetchFn) {
          await refetchFn();
        }
      } else {
        openModal('voiceMail');
      }
    } else {
      setFormValues((prev) => ({ ...prev, [field]: value }));
    }
  };

  const showEnableVoice =
    edgeDeviceDetail?.myEpik ||
    !(
      selectedPort?.assignedNumberDoc?.type === 'voice' ||
      selectedPort?.assignedNumberDoc?.type === 'hunt'
    );

  const sections = [
    {
      fields: [
        {
          name: 'portNumber',
          label: 'Port Number',
          placeholder: 'Enter Port Number',
          disabled: true,
          span: 24,
        },
        {
          name: 'portLabel',
          label: 'Port Label',
          placeholder: 'Enter Port Label',
          disabled: true,
          span: 24,
        },
        {
          name: 'lastUpdated',
          label: 'Last Updated',
          placeholder: 'Last Updated Value',
          disabled: true,
          span: 24,
        },
        {
          name: 'assignedNumber',
          label: 'Assigned Number',
          placeholder: 'No number assigned',
          bottomComponent: (
            <Flex
              gap={3}
              onClick={() => handlePortNumberDetail(!showPortNumberDetail)}
            >
              <EditThreeIcon height={16} width={16} />
              <Typography>Modify</Typography>
            </Flex>
          ),
          disabled: true,
          span: 24,
        },
        {
          name: 'serviceName',
          label: 'Service Name',
          placeholder: 'Unassigned',
          disabled: true,
          span: 24,
        },
        {
          name: 'callerIdName',
          label: 'Caller ID Name (CNAM)',
          placeholder: 'Please Specify',
          disabled: true,
          span: 24,
        },
        {
          name: 'remoteCallForwarding',
          label: 'Remote Call Forwarding',
          component: (
            <Toggle
              checked={formValues.remoteCallForwarding}
              onChange={(value) => handleChange('remoteCallForwarding', value)}
              size="small"
            />
          ),
          inline: true,
          span: 24,
        },
        {
          name: 'monitorPort',
          label: 'Monitor Port',
          component: (
            <Toggle
              checked={formValues.monitorPort}
              onChange={(value) => handleChange('monitorPort', value)}
              size="small"
            />
          ),
          inline: true,
          span: 24,
        },
        {
          name: 'enableVoiceMail:',
          label: 'Enable Voicemail:',
          component: (
            <Flex align="center" gap={4}>
              {isPending && <Spin size="small" />}
              <Toggle
                checked={formValues?.enableVoiceMail}
                onChange={(value) => handleChange('enableVoiceMail', value)}
                size="small"
                disabled={isPending}
              />
              {formValues?.enableVoiceMail && !isPending && (
                <Typography href onClick={() => openModal('voiceMail')}>
                  Show
                </Typography>
              )}
            </Flex>
          ),
          inline: true,
          hide: showEnableVoice,
          span: 24,
        },
        {
          name: 'usePortAssignedNumberForE911',
          label: 'Use Port Assigned Number for e911:',
          component: (
            <Button
              onClick={() =>
                openModal('e911Modal', {
                  isBoxLevel: false,
                  numberId: selectedPort?.assignedNumberDoc?._id,
                  e911Number: selectedPort?.assignedNumberDoc?.number,
                  isE911: !!(
                    selectedPort?.e911Number && selectedPort?.e911Enabled
                  ),
                })
              }
              disabled={
                !selectedPort?.assignedNumberDoc?._id ||
                !selectedPort?.assignedNumberDoc?.number
              }
            >
              Use Port e911
            </Button>
          ),
          inline: true,
          span: 24,
        },
      ],
    },
  ];

  return (
    <Flex
      vertical
      style={{
        flex: 1,
        marginTop: '8px',
        paddingBottom: '16px',
        background: 'white',
        borderRadius: '12px',
        boxShadow: '0px 1px 3px 0px var(--box-shadow)',
      }}
    >
      <Flex vertical style={{ margin: '12px 12px' }}>
        <Flex justify="space-between">
          <Typography className="heading-four-bold">
            {selectedPort?.boxPortNumber}
          </Typography>
          <Flex gap={4}>
            <Button onClick={openCallFeaturesDrawer}>Call Features</Button>
            <Button onClick={() => openModal('portConfiguration')}>
              Port Configuartion
            </Button>
            <Button onClick={() => openModal('portCallHistory')}>
              Port Call History
            </Button>
          </Flex>
        </Flex>
        <Divider className="divider-sm" />
      </Flex>

      <Row gutter={24} style={{ margin: '4px 4px' }}>
        {/* Left Column: Input Fields */}
        <Col xs={24} sm={24} md={12}>
          {sections.map((section, sectionIndex) => (
            <React.Fragment key={`section-${sectionIndex}`}>
              {(section?.title || section?.showDivider) && (
                <Flex vertical>
                  <Typography className="heading-five">
                    {section.title}
                  </Typography>
                  <Divider className="divider-sm" />
                </Flex>
              )}
              <Row gutter={[16, 18]}>
                {section.fields.map((field, fieldIndex) => (
                  <>
                    {field?.hide ? null : (
                      <Col
                        key={`field-${sectionIndex}-${fieldIndex}`}
                        span={field.span}
                      >
                        <Field
                          label={field.label}
                          component={
                            field.component || (
                              <Input
                                placeholder={field.placeholder}
                                value={formValues[field.name]}
                                disabled={field.disabled}
                                onChange={(e) =>
                                  handleChange(field.name, e.target.value)
                                }
                              />
                            )
                          }
                          bottomComponent={field?.bottomComponent || null}
                          inline={field.inline} // Inline layout for toggles
                        />
                      </Col>
                    )}
                  </>
                ))}
              </Row>
            </React.Fragment>
          ))}
        </Col>

        {/* Right Column: Info Section */}
        <Col xs={24} sm={24} md={12}>
          <PortDetailInfoSection />
        </Col>
      </Row>
    </Flex>
  );
};

PortDetails.propTypes = {
  openCallFeaturesDrawer: PropTypes.func.isRequired,
};

export default PortDetails;
