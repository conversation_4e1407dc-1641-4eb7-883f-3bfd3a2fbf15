import { useState, useMemo, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  Input,
  Flex,
  Button,
  Typography,
  Modal,
  Select,
  Tooltip,
  InfoIcon,
} from '@/components';
import { Divider, Row, Col, message, Spin } from 'antd';
import { APIS, COUNTRIES } from '@/constants';
import { useEdgeDeviceDetailContext } from '../../context';
import { useGet, useRestMutation } from '@/hooks';

const REQUIRED_FIELDS = [
  'callername',
  'address1',
  'city',
  'state',
  'zip',
  'country',
];

const Field = ({ label, children, error }) => (
  <Flex vertical gap={8}>
    <Typography className="small-text" style={{ color: '#344054' }}>
      {label}
    </Typography>
    {children}
    {error && (
      <Typography className="small-text" style={{ color: 'var(--error-600)' }}>
        {error}
      </Typography>
    )}
  </Flex>
);

Field.propTypes = {
  label: PropTypes.string.isRequired,
  children: PropTypes.node.isRequired,
  error: PropTypes.string,
};

const Title = ({ title, isLoading, error }) => (
  <Flex vertical>
    <Flex gap={4} align="center">
      <Typography className="heading-four-bold">
        {title || 'Add E911 Number'}
      </Typography>
      {isLoading && <Spin size="small" />}
      {error && (
        <Tooltip title={error} placement="top">
          <InfoIcon height={12} width={12} stroke={'var(--error-600)'} />
        </Tooltip>
      )}
    </Flex>
    <Divider className="divider-sm" />
  </Flex>
);

Title.propTypes = {
  title: PropTypes.string,
  isLoading: PropTypes.bool,
  error: PropTypes.string || PropTypes.undefined,
};

const AssignE911 = ({
  onCancel,
  handleOk,
  numberId = '',
  e911Number = '',
  isE911 = false,
  hanldeIsE911Verified,
}) => {
  const { edgeDeviceDetail, selectedPort } = useEdgeDeviceDetailContext();

  const [values, setValues] = useState({
    callername: '',
    address1: '',
    address2: '',
    city: '',
    state: '',
    zip: '',
    country: 'US',
  });

  const [errors, setErrors] = useState({});

  const { mutateAsync: getGeoCode } = useRestMutation({
    method: 'PUT',
    api: APIS.EPIKV2,
    endpoint: numberId ? `/epikbox/${numberId}/geocode` : '',
    onSuccess: (resp) => {
      if (resp?.ok) message.success('Voicemail settings updated.');
    },
    onError: (err) => {
      message.error(err?.message || 'Update failed.');
    },
  });

  const { mutateAsync: updateE911Address } = useRestMutation({
    method: 'PUT',
    api: APIS.EPIKV2,
    endpoint: numberId ? `/number/${numberId}/e911` : '',
    onSuccess: (resp) => {
      if (resp?.ok) message.success('E911 updated.');
    },
    onError: (err) => {
      message.error(err?.message || 'Update failed.');
    },
  });

  const { data: e911InfoResponse, isLoading } = useGet({
    api: APIS.EPIKV2,
    endpoint: numberId && isE911 ? `/number/${numberId}/e911` : '',
    enabled: Boolean(numberId && isE911),
    retry: false,
    refetchOnWindowFocus: false,
    keepPreviousData: false,
  });

  useEffect(() => {
    if (
      !!e911InfoResponse?.status ||
      !!e911InfoResponse?.data?.status === 'Success'
    ) {
      setErrors((prev) => ({
        ...prev,
        ['e911InfoError']:
          e911InfoResponse?.data?.message || 'Unexpected error.',
      }));
      message.error(e911InfoResponse?.data?.message || 'Unexpected error.');
      return;
    }

    const e911Info = e911InfoResponse?.data?.entry_query || {};
    const merged = {
      callername: e911Info.callername || '',
      address1: e911Info.address1 || '',
      address2: e911Info.address2 || '',
      city: e911Info.city || '',
      state: e911Info.state || '',
      zip: e911Info.zip || '',
      country: (e911Info.country || '').toString().toUpperCase() || 'US',
    };

    setValues((prev) => ({ ...prev, ...merged }));
  }, [e911InfoResponse, selectedPort]);

  const countryOptions = useMemo(() => COUNTRIES, []);
  const setField = (name, val) => {
    setValues((p) => ({ ...p, [name]: val }));
    setErrors((prev) => ({ ...prev, [name]: undefined }));
  };

  const validate = () => {
    const next = {};
    for (const k of REQUIRED_FIELDS) {
      if (!String(values[k] ?? '').trim()) next[k] = 'Required';
    }
    setErrors(next);
    return Object.keys(next).length === 0;
  };

  const onSubmit = async () => {
    if (!validate()) return;

    const payload1 = {
      callername: values.callername.trim(),
      address1: values.address1.trim(),
      address2: values.address2.trim(),
      city: values.city.trim(),
      state: values.state.trim(),
      zip: values.zip.trim(),
      country: values?.country,
      boxId: edgeDeviceDetail?._id,
    };

    const payload = {
      AddressLine1: values.address1.trim(),
      AddressLine2: values.address2.trim(),
      City: values.city.trim(),
      StateCode: values.state.trim(),
      Zip: values.zip.trim(),
    };

    try {
      const resp = await getGeoCode(payload);

      if (!resp?.ok) {
        const text = await resp?.text?.().catch(() => '');
        message.error(text || `Upload failed: ${resp?.status}`);

        const allErrors = {};
        const filteredRequiredFields = REQUIRED_FIELDS?.filter(
          (key) => key !== 'callername' && key !== 'country',
        );
        for (const k of filteredRequiredFields) {
          allErrors[k] = 'Invalid address';
        }
        setErrors(allErrors);
        hanldeIsE911Verified(false);
        return;
      }

      hanldeIsE911Verified(true);
      const resp2 = await updateE911Address(payload1);
      if (!resp2?.ok) {
        const text = await resp2?.text?.().catch(() => '');
        throw new Error(text || `Upload failed: ${resp2?.status}`);
      }
    } catch (err) {
      message.error(err?.message || 'Unexpected error.');
    } finally {
      handleOk();
    }
  };

  return (
    <Modal
      title={
        <Title
          title={e911Number}
          isLoading={isLoading}
          error={errors?.e911InfoError}
        />
      }
      open
      onCancel={onCancel}
      cancelText="Cancel"
      handleOk={onSubmit}
      width="48%"
      footer={
        <Flex justify="center" gap={8} style={{ marginTop: 24 }} vertical>
          <Divider className="divider-sm" />
          <Flex gap={8} justify="end">
            <Button
              color="default"
              variant="outlined"
              style={{ minWidth: '30%' }}
              onClick={onCancel}
            >
              Cancel
            </Button>
            <Button style={{ minWidth: '30%' }} onClick={onSubmit}>
              Update
            </Button>
          </Flex>
        </Flex>
      }
    >
      <Flex vertical>
        <Row gutter={[8, 16]} style={{ marginTop: 16 }}>
          <Col span={24} xs={24}>
            <Field label="Caller Name" error={errors.callername}>
              <Input
                placeholder="Caller Name"
                value={values.callername}
                onChange={(e) => setField('callername', e.target.value)}
                className="text-medium-regular"
              />
            </Field>
          </Col>

          <Col span={12} xs={24} sm={12}>
            <Field label="Address 1" error={errors.address1}>
              <Input
                placeholder="Street address"
                value={values.address1}
                onChange={(e) => setField('address1', e.target.value)}
                className="text-medium-regular"
              />
            </Field>
          </Col>

          <Col span={12} xs={24} sm={12}>
            <Field label="Address 2" error={errors.address2}>
              <Input
                placeholder="Specify"
                value={values.address2}
                onChange={(e) => setField('address2', e.target.value)}
                className="text-medium-regular"
              />
            </Field>
          </Col>

          <Col span={12} xs={24} sm={12}>
            <Field label="City" error={errors.city}>
              <Input
                placeholder="City"
                value={values.city}
                onChange={(e) => setField('city', e.target.value)}
                className="text-medium-regular"
              />
            </Field>
          </Col>

          <Col span={12} xs={24} sm={12}>
            <Field label="State" error={errors.state}>
              <Input
                placeholder="State"
                value={values.state}
                onChange={(e) => setField('state', e.target.value)}
                className="text-medium-regular"
              />
            </Field>
          </Col>

          <Col span={12} xs={24} sm={12}>
            <Field label="ZIP" error={errors.zip}>
              <Input
                placeholder="ZIP"
                value={values.zip}
                onChange={(e) => setField('zip', e.target.value)}
                className="text-medium-regular"
              />
            </Field>
          </Col>

          <Col span={12} xs={24} sm={12}>
            <Field label="Country" error={errors.country}>
              <Select
                showSearch
                placeholder="Select a Country"
                options={countryOptions}
                value={values.country}
                optionFilterProp="label"
                onChange={(val) => setField('country', val)}
              />
            </Field>
          </Col>
        </Row>
      </Flex>
    </Modal>
  );
};

AssignE911.propTypes = {
  onCancel: PropTypes.func.isRequired,
  handleOk: PropTypes.func.isRequired,
  numberId: PropTypes.string,
  e911Number: PropTypes.string,
  isE911: PropTypes.bool,
  hanldeIsE911Verified: PropTypes.func,
};

export default AssignE911;
