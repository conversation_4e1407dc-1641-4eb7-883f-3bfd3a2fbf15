import PropTypes from 'prop-types';
import { Modal, Button, Flex, Typography, Input } from '@/components';
import { Divider } from 'antd';

const Title = () => (
  <Flex vertical justify={'center'} style={{ padding: '0', margin: '0px' }}>
    <Typography className="heading-four-bold">New Category</Typography>
    <Divider className="divider-sm" />
  </Flex>
);

const AddOOBMCategory = ({ onCancel }) => {
  return (
    <Modal
      title={<Title />}
      open={true}
      onCancel={onCancel}
      width={'40%'}
      footer={
        <Flex vertical align="flex-end">
          <Divider className="divider-sm" />
          <Button onClick={onCancel} style={{ width: '20%' }}>
            Save
          </Button>
        </Flex>
      }
    >
      <Flex gap={12} vertical>
        <Flex vertical gap={8}>
          <Typography
            className="small-text"
            style={{ color: 'var(--primary-gray)' }}
          >
            Add Category
          </Typography>

          <Input
            placeholder={'Category Name'}
            className="text-medium-regular"
          />
        </Flex>
      </Flex>
    </Modal>
  );
};

// Add PropTypes for AddOOBMCategory Component
AddOOBMCategory.propTypes = {
  onCancel: PropTypes.func.isRequired,
};

export default AddOOBMCategory;
