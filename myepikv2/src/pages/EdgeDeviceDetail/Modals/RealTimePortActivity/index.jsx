import PropTypes from 'prop-types';
import { Button, Flex, Typography, CloseIcon } from '@/components';
import { Divider } from 'antd';
import { useStore } from '@/store';
import RealTimePortActivityTable from '../../RealTimePortActivityTable';

const RealTimePortActivity = ({ openActivityDetailsModal }) => {
  const { closeDrawer } = useStore((state) => state.drawer);
  return (
    <Flex vertical>
      <Flex vertical>
        <Flex justify="space-between" align="center">
          <Typography className="heading-four-bold">
            Real Time Port Activity
          </Typography>
          <CloseIcon onClick={closeDrawer} style={{ cursor: 'pointer' }} />
        </Flex>
        <Divider className="divider-sm" />
      </Flex>
      <RealTimePortActivityTable
        openActivityDetailsModal={openActivityDetailsModal}
      />
      <Flex vertical align="end" style={{ marginTop: '16px' }}>
        <Divider className="divider-sm" />
        <Button
          variant="outlined"
          onClick={closeDrawer}
          style={{ width: '20%' }}
        >
          Close
        </Button>
      </Flex>
    </Flex>
  );
};

// Add PropTypes for RealTimePortActivity Component
RealTimePortActivity.propTypes = {
  openActivityDetailsModal: PropTypes.func.isRequired,
};

export default RealTimePortActivity;
