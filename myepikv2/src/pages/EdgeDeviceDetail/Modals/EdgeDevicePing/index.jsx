import { useEffect, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import { Modal, Flex, Button, Typography } from '@/components';
import { Divider, Spin } from 'antd';
import { useRestMutation } from '@/hooks';
import { APIS } from '@/constants';

const Title = () => (
  <Flex vertical>
    <Typography className="heading-four-bold">Ping Edge Device</Typography>
    <Divider className="divider-sm" />
  </Flex>
);

const EdgeDevicePing = ({ serialNumber, ip, onCancel }) => {
  const [pingData, setPingData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const scrollRef = useRef(null);
  const abortControllerRef = useRef(null);

  const pingMutation = useRestMutation({
    method: 'POST',
    endpoint: `/epikbox/ping/${serialNumber}`,
    api: APIS.EPIKV2,
    ...(ip && { body: { ip } }),
    returnRawResponse: true,
    onError: (error) => {
      console.error('Ping failed:', error);
      setIsLoading(false);
    },
  });

  const readStreamLineByLine = async (response) => {
    const reader = response.body?.getReader();
    if (!reader) return;

    const decoder = new TextDecoder();
    let partial = '';

    setIsLoading(true);

    try {
      while (true) {
        const { value, done } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        const combined = partial + chunk;
        const lines = combined.split('\n');

        partial = lines.pop() || '';

        const newLines = lines
          .map((line) => line.trim())
          .filter((line) => line.startsWith('data:'))
          .map((line) => line.replace(/^data:\s*/, ''));

        setPingData((prev) => [...prev, ...newLines]);
      }
    } catch (err) {
      if (err.name === 'AbortError') {
        console.log('Ping aborted.');
      } else {
        console.error('Stream error:', err);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const startPing = async () => {
    setPingData([]);
    setIsLoading(true);

    const controller = new AbortController();
    abortControllerRef.current = controller;

    try {
      const response = await pingMutation.mutateAsync({
        signal: controller.signal,
      });

      if (response && response.body) {
        readStreamLineByLine(response);
      } else {
        setIsLoading(false);
      }
    } catch (err) {
      if (err.name === 'AbortError') {
        console.log('Ping request aborted.');
      } else {
        console.error('Ping request error:', err);
      }
      setIsLoading(false);
    }
  };

  const stopPing = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    onCancel();
  };

  useEffect(() => {
    startPing();
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [serialNumber]);

  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [pingData]);

  return (
    <Modal
      title={<Title />}
      open={true}
      onCancel={stopPing}
      width={'50%'}
      footer={
        <Flex justify="end" style={{ width: '100%' }}>
          <Button onClick={stopPing}>Close</Button>
        </Flex>
      }
    >
      <Flex vertical gap={12}>
        <Flex gap={12} align="center">
          <Typography className="text-inter-500-14-20">
            Streaming ping for device: {serialNumber}
          </Typography>
          {isLoading && !ip && <Spin size="small" />}
        </Flex>
        <Flex gap={12} align="center">
          <Typography className="text-inter-600-16-22">{ip}</Typography>
          {isLoading && ip && <Spin size="small" />}
        </Flex>

        <div
          ref={scrollRef}
          style={{
            backgroundColor: '#111',
            color: '#0f0',
            fontFamily: 'monospace',
            height: 250,
            overflowY: 'auto',
            padding: '8px',
            borderRadius: 6,
            border: '1px solid #333',
            marginTop: 12,
          }}
        >
          {pingData.length === 0 ? (
            <div>Waiting for response...</div>
          ) : (
            pingData.map((line, index) => <div key={index}>{line}</div>)
          )}
        </div>
      </Flex>
    </Modal>
  );
};

EdgeDevicePing.propTypes = {
  serialNumber: PropTypes.string.isRequired,
  ip: PropTypes.string.isRequired,
  onCancel: PropTypes.func.isRequired,
};

export default EdgeDevicePing;
