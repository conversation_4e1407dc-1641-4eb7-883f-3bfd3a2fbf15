import { useState } from 'react';
import PropTypes from 'prop-types';
import { Modal, Checkbox, Flex, Typography, Button } from '@/components';
import { Divider, Row, Col } from 'antd';

const Title = () => (
  <Flex vertical>
    <Typography className="heading-four-bold">
      Port Activation Checklist
    </Typography>
    <Divider className="divider-sm" />
  </Flex>
);

const Field = ({ label, checked, onChange }) => (
  <Flex align="center" gap={16}>
    <Checkbox checked={checked} onChange={onChange} />
    <Typography className="small-text" style={{ color: 'var(--primary-gray)' }}>
      {label}
    </Typography>
  </Flex>
);

Field.propTypes = {
  label: PropTypes.string.isRequired,
  checked: PropTypes.bool.isRequired,
  onChange: PropTypes.func.isRequired,
};

const ActivePort = ({ onCancel, handleOk }) => {
  const [formValues, setFormValues] = useState({
    dialToneOnly: false,
    firmwareUpdate: false,
    provisioningEnabled: false,
    ethernetPrimary: false,
    simSwapCheck: false,
    portStatusCheck: false,
    portConfigurationsVerified: false,
  });

  const handleChange = (field, value) => {
    setFormValues((prev) => ({ ...prev, [field]: value }));
  };

  const sections = [
    {
      fields: [
        {
          name: 'dialToneOnly',
          label: 'Dial Tone Only',
        },
        {
          name: 'firmwareUpdate',
          label: 'A Manual Firmware Update Has Been Run',
        },
        {
          name: 'provisioningEnabled',
          label: 'EPIK EPI Provisioning Is Enabled',
        },
        {
          name: 'ethernetPrimary',
          label:
            'Ethernet Set to Primary Connection (if no, confirm you added a note)',
        },
        {
          name: 'simSwapCheck',
          label:
            'SIM Swap Check (moved back to SIM 1, or added a note if SIM 2 is primary)',
        },
        {
          name: 'portStatusCheck',
          label: 'Port Status Check',
        },
        {
          name: 'portConfigurationsVerified',
          label: 'Verified Port Configurations',
        },
      ],
    },
  ];

  return (
    <Modal
      title={<Title />}
      open={true}
      onCancel={onCancel}
      okText="Assign Edge Device"
      cancelText="Cancel"
      handleOk={() => handleOk(formValues)}
      width="48%"
      footer={
        <Flex justify="center" gap={8} style={{ marginTop: '24px' }} vertical>
          <Divider className="divider-sm" />
          <Flex gap={8} justify="center">
            <Button
              color="default"
              variant="outlined"
              style={{ minWidth: '30%' }}
              onClick={onCancel}
            >
              Cancel
            </Button>
            <Button style={{ minWidth: '30%' }}>Save Porgress</Button>
            <Button style={{ minWidth: '30%' }}>Active</Button>
          </Flex>
        </Flex>
      }
    >
      <Row gutter={[24, 24]}>
        {sections.map((section, sectionIndex) => (
          <Col xs={24} key={`section-${sectionIndex}`}>
            <Row gutter={[16, 16]}>
              {section.fields.map((field, fieldIndex) => (
                <Col key={`field-${sectionIndex}-${fieldIndex}`} span={24}>
                  <Field
                    label={field.label}
                    checked={formValues[field.name]}
                    onChange={(e) => handleChange(field.name, e.target.checked)}
                  />
                </Col>
              ))}
            </Row>
          </Col>
        ))}
      </Row>
    </Modal>
  );
};

ActivePort.propTypes = {
  onCancel: PropTypes.func.isRequired,
  handleOk: PropTypes.func.isRequired,
};

export default ActivePort;
