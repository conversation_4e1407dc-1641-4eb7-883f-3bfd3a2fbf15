import PropTypes from 'prop-types';
import {
  Button,
  Flex,
  Typography,
  CloseIcon,
  Checkbox,
  Toggle,
} from '@/components';
import { Divider } from 'antd';
import { useStore } from '@/store';
import OOBMTable from '../../OOBMTable';

const OOBM = ({ handleOOMBAction }) => {
  const { closeDrawer } = useStore((state) => state.drawer);
  return (
    <Flex vertical>
      <Flex vertical>
        <Flex justify="space-between" align="center">
          <Typography className="heading-four-bold">
            Out of Band Management
          </Typography>
          <CloseIcon onClick={closeDrawer} style={{ cursor: 'pointer' }} />
        </Flex>
        <Divider className="divider-sm" />
      </Flex>
      <OOBMTable handleOOMBAction={handleOOMBAction} />
      <Flex gap={16} style={{ marginTop: '16px' }}>
        <Flex flex={1} vertical>
          <Typography className="heading-four-bold">2FA Method</Typography>
          <Divider
            className="divider-sm"
            style={{ border: '1px solid #717BBC' }}
          />
          <Flex vertical gap={8} style={{ marginTop: '8px' }}>
            <Flex align="center" gap={8}>
              <Checkbox />
              <Typography className="small-text-bold">EP2</Typography>
            </Flex>
            <Flex align="center" gap={8}>
              <Checkbox />
              <Typography className="small-text-bold">EP3</Typography>
            </Flex>
            <Flex align="center" gap={8}>
              <Checkbox />
              <Typography className="small-text-bold">EP4</Typography>
            </Flex>
          </Flex>
        </Flex>
        <Flex flex={1} vertical>
          <Typography className="heading-four-bold">Options</Typography>
          <Divider
            className="divider-sm"
            style={{ border: '1px solid #717BBC' }}
          />
          <Flex vertical gap={12} style={{ marginTop: '8px' }}>
            <Flex align="center" gap={8}>
              <Typography className="small-text-bold">TTY Over VPN</Typography>
              <Toggle size="small" />
            </Flex>
            <Flex align="center" gap={8}>
              <Button>Download VPN Profile</Button>
              <Button>Download Software</Button>
            </Flex>
          </Flex>
        </Flex>
      </Flex>
    </Flex>
  );
};

// Add PropTypes for OOBM Component
OOBM.propTypes = {
  handleOOMBAction: PropTypes.func.isRequired,
};

export default OOBM;
