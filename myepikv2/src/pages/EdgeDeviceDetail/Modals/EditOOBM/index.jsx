import { useState } from 'react';
import PropTypes from 'prop-types';
import {
  Modal,
  Button,
  Flex,
  Typography,
  Input,
  Select,
  Toggle,
} from '@/components';
import { Col, Divider, Row } from 'antd';
import { useEdgeDeviceDetailContext } from '../../context';

const Title = () => (
  <Flex vertical justify={'center'} style={{ padding: '0', margin: '0px' }}>
    <Flex justify="space-between">
      <Typography className="heading-four-bold">
        OOBM - Edit Mode (+19807900757)
      </Typography>
    </Flex>
    <Divider className="divider-sm" style={{ borderColor: '#717BBC' }} />
  </Flex>
);

const Field = ({ label, rightLabel, component, addOOBMCategory }) => (
  <Flex vertical gap={8}>
    {rightLabel ? (
      <Flex justify="space-between">
        <Typography
          className="small-text"
          style={{ color: 'var(--primary-gray)' }}
        >
          {label}
        </Typography>
        <Typography
          className="small-text"
          style={{ color: 'var(--link)', cursor: 'pointer' }}
          onClick={addOOBMCategory}
        >
          {rightLabel}
        </Typography>
      </Flex>
    ) : (
      <Typography
        className="small-text"
        style={{ color: 'var(--primary-gray)' }}
      >
        {label}
      </Typography>
    )}

    {component}
  </Flex>
);

// Add PropTypes for Field Component
Field.propTypes = {
  label: PropTypes.string.isRequired, // `label` is a required string
  rightLabel: PropTypes.string,
  component: PropTypes.node.isRequired, // `component` is a required React node
  addOOBMCategory: PropTypes.func,
};

const EditOOBM = ({ onCancel }) => {
  const { openModal } = useEdgeDeviceDetailContext();
  const [showTtyAccess, setShowTtyAccess] = useState(false);
  return (
    <Modal
      title={<Title />}
      open={true}
      onCancel={onCancel}
      width={'70%'}
      footer={
        <Flex justify="flex-end" gap={8}>
          <Button
            variant="outlined"
            onClick={onCancel}
            style={{ width: '20%' }}
          >
            Close
          </Button>
          <Button onClick={onCancel} style={{ width: '20%' }}>
            Save
          </Button>
        </Flex>
      }
    >
      <Flex gap={12} vertical>
        <Row gutter={[8, 16]} style={{ marginTop: '16px' }}>
          <Col key={`field-group-name`} span={8}>
            {Field({
              label: 'USB Interface',
              component: (
                <Input
                  placeholder={'Set Box Phone'}
                  disabled={false}
                  className="text-medium-regular"
                />
              ),
            })}
          </Col>
          <Col key={`field-management-company`} span={8}>
            {Field({
              label: 'End Point',
              component: (
                <Input
                  placeholder={'Specify...'}
                  disabled={false}
                  className="text-medium-regular"
                />
              ),
            })}
          </Col>
          <Col key={`field-management-company`} span={8}>
            {Field({
              label: 'Category',
              rightLabel: 'Add New Category',
              component: (
                <Input
                  placeholder={'Specify ...'}
                  disabled={false}
                  className="text-medium-regular"
                />
              ),
              addOOBMCategory: () => openModal('addOOBMCategory'),
            })}
          </Col>
        </Row>
        <Row gutter={[8, 16]} style={{ marginTop: '16px' }}>
          <Col key={`field-group-name`} span={4}>
            {Field({
              label: 'Speed',
              component: (
                <Select
                  defaultValue="57600"
                  options={[{ key: '57600', label: '57600' }]}
                  className="text-medium-regular"
                />
              ),
            })}
          </Col>
          <Col key={`field-group-name`} span={4}>
            {Field({
              label: 'Data Bits',
              component: (
                <Select
                  defaultValue="8"
                  options={[{ key: '8', label: '8' }]}
                  className="text-medium-regular"
                />
              ),
            })}
          </Col>
          <Col key={`field-group-name`} span={4}>
            {Field({
              label: 'Stop Bits',
              component: (
                <Select
                  defaultValue="1"
                  options={[{ key: '1', label: '1' }]}
                  className="text-medium-regular"
                />
              ),
            })}
          </Col>
          <Col key={`field-group-name`} span={4}>
            {Field({
              label: 'Flow Control',
              component: (
                <Select
                  defaultValue="1"
                  options={[{ key: '1', label: '1' }]}
                  className="text-medium-regular"
                />
              ),
            })}
          </Col>
          <Col key={`field-group-name`} span={8}>
            {Field({
              label: 'Parity',
              component: (
                <Select
                  defaultValue="none"
                  options={[{ key: 'none', label: 'None' }]}
                  className="text-medium-regular"
                />
              ),
            })}
          </Col>
        </Row>
        <Divider className="divider-sm" style={{ borderColor: '#717BBC' }} />
        <Row gutter={[16, 16]}>
          <Col key={`field-group-name`} span={4}>
            <Flex align="center" justify="space-between">
              <Typography className="small-text"> Allow TTY Access</Typography>
              <Toggle
                size="small"
                value={showTtyAccess}
                onChange={() => setShowTtyAccess(!showTtyAccess)}
              />
            </Flex>
          </Col>
          {showTtyAccess && (
            <Col key={`field-group-name`} span={8}>
              {Field({
                label: 'Shipping',
                component: (
                  <Input
                    placeholder={'Specify ...'}
                    disabled={false}
                    className="text-medium-regular"
                  />
                ),
              })}
            </Col>
          )}
        </Row>
        <Row gutter={[8, 16]}>
          {showTtyAccess && (
            <Col key={`field-group-name`} span={24}>
              {Field({
                label: 'SSH Public Key (Optional)',
                component: (
                  <Input
                    type="textarea"
                    placeholder={'Specify ..'}
                    disabled={false}
                    rows={4}
                    className="text-medium-regular"
                  />
                ),
              })}
            </Col>
          )}
        </Row>
        <Divider className="divider-sm" style={{ borderColor: '#717BBC' }} />
      </Flex>
    </Modal>
  );
};

// Add PropTypes for EditOOBM Component
EditOOBM.propTypes = {
  onCancel: PropTypes.func.isRequired, // `onCancel` is a required function
};

export default EditOOBM;
