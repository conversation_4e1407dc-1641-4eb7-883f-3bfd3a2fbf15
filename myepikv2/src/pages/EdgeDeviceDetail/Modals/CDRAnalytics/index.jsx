import PropTypes from 'prop-types';
import { Modal, Button, Flex, Typography } from '@/components';
import { Divider } from 'antd';

const Title = () => (
  <Flex vertical justify={'center'} style={{ padding: '0', margin: '0px' }}>
    <Flex justify="space-between" style={{ marginTop: '-3px' }}>
      <Typography className="heading-four-bold">CDR log Analysis</Typography>
    </Flex>
    <Divider className="divider-sm" />
  </Flex>
);

const CDRAnalytics = ({ onCancel }) => {
  return (
    <Modal
      title={<Title />}
      open={true}
      onCancel={onCancel}
      width={'70%'}
      footer={
        <Button variant="outlined" onClick={onCancel} style={{ width: '20%' }}>
          Close
        </Button>
      }
    >
      <Flex gap={12} vertical>
        <Flex
          style={{
            background: 'var(--secondary-gray)',
            padding: '10px 14px 16px 14px',
            border: '1px solid #F9FAFB',
            boxShadow: '0px 1px 2px 0px #1018280D',
            borderRadius: '8px',
          }}
          gap={12}
          vertical
        >
          <Flex gap={42}>
            <Typography>No CDR Logs Activity Found!</Typography>
          </Flex>
        </Flex>
      </Flex>
    </Modal>
  );
};

// Add PropTypes for CDRLogs Component
CDRAnalytics.propTypes = {
  onCancel: PropTypes.func.isRequired, // `onCancel` is a required function
};

export default CDRAnalytics;
