import { useState, useEffect, useMemo } from 'react';
import PropTypes from 'prop-types';
import {
  Modal,
  Flex,
  Typography,
  Input,
  Button,
  CheckTickIcon,
  InfoIcon,
  Toggle,
  RefreshIcon,
  Tooltip,
} from '@/components';
import { Divider, Row, Col, Spin } from 'antd';
import './styles.css';
import { useEdgeDeviceDetailContext } from '../../context';
import { isEscene } from '@/utils/helpers';
import { APIS } from '@/constants';
import { useGet } from '@/hooks';

const Title = ({ isLoading }) => (
  <Flex vertical>
    <Flex gap={4} align="center">
      <Typography className="heading-four-bold">Port Configuration</Typography>
      {isLoading && <Spin size="small" />}
    </Flex>
    <Divider className="divider-sm" />
  </Flex>
);

Title.propTypes = {
  isLoading: PropTypes.bool,
};

const Field = ({ label, component, defaultValue = '', tooltip, error }) => (
  <Row gutter={8} style={{ marginBottom: '16px' }}>
    <Col xs={8} lg={8} md={8} sm={24}>
      <Flex align="center" gap={8}>
        <Typography
          className="small-text"
          style={{ color: 'var(--primary-gray)' }}
        >
          {label}
        </Typography>

        {tooltip && (
          <Tooltip title={tooltip} placement="top">
            <InfoIcon
              height={12}
              width={12}
              stroke={error ? 'var(--error-600)' : 'var(--primary-gray)'}
            />
          </Tooltip>
        )}
      </Flex>
    </Col>

    <Col xs={12} lg={12} md={8} sm={24}>
      {component}
      {error && (
        <Typography style={{ color: 'var(--error-600)', fontSize: '12px' }}>
          {error}
        </Typography>
      )}
    </Col>

    <Col xs={4} lg={4} md={8} sm={24} align="end">
      <Button color="default" variant="outlined" style={{ width: '80%' }}>
        {defaultValue || '--'}
      </Button>
    </Col>
  </Row>
);

Field.propTypes = {
  label: PropTypes.string.isRequired,
  component: PropTypes.node.isRequired,
  defaultValue: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  tooltip: PropTypes.string,
  error: PropTypes.string,
};

const getPortNumber = (portString) => {
  return Number(portString.replace(/\D/g, ''));
};

export const normalizeSettingName = (fullName) => {
  const parts = fullName.split('.');
  return parts[parts.length - 1]; // e.g. "CPCDelayTime"
};

const PortConfiguration = ({ onCancel, handleOk }) => {
  const { selectedPort } = useEdgeDeviceDetailContext();
  const [formValues, setFormValues] = useState({
    CPCDuration: '',
    OnHookTipRingVoltage: '',
    OffHookCurrentMax: '',
    DTMFDetectMinLength: '',
    DTMFDetectMinGap: '',
    ChannelTxGain: '',
    ChannelRxGain: '',
    DTMFMethod: '',
    DTMFPlaybackLevel: '',
    SilenceDetectSensitivity: 'Med',
    showVadToggle: 'Disable',
    modemMode: selectedPort?.appliedConfigTemplateDoc?.modemMode || false,
    t38Enabled: selectedPort?.appliedConfigTemplateDoc?.t38Enabled || false,
    faxEnabled: selectedPort?.appliedConfigTemplateDoc?.faxEnabled || false,
    JitterMinDelay: selectedPort?.appliedConfigTemplateDoc?.JitterMinDelay || 0,
    JitterMaxDelay: selectedPort?.appliedConfigTemplateDoc?.JitterMaxDelay || 0,
    inbandRoute: selectedPort?.inbandRoute || false,
  });

  const [readOnlyValues, setReadOnlyValues] = useState({});
  const [validationErrors, setValidationErrors] = useState({});
  const [hasValidated, setHasValidated] = useState(false);

  const { data: portConfigResponse, isLoading } = useGet({
    api: APIS.EPIKV2,
    endpoint:
      selectedPort?.obiId && selectedPort?.boxPortNumber
        ? `/epi/${selectedPort?.obiId}/portValues?portNumber=${getPortNumber(selectedPort?.boxPortNumber)}`
        : '',
    enabled: Boolean(selectedPort?.obiId && selectedPort?.boxPortNumber),
    retry: false,
    refetchOnWindowFocus: false,
    keepPreviousData: false,
  });

  const tooltips = useMemo(() => {
    if (!portConfigResponse?.data?.validationRules) return {};
    return portConfigResponse.data.validationRules.reduce((acc, rule) => {
      acc[rule.name] = rule.tooltip || '';
      return acc;
    }, {});
  }, [portConfigResponse]);

  useEffect(() => {
    if (!portConfigResponse?.data?.settings) return;

    const normalizedValues = {};
    const readOnly = {};

    portConfigResponse.data.settings.forEach((setting) => {
      const key = normalizeSettingName(setting.name);
      normalizedValues[key] = setting.dbValue ?? '';
      readOnly[key] = setting.value ?? '';
    });

    setFormValues((prev) => ({
      ...prev,
      ...Object.fromEntries(
        Object.entries(normalizedValues).map(([key, value]) => [
          key,
          prev[key] !== undefined && prev[key] !== '' ? prev[key] : value,
        ]),
      ),
    }));

    setReadOnlyValues(readOnly);
  }, [portConfigResponse]);

  const isEsceneDevice =
    selectedPort?.macAddress && isEscene(selectedPort?.macAddress);

  const handleChange = (field, value) => {
    setFormValues((prev) => ({ ...prev, [field]: value }));
  };

  const validateForm = () => {
    const errors = {};
    if (portConfigResponse?.data?.validationRules) {
      portConfigResponse.data.validationRules.forEach((rule) => {
        const rawValue = formValues[rule.name];
        const valueStr = String(rawValue ?? '').trim();

        if (valueStr === '') {
          errors[rule.name] = rule.tooltip;
          return;
        }

        if (rule.regEx && !new RegExp(rule.regEx).test(valueStr)) {
          errors[rule.name] = rule.tooltip;
          return;
        }

        if (!isNaN(Number(valueStr))) {
          const rangeMatch = rule.tooltip.match(
            /(-?\d+)\s*(?:V|mA|ms)?\s*(?:-|~|to)\s*(-?\d+)/i,
          );
          if (rangeMatch) {
            const min = parseInt(rangeMatch[1], 10);
            const max = parseInt(rangeMatch[2], 10);
            const numericValue = Number(valueStr);
            if (numericValue < min || numericValue > max) {
              errors[rule.name] = rule.tooltip;
            }
          }
        }
      });
    }
    setValidationErrors(errors);
    setHasValidated(true);
    return Object.keys(errors).length === 0;
  };

  const fieldsConfig = [
    {
      name: 'CPCDuration',
      label: 'CPC Duration (in Millisecond)',
      placeholder: '2000',
      defaultValue: '2000',
    },
    {
      name: 'OnHookTipRingVoltage',
      label: 'Onhook Volts',
      placeholder: 'Enter Value',
    },
    {
      name: 'OffHookCurrentMax',
      label: 'Onhook Current',
      placeholder: 'Enter Value',
    },
    {
      name: 'DTMFDetectMinLength',
      label: 'DTMF Detect Length',
      placeholder: 'Enter Value',
    },
    {
      name: 'DTMFDetectMinGap',
      label: 'DTMF Detect Gap',
      placeholder: 'Enter Value',
    },
    {
      name: 'ChannelTxGain',
      label: 'TX Gain',
      placeholder: 'Enter Value',
    },
    {
      name: 'ChannelRxGain',
      label: 'RX Gain',
      placeholder: 'Enter Value',
    },
    {
      name: 'DTMFMethod',
      label: 'DTMF Method',
      placeholder: 'Enter Value',
    },
    {
      name: 'DTMFPlaybackLevel',
      label: 'DTMF Playback Level',
      placeholder: 'Enter Value',
    },
  ];

  return (
    <Modal
      title={<Title isLoading={isLoading} />}
      open={true}
      onCancel={onCancel}
      okText="Assign Edge Device"
      cancelText="Cancel"
      handleOk={() => handleOk(formValues)}
      width="50%"
      footer={
        <Flex justify="center" gap={6} style={{ marginTop: '24px' }} vertical>
          <Divider className="divider-sm" />
          <Flex gap={4} justify="end">
            <Button
              color="default"
              variant="filled"
              onlyIcon={true}
              icon={<RefreshIcon height={18} width={18} />}
            />
            <Button
              color="default"
              variant="outlined"
              style={{ minWidth: '25%' }}
              onClick={onCancel}
            >
              Cancel
            </Button>
            <Button
              style={{ minWidth: '25%' }}
              onClick={() => {
                if (validateForm(portConfigResponse, formValues)) {
                  handleOk(formValues);
                }
              }}
            >
              Apply
            </Button>
          </Flex>
        </Flex>
      }
    >
      <Row gutter={8} style={{ marginBottom: '16px' }}>
        <Col xs={8} lg={8} md={8} sm={24}></Col>
        <Col xs={12} lg={12} md={8} sm={24} align="center">
          <Typography>Database Settings</Typography>
        </Col>
        <Col xs={4} lg={4} md={8} sm={24} align="end">
          <Typography>Port Settings</Typography>
        </Col>
      </Row>
      {fieldsConfig.map((field) => (
        <Field
          key={field.name}
          label={field.label}
          tooltip={tooltips[field.name]}
          error={hasValidated ? validationErrors[field.name] : ''}
          component={
            <Input
              placeholder={field.placeholder}
              value={formValues[field.name] || '--'}
              disabled={field.disabled || !formValues[field.name]}
              onChange={(e) => handleChange(field.name, e.target.value)}
              suffix={
                hasValidated &&
                formValues[field.name] &&
                !validationErrors[field.name] && (
                  <CheckTickIcon height={24} width={24} />
                )
              }
            />
          }
          defaultValue={readOnlyValues[field?.name]}
        />
      ))}

      <Row gutter={6} align="middle" style={{ marginBottom: '16px' }}>
        <Col xs={24} lg={8} md={8} sm={24}>
          <Flex align="center" gap={4}>
            <Typography
              className="small-text"
              style={{ color: 'var(--primary-gray)' }}
            >
              Silence Detect
            </Typography>
            <InfoIcon height="12" width="12" />
          </Flex>
        </Col>
        <Col xs={24} lg={8} md={16} sm={24}>
          <Flex align="center" gap={8}>
            <Typography>Med</Typography>
            <Toggle
              size="small"
              checked={formValues.SilenceDetectSensitivity === 'Low'}
              onChange={(checked) =>
                handleChange(
                  'SilenceDetectSensitivity',
                  checked ? 'Low' : 'Medium',
                )
              }
            />
            <Typography>Low</Typography>
          </Flex>
        </Col>
        <Col xs={24} lg={8} md={8} sm={24}>
          <Flex
            justify="center"
            style={{
              border: '1px solid #D0D5DD',
              padding: '2px 6px',
              borderRadius: '6px',
              textAlign: 'center',
            }}
          >
            <Typography>Unable to fetch Silence Detect Sensitivity</Typography>
          </Flex>
        </Col>
      </Row>
      {/* VAD Field */}
      <Row gutter={6} align="middle" style={{ marginBottom: '16px' }}>
        <Col xs={24} lg={8} md={8} sm={24}>
          <Flex align="center" gap={4}>
            <Typography
              className="small-text"
              style={{ color: 'var(--primary-gray)' }}
            >
              VAD
            </Typography>
            <InfoIcon height="12" width="12" />
          </Flex>
        </Col>
        <Col xs={24} lg={8} md={16} sm={24}>
          <Flex align="center" gap={6}>
            <Typography>Disable</Typography>
            <Toggle
              size="small"
              checked={formValues.showVadToggle === 'Enable'}
              onChange={(checked) =>
                handleChange('showVadToggle', checked ? 'Enable' : 'Disable')
              }
            />
            <Typography>Enable</Typography>
          </Flex>
        </Col>
        <Col xs={24} lg={8} md={8} sm={24}>
          <Flex
            justify="center"
            style={{
              border: '1px solid #D0D5DD',
              padding: '2px 6px',
              borderRadius: '6px',
              textAlign: 'center',
            }}
          >
            <Typography>Disable</Typography>
          </Flex>
        </Col>
      </Row>

      {/* Inband route */}
      {selectedPort?.appliedConfigTemplateDoc?.showInbandRouteToggle && (
        <Row gutter={6} align="middle" style={{ marginBottom: '16px' }}>
          <Col xs={24} lg={8} md={8} sm={24}>
            <Flex align="center" gap={4}>
              <Typography
                className="small-text"
                style={{ color: 'var(--primary-gray)' }}
              >
                Inband Route
              </Typography>
              <InfoIcon height="12" width="12" />
            </Flex>
          </Col>
          <Col xs={24} lg={8} md={16} sm={24}>
            <Flex align="center" gap={6}>
              <Typography>Disable</Typography>
              <Toggle
                size="small"
                checked={formValues?.inbandRoute}
                onChange={(checked) =>
                  handleChange('inbandRoute', checked ? true : false)
                }
              />
              <Typography>Enable</Typography>
            </Flex>
          </Col>
          <Col xs={24} lg={8} md={8} sm={24}>
            <Flex
              justify="center"
              style={{
                border: '1px solid #D0D5DD',
                padding: '2px 6px',
                borderRadius: '6px',
                textAlign: 'center',
              }}
            >
              <Typography>
                {selectedPort?.inbandRoute ? 'Enabled' : 'Disabled'}
              </Typography>
            </Flex>
          </Col>
        </Row>
      )}

      {(selectedPort?.appliedConfigTemplateDoc?.showModemModeToggle ||
        isEsceneDevice) && (
        <Row gutter={6} align="middle" style={{ marginBottom: '16px' }}>
          <Col xs={24} lg={8} md={8} sm={24}>
            <Flex align="center" gap={4}>
              <Typography
                className="small-text"
                style={{ color: 'var(--primary-gray)' }}
              >
                Modem Mode
              </Typography>
              <InfoIcon height="12" width="12" />
            </Flex>
          </Col>
          <Col xs={24} lg={8} md={16} sm={24}>
            <Flex align="center" gap={6}>
              <Typography>Disable</Typography>
              <Toggle
                size="small"
                checked={formValues?.modemMode}
                onChange={(checked) =>
                  handleChange('modemMode', checked ? true : false)
                }
              />
              <Typography>Enable</Typography>
            </Flex>
          </Col>
          <Col xs={24} lg={8} md={8} sm={24}>
            <Flex
              justify="center"
              style={{
                border: '1px solid #D0D5DD',
                padding: '2px 6px',
                borderRadius: '6px',
                textAlign: 'center',
              }}
            >
              <Typography>
                {selectedPort?.appliedConfigTemplateDoc?.modemMode
                  ? 'Enabled'
                  : 'Disabled'}
              </Typography>
            </Flex>
          </Col>
        </Row>
      )}

      {/* t38Enabled */}
      {(selectedPort?.appliedConfigTemplateDoc?.showT38EnableToggle ||
        isEsceneDevice) && (
        <Row gutter={6} align="middle" style={{ marginBottom: '16px' }}>
          <Col xs={24} lg={8} md={8} sm={24}>
            <Flex align="center" gap={4}>
              <Typography
                className="small-text"
                style={{ color: 'var(--primary-gray)' }}
              >
                T38 Enable
              </Typography>
              <InfoIcon height="12" width="12" />
            </Flex>
          </Col>
          <Col xs={24} lg={8} md={16} sm={24}>
            <Flex align="center" gap={6}>
              <Typography>Disable</Typography>
              <Toggle
                size="small"
                checked={formValues?.t38Enabled}
                onChange={(checked) =>
                  handleChange('t38Enabled', checked ? true : false)
                }
              />
              <Typography>Enable</Typography>
            </Flex>
          </Col>
          <Col xs={24} lg={8} md={8} sm={24}>
            <Flex
              justify="center"
              style={{
                border: '1px solid #D0D5DD',
                padding: '2px 6px',
                borderRadius: '6px',
                textAlign: 'center',
              }}
            >
              <Typography>
                {selectedPort?.appliedConfigTemplateDoc?.t38Enabled
                  ? 'Enabled'
                  : 'Disabled'}
              </Typography>
            </Flex>
          </Col>
        </Row>
      )}

      {/* faxEnabled */}
      {(selectedPort?.appliedConfigTemplateDoc?.showFaxEnabledToggle ||
        isEsceneDevice) && (
        <Row gutter={6} align="middle" style={{ marginBottom: '16px' }}>
          <Col xs={24} lg={8} md={8} sm={24}>
            <Flex align="center" gap={4}>
              <Typography
                className="small-text"
                style={{ color: 'var(--primary-gray)' }}
              >
                Fax Event
              </Typography>
              <InfoIcon height="12" width="12" />
            </Flex>
          </Col>
          <Col xs={24} lg={8} md={16} sm={24}>
            <Flex align="center" gap={6}>
              <Typography>Disable</Typography>
              <Toggle
                size="small"
                checked={formValues?.faxEnabled}
                onChange={(checked) =>
                  handleChange('faxEnabled', checked ? true : false)
                }
              />
              <Typography>Enable</Typography>
            </Flex>
          </Col>
          <Col xs={24} lg={8} md={8} sm={24}>
            <Flex
              justify="center"
              style={{
                border: '1px solid #D0D5DD',
                padding: '2px 6px',
                borderRadius: '6px',
                textAlign: 'center',
              }}
            >
              <Typography>
                {selectedPort?.appliedConfigTemplateDoc?.faxEnabled
                  ? 'Enabled'
                  : 'Disabled'}
              </Typography>
            </Flex>
          </Col>
        </Row>
      )}

      {(selectedPort?.appliedConfigTemplateDoc?.showJitterDelay ||
        isEsceneDevice) && (
        <>
          <Field
            key={'JitterMinDelay'}
            label={'Jitter Min Delay'}
            component={
              <Input
                suffix={<CheckTickIcon height={24} width={24} />}
                placeholder={'Jitter Min Delay'}
                value={formValues['JitterMinDelay']}
                onChange={(e) => handleChange('JitterMinDelay', e.target.value)}
                defaultValue={
                  selectedPort?.appliedConfigTemplateDoc?.JitterMinDelay
                }
              />
            }
          />
          <Field
            key={'JitterMaxDelay'}
            label={'Jitter Max Delay'}
            component={
              <Input
                suffix={<CheckTickIcon height={24} width={24} />}
                placeholder={'Jitter Max Delay'}
                value={formValues['JitterMaxDelay']}
                onChange={(e) => handleChange('JitterMaxDelay', e.target.value)}
                defaultValue={
                  selectedPort?.appliedConfigTemplateDoc?.JitterMaxDelay
                }
              />
            }
          />
        </>
      )}
    </Modal>
  );
};

PortConfiguration.propTypes = {
  onCancel: PropTypes.func.isRequired,
  handleOk: PropTypes.func.isRequired,
};

export default PortConfiguration;
