import PropTypes from 'prop-types';
import { Modal, Flex, Typography } from '@/components';
import { Divider } from 'antd';

const Title = () => (
  <Flex vertical>
    <Typography className="heading-four-bold">Port Call History</Typography>
    <Divider className="divider-sm" />
  </Flex>
);

const CardRow = ({ dateTime, description }) => {
  return (
    <Flex align="center" justify="space-between">
      <Typography style={{ fontSize: '14px', color: '#7093EC' }}>
        {dateTime}
      </Typography>
      <Typography style={{ fontSize: '14px' }}>{description}</Typography>
    </Flex>
  );
};

CardRow.propTypes = {
  dateTime: PropTypes.string,
  description: PropTypes.string,
};

const CallCard = () => {
  return (
    <Flex
      vertical
      style={{
        background: '#F2F4F7',
        padding: '10px 14px 16px 14px',
        borderRadius: '8px',
      }}
    >
      <Typography>15:28:33 Date: 12/03/2025</Typography>
      <Divider className="divider-sm" />
      <Flex vertical gap={8} style={{ marginTop: '8px' }}>
        <CardRow dateTime="15:28:33" description="To PH1" />
        <CardRow dateTime="15:28:33" description="Ringing" />
        <CardRow dateTime="15:28:33" description="Call Connected" />
        <CardRow dateTime="15:28:33" description="Call Ended (SP1)" />
      </Flex>
    </Flex>
  );
};

const PortCallHistory = ({ onCancel }) => {
  return (
    <Modal
      title={<Title />}
      open={true}
      cancelText="Cancel"
      width="50%"
      onCancel={onCancel}
      footer={() => (
        <Flex>
          <Divider className="divider-sm" />
        </Flex>
      )}
    >
      <Flex
        vertical
        gap={8}
        style={{ height: '400px' }}
        className="custom-scrollbar"
      >
        <CallCard />
        <CallCard />
        <CallCard />
        <CallCard />
      </Flex>
    </Modal>
  );
};

PortCallHistory.propTypes = {
  onCancel: PropTypes.func.isRequired,
  handleOk: PropTypes.func.isRequired,
};

export default PortCallHistory;
