import { createContext, useContext, useState, useMemo } from 'react';
import PropTypes from 'prop-types';
import { useDebounce } from '@/hooks';

const EdgeDeviceDetailContext = createContext(null);

export const EdgeDevicesProvider = ({ children }) => {
  const [selectedPort, setSelectedPort] = useState(null);
  const [edgeDeviceDetail, setEdgeDeviceDetail] = useState(null);

  const [debouncedSearch, setDebouncedSearch] = useState('');

  const [modals, setModals] = useState({});

  const [selectedNumber, setSelectedNumber] = useState(null);

  const [showPortNumberDetail, setShowPortNumberDetail] = useState(false);

  const [refetchFn, setRefetchFn] = useState(null);

  const debouncedSearchHandler = useDebounce((value) => {
    setDebouncedSearch(value);
  }, 300);

  const openModal = (key, config = {}) => {
    setModals((prev) => ({ ...prev, [key]: { open: true, ...config } }));
  };

  const closeModal = (key) => {
    setModals((prev) => ({ ...prev, [key]: { ...prev[key], open: false } }));
  };

  const handleMenuClick = (e) => {
    setDebouncedSearch(e);
  };

  const handleSelectEdgeDeviceDetail = (edgeDevice) => {
    setEdgeDeviceDetail(edgeDevice);
  };

  const handleSelectPort = (port) => {
    setSelectedPort(port);
  };

  const handlePortNumberDetail = (show) => {
    setShowPortNumberDetail(show);
  };

  const handleSetRefetch = (fn) => {
    setRefetchFn(() => fn);
  };

  const contextValue = useMemo(
    () => ({
      selectedPort,
      edgeDeviceDetail,
      refetchFn,
      modals,
      openModal,
      closeModal,
      selectedNumber,
      showPortNumberDetail,

      setSelectedNumber,
      debouncedSearchHandler,
      handlePortNumberDetail,
      handleMenuClick,
      handleSelectEdgeDeviceDetail,
      handleSelectPort,
      handleSetRefetch,
    }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      showPortNumberDetail,
      selectedNumber,
      modals,
      selectedPort,
      edgeDeviceDetail,
      debouncedSearch,
      refetchFn,
    ],
  );

  return (
    <EdgeDeviceDetailContext.Provider value={contextValue}>
      {children}
    </EdgeDeviceDetailContext.Provider>
  );
};

EdgeDevicesProvider.propTypes = {
  children: PropTypes.node.isRequired,
};

export const useEdgeDeviceDetailContext = () => {
  const context = useContext(EdgeDeviceDetailContext);
  if (!context)
    throw new Error(
      'useEdgeDeviceDetailContext must be used within EdgeDeviceDetailProvider',
    );
  return context;
};
