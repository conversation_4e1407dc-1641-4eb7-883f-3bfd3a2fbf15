import {
  Flex,
  Button,
  ArrowLeftCircleIcon,
  Typography,
  UserApprovalRequest2Icon,
} from '@/components';
import PropTypes from 'prop-types';
import ApprovalRequestsTable from '../ApprovalRequestsTable';

const ApprovelRequests = ({ onBack, handleAction }) => {
  return (
    <Flex vertical gap={8} style={{ height: '100%' }}>
      <Flex
        align="center"
        justify="space-between"
        style={{ padding: '0 12px' }}
      >
        <Flex align="center">
          <ArrowLeftCircleIcon
            className="edge-device-detail__back-icon"
            onClick={onBack}
          />
          <Typography className="heading-four-bold">
            Approvel Requests
          </Typography>
        </Flex>
        <Flex gap={4}>
          <Button
            style={{ paddingLeft: '8px', paddingRight: '8px' }}
            icon={<UserApprovalRequest2Icon />}
          />
        </Flex>
      </Flex>
      <div style={{ padding: '0 12px' }} className="custom-scrollbar">
        <Flex
          vertical
          style={{
            background: 'white',
            borderRadius: '8px',
            marginBottom: '12px',
            paddingTop: '12px',
          }}
        >
          <ApprovalRequestsTable handleAction={handleAction} />
        </Flex>
      </div>
    </Flex>
  );
};

ApprovelRequests.propTypes = {
  onBack: PropTypes.func.isRequired,
  handleAction: PropTypes.func.isRequired,
};

export default ApprovelRequests;
