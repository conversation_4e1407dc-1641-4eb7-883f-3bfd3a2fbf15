import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  Input,
  Flex,
  Button,
  Typography,
  Toggle,
  CloseIcon,
  Select,
} from '@/components';
import { Divider, Row, Col } from 'antd';

const Field = ({ label, component }) => (
  <Flex vertical gap={8}>
    <Typography className="small-text" style={{ color: '#344054' }}>
      {label}
    </Typography>
    {component && component}
  </Flex>
);

Field.propTypes = {
  label: PropTypes.string.isRequired,
  component: PropTypes.node.isRequired,
};

const deriveFormValues = (u) => {
  if (!u) return {};

  return {
    name: u.name || '',
    email: u.email || '',
    phone: u.phone || u.phoneNumber || '',
    company: u.companyDoc?.name || '',
    role: u.role || '',
    permissionGroup: u.permissionsGroup || u.permissionGroup || '',

    status: u.enabled || false,
    readOnly: u.readOnly || false,
    releaseNotesAdmin: u.isReleaseNotesAdmin || false,
    userAccountApprover: u.isAccountApprover || u.userAccountApprover || false,
    epikEngineer: u.epikEngineering || false,
    accountLocked: u.accountLocked || false,
    systemServiceAccount: u.systemServiceAccount || false,
    manager: u.manager || false,
  };
};

const UpdateUser = ({ onCancel, user }) => {
  const [formValues, setFormValues] = useState({});

  useEffect(() => {
    setFormValues(deriveFormValues(user));
  }, [user]);

  const handleChange = (field, value) => {
    setFormValues((prev) => ({ ...prev, [field]: value }));
  };

  const sections = [
    {
      fields: [
        {
          name: 'name',
          label: 'Name',
          placeholder: 'Name',
          span: 12,
        },
        {
          name: 'email',
          label: 'Email',
          placeholder: 'email',
          span: 12,
        },
        {
          name: 'phone',
          label: 'Phone',
          placeholder: 'phone',
          span: 12,
        },
        {
          name: 'company',
          label: 'Company',
          placeholder: 'company',
          span: 12,
        },
        {
          name: 'role',
          label: 'Role',
          placeholder: 'Select',
          component: <Select />,
          span: 12,
        },
        {
          name: 'permissionGroup',
          label: 'Permission Group',
          placeholder: 'Select',
          component: <Select />,
          span: 12,
        },
      ],
    },
    {
      title: '',
      fields: [
        {
          onlyComponent: (
            <Flex gap={12} align="center">
              <Typography className="small-text">Password</Typography>
              <Button size="small" style={{ width: '30%' }}>
                <Typography className="small-text" style={{ color: 'white' }}>
                  Reset
                </Typography>
              </Button>
            </Flex>
          ),
          span: 6,
        },
      ],
    },
    {
      title: '',
      gutterSpace: 46,
      fields: [
        {
          onlyComponent: (
            <Flex justify="space-between">
              <Typography className="small-text">Status</Typography>
              <Toggle
                size="small"
                checked={formValues?.status}
                onChange={(val) => handleChange('status', val)}
              />
            </Flex>
          ),
          span: 4,
        },
        {
          onlyComponent: (
            <Flex justify="space-between">
              <Typography className="small-text">Read Only</Typography>
              <Toggle
                size="small"
                checked={formValues?.readOnly}
                onChange={(val) => handleChange('readOnly', val)}
              />
            </Flex>
          ),
          span: 4,
        },
        {
          onlyComponent: (
            <Flex justify="space-between">
              <Typography className="small-text">
                Release Notes Admin
              </Typography>
              <Toggle
                size="small"
                checked={formValues?.releaseNotesAdmin}
                onChange={(val) => handleChange('releaseNotesAdmin', val)}
              />
            </Flex>
          ),
          span: 6,
        },
      ],
    },
    {
      title: 'Advanced Permissions',
      gutterSpace: 36,
      fields: [
        {
          onlyComponent: (
            <Flex justify="space-between">
              <Typography className="small-text">
                User Account Approver
              </Typography>
              <Toggle
                size="small"
                checked={formValues?.userAccountApprover}
                onChange={(val) => handleChange('userAccountApprover', val)}
              />
            </Flex>
          ),
          span: 6,
        },
        {
          onlyComponent: (
            <Flex justify="space-between">
              <Typography className="small-text">
                Ignore Device Lock Message
              </Typography>
              <Toggle
                size="small"
                checked={formValues?.userAccountApprover}
                onChange={(val) => handleChange('userAccountApprover', val)}
              />
            </Flex>
          ),
          span: 6,
        },
        {
          onlyComponent: (
            <Flex justify="space-between">
              <Typography className="small-text">Epik Engineer</Typography>
              <Toggle
                size="small"
                checked={formValues?.epikEngineer}
                onChange={(val) => handleChange('epikEngineer', val)}
              />
            </Flex>
          ),
          span: 6,
        },
        {
          onlyComponent: (
            <Flex justify="space-between">
              <Typography className="small-text">Account Locked</Typography>
              <Toggle
                size="small"
                checked={formValues?.accountLocked}
                onChange={(val) => handleChange('accountLocked', val)}
              />
            </Flex>
          ),
          span: 6,
        },
        {
          onlyComponent: (
            <Flex justify="space-between">
              <Typography className="small-text">
                System Service Account
              </Typography>
              <Toggle
                size="small"
                checked={formValues?.systemServiceAccount}
                onChange={(val) => handleChange('systemServiceAccount', val)}
              />
            </Flex>
          ),
          span: 6,
        },
        {
          onlyComponent: (
            <Flex align="center" justify="space-between">
              <Typography className="small-text">Manager</Typography>
              <Toggle
                size="small"
                checked={formValues?.manager}
                onChange={(val) => handleChange('manager', val)}
              />
            </Flex>
          ),
          span: 6,
        },
      ],
    },
    {
      title: '',
      fields: [
        {
          onlyComponent: (
            <Flex gap={12} align="center">
              <Typography className="small-text">Access Control</Typography>
              <Button size="small" style={{ width: '40%' }}>
                <Typography className="small-text" style={{ color: 'white' }}>
                  Add Access
                </Typography>
              </Button>
            </Flex>
          ),
          span: 6,
        },
      ],
    },
  ];

  return (
    <Flex vertical>
      <Flex vertical>
        <Flex justify="space-between" align="center">
          <Typography className="heading-four-bold">Update User</Typography>
          <CloseIcon onClick={onCancel} style={{ cursor: 'pointer' }} />
        </Flex>
        <Divider className="divider-sm" />
      </Flex>
      {sections.map((section, sectionIndex) => (
        <React.Fragment key={`section-${sectionIndex}`}>
          {(section?.title || section?.showDivider) && (
            <Flex vertical style={{ marginTop: '24px' }}>
              <Typography className="heading-five">{section.title}</Typography>
              <Divider className="divider-sm" />
            </Flex>
          )}
          <Row
            gutter={[section?.gutterSpace ? section?.gutterSpace : 8, 16]}
            style={{ marginTop: '16px' }}
          >
            {section.fields.map((field, fieldIndex) => (
              <Col
                key={`field-${sectionIndex}-${fieldIndex}`}
                span={field.span}
                xs={24}
                sm={12}
                md={12}
                lg={field.span}
                xl={field.span}
                xxl={field.span}
              >
                {field?.onlyComponent
                  ? field.onlyComponent
                  : Field({
                      label: field.label,
                      component: field.component || (
                        <Input
                          placeholder={field.placeholder}
                          value={formValues[field?.name] || ''}
                          onChange={(e) =>
                            handleChange(field.name, e.target.value)
                          }
                          className="text-medium-regular"
                        />
                      ),
                    })}
              </Col>
            ))}
          </Row>
        </React.Fragment>
      ))}

      <Flex vertical style={{ marginTop: '4px' }}>
        <Divider className="divider-sm" />
        <Flex justify="flex-end" gap={8}>
          <Button onClick={onCancel}>Cancel</Button>
          <Button onClick={() => console.log(formValues)}>Update</Button>
        </Flex>
      </Flex>
    </Flex>
  );
};

UpdateUser.propTypes = {
  onCancel: PropTypes.func.isRequired,
  user: PropTypes.object,
};

export default UpdateUser;
