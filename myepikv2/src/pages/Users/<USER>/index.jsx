import React, { useState } from 'react';
import PropTypes from 'prop-types';
import {
  Input,
  Flex,
  Button,
  Typography,
  Toggle,
  CloseIcon,
  Select,
  Checkbox,
} from '@/components';
import { Divider, Row, Col } from 'antd';

const Field = ({ label, component }) => (
  <Flex vertical gap={8}>
    <Typography className="small-text" style={{ color: '#344054' }}>
      {label}
    </Typography>
    {component}
  </Flex>
);

Field.propTypes = {
  label: PropTypes.string.isRequired,
  component: PropTypes.node.isRequired,
};

const NewUser = ({ onCancel }) => {
  const [formValues, setFormValues] = useState({});

  const handleChange = (field, value) => {
    setFormValues((prev) => ({ ...prev, [field]: value }));
  };

  const sections = [
    {
      fields: [
        {
          name: 'name',
          label: 'Name',
          placeholder: 'Name',
          span: 12,
        },
        {
          name: 'email',
          label: 'Email',
          placeholder: 'Email',
          span: 12,
        },
        {
          name: 'phone',
          label: 'Phone',
          placeholder: '+1',
          span: 12,
        },
        {
          name: 'password',
          label: 'Password',
          placeholder: 'Please Specify',
          span: 12,
        },
        {
          name: 'autoPassword',
          onlyComponent: (
            <Flex gap={8} align="center">
              <Checkbox
                checked={formValues.autoPassword}
                onChange={(e) => handleChange('autoPassword', e.target.checked)}
              />
              <Typography className="small-text">
                Automatically Create a Password
              </Typography>
            </Flex>
          ),
          span: 12,
        },
        {
          name: 'promptPasswordChange',
          onlyComponent: (
            <Flex gap={8} align="center">
              <Checkbox
                checked={formValues.promptPasswordChange}
                onChange={(e) =>
                  handleChange('promptPasswordChange', e.target.checked)
                }
              />
              <Typography className="small-text">
                Prompt user to change password at first login
              </Typography>
            </Flex>
          ),
          span: 12,
        },
        {
          name: 'company',
          label: 'Company',
          placeholder: 'EPIK',
          span: 12,
        },
        {
          name: 'role',
          label: 'Role',
          placeholder: 'Select',
          component: (
            <Select
              value={formValues.role}
              onChange={(val) => handleChange('role', val)}
            />
          ),
          span: 12,
        },
        {
          name: 'enable2FA',
          onlyComponent: (
            <Flex gap={8} align="center">
              <Typography className="small-text-bold">Enable 2FA</Typography>
              <Toggle
                size="small"
                checked={formValues.enable2FA}
                onChange={(val) => handleChange('enable2FA', val)}
              />
            </Flex>
          ),
          span: 12,
        },
        {
          name: 'forceReset',
          onlyComponent: (
            <Flex gap={8} justify="flex-end">
              <Checkbox
                checked={formValues.forceReset}
                onChange={(e) => handleChange('forceReset', e.target.checked)}
              />
              <Typography className="small-text">
                Force password reset at next login
              </Typography>
            </Flex>
          ),
          span: 12,
        },
      ],
    },
  ];

  return (
    <Flex vertical>
      <Flex justify="space-between" align="center">
        <Typography className="heading-four-bold">New User</Typography>
        <CloseIcon onClick={onCancel} style={{ cursor: 'pointer' }} />
      </Flex>
      <Divider className="divider-sm" />

      {sections.map((section, sectionIndex) => (
        <React.Fragment key={`section-${sectionIndex}`}>
          <Row
            gutter={[section?.gutterSpace ?? 8, 16]}
            style={{ marginTop: '16px' }}
          >
            {section.fields.map((field, fieldIndex) => (
              <Col
                key={`field-${sectionIndex}-${fieldIndex}`}
                span={field.span}
                xs={24}
                sm={12}
                md={12}
                lg={field.span}
                xl={field.span}
                xxl={field.span}
              >
                {field?.onlyComponent
                  ? field.onlyComponent
                  : Field({
                      label: field.label,
                      component: field.component || (
                        <Input
                          placeholder={field.placeholder}
                          value={formValues[field.name]}
                          onChange={(e) =>
                            handleChange(field.name, e.target.value)
                          }
                          className="text-medium-regular"
                        />
                      ),
                    })}
              </Col>
            ))}
          </Row>
        </React.Fragment>
      ))}

      <Flex vertical style={{ marginTop: '4px' }}>
        <Divider className="divider-sm" />
        <Flex justify="flex-end" gap={8}>
          <Button onClick={onCancel}>Cancel</Button>
          <Button onClick={() => console.log('Save Form:', formValues)}>
            Create
          </Button>
        </Flex>
      </Flex>
    </Flex>
  );
};

NewUser.propTypes = {
  onCancel: PropTypes.func.isRequired,
};

export default NewUser;
