import { useEffect, useMemo, useState } from 'react';
import {
  <PERSON>lex,
  Button,
  ArrowLeftCircleIcon,
  Typography,
  Checkbox,
  Tooltip,
} from '@/components';
import PropTypes from 'prop-types';
import { Table } from 'antd';
import './styles.css';
import { useListPermissionGroups } from '@/graphqlHooks/auth/useListPermissionGroups';

const UpdatePermissions = ({ onBack, handleAddNewGroup }) => {
  const [permissionsState, setPermissionsState] = useState({});
  const [localLoading, setLocalLoading] = useState(true); // Local loading flag

  const { data } = useListPermissionGroups();

  const permissionOptions = useMemo(
    () => data?.ListPermissionGroups?.options || [],
    [data],
  );

  const groups = useMemo(() => data?.ListPermissionGroups?.docs || [], [data]);

  useEffect(() => {
    if (
      !data ||
      !data.ListPermissionGroups?.docs ||
      !data.ListPermissionGroups?.options
    )
      return;

    const state = {};
    for (const group of data.ListPermissionGroups.docs) {
      for (const perm of group.permissions) {
        const readKey = `${perm.permission}_${group.title}_read`;
        const writeKey = `${perm.permission}_${group.title}_write`;
        state[readKey] = perm.schema.read;
        state[writeKey] = perm.schema.write;
      }
    }

    setPermissionsState(state);
    setLocalLoading(false);
  }, [data]);

  const handleCheckboxChange = (perm, group, type) => (e) => {
    const key = `${perm}_${group}_${type}`;
    setPermissionsState((prev) => ({
      ...prev,
      [key]: e.target.checked,
    }));
  };

  const dataSource = useMemo(() => {
    return permissionOptions.map((perm, i) => {
      const row = {
        key: i,
        permission: (
          <Typography
            ellipsis={{
              tooltip: <Tooltip>{perm.label}</Tooltip>,
            }}
            className="small-text-bold"
          >
            {perm.label}
          </Typography>
        ),
      };
      groups.forEach((group) => {
        row[`read-${group.title}`] = (
          <Checkbox
            checked={
              permissionsState[`${perm.value}_${group.title}_read`] || false
            }
            onChange={handleCheckboxChange(perm.value, group.title, 'read')}
          />
        );
        row[`write-${group.title}`] = (
          <Checkbox
            checked={
              permissionsState[`${perm.value}_${group.title}_write`] || false
            }
            onChange={handleCheckboxChange(perm.value, group.title, 'write')}
          />
        );
      });
      return row;
    });
  }, [permissionsState, permissionOptions, groups]);

  const columns = useMemo(() => {
    return [
      {
        title: <Typography className="small-text-bold">Permission</Typography>,
        dataIndex: 'permission',
        key: 'permission',
        fixed: 'left',
        width: '15%',
      },
      ...groups.map((group) => ({
        title: (
          <Typography className="small-text-bold">{group.title}</Typography>
        ),
        width: 100,
        children: [
          {
            title: (
              <Typography
                ellipsis={{
                  tooltip: <Tooltip>Read</Tooltip>,
                }}
                className="small-text-bold"
              >
                Read
              </Typography>
            ),
            dataIndex: `read-${group.title}`,
            key: `read-${group.title}`,
          },
          {
            title: (
              <Typography
                ellipsis={{
                  tooltip: <Tooltip>Write</Tooltip>,
                }}
                className="small-text-bold"
              >
                Write
              </Typography>
            ),
            dataIndex: `write-${group.title}`,
            key: `write-${group.title}`,
          },
        ],
      })),
    ];
  }, [groups]);

  return (
    <Flex vertical gap={8} style={{ height: '100%' }}>
      <Flex
        align="center"
        justify="space-between"
        style={{ padding: '0 12px' }}
      >
        <Flex align="center">
          <ArrowLeftCircleIcon
            className="edge-device-detail__back-icon"
            onClick={onBack}
          />
          <Typography className="heading-four-bold">
            Update Permissions
          </Typography>
        </Flex>
        <Flex gap={4}>
          <Button>Update</Button>
          <Button onClick={handleAddNewGroup}>Add New Group</Button>
        </Flex>
      </Flex>
      <div style={{ padding: '0 12px' }} className="custom-scrollbar">
        <Table
          rowKey="key"
          columns={columns}
          dataSource={localLoading ? [] : dataSource}
          loading={localLoading}
          bordered
          size="small"
          sticky
          pagination={false}
          scroll={{ x: 'max-content' }}
          tableLayout="auto"
          className="custom-ant-table-container"
        />
      </div>
    </Flex>
  );
};

UpdatePermissions.propTypes = {
  onBack: PropTypes.func.isRequired,
  handleAddNewGroup: PropTypes.func.isRequired,
};

export default UpdatePermissions;
