import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Input, Flex, Button, Typography, CloseIcon } from '@/components';
import { Divider, Row, Col } from 'antd';
import { useStore } from '@/store';

const Field = ({ label, component }) => (
  <Flex vertical gap={8}>
    <Typography className="small-text" style={{ color: '#344054' }}>
      {label}
    </Typography>
    {component && component}
  </Flex>
);

Field.propTypes = {
  label: PropTypes.string.isRequired,
  component: PropTypes.node.isRequired,
};

const AddNewPermissionGroup = () => {
  const [formValues, setFormValues] = useState({});

  const { closeDrawer } = useStore((state) => state.drawer);

  const handleChange = (field, value) => {
    setFormValues((prev) => ({ ...prev, [field]: value }));
  };

  const sections = [
    {
      fields: [
        {
          name: 'groupName',
          label: 'Group Name',
          placeholder: 'Group Name',
          span: 24,
        },
      ],
    },
  ];

  return (
    <Flex vertical>
      <Flex vertical>
        <Flex justify="space-between" align="center">
          <Typography className="heading-four-bold">
            Add Permission Group
          </Typography>
          <CloseIcon onClick={closeDrawer} style={{ cursor: 'pointer' }} />
        </Flex>
        <Divider className="divider-sm" />
      </Flex>
      {sections.map((section, sectionIndex) => (
        <React.Fragment key={`section-${sectionIndex}`}>
          {(section?.title || section?.showDivider) && (
            <Flex vertical style={{ marginTop: '24px' }}>
              <Typography className="heading-five">{section.title}</Typography>
              <Divider className="divider-sm" />
            </Flex>
          )}
          <Row
            gutter={[section?.gutterSpace ? section?.gutterSpace : 8, 16]}
            style={{ marginTop: '16px' }}
          >
            {section.fields.map((field, fieldIndex) => (
              <Col
                key={`field-${sectionIndex}-${fieldIndex}`}
                span={field.span}
                xs={24}
                sm={12}
                md={12}
                lg={field.span}
                xl={field.span}
                xxl={field.span}
              >
                {field?.onlyComponent
                  ? field.onlyComponent
                  : Field({
                      label: field.label,
                      component: field.component || (
                        <Input
                          placeholder={field.placeholder}
                          value={formValues[field.name]}
                          onChange={(e) =>
                            handleChange(field.name, e.target.value)
                          }
                          className="text-medium-regular"
                        />
                      ),
                    })}
              </Col>
            ))}
          </Row>
        </React.Fragment>
      ))}

      <Flex vertical style={{ marginTop: '4px' }}>
        <Divider className="divider-sm" />
        <Flex justify="flex-end" gap={8}>
          <Button onClick={closeDrawer}>Cancel</Button>
          <Button onClick={() => console.log('Save Form:', formValues)}>
            Update
          </Button>
        </Flex>
      </Flex>
    </Flex>
  );
};

export default AddNewPermissionGroup;
