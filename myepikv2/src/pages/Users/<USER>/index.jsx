import PropTypes from 'prop-types';
import {
  Flex,
  Button,
  Input,
  SearchIcon,
  CustomDropDown,
  UserApprovalRequest1Icon,
  UserApprovalRequest2Icon,
  UsersGroupIcon,
  UserAddIcon,
  DataTable,
  PageTitle,
} from '@/components';
import { useStore } from '@/store';
import { getUsersMainTableColumns } from '../constant';

const menuItems = [
  { key: '', label: 'Select Filter' },
  { key: 'company', label: 'By Company' },
  { key: 'email', label: 'By Email' },
  { key: 'name', label: 'By Name' },
  { key: 'role', label: 'By Role' },
];

const UsersMainView = ({
  nextView,
  handleUserMainAction,
  data,
  loading,
  paginationData,
  onPaginationChange,
  onToggleChange,
  searchText,
  onSearchChange,
  selectedFilter,
  onFilterChange,
}) => {
  const getMultipleFeatureAccess = useStore(
    (state) => state.permission.getMultipleFeatureAccess,
  );

  const {
    USER_LIST,
    USER_ADD,
    USER_UPDATE,
    USER_PERMISSIONGROUP,
    USER_USERAPPROVAL,
  } = getMultipleFeatureAccess([
    'USER_LIST',
    'USER_ADD',
    'USER_UPDATE',
    'USER_PERMISSIONGROUP',
    'USER_USERAPPROVAL',
  ]);

  const onRowClick = () => {
    // navigate(`/edge-devices/${row?.serialNumber}`);
  };

  const columns = getUsersMainTableColumns({
    canUpdate: USER_UPDATE?.read && USER_UPDATE?.write,
    onMenuClick: (key, user) => handleUserMainAction(key, user),
    onToggleChange,
  });

  return (
    <Flex vertical gap={8} style={{ height: '100%' }}>
      <Flex
        align="center"
        wrap
        justify="space-between"
        style={{ padding: '0 12px' }}
      >
        <PageTitle pageTitle="Users" />
        <Flex align="center" wrap gap={6}>
          {USER_PERMISSIONGROUP?.read && (
            <Button
              style={{ paddingLeft: '8px', paddingRight: '8px' }}
              icon={<UserApprovalRequest1Icon />}
              onClick={() => nextView('updatePermission')}
            />
          )}
          {USER_USERAPPROVAL?.read && (
            <Button
              style={{ paddingLeft: '8px', paddingRight: '8px' }}
              icon={<UserApprovalRequest2Icon />}
              onClick={() => nextView('approvedRequest')}
            />
          )}
          <Button
            style={{ paddingLeft: '8px', paddingRight: '8px' }}
            icon={<UsersGroupIcon />}
          />
          {USER_ADD?.read && USER_ADD?.write && (
            <Button
              style={{ paddingLeft: '8px', paddingRight: '8px' }}
              icon={<UserAddIcon />}
              onClick={() => nextView('addNewUser')}
            />
          )}
          <CustomDropDown
            filterLabel={
              selectedFilter
                ? `By ${selectedFilter.charAt(0).toUpperCase() + selectedFilter.slice(1)}`
                : 'Select Filter'
            }
            menuItems={menuItems}
            handleMenuClick={onFilterChange}
          />
          <Input
            prefix={<SearchIcon />}
            placeholder={
              selectedFilter ? `Search by ${selectedFilter}` : 'Search'
            }
            style={{ maxWidth: '180px' }}
            value={searchText}
            onChange={onSearchChange}
          />
        </Flex>
      </Flex>
      <div style={{ padding: '0 12px' }} className="custom-scrollbar">
        <Flex
          vertical
          style={{
            background: 'white',
            borderRadius: '12px',
            boxShadow: '0px 1px 2px 0px #1018280F, 0px 1px 3px 0px #1018281A',
          }}
          flex={1}
        >
          <div style={{ marginTop: '16px' }}>
            <DataTable
              columns={columns}
              data={USER_LIST?.read ? data : []}
              onRowClick={onRowClick}
              loading={loading}
              rowSelection={false}
              onRowsSelectionChange={(rowsKeys, rowsData) => {
                console.log('Rows selected:', rowsKeys, rowsData);
              }}
              onPaginationChange={onPaginationChange}
              paginationData={paginationData}
              pageSizeOptions={['10', '25', '50', '100']}
              paginationBtnOutside={true}
              paginationBtnText={['Previous']}
              customPaginationClass={'custom-pagination'}
              paginationAlign="end"
              headerClass="device-table-header-row"
              isPagination={USER_LIST?.read ? true : false}
              rowClassName="device-table-content-row"
            />
          </div>
        </Flex>
      </div>
    </Flex>
  );
};

UsersMainView.propTypes = {
  nextView: PropTypes.func.isRequired,
  handleUserMainAction: PropTypes.func.isRequired,
  data: PropTypes.array.isRequired,
  loading: PropTypes.bool.isRequired,
  onPaginationChange: PropTypes.func.isRequired,
  paginationData: PropTypes.object.isRequired,
  onToggleChange: PropTypes.func.isRequired,
  searchText: PropTypes.string.isRequired,
  onSearchChange: PropTypes.func.isRequired,
  selectedFilter: PropTypes.string,
  onFilterChange: PropTypes.func.isRequired,
};

export default UsersMainView;
