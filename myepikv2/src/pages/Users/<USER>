import { useState, useMemo } from 'react';
import { DeleteModal } from '@/components';
import { useListUser } from '@/graphqlHooks/auth/useListUsers';
import { useStore } from '@/store';
import { useDebounce } from '@/hooks';
import UsersMainView from './UsersMainView';
import UpdatePermissions from './UpdatePermissions';
import ApprovelRequests from './ApprovalRequests';
import UpdateUser from './UpdateUser';
import NewUser from './NewUser';
import UpdateApprovalRequest from './UpdateApprovalRequest';
import AddNewPermissionGroup from './AddNewPermissionGroup';
import './styles.css';

const withKeys = (list, keyField = '_id') =>
  list.map((item) => ({
    ...item,
    key: item?.[keyField],
  }));

const Users = () => {
  const [showDetails, setShowDetails] = useState(false);
  const [renderDetails, setRenderDetails] = useState('');
  const [actionModal, setActionModal] = useState('');
  const [searchText, setSearchText] = useState('');
  const [debouncedSearch, setDebouncedSearch] = useState('');
  const [selectedFilter, setSelectedFilter] = useState(null);

  const [paginationData, setPaginationData] = useState({
    page: 1,
    pageSize: 10,
  });

  const { openDrawer, closeDrawer } = useStore((state) => state.drawer);

  const input = useMemo(() => {
    const base = {
      pagination: paginationData,
    };

    if (selectedFilter && debouncedSearch?.trim()) {
      base[selectedFilter] = debouncedSearch.trim();
    } else if (debouncedSearch?.trim()) {
      base.query = debouncedSearch.trim();
    }
    return base;
  }, [paginationData, selectedFilter, debouncedSearch]);

  const { data, isLoading } = useListUser({
    input,
  });

  const debouncedSearchHandler = useDebounce((value) => {
    setDebouncedSearch(value);
  }, 300);

  const totalRecord = data?.ListUsers?.pagination?.count || 0;

  const userList = data?.ListUsers?.docs || [];
  const updatedUserlist = withKeys(userList);

  const onPaginationChange = (page, pageSize) => {
    setPaginationData({ page, pageSize });
  };

  const handleToggleChange = (user, field, value) => {
    console.log('Toggle Changed:', {
      userId: user._id,
      field,
      newValue: value,
    });
  };

  const handleSearchChange = (e) => {
    const value = e.target.value;
    setSearchText(value);
    debouncedSearchHandler(value);
  };

  const handleFilterChange = (e) => {
    setSelectedFilter(e.key);
  };

  const handleBack = () => setShowDetails(false);

  const handleAnimationEnd = () => {
    if (!showDetails) setRenderDetails('');
  };

  const handleNextView = (nextView) => {
    if (nextView === 'addNewUser') {
      handleUserMainAction(nextView);
    } else {
      setRenderDetails(nextView);
      setShowDetails(true);
    }
  };

  const modalConfig = {
    userDelete: {
      component: DeleteModal,
      props: {
        onCancel: () => setActionModal(''),
        handleOk: () => setActionModal(''),
      },
      condition: actionModal === 'delete',
    },
  };

  const handleUserMainAction = (key, user) => {
    setActionModal(key);
    const {
      updateUser,
      newUser,
      updateApprovalRequest,
      addNewPermissionGroup,
    } = drawerConfig;
    switch (key) {
      case 'edit':
        return updateUser(user);
      case 'editApprovalRequest':
        return updateApprovalRequest();
      case 'addNewUser':
        return newUser();
      case 'AddNewPermissionGroup':
        return addNewPermissionGroup();
      default:
        return null;
    }
  };

  const drawerConfig = {
    updateUser: (user) =>
      openDrawer(UpdateUser, {
        key: 'update-user-drawer',
        onCancel: closeDrawer,
        user,
      }),
    newUser: () =>
      openDrawer(NewUser, {
        key: 'new-user-drawer',
        onCancel: closeDrawer,
      }),
    updateApprovalRequest: () => openDrawer(UpdateApprovalRequest, {}),
    addNewPermissionGroup: () => openDrawer(AddNewPermissionGroup, {}),
  };

  const renderModals = () => {
    return Object.entries(modalConfig).map(([key, modal]) => {
      const { component: ModalComponent, props, condition } = modal;
      return condition ? <ModalComponent key={key} {...props} /> : null;
    });
  };

  return (
    <div
      style={{
        position: 'relative',
        width: '100%',
        height: '100%',
      }}
    >
      <UsersMainView
        nextView={handleNextView}
        handleUserMainAction={handleUserMainAction}
        handleAction={handleUserMainAction}
        data={updatedUserlist}
        loading={isLoading}
        paginationData={{
          ...paginationData,
          totalRecord,
        }}
        onPaginationChange={onPaginationChange}
        onToggleChange={handleToggleChange}
        searchText={searchText}
        onSearchChange={handleSearchChange}
        selectedFilter={selectedFilter}
        onFilterChange={handleFilterChange}
      />

      {renderDetails && (
        <div
          className={`sliding-overlay ${showDetails ? 'slide-in' : 'slide-out'}`}
          onAnimationEnd={handleAnimationEnd}
          style={{ height: '100%' }}
        >
          {renderDetails === 'updatePermission' ? (
            <UpdatePermissions
              onBack={handleBack}
              handleAddNewGroup={() => drawerConfig?.addNewPermissionGroup()}
            />
          ) : renderDetails === 'approvedRequest' ? (
            <ApprovelRequests
              onBack={handleBack}
              handleAction={handleUserMainAction}
            />
          ) : null}
        </div>
      )}
      {renderModals()}
    </div>
  );
};

export default Users;
