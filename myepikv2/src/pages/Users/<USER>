import dayjs from 'dayjs';
import {
  CheckPrimaryTickIcon,
  DropDown,
  EditIcon,
  Flex,
  MoreHorizontalIcon,
  StatusTag,
  Toggle,
  Tooltip,
  TrashIcon,
  Typography,
} from '@/components';

const menuItems = [
  { key: 'edit', label: 'Edit', icon: <EditIcon /> },
  { key: 'delete', label: 'Delete', icon: <TrashIcon /> },
];

const menuItemsApprovalReq = [
  { key: 'editApprovalRequest', label: 'Edit', icon: <EditIcon /> },
];

export function getUsersMainTableColumns({
  canUpdate,
  onMenuClick,
  onToggleChange,
}) {
  const handleMenuClick = (e, user) => {
    onMenuClick?.(e?.key, user);
    e?.domEvent?.stopPropagation();
  };

  const renderActionButtons = (user) => (
    <Flex
      align="center"
      style={{ height: '16px' }}
      onClick={(e) => e?.stopPropagation()}
    >
      <DropDown
        menuItems={canUpdate ? menuItems : []}
        onClick={(e) => handleMenuClick(e, user)}
        trigger={['click']}
        dropDownStyles={{ height: 'inherit', display: 'flex' }}
        buttonLabel=""
        buttonProps={{
          onlyIcon: true,
          variant: 'filled',
          icon: <MoreHorizontalIcon width={16} height={16} />,
          type: 'default',
          style: { height: 'inherit', padding: 0 },
        }}
      />
    </Flex>
  );

  return [
    {
      title: <Typography className="small-text">Company</Typography>,
      dataIndex: 'companyDoc',
      key: 'companyDoc',
      render: (record) => {
        const text = record?.name || '--';
        return (
          <Typography
            ellipsis={{
              tooltip: <Tooltip>{text}</Tooltip>,
            }}
            className="small-text"
          >
            {text || '--'}
          </Typography>
        );
      },
    },
    {
      title: <Typography className="small-text">Name</Typography>,
      dataIndex: 'name',
      key: 'name',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text || '--'}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Email</Typography>,
      dataIndex: 'email',
      key: 'email',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text || '--'}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Status</Typography>,
      dataIndex: 'enabled',
      key: 'enabled',
      render: (value, record) => (
        <Toggle
          size="small"
          variant="secondary"
          checked={value}
          onChange={(checked) => onToggleChange(record, 'enabled', checked)}
        />
      ),
    },
    {
      title: <Typography className="small-text">2FA</Typography>,
      dataIndex: 'twoFactor',
      key: 'twoFactor',
      render: (value, record) => (
        <Toggle
          size="small"
          variant="secondary"
          checked={value}
          onChange={(checked) => onToggleChange(record, 'twoFactor', checked)}
        />
      ),
    },
    {
      title: <Typography className="small-text">Created On</Typography>,
      dataIndex: 'registerDate',
      key: 'registerDate',
      render: (text) => {
        const formattedDate = dayjs(text).format('MMM D, YYYY'); // e.g., Aug 2, 2013
        return (
          <Typography
            ellipsis={{
              tooltip: <Tooltip>{formattedDate}</Tooltip>,
            }}
            className="small-text"
          >
            {formattedDate || '--'}
          </Typography>
        );
      },
    },
    {
      title: '',
      key: 'action',
      render: (user) => renderActionButtons(user),
      width: 50,
    },
  ];
}

export function getApprovalRequestsTableColumns({ onMenuClick }) {
  const handleMenuClick = (e, key) => {
    console.log('handleMenuClick', e, e?.target?.value, key);
    onMenuClick?.(e?.key);
    e?.domEvent?.stopPropagation();
  };

  const render3rdParty = (state) => (
    <div
      style={{
        backgroundColor: '#EBEFF6',
        width: '24px',
        height: '24px',
        borderRadius: '10px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      {state && <CheckPrimaryTickIcon />}
    </div>
  );

  const renderActionButtons = () => (
    <Flex
      align="center"
      style={{ height: '16px' }}
      onClick={(e) => e?.stopPropagation()}
    >
      <DropDown
        menuItems={menuItemsApprovalReq}
        onClick={(e) => handleMenuClick(e)}
        trigger={['click']}
        dropDownStyles={{ height: 'inherit', display: 'flex' }}
        buttonLabel=""
        buttonProps={{
          onlyIcon: true,
          icon: <MoreHorizontalIcon width={16} height={16} />,
          type: 'default',
          variant: 'filled',
          style: { height: 'inherit', padding: 0 },
        }}
      />
    </Flex>
  );

  return [
    {
      title: <Typography className="small-text">Name</Typography>,
      dataIndex: 'name',
      key: 'name',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Email</Typography>,
      dataIndex: 'email',
      key: 'email',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Company Name</Typography>,
      dataIndex: 'companyName',
      key: 'companyName',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: (
        <Typography className="small-text">Granite Parent Account</Typography>
      ),
      dataIndex: 'graniteParentAccount',
      key: 'graniteParentAccount',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
      width: 150,
    },
    {
      title: <Typography className="small-text">Rquest Date</Typography>,
      dataIndex: 'requestDate',
      key: 'requestDate',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Approve/Reject</Typography>,
      dataIndex: 'approveReject',
      key: 'approveReject',
      render: (text) => <StatusTag text={text} />,
    },
    {
      title: <Typography className="small-text">Rquest Date</Typography>,
      dataIndex: 'requestDate',
      key: 'requestDate',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">3rd Party</Typography>,
      dataIndex: 'thirdParty',
      key: 'thirdParty',
      render: (text) => render3rdParty(text),
    },
    {
      title: '',
      key: 'action',
      render: renderActionButtons,
      width: 50,
    },
  ];
}

export const approvalRequestsTableDataSource = [
  {
    _id: '1',
    name: 'Eleanor',
    email: '<EMAIL>',
    companyName: 'Epik Company Name',
    graniteParentAccount: '********',
    requestDate: 'Apr 28, 2016',
    approveReject: 'Pending',
    thirdParty: true,
  },
  {
    _id: '2',
    name: 'Eleanor',
    email: '<EMAIL>',
    companyName: 'Epik Company Name',
    graniteParentAccount: '********',
    requestDate: 'Apr 28, 2016',
    approveReject: 'Approved',
    thirdParty: false,
  },
  {
    _id: '3',
    name: 'Eleanor',
    email: '<EMAIL>',
    companyName: 'Epik Company Name',
    graniteParentAccount: '********',
    requestDate: 'Apr 28, 2016',
    approveReject: 'Rejected',
    thirdParty: false,
  },
  {
    _id: '4',
    name: 'Eleanor',
    email: '<EMAIL>',
    companyName: 'Epik Company Name',
    graniteParentAccount: '********',
    requestDate: 'Apr 28, 2016',
    approveReject: 'Approved',
    thirdParty: false,
  },
  {
    _id: '5',
    name: 'Eleanor',
    email: '<EMAIL>',
    companyName: 'Epik Company Name',
    graniteParentAccount: '********',
    requestDate: 'Apr 28, 2016',
    approveReject: 'Approved',
    thirdParty: false,
  },
  {
    _id: '6',
    name: 'Eleanor',
    email: '<EMAIL>',
    companyName: 'Epik Company Name',
    graniteParentAccount: '********',
    requestDate: 'Apr 28, 2016',
    approveReject: 'Approved',
    thirdParty: false,
  },
  {
    _id: '7',
    name: 'Eleanor',
    email: '<EMAIL>',
    companyName: 'Epik Company Name',
    graniteParentAccount: '********',
    requestDate: 'Apr 28, 2016',
    approveReject: 'Approved',
    thirdParty: false,
  },
  {
    _id: '8',
    name: 'Eleanor',
    email: '<EMAIL>',
    companyName: 'Epik Company Name',
    graniteParentAccount: '********',
    requestDate: 'Apr 28, 2016',
    approveReject: 'Approved',
    thirdParty: false,
  },
  {
    _id: '9',
    name: 'Eleanor',
    email: '<EMAIL>',
    companyName: 'Epik Company Name',
    graniteParentAccount: '********',
    requestDate: 'Apr 28, 2016',
    approveReject: 'Approved',
    thirdParty: false,
  },
  {
    _id: '10',
    name: 'Eleanor',
    email: '<EMAIL>',
    companyName: 'Epik Company Name',
    graniteParentAccount: '********',
    requestDate: 'Apr 28, 2016',
    approveReject: 'Approved',
    thirdParty: false,
  },
  {
    _id: '11',
    name: 'Eleanor',
    email: '<EMAIL>',
    companyName: 'Epik Company Name',
    graniteParentAccount: '********',
    requestDate: 'Apr 28, 2016',
    approveReject: 'Approved',
    thirdParty: false,
  },
  {
    _id: '12',
    name: 'Eleanor',
    email: '<EMAIL>',
    companyName: 'Epik Company Name',
    graniteParentAccount: '********',
    requestDate: 'Apr 28, 2016',
    approveReject: 'Approved',
    thirdParty: false,
  },
  {
    _id: '13',
    name: 'Eleanor',
    email: '<EMAIL>',
    companyName: 'Epik Company Name',
    graniteParentAccount: '********',
    requestDate: 'Apr 28, 2016',
    approveReject: 'Approved',
    thirdParty: false,
  },
  {
    _id: '14',
    name: 'Eleanor',
    email: '<EMAIL>',
    companyName: 'Epik Company Name',
    graniteParentAccount: '********',
    requestDate: 'Apr 28, 2016',
    approveReject: 'Approved',
    thirdParty: false,
  },
  {
    _id: '15',
    name: 'Eleanor',
    email: '<EMAIL>',
    companyName: 'Epik Company Name',
    graniteParentAccount: '********',
    requestDate: 'Apr 28, 2016',
    approveReject: 'Approved',
    thirdParty: false,
  },
  {
    _id: '16',
    name: 'Eleanor',
    email: '<EMAIL>',
    companyName: 'Epik Company Name',
    graniteParentAccount: '********',
    requestDate: 'Apr 28, 2016',
    approveReject: 'Approved',
    thirdParty: false,
  },
  {
    _id: '17',
    name: 'Eleanor',
    email: '<EMAIL>',
    companyName: 'Epik Company Name',
    graniteParentAccount: '********',
    requestDate: 'Apr 28, 2016',
    approveReject: 'Approved',
    thirdParty: false,
  },
  {
    _id: '18',
    name: 'Eleanor',
    email: '<EMAIL>',
    companyName: 'Epik Company Name',
    graniteParentAccount: '********',
    requestDate: 'Apr 28, 2016',
    approveReject: 'Approved',
    thirdParty: false,
  },
  {
    _id: '19',
    name: 'Eleanor',
    email: '<EMAIL>',
    companyName: 'Epik Company Name',
    graniteParentAccount: '********',
    requestDate: 'Apr 28, 2016',
    approveReject: 'Approved',
    thirdParty: false,
  },
  {
    _id: '20',
    name: 'Eleanor',
    email: '<EMAIL>',
    companyName: 'Epik Company Name',
    graniteParentAccount: '********',
    requestDate: 'Apr 28, 2016',
    approveReject: 'Approved',
    thirdParty: false,
  },
  {
    _id: '21',
    name: 'Eleanor',
    email: '<EMAIL>',
    companyName: 'Epik Company Name',
    graniteParentAccount: '********',
    requestDate: 'Apr 28, 2016',
    approveReject: 'Approved',
    thirdParty: false,
  },
];
