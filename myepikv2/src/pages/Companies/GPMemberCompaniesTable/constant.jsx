import {
  Flex,
  DropDown,
  Typography,
  MoreHorizontalIcon,
  Tooltip,
} from '@/components';

export function getColumns() {
  const renderActionButtons = () => (
    <Flex
      align="center"
      style={{ height: '16px' }}
      onClick={(e) => e?.stopPropagation()}
    >
      <DropDown
        trigger={['click']}
        dropDownStyles={{ height: 'inherit', display: 'flex' }}
        buttonLabel=""
        buttonProps={{
          onlyIcon: true,
          icon: <MoreHorizontalIcon width={16} height={16} />,
          type: 'default',
          variant: 'filled',
          style: { height: 'inherit', padding: 0 },
        }}
      />
    </Flex>
  );
  return [
    {
      title: (
        <Typography className="text-inter-500-9-13-gray-600">
          Parent Account
        </Typography>
      ),
      dataIndex: 'parentAccount',
      key: 'parentAccount',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="text-inter-500-10-16-gray-900"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: (
        <Typography className="text-inter-500-9-13-gray-600">
          Company
        </Typography>
      ),
      dataIndex: 'company',
      key: 'company',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="text-inter-400-10-16-gray-600"
        >
          {text}
        </Typography>
      ),
    },

    {
      title: (
        <Typography className="text-inter-500-9-13-gray-600">
          Devices Under Management
        </Typography>
      ),
      dataIndex: 'devicesUnderManagement',
      key: 'devicesUnderManagement',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="text-inter-400-10-16-gray-600"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: '',
      key: 'action',
      render: renderActionButtons,
      width: 40,
    },
  ];
}
