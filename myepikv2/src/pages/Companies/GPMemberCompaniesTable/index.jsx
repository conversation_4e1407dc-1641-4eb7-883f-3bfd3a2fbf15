import { useEffect, useState } from 'react';
import { DataTable } from '@/components';
import { getColumns } from './constant';

const GPMemberCompaniesTable = () => {
  const dataSource = [
    {
      key: '1',
      parentAccount: '********',
      company: 'Epik Company Name ',
      devicesUnderManagement: '2',
    },
    {
      key: '2',
      parentAccount: '********',
      company: 'Epik Company Name',
      devicesUnderManagement: '6',
    },
    {
      key: '3',
      parentAccount: '********',
      company: 'Epik Company Name',
      devicesUnderManagement: '7',
    },
    {
      key: '4',
      parentAccount: '********',
      company: 'Epik Company Name',
      devicesUnderManagement: '8',
    },
    {
      key: '5',
      parentAccount: '********',
      company: 'Epik Company Name',
      devicesUnderManagement: '10',
    },
    {
      key: '6',
      parentAccount: '********',
      company: 'Epik Company Name ',
      devicesUnderManagement: '1',
    },
    {
      key: '7',
      parentAccount: '********',
      company: 'Epik Company Name ',
      devicesUnderManagement: '2',
    },
    {
      key: '8',
      parentAccount: '********',
      company: 'Epik Company Name ',
      devicesUnderManagement: '3',
    },
    {
      key: '9',
      parentAccount: '********',
      company: 'Epik Company Name ',
      devicesUnderManagement: '4',
    },
    {
      key: '10',
      parentAccount: '********',
      company: 'Epik Company Name',
      devicesUnderManagement: '5',
    },
  ];

  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, 0);
  }, []);

  const columns = getColumns();

  return (
    <DataTable
      columns={columns}
      data={dataSource}
      loading={loading}
      size="small"
      headerClass="device-table-header-row"
    />
  );
};

export default GPMemberCompaniesTable;
