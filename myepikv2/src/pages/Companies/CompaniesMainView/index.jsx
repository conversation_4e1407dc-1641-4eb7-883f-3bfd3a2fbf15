import {
  AnimatedTab,
  Button,
  CustomDropDown,
  DeleteModal,
  Flex,
  Input,
  PageTitle,
  SearchIcon,
  Typography,
} from '@/components';
import { useListCompanies } from '@/graphqlHooks/auth/useListCompanies';
import { useDebounce } from '@/hooks';
import { useStore } from '@/store';
import PropTypes from 'prop-types';
import { useMemo, useState } from 'react';
import NewCompany from '../NewCompany';
import EditCompany from '../EditCompany';
import NewGPCompanyGroup from '../NewGPCompanyGroup';
import EditGrantParentManagement from '../EditGrandParentManagement';
import CompanyTable from '../CompanyTable';
import GrandParentManagementTable from '../GrandParentManagementTable';

const menuItems = [
  { key: '', label: 'Select Filter' },
  { key: 'epikCustomerId', label: 'Parent Account' },
  { key: 'group', label: 'Group' },
  { key: 'name', label: 'Company Name' },
  { key: 'type', label: 'Type' },
];

const CompaniesMainView = () => {
  const [companyActionModal, setCompanyActionModal] = useState('');
  const [companyTab, setSelectedCompanyTab] = useState('Companies');
  const [paginationData, setPaginationData] = useState({
    page: 1,
    pageSize: 10,
  });
  const [searchText, setSearchText] = useState('');
  const [debouncedSearch, setDebouncedSearch] = useState('');
  const [selectedFilter, setSelectedFilter] = useState(null);

  const { openDrawer } = useStore((state) => state.drawer);

  const input = useMemo(() => {
    const base = {
      pagination: paginationData,
    };

    if (selectedFilter && debouncedSearch?.trim()) {
      base[selectedFilter] = debouncedSearch.trim();
    } else if (debouncedSearch?.trim()) {
      base.query = debouncedSearch.trim();
    }
    return base;
  }, [paginationData, selectedFilter, debouncedSearch]);

  const { data, isLoading } = useListCompanies({
    input,
  });

  const totalRecord = data?.ListCompanies?.pagination?.count || 0;

  const debouncedSearchHandler = useDebounce((value) => {
    setDebouncedSearch(value);
  }, 300);

  const onPaginationChange = (page, pageSize) => {
    setPaginationData({ page, pageSize });
  };

  const handleSearchChange = (e) => {
    const value = e.target.value;
    setSearchText(value);
    debouncedSearchHandler(value);
  };

  const modalConfig = {
    deleteCompany: {
      component: DeleteModal,
      props: {
        onCancel: () => setCompanyActionModal(''),
        handleOk: () => setCompanyActionModal(''),
      },
      condition: companyActionModal === 'deleteCompany',
    },
  };

  const drawerConfig = {
    addCompany: () => openDrawer(NewCompany, {}),
    editCompany: () => openDrawer(EditCompany, {}),
    newGPCompanyGroup: () => openDrawer(NewGPCompanyGroup, {}),
    editGrandParentManagement: () => openDrawer(EditGrantParentManagement, {}),
  };

  const handleActions = (key) => {
    const {
      addCompany,
      editCompany,
      newGPCompanyGroup,
      editGrandParentManagement,
    } = drawerConfig;

    switch (key) {
      case 'newCompany':
        return addCompany();
      case 'editCompany':
        return editCompany();
      case 'newGPCompanyGroup':
        return newGPCompanyGroup();
      case 'editGrandParentManagement':
        return editGrandParentManagement();
      default:
        return null;
    }
  };

  const renderContent = (companyTab) => {
    switch (companyTab) {
      case 'Companies':
        return (
          <CompanyTable
            data={data?.ListCompanies?.docs || []}
            loading={isLoading}
            paginationData={{
              ...paginationData,
              totalRecord,
            }}
            onPaginationChange={onPaginationChange}
            handleAction={handleActions}
          />
        );
      case 'Grandparent Management':
        return <GrandParentManagementTable handleAction={handleActions} />;
      default:
        return null;
    }
  };

  const renderActionButtons = () => {
    switch (companyTab) {
      case 'Companies':
        return (
          <ActionButton
            label={'New Company'}
            action={() => handleActions('newCompany')}
          />
        );
      case 'Grandparent Management':
        return (
          <ActionButton
            label={'New GP Group'}
            action={() => handleActions('newGPCompanyGroup')}
          />
        );
      default:
        return null;
    }
  };

  const renderModals = () => {
    return Object.entries(modalConfig).map(([key, modal]) => {
      const { component: ModalComponent, props, condition } = modal;
      return condition ? <ModalComponent key={key} {...props} /> : null;
    });
  };

  const selectedFilterItem = menuItems?.find(
    (item) => selectedFilter === item?.key,
  );

  return (
    <Flex vertical gap={12} style={{ height: '100%' }}>
      <Flex
        align="center"
        wrap
        justify="space-between"
        style={{ padding: '0 12px' }}
      >
        <PageTitle
          pageTitle={
            companyTab === 'Grandparent Management'
              ? 'Grandparent (GP) Management'
              : companyTab
          }
        />
        <Flex align="center" wrap gap={6}>
          <CustomDropDown
            filterLabel={
              selectedFilter
                ? `By ${selectedFilterItem?.label}`
                : 'Select Filter'
            }
            menuItems={menuItems}
            handleMenuClick={(e) => setSelectedFilter(e?.key)}
          />
          <Input
            prefix={<SearchIcon />}
            placeholder={
              selectedFilter
                ? `Search by ${selectedFilterItem?.label}`
                : 'Search'
            }
            style={{
              maxWidth: '165px',
              paddingTop: '5px',
              paddingBottom: '5px',
            }}
            value={searchText}
            onChange={handleSearchChange}
          />
          {renderActionButtons()}
        </Flex>
      </Flex>
      <div style={{ padding: '0 12px' }} className="custom-scrollbar">
        <Flex
          vertical
          style={{
            background: 'white',
            borderRadius: '12px',
            boxShadow: '0px 1px 2px 0px #1018280F, 0px 1px 3px 0px #1018281A',
          }}
          flex={1}
        >
          <Flex
            align="center"
            justify="space-between"
            style={{ padding: '12px' }}
          >
            <AnimatedTab
              options={['Companies', 'Grandparent Management']}
              value={companyTab}
              onChange={(value) => {
                setSelectedCompanyTab(value);
              }}
              size="default"
            />
          </Flex>
          {renderContent(companyTab)}
          {renderModals()}
        </Flex>
      </div>
    </Flex>
  );
};

export default CompaniesMainView;

const ActionButton = ({ label, action }) => {
  return (
    <Button onClick={() => action(true)}>
      <Typography className="text-inter-600-10-16-white">{label}</Typography>
    </Button>
  );
};

ActionButton.propTypes = {
  label: PropTypes.string.isRequired,
  action: PropTypes.func.isRequired,
};
