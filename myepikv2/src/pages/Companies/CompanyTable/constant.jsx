import dayjs from 'dayjs';
import {
  Flex,
  DropDown,
  Tooltip,
  EditIcon,
  MoreHorizontalIcon,
  TrashIcon,
  Typography,
  StatusTag,
} from '@/components';

export function getColumns({ onMenuClick }) {
  const handleMenuClick = (e, key) => {
    console.log('handleMenuClick', e, e?.target?.value, key);
    onMenuClick?.(e?.key);
    e?.domEvent?.stopPropagation();
  };

  const menuItems = [
    { key: 'editCompany', label: 'Edit', icon: <EditIcon /> },
    { key: 'deleteCompany', label: 'Delete', icon: <TrashIcon /> },
  ];

  const renderActionButtons = () => (
    <Flex
      align="center"
      style={{ height: '16px' }}
      onClick={(e) => e?.stopPropagation()}
    >
      <DropDown
        menuItems={menuItems}
        onClick={(e) => handleMenuClick(e)}
        trigger={['click']}
        dropDownStyles={{ height: 'inherit', display: 'flex' }}
        buttonLabel=""
        buttonProps={{
          onlyIcon: true,
          icon: <MoreHorizontalIcon width={16} height={16} />,
          type: 'default',
          variant: 'filled',
          style: { height: 'inherit', padding: 0 },
        }}
      />
    </Flex>
  );

  return [
    {
      title: (
        <Typography className="text-inter-500-9-13-gray-600">
          Parant Account
        </Typography>
      ),
      dataIndex: 'epikCustomerId',
      key: 'epikCustomerId',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="text-inter-500-10-16-gray-900"
          paragraph
        >
          {text || '--'}
        </Typography>
      ),
    },
    {
      title: (
        <Typography className="text-inter-500-9-13-gray-600">Group</Typography>
      ),
      dataIndex: 'enterpriseNames',
      key: 'enterpriseNames',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="text-inter-400-10-16-gray-600"
        >
          {text || '--'}
        </Typography>
      ),
    },
    {
      title: (
        <Typography className="text-inter-500-9-13-gray-600">Name</Typography>
      ),
      dataIndex: 'name',
      key: 'name',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="text-inter-400-10-16-gray-600"
        >
          {text || '--'}
        </Typography>
      ),
    },
    {
      title: (
        <Typography className="text-inter-500-9-13-gray-600">
          Managed Devices
        </Typography>
      ),
      dataIndex: 'managedDevices',
      key: 'managedDevices',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="text-inter-400-10-16-gray-600"
        >
          {text || '--'}
        </Typography>
      ),
    },
    {
      title: (
        <Typography className="text-inter-500-9-13-gray-600">
          Monitored Devices
        </Typography>
      ),
      dataIndex: 'monitoredDevices',
      key: 'monitoredDevices',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="text-inter-400-10-16-gray-600"
        >
          {text || '--'}
        </Typography>
      ),
    },
    {
      title: (
        <Typography className="text-inter-500-9-13-gray-600">Type</Typography>
      ),
      dataIndex: 'companyType',
      key: 'companyType',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="text-inter-400-10-16-gray-600"
        >
          {text || '--'}
        </Typography>
      ),
    },
    {
      title: (
        <Typography className="text-inter-500-9-13-gray-600">Status</Typography>
      ),
      dataIndex: 'companyState',
      key: 'companyState',
      render: (text) => <StatusTag text={text} />,
    },
    {
      title: (
        <Typography className="text-inter-500-9-13-gray-600">
          Created On
        </Typography>
      ),
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (text) => {
        const formattedDate = dayjs(text).format('MMM D, YYYY');
        return (
          <Typography
            ellipsis={{
              tooltip: <Tooltip>{formattedDate}</Tooltip>,
            }}
            className="text-inter-400-10-16-gray-600"
          >
            {formattedDate || '--'}
          </Typography>
        );
      },
    },
    {
      title: '',
      key: 'action',
      render: renderActionButtons,
      width: 50,
    },
  ];
}
