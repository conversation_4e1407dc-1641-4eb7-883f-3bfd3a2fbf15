import PropTypes from 'prop-types'; // Import PropTypes for validation
import { DataTable } from '@/components';
import { getColumns } from './constant';

const CompanyTable = ({
  data,
  loading,
  paginationData,
  onPaginationChange,
  handleAction,
}) => {
  const handleToggleChange = (checked, record) => {
    console.log(
      `Monitor toggled to ${checked} for device:`,
      record.serialNumber,
    );
  };

  const columns = getColumns({
    onNameClick: (record, index) => console.log('Row clicked:', record, index),
    onMenuClick: (key) => handleAction(key),
    onToggleChange: handleToggleChange,
  });

  return (
    <DataTable
      columns={columns}
      data={data}
      loading={loading}
      onPaginationChange={onPaginationChange}
      paginationData={paginationData}
      pageSizeOptions={['10', '25', '50', '100']}
      paginationBtnOutside={true}
      paginationBtnText={['Previous']}
      customPaginationClass={'custom-pagination'}
      paginationAlign="end"
      headerClass="device-table-header-row"
      isPagination={true}
    />
  );
};

// Add PropTypes validation
CompanyTable.propTypes = {
  handleAction: PropTypes.func.isRequired, // `handleAction` is required and must be a function
  data: PropTypes.array.isRequired,
  loading: PropTypes.bool.isRequired,
  onPaginationChange: PropTypes.func.isRequired,
  paginationData: PropTypes.object.isRequired,
};

export default CompanyTable;
