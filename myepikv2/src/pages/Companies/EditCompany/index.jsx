import React, { useState } from 'react';
import PropTypes from 'prop-types';
import {
  Input,
  Flex,
  Button,
  Typography,
  DropDown,
  Tag,
  DownIcon,
  Toggle,
  CloseTwoIcon,
  CloseIcon,
} from '@/components';
import { Divider, Row, Col } from 'antd';
import { useStore } from '@/store';
import './styles.css';

const menuItems = [
  { key: 'unitesStats', label: 'United Stats' },
  { key: 'germany', label: 'Germany' },
];

const Field = ({ label, component, bottomLabel }) => (
  <Flex vertical gap={8}>
    <Typography className="text-inter-500-9-13-gray-700">{label}</Typography>
    {component}
    {bottomLabel && (
      <Typography className="extra-small-text">{bottomLabel}</Typography>
    )}
  </Flex>
);

// Add PropTypes for Field Component
Field.propTypes = {
  label: PropTypes.string.isRequired, // `label` is a required string
  component: PropTypes.node.isRequired, // `component` is a required React node
  bottomLabel: PropTypes.string.isRequired, // `bottomlabel` is a required string
};

const EditCompany = () => {
  const [formValues, setFormValues] = useState({});
  const [emailList, setEmailList] = useState([
    { email: '<EMAIL>' },
    { email: '<EMAIL>' },
  ]);
  const [secondaryNotification, setSecondaryNotification] = useState(false);

  const { closeDrawer } = useStore((state) => state.drawer);

  const handleChange = (field, value) => {
    setFormValues((prev) => ({ ...prev, [field]: value }));
  };

  const handleKeyDown = (field, value, event) => {
    if (event.key === 'Enter' && value.trim()) {
      if (field === 'alertEmail') {
        setEmailList((prev) => [...prev, value]);
        handleChange(field, ''); // Clear input
      }
    }
  };

  const sections = [
    {
      title: '',
      fields: [
        {
          name: 'graniteParentAccount',
          label: 'Granite Parent Account',
          placeholder: 'Please Specify',
          span: 12,
        },
        {
          name: 'companyName',
          label: 'Company Name',
          placeholder: 'Please Specify',
          span: 12,
        },
        {
          name: 'country',
          label: 'Country',
          placeholder: 'Active',
          component: (
            <DropDown
              menuItems={menuItems}
              buttonLabel="Country"
              trigger={['click']}
              icon={<DownIcon />}
              buttonProps={{
                variant: 'outlined',
                variantColorBgContainer: true,
                style: {
                  border: 'none',
                  width: '100%',
                  display: 'flex',
                  justifyContent: 'space-between',
                  background: 'var(--granite-blue-light-active)',
                },
              }}
            />
          ),
          span: 12,
        },
        {
          name: 'city',
          label: 'City',
          placeholder: 'Please Specify',
          span: 12,
        },
        {
          name: 'address1',
          label: 'Address 1',
          placeholder: 'Please Specify',
          span: 12,
        },
        {
          name: 'state',
          label: 'State',
          placeholder: 'Please Specify',
          span: 12,
        },
        {
          name: 'address2',
          label: 'Address 2',
          placeholder: 'Please Specify',
          span: 12,
        },
        {
          name: 'zipCode',
          label: 'ZIP Code',
          placeholder: 'Please Specify',
          span: 12,
        },
      ],
    },
    {
      showDivider: true,
      fields: [
        {
          name: 'vertical',
          label: 'Vertical',
          component: (
            <Flex
              gap={2}
              style={{
                border: '1px solid #d9d9d9',
                padding: '4px 11px',
                borderRadius: '6px',
              }}
            >
              <Tag
                text="Label 1"
                closable
                closeIcon={
                  <CloseIcon stroke={'#3B5CA9'} height={12} width={12} />
                }
              />
              <Tag
                text="Label 2"
                closable
                closeIcon={
                  <CloseIcon stroke={'#3B5CA9'} height={12} width={12} />
                }
              />
            </Flex>
          ),
          span: 12,
        },
        {
          name: 'contactPersonName',
          label: 'Contact Person Name',
          placeholder: 'Please Specify',
          span: 12,
        },
        {
          name: 'phoneNumber',
          label: 'Phone Number',
          placeholder: 'Please Specify',
          span: 12,
        },
        {
          name: 'email',
          label: 'Email',
          placeholder: 'Please Specify',
          span: 12,
        },
        {
          name: 'alertContacts',
          label: 'Alert Contacts',
          placeholder: 'Please Specify',
          bottomLabel: 'Type a value and press enter to added to the listy',
          span: 12,
        },
        {
          name: 'alertContactList',
          label: 'Alert Contact List',
          component: (
            <Flex
              vertical
              style={{
                background: '#F2F4F7',
                borderRadius: '8px',
                padding: '10px 14px 16px 14px',
              }}
              justify="center"
            >
              {emailList.map((item, idx) => (
                <React.Fragment key={`${item.email}-${idx}`}>
                  <Flex
                    justify="space-between"
                    align="center"
                    style={{ width: '100%' }}
                  >
                    <Typography className="text-inter-500-10-16-gray-500">
                      {item.email}
                    </Typography>
                    <CloseTwoIcon
                      style={{ cursor: 'pointer' }}
                      onClick={() => {
                        setEmailList((prev) =>
                          prev.filter(
                            (emailItem) => emailItem.email !== item.email,
                          ),
                        );
                      }}
                    />
                  </Flex>
                  <Divider className="divider-sm" />
                </React.Fragment>
              ))}
            </Flex>
          ),
          span: 12,
        },
        {
          name: 'groupManagement',
          label: 'Group Management:',
          placeholder: 'EPIK Lab, EPIK Demo Group, EPIK Retail',
          value: 'EPIK Lab, EPIK Demo Group, EPIK Retail',
          span: 12,
        },
      ],
    },
    {
      fields: [
        {
          name: 'secondaryNotification1',
          span: 6,
          component: (
            <>
              <Flex gap={16} justify="space-between">
                <Typography className="text-inter-500-10-16-gray-700">
                  Status
                </Typography>
                <Toggle
                  size="small"
                  checked={secondaryNotification}
                  onChange={() =>
                    setSecondaryNotification(!secondaryNotification)
                  }
                />
              </Flex>
              <Flex gap={16} justify="space-between">
                <Typography className="text-inter-500-10-16-gray-700">
                  Email Notification
                </Typography>
                <Toggle
                  size="small"
                  checked={secondaryNotification}
                  onChange={() =>
                    setSecondaryNotification(!secondaryNotification)
                  }
                />
              </Flex>
              <Flex gap={16} justify="space-between">
                <Typography className="text-inter-500-10-16-gray-700">
                  Enforce 2FA
                </Typography>
                <Toggle
                  size="small"
                  checked={secondaryNotification}
                  onChange={() =>
                    setSecondaryNotification(!secondaryNotification)
                  }
                />
              </Flex>
            </>
          ),
        },
        {
          name: 'secondaryNotification2',
          span: 6,
          component: (
            <>
              <Flex gap={16} justify="space-between">
                <Typography className="text-inter-500-10-16-gray-700">
                  Fax Download Option
                </Typography>
                <Toggle
                  size="small"
                  checked={secondaryNotification}
                  onChange={() =>
                    setSecondaryNotification(!secondaryNotification)
                  }
                />
              </Flex>
              <Flex gap={16} justify="space-between">
                <Typography className="text-inter-500-10-16-gray-700">
                  Show Call Recording
                </Typography>
                <Toggle
                  size="small"
                  checked={secondaryNotification}
                  onChange={() =>
                    setSecondaryNotification(!secondaryNotification)
                  }
                />
              </Flex>
            </>
          ),
        },
      ],
    },
  ];

  return (
    <Flex vertical>
      <Flex vertical>
        <Flex justify="space-between" align="center">
          <Typography className="text-inter-600-13-20-gray-900">
            Edit Company
          </Typography>
          <CloseIcon onClick={closeDrawer} style={{ cursor: 'pointer' }} />
        </Flex>
        <Divider className="divider-sm" />
      </Flex>
      {sections.map((section, sectionIndex) => (
        <React.Fragment key={`section-${sectionIndex}`}>
          {(section?.title || section?.showDivider) && (
            <Flex vertical style={{ marginTop: '24px' }}>
              <Typography className="heading-five">{section.title}</Typography>
              <Divider className="divider-sm" />
            </Flex>
          )}
          <Row gutter={[8, 16]} style={{ marginTop: '16px' }}>
            {section.fields.map((field, fieldIndex) => (
              <Col
                key={`field-${sectionIndex}-${fieldIndex}`}
                span={field.span}
                xs={24}
                sm={12}
                md={12}
                lg={field.span}
                xl={field.span}
                xxl={field.span}
              >
                {Field({
                  label: field.label,
                  component: field.component || (
                    <Input
                      placeholder={field.placeholder}
                      value={formValues[field.name]}
                      onChange={(e) => handleChange(field.name, e.target.value)}
                      onKeyDown={(e) =>
                        field.list &&
                        e.key === 'Enter' &&
                        handleKeyDown(field.name, formValues[field.name], e)
                      }
                      style={{
                        background: `${field.name === 'groupManagement' ? 'var(--granite-blue-light-active)' : null}`,
                      }}
                      className="text-medium-regular"
                    />
                  ),
                  bottomLabel: field?.bottomLabel,
                })}
              </Col>
            ))}
          </Row>
        </React.Fragment>
      ))}
      <Flex vertical justify="end" gap={8}>
        <Divider className="divider-sm" />
        <Flex gap={4} justify="flex-end">
          <Button
            variant="outlined"
            onClick={closeDrawer}
            style={{ width: '20%' }}
          >
            <Typography className="text-inter-600-10-16-gray-700">
              Cancel
            </Typography>
          </Button>
          <Button
            onClick={() => console.log('Save Form:', formValues)}
            style={{ width: '20%' }}
          >
            <Typography className="text-inter-600-10-16-white">
              Submit
            </Typography>
          </Button>
        </Flex>
      </Flex>
    </Flex>
  );
};

export default EditCompany;
