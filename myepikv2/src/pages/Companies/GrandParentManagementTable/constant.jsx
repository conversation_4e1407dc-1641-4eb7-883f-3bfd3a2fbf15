import {
  Flex,
  Typography,
  DropDown,
  EditIcon,
  MoreHorizontalIcon,
  TrashIcon,
  Tooltip,
} from '@/components';

export function getColumns({ onMenuClick }) {
  const handleMenuClick = (e, key) => {
    console.log('handleMenuClick', e, e?.target?.value, key);
    onMenuClick?.(e?.key);
    e?.domEvent?.stopPropagation();
  };

  const menuItems = [
    { key: 'editGrandParentManagement', label: 'Edit', icon: <EditIcon /> },
    {
      key: 'deleteGrantParentManagement',
      label: 'Delete',
      icon: <TrashIcon />,
    },
  ];

  const renderActionButtons = () => (
    <Flex
      align="center"
      style={{ height: '16px' }}
      onClick={(e) => e?.stopPropagation()}
    >
      <DropDown
        menuItems={menuItems}
        onClick={(e) => handleMenuClick(e)}
        trigger={['click']}
        dropDownStyles={{ height: 'inherit', display: 'flex' }}
        buttonLabel=""
        buttonProps={{
          onlyIcon: true,
          icon: <MoreHorizontalIcon width={16} height={16} />,
          type: 'default',
          variant: 'filled',
          style: { height: 'inherit', padding: 0 },
        }}
      />
    </Flex>
  );

  return [
    {
      title: <Typography className="small-text">Group Name</Typography>,
      dataIndex: 'groupName',
      key: 'groupName',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Parent Account</Typography>,
      dataIndex: 'parentAccount',
      key: 'parentAccount',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Management Company</Typography>,
      dataIndex: 'managementCompany',
      key: 'managementCompany',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Group Users</Typography>,
      dataIndex: 'groupUsers',
      key: 'groupUsers',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Managed Companies</Typography>,
      dataIndex: 'managedCompanies',
      key: 'managedCompanies',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Managed Devices</Typography>,
      dataIndex: 'managedDevices',
      key: 'managedDevices',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Monitor Devices</Typography>,
      dataIndex: 'monitorDevices',
      key: 'monitorDevices',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },

    {
      title: '',
      key: 'action',
      render: renderActionButtons,
      width: 60,
    },
  ];
}
