import { useEffect, useState } from 'react';
import PropTypes from 'prop-types'; // Import PropTypes for validation
import { DataTable } from '@/components';
import { getColumns } from './constant';

const dataSource = [
  {
    _id: '1',
    groupName: 'Group 1',
    parentAccount: '6051',
    managementCompany: 'Company 1',
    groupUsers: '501',
    managedDevices: '2',
    monitorDevices: '2',
    managedCompanies: '2',
  },
  {
    _id: '2',
    groupName: 'Group 2',
    parentAccount: '6051',
    managementCompany: 'Company 2',
    groupUsers: '502',
    managedDevices: '3',
    monitorDevices: '3',
    managedCompanies: '3',
  },
  {
    _id: '3',
    groupName: 'Group 3',
    parentAccount: '6052',
    managementCompany: 'Company 3',
    groupUsers: '503',
    managedDevices: '4',
    monitorDevices: '4',
    managedCompanies: '4',
  },
  {
    _id: '4',
    groupName: 'Group 4',
    parentAccount: '6051',
    managementCompany: 'Company 4',
    groupUsers: '504',
    managedDevices: '5',
    monitorDevices: '5',
    managedCompanies: '5',
  },
  {
    _id: '5',
    groupName: 'Group 5',
    parentAccount: '6051',
    managementCompany: 'Company 5',
    groupUsers: '505',
    managedDevices: '6',
    monitorDevices: '1',
    managedCompanies: '6',
  },
  {
    _id: '6',
    groupName: 'Group 6',
    parentAccount: '6056',
    managementCompany: 'Company 6',
    groupUsers: '506',
    managedDevices: '7',
    monitorDevices: '2',
    managedCompanies: '7',
  },
  {
    _id: '7',
    groupName: 'Group 7',
    parentAccount: '6056',
    managementCompany: 'Company 7',
    groupUsers: '507',
    managedDevices: '8',
    monitorDevices: '3',
    managedCompanies: '1',
  },
  {
    _id: '8',
    groupName: 'Group 8',
    parentAccount: '6057',
    managementCompany: 'Company 8',
    groupUsers: '508',
    managedDevices: '9',
    monitorDevices: '4',
    managedCompanies: '2',
  },
  {
    _id: '9',
    groupName: 'Group 9',
    parentAccount: '6058',
    managementCompany: 'Company 9',
    groupUsers: '509',
    managedDevices: '10',
    monitorDevices: '5',
    managedCompanies: '3',
  },
  {
    _id: '10',
    groupName: 'Group 10',
    parentAccount: '6056',
    managementCompany: 'Company 10',
    groupUsers: '510',
    managedDevices: '1',
    monitorDevices: '1',
    managedCompanies: '4',
  },
  {
    _id: '11',
    groupName: 'Group 11',
    parentAccount: '6056',
    managementCompany: 'Company 11',
    groupUsers: '511',
    managedDevices: '2',
    monitorDevices: '2',
    managedCompanies: '5',
  },
  {
    _id: '12',
    groupName: 'Group 12',
    parentAccount: '6056',
    managementCompany: 'Company 12',
    groupUsers: '512',
    managedDevices: '3',
    monitorDevices: '3',
    managedCompanies: '6',
  },
  {
    _id: '13',
    groupName: 'Group 13',
    parentAccount: '6056',
    managementCompany: 'Company 13',
    groupUsers: '513',
    managedDevices: '4',
    monitorDevices: '4',
    managedCompanies: '7',
  },
  {
    _id: '14',
    groupName: 'Group 14',
    parentAccount: '6056',
    managementCompany: 'Company 14',
    groupUsers: '514',
    managedDevices: '5',
    monitorDevices: '5',
    managedCompanies: '1',
  },
  {
    _id: '15',
    groupName: 'Group 15',
    parentAccount: '6056',
    managementCompany: 'Company 15',
    groupUsers: '515',
    managedDevices: '6',
    monitorDevices: '1',
    managedCompanies: '2',
  },
  {
    _id: '16',
    groupName: 'Group 16',
    parentAccount: '6056',
    managementCompany: 'Company 16',
    groupUsers: '516',
    managedDevices: '7',
    monitorDevices: '2',
    managedCompanies: '3',
  },
  {
    _id: '17',
    groupName: 'Group 17',
    parentAccount: '6056',
    managementCompany: 'Company 17',
    groupUsers: '517',
    managedDevices: '8',
    monitorDevices: '3',
    managedCompanies: '4',
  },
  {
    _id: '18',
    groupName: 'Group 18',
    parentAccount: '6056',
    managementCompany: 'Company 18',
    groupUsers: '518',
    managedDevices: '9',
    monitorDevices: '4',
    managedCompanies: '5',
  },
  {
    _id: '19',
    groupName: 'Group 19',
    parentAccount: '6056',
    managementCompany: 'Company 19',
    groupUsers: '519',
    managedDevices: '10',
    monitorDevices: '5',
    managedCompanies: '6',
  },
  {
    _id: '20',
    groupName: 'Group 20',
    parentAccount: '6056',
    managementCompany: 'Company 20',
    groupUsers: '520',
    managedDevices: '1',
    monitorDevices: '1',
    managedCompanies: '7',
  },
  {
    _id: '21',
    groupName: 'Group 21',
    parentAccount: '6056',
    managementCompany: 'Company 21',
    groupUsers: '521',
    managedDevices: '2',
    monitorDevices: '2',
    managedCompanies: '1',
  },
  {
    _id: '22',
    groupName: 'Group 22',
    parentAccount: '6056',
    managementCompany: 'Company 22',
    groupUsers: '522',
    managedDevices: '3',
    monitorDevices: '3',
    managedCompanies: '2',
  },
  {
    _id: '23',
    groupName: 'Group 23',
    parentAccount: '6056',
    managementCompany: 'Company 23',
    groupUsers: '523',
    managedDevices: '4',
    monitorDevices: '4',
    managedCompanies: '3',
  },
  {
    _id: '24',
    groupName: 'Group 24',
    parentAccount: '6056',
    managementCompany: 'Company 24',
    groupUsers: '524',
    managedDevices: '5',
    monitorDevices: '5',
    managedCompanies: '4',
  },
  {
    _id: '25',
    groupName: 'Group 25',
    parentAccount: '6056',
    managementCompany: 'Company 25',
    groupUsers: '525',
    managedDevices: '6',
    monitorDevices: '1',
    managedCompanies: '5',
  },
  {
    _id: '26',
    groupName: 'Group 26',
    parentAccount: '6056',
    managementCompany: 'Company 26',
    groupUsers: '526',
    managedDevices: '7',
    monitorDevices: '2',
    managedCompanies: '6',
  },
  {
    _id: '27',
    groupName: 'Group 27',
    parentAccount: '6056',
    managementCompany: 'Company 27',
    groupUsers: '527',
    managedDevices: '8',
    monitorDevices: '3',
    managedCompanies: '7',
  },
  {
    _id: '28',
    groupName: 'Group 28',
    parentAccount: '6056',
    managementCompany: 'Company 28',
    groupUsers: '528',
    managedDevices: '9',
    monitorDevices: '4',
    managedCompanies: '1',
  },
  {
    _id: '29',
    groupName: 'Group 29',
    parentAccount: '6056',
    managementCompany: 'Company 29',
    groupUsers: '529',
    managedDevices: '10',
    monitorDevices: '5',
    managedCompanies: '2',
  },
  {
    _id: '30',
    groupName: 'Group 30',
    parentAccount: '6056',
    managementCompany: 'Company 30',
    groupUsers: '530',
    managedDevices: '1',
    monitorDevices: '1',
    managedCompanies: '3',
  },
];

const GrandParentManagementTable = ({ handleAction }) => {
  const [loading, setLoading] = useState(true);
  const [paginationData, setPaginationData] = useState({
    page: 1,
    totalRecord: dataSource.length,
    pageSize: 10,
  });
  const [data, setData] = useState([]);

  const onPaginationChange = (page, pageSize) => {
    setPaginationData((prev) => ({
      ...prev,
      page,
      pageSize,
    }));
  };

  const handleToggleChange = (checked, record) => {
    console.log(
      `Monitor toggled to ${checked} for device:`,
      record.serialNumber,
    );
  };

  const getPaginatedData = () => {
    const { page, pageSize } = paginationData;
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return dataSource.slice(startIndex, endIndex);
  };

  useEffect(() => {
    setLoading(true);
    const paginatedData = getPaginatedData();
    setTimeout(() => {
      setLoading(false);
      setData(paginatedData);
    }, 0);
  }, [paginationData]);

  const columns = getColumns({
    onNameClick: (record, index) => console.log('Row clicked:', record, index),
    onMenuClick: (key) => handleAction(key),
    onToggleChange: handleToggleChange,
  });

  return (
    <DataTable
      columns={columns}
      data={data}
      loading={loading}
      onPaginationChange={onPaginationChange}
      paginationData={paginationData}
      pageSizeOptions={['10', '25', '50', '100']}
      paginationBtnOutside={true}
      paginationBtnText={['Previous']}
      customPaginationClass={'custom-pagination'}
      paginationAlign="end"
      headerClass="device-table-header-row"
      isPagination={true}
    />
  );
};

// Add PropTypes validation
GrandParentManagementTable.propTypes = {
  handleAction: PropTypes.func.isRequired, // `handleAction` is required and must be a function
};

export default GrandParentManagementTable;
