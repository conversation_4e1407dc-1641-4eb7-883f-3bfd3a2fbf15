import { useEffect, useState } from 'react';
import { DataTable } from '@/components';
import { getColumns } from './constant';

const GroupAdminTable = () => {
  const dataSource = [
    {
      key: '1',
      company: 'Epik Company Name ',
      name: 'Demo Grandparent Name',
      email: '<EMAIL>',
    },
    {
      key: '2',
      company: 'Epik Company Name ',
      name: 'Demo Grandparent Name',
      email: '<EMAIL>',
    },
    {
      key: '3',
      company: 'Epik Company Name ',
      name: 'Demo Grandparent Name',
      email: '<EMAIL>',
    },
    {
      key: '4',
      company: 'Epik Company Name ',
      name: 'Demo Grandparent Name',
      email: '<EMAIL>',
    },
    {
      key: '5',
      company: 'Epik Company Name ',
      name: 'Demo Grandparent Name',
      email: '<EMAIL>',
    },
    {
      key: '6',
      company: 'Epik Company Name ',
      name: 'Demo Grandparent Name',
      email: '<EMAIL>',
    },
    {
      key: '7',
      company: 'Epik Company Name ',
      name: 'Demo Grandparent Name',
      email: '<EMAIL>',
    },
    {
      key: '8',
      company: 'Epik Company Name ',
      name: 'Demo Grandparent Name',
      email: '<EMAIL>',
    },
    {
      key: '9',
      company: 'Epik Company Name ',
      name: 'Demo Grandparent Name',
      email: '<EMAIL>',
    },
    {
      key: '10',
      company: 'Epik Company Name ',
      name: 'Demo Grandparent Name',
      email: '<EMAIL>',
    },
  ];

  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, 0);
  }, []);

  const columns = getColumns();

  return (
    <DataTable
      columns={columns}
      data={dataSource}
      loading={loading}
      size="small"
      headerClass="device-table-header-row"
    />
  );
};

export default GroupAdminTable;
