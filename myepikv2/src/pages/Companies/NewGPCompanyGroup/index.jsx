import PropTypes from 'prop-types'; // Import PropTypes for prop validation
import { Button, Flex, Input, Typography, CloseIcon } from '@/components';
import { useStore } from '@/store';
import { Divider, Row, Col } from 'antd';

const Field = ({ label, component }) => (
  <Flex vertical gap={8}>
    <Typography className="text-inter-500-9-13-gray-700">{label}</Typography>
    {component}
  </Flex>
);

// Add PropTypes for Field Component
Field.propTypes = {
  label: PropTypes.string.isRequired, // `label` is a required string
  component: PropTypes.node.isRequired, // `component` is a required React node
};

const NewGPCompanyGroup = () => {
  const { closeDrawer } = useStore((state) => state.drawer);
  return (
    <Flex vertical>
      <Flex vertical>
        <Flex justify="space-between" align="center">
          <Typography className="text-inter-600-13-20-gray-900">
            Add New Enterprise Group
          </Typography>
          <CloseIcon onClick={closeDrawer} style={{ cursor: 'pointer' }} />
        </Flex>
        <Divider className="divider-sm" />
      </Flex>
      <Row gutter={[8, 16]} style={{ marginTop: '16px' }}>
        <Col
          key={`field-group-name`}
          span={12}
          xs={24}
          sm={12}
          md={12}
          lg={8}
          xl={8}
          xxl={8}
        >
          {Field({
            label: 'Group Name',
            component: (
              <Input
                placeholder={'Specify Group Name'}
                value={'EPIK Lab'}
                disabled={false}
                className="text-medium-regular"
              />
            ),
          })}
        </Col>
        <Col
          key={`field-management-company`}
          span={12}
          xs={24}
          sm={12}
          md={12}
          lg={16}
          xl={16}
          xxl={16}
        >
          {Field({
            label: 'Management Company',
            component: (
              <Input
                placeholder={'Filter Company'}
                value={''}
                disabled={false}
                className="text-medium-regular"
              />
            ),
          })}
        </Col>
      </Row>
      <Flex vertical justify="flex-end" gap={8} style={{ marginTop: 16 }}>
        <Divider className="divider-sm" />
        <Flex gap={4} justify="flex-end">
          <Button
            variant="outlined"
            onClick={closeDrawer}
            style={{ width: '20%' }}
          >
            <Typography className="text-inter-600-10-16-gray-700">
              Cancel
            </Typography>
          </Button>
          <Button
            onClick={() => console.log('Save Form:')}
            style={{ width: '20%' }}
          >
            <Typography className="text-inter-600-10-16-white">
              Submit
            </Typography>
          </Button>
        </Flex>
      </Flex>
    </Flex>
  );
};

export default NewGPCompanyGroup;
