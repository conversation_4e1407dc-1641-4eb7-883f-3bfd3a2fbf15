import { createContext, useContext, useState, useMemo } from 'react';
import PropTypes from 'prop-types';

const CompaniesContext = createContext(null);

export const CompaniesProvider = ({ children }) => {
  const [modals, setModals] = useState({});

  const openModal = (key, config = {}) => {
    setModals((prev) => ({ ...prev, [key]: { open: true, ...config } }));
  };

  const closeModal = (key) => {
    setModals((prev) => ({ ...prev, [key]: { ...prev[key], open: false } }));
  };

  const contextValue = useMemo(
    () => ({
      modals,
      openModal,
      closeModal,
    }),
    [modals],
  );

  return (
    <CompaniesContext.Provider value={contextValue}>
      {children}
    </CompaniesContext.Provider>
  );
};

CompaniesProvider.propTypes = {
  children: PropTypes.node.isRequired,
};

export const useCompaniesContext = () => {
  const context = useContext(CompaniesContext);
  if (!context)
    throw new Error(
      'useCompaniesContext must be used within CompaniesProvider',
    );
  return context;
};
