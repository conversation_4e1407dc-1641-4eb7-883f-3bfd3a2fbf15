import {
  AnimatedTab,
  Button,
  CloseIcon,
  Flex,
  Input,
  SearchIcon,
  Typography,
} from '@/components';
import { useStore } from '@/store';
import { Col, Divider, Row } from 'antd';
import PropTypes from 'prop-types'; // Import PropTypes for prop validation
import { useState } from 'react';
import GroupAdminTable from '../GPAdminTable';
import GPMemberCompaniesTable from '../GPMemberCompaniesTable';

const Field = ({ label, component }) => (
  <Flex vertical gap={8}>
    <Typography className="text-inter-500-9-13-gray-700">{label}</Typography>
    {component}
  </Flex>
);

// Add PropTypes for Field Component
Field.propTypes = {
  label: PropTypes.string.isRequired, // `label` is a required string
  component: PropTypes.node.isRequired, // `component` is a required React node
};

const groupManagementTabs = [
  { label: 'Main', value: 'main' },
  { label: 'Member Companies', value: 'memberCompanies' },
  { label: 'GP Admin', value: 'gpAdmin' },
];

const EditGrantParentManagement = () => {
  const [selectedTab, setSelectedTab] = useState('main');
  const { closeDrawer } = useStore((state) => state.drawer);

  const onChangeTab = (value) => {
    setSelectedTab(value);
  };

  return (
    <Flex vertical>
      <Flex vertical>
        <Flex justify="space-between" align="center">
          <Typography className="text-inter-600-13-20-gray-900">
            GP Management
          </Typography>
          <CloseIcon onClick={closeDrawer} style={{ cursor: 'pointer' }} />
        </Flex>
        <Divider className="divider-sm" />
      </Flex>
      <Flex
        justify="space-between"
        align="center"
        style={{ margin: '16px 0px' }}
      >
        <AnimatedTab
          value={selectedTab}
          onChange={onChangeTab}
          options={groupManagementTabs}
          size="large"
        />

        {(selectedTab === 'memberCompanies' || selectedTab === 'gpAdmin') && (
          <Flex align="center" gap={2}>
            <Input
              prefix={<SearchIcon />}
              placeholder="Search"
              style={{
                maxWidth: '165px',
                paddingTop: '5px',
                paddingBottom: '5px',
              }}
            />
            {selectedTab === 'memberCompanies' ? (
              <Button>Add Companies</Button>
            ) : (
              selectedTab === 'gpAdmin' && <Button>Add Users</Button>
            )}
          </Flex>
        )}
      </Flex>
      {selectedTab === 'main' ? (
        <Row gutter={[8, 16]} style={{ marginTop: '16px' }}>
          <Col
            key={`gp-group-name`}
            span={12}
            xs={24}
            sm={12}
            md={12}
            lg={8}
            xl={8}
            xxl={8}
          >
            {Field({
              label: 'GP Group Name',
              component: (
                <Input
                  placeholder={'Specify GP Group Name'}
                  value={'EPIK Lab'}
                  disabled={false}
                  className="text-medium-regular"
                />
              ),
            })}
          </Col>
          <Col
            key={`gb-management-company`}
            span={12}
            xs={24}
            sm={12}
            md={12}
            lg={16}
            xl={16}
            xxl={16}
          >
            {Field({
              label: 'GP Management Company',
              component: (
                <Input
                  placeholder={'Specify GP Management Company'}
                  value={'EPIK'}
                  disabled={false}
                  className="text-medium-regular"
                />
              ),
            })}
          </Col>
        </Row>
      ) : selectedTab === 'memberCompanies' ? (
        <GPMemberCompaniesTable />
      ) : selectedTab === 'gpAdmin' ? (
        <GroupAdminTable />
      ) : null}
      <Flex vertical justify="end" gap={8} style={{ marginTop: 16 }}>
        <Divider className="divider-sm" />
        <Flex gap={4} justify="end">
          <Button
            variant="outlined"
            onClick={closeDrawer}
            style={{ width: '20%' }}
          >
            <Typography className="text-inter-600-10-16-gray-700">
              Cancel
            </Typography>
          </Button>
          <Button
            onClick={() => console.log('Save Form:')}
            style={{ width: '20%' }}
          >
            <Typography className="text-inter-600-10-16-white">
              Submit
            </Typography>
          </Button>
        </Flex>
      </Flex>
    </Flex>
  );
};

export default EditGrantParentManagement;
