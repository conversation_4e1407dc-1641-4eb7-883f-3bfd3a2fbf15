import PropTypes from 'prop-types';
import { Tabs } from 'antd';
import {
  BadgeExamples,
  ButtonExamples,
  InputExamples,
  DropDownExamples,
  ToggleExamples,
  CheckboxExamples,
  TooltipExamples,
  TagExamples,
  ProgressExamples,
  RadioExamples,
  SelectExamples,
  AlertExamples,
  ModalExample,
  ChartExamples,
  SliderExample,
  TypographyExamples,
  FlexExamples,
  TableExamples,
  AnimatedTabsExample,
} from '@/components';

const CenteredContainer = ({ children }) => (
  <div
    style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      minHeight: '80vh',
    }}
  >
    {children}
  </div>
);

// Add PropTypes for CenteredContainer
CenteredContainer.propTypes = {
  children: PropTypes.node.isRequired, // children must be a React node and is required
};

const SharedComponents = () => {
  const items = [
    {
      key: '1',
      label: 'Datatable Examples',
      children: <TableExamples />,
    },
    {
      key: '2',
      label: 'Button Examples',
      children: (
        <CenteredContainer>
          <ButtonExamples />
        </CenteredContainer>
      ),
    },
    {
      key: '3',
      label: 'Input Examples',
      children: (
        <CenteredContainer>
          <InputExamples />
        </CenteredContainer>
      ),
    },
    {
      key: '4',
      label: 'Dropdown Examples',
      children: (
        <CenteredContainer>
          <DropDownExamples />
        </CenteredContainer>
      ),
    },
    {
      key: '5',
      label: 'Toggle Examples',
      children: (
        <CenteredContainer>
          <ToggleExamples />
        </CenteredContainer>
      ),
    },
    {
      key: '6',
      label: 'Checkbox Examples',
      children: (
        <CenteredContainer>
          <CheckboxExamples />
        </CenteredContainer>
      ),
    },
    {
      key: '7',
      label: 'Tooltip Examples',
      children: (
        <CenteredContainer>
          <TooltipExamples />
        </CenteredContainer>
      ),
    },
    {
      key: '8',
      label: 'Tag Examples',
      children: (
        <CenteredContainer>
          <TagExamples />
        </CenteredContainer>
      ),
    },
    {
      key: '9',
      label: 'Progress Examples',
      children: (
        <CenteredContainer>
          <ProgressExamples />
        </CenteredContainer>
      ),
    },
    {
      key: '10',
      label: 'Radio Examples',
      children: (
        <CenteredContainer>
          <RadioExamples />
        </CenteredContainer>
      ),
    },
    {
      key: '11',
      label: 'Select Examples',
      children: (
        <CenteredContainer>
          <SelectExamples />
        </CenteredContainer>
      ),
    },
    {
      key: '12',
      label: 'Alert Examples',
      children: (
        <CenteredContainer>
          <AlertExamples />
        </CenteredContainer>
      ),
    },
    {
      key: '13',
      label: 'Badge Examples',
      children: (
        <CenteredContainer>
          <BadgeExamples />
        </CenteredContainer>
      ),
    },
    {
      key: '14',
      label: 'Modal Examples',
      children: (
        <CenteredContainer>
          <ModalExample />
        </CenteredContainer>
      ),
    },
    {
      key: '15',
      label: 'Slider Examples',
      children: <SliderExample />,
    },
    {
      key: '16',
      label: 'Charts Examples',
      children: <ChartExamples />,
    },
    {
      key: '17',
      label: 'Typography Examples',
      children: <TypographyExamples />,
    },
    {
      key: '18',
      label: 'Flex Examples',
      children: <FlexExamples />,
    },
    {
      key: '19',
      label: 'Animated Tabs Examples',
      children: <AnimatedTabsExample />,
    },
  ];

  return (
    <div style={{ background: '#F2F4F7' }}>
      <Tabs defaultActiveKey="1" items={items} />
    </div>
  );
};

export default SharedComponents;
