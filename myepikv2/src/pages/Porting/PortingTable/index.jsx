import { useEffect, useState } from 'react';
import PropTypes from 'prop-types'; // Import PropTypes for validation
import { DataTable } from '@/components';
import { getPortingTableColumns, portingTableDataSource } from '../constant';

const PortingTable = ({ handleAction }) => {
  const [loading, setLoading] = useState(true);
  const [paginationData, setPaginationData] = useState({
    page: 1,
    totalRecord: portingTableDataSource.length,
    pageSize: 10,
  });
  const [data, setData] = useState([]);

  const onRowClick = () => {
    // navigate(`/edge-devices/${row?.serialNumber}`);
  };

  const onPaginationChange = (page, pageSize) => {
    setPaginationData((prev) => ({
      ...prev,
      page,
      pageSize,
    }));
  };

  const getPaginatedData = () => {
    const { page, pageSize } = paginationData;
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return portingTableDataSource.slice(startIndex, endIndex);
  };

  useEffect(() => {
    setLoading(true);
    const paginatedData = getPaginatedData();
    setTimeout(() => {
      setLoading(false);
      setData(paginatedData);
    }, 0);
  }, [paginationData]);

  const columns = getPortingTableColumns({
    onDetailsClick: (key) => handleAction(key),
  });

  return (
    <DataTable
      columns={columns}
      data={data}
      onRowClick={onRowClick}
      loading={loading}
      rowSelection={false}
      onRowsSelectionChange={(rowsKeys, rowsData) => {
        console.log('Rows selected:', rowsKeys, rowsData);
      }}
      onPaginationChange={onPaginationChange}
      paginationData={paginationData}
      pageSizeOptions={['10', '25', '50', '100']}
      paginationBtnOutside={true}
      paginationBtnText={['Previous']}
      customPaginationClass={'custom-pagination'}
      paginationAlign="end"
      headerClass="device-table-header-row"
      isPagination={false}
      rowClassName="device-table-content-row"
    />
  );
};

// Add PropTypes validation
PortingTable.propTypes = {
  handleAction: PropTypes.func.isRequired, // `handleAction` is required and must be a function
};

export default PortingTable;
