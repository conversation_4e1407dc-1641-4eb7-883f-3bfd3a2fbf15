import {
  Button,
  <PERSON>lex,
  StatusCircle,
  StatusTag,
  Tooltip,
  Typography,
} from '@/components';

export function getPortingTableColumns({ onDetailsClick }) {
  const renderActionButtons = () => (
    <Flex
      align="center"
      style={{ height: '16px' }}
      onClick={(e) => e?.stopPropagation()}
    >
      <Button size="small" onClick={onDetailsClick}>
        <Typography className="small-text" style={{ color: 'white' }}>
          Details
        </Typography>
      </Button>
    </Flex>
  );

  return [
    {
      title: <Typography className="small-text">Order Number</Typography>,
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">PON</Typography>,
      dataIndex: 'pon',
      key: 'pon',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Submitted</Typography>,
      dataIndex: 'submittedDate',
      key: 'submittedDate',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Status</Typography>,
      dataIndex: 'status',
      key: 'status',
      render: (text) => <StatusTag text={text} />,
    },
    {
      title: <Typography className="small-text">Total Quantity</Typography>,
      dataIndex: 'totalQuantity',
      key: 'totalQuantity',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">FOCED</Typography>,
      dataIndex: 'foced',
      key: 'foced',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Rejected</Typography>,
      dataIndex: 'rejected',
      key: 'rejected',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Activated</Typography>,
      dataIndex: 'activated',
      key: 'activated',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Created By</Typography>,
      dataIndex: 'createdBy',
      key: 'createdBy',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Action</Typography>,
      key: 'action',
      render: renderActionButtons,
      width: 70,
    },
  ];
}

export function getPortingDetailsTableColumns() {
  const renderStatusCircle = (color) => {
    return <StatusCircle color={color} />;
  };

  return [
    {
      title: <Typography className="small-text">Telephone Number</Typography>,
      dataIndex: 'telephoneNumber',
      key: 'telephoneNumber',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
      width: 150,
    },
    {
      title: <Typography className="small-text">Status</Typography>,
      dataIndex: 'status',
      key: 'status',
      render: (text) => <StatusTag text={text} />,
    },
    {
      title: <Typography className="small-text">Port Date/Time</Typography>,
      dataIndex: 'portDateTime',
      key: 'portDateTime',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
      width: 110,
    },
    {
      title: <Typography className="small-text">Trunk Group</Typography>,
      dataIndex: 'trunkGroup',
      key: 'trunkGroup',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Rate Center</Typography>,
      dataIndex: 'rateCenter',
      key: 'rateCenter',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">State</Typography>,
      dataIndex: 'state',
      key: 'state',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">CNAME DIP</Typography>,
      dataIndex: 'cnameDip',
      key: 'cnameDip',
      render: (color) => renderStatusCircle(color),
    },
    {
      title: <Typography className="small-text">CNAME</Typography>,
      dataIndex: 'cname',
      key: 'cname',
      render: (color) => renderStatusCircle(color),
    },
    {
      title: <Typography className="small-text">Direct Listing</Typography>,
      dataIndex: 'directListing',
      key: 'directListing',
      render: (color) => renderStatusCircle(color),
    },
    {
      title: <Typography className="small-text">E911</Typography>,
      dataIndex: 'e911',
      key: 'e911',
      render: (color) => renderStatusCircle(color),
    },
    {
      title: <Typography className="small-text">Messaging</Typography>,
      dataIndex: 'messaging',
      key: 'messaging',
      render: (color) => renderStatusCircle(color),
    },
  ];
}

export const portingTableDataSource = [
  {
    key: '1',
    orderNumber: 'EPK2501713',
    pon: '04529180_1',
    submittedDate: '09/04/2020',
    status: 'Incomplete',
    totalQuantity: '2',
    foced: '1',
    rejected: '2',
    activated: '3',
    createdBy: 'Shane',
  },
  {
    key: '2',
    orderNumber: 'EPK2501713',
    pon: '04529180_1',
    submittedDate: '09/04/2020',
    status: 'Pending Cancel',
    totalQuantity: '2',
    foced: '1',
    rejected: '2',
    activated: '3',
    createdBy: 'Shane',
  },
  {
    key: '3',
    orderNumber: 'EPK2501713',
    pon: '04529180_1',
    submittedDate: '09/04/2020',
    status: 'Canceled',
    totalQuantity: '2',
    foced: '1',
    rejected: '2',
    activated: '3',
    createdBy: 'Shane',
  },
  {
    key: '4',
    orderNumber: 'EPK2501713',
    pon: '04529180_1',
    submittedDate: '09/04/2020',
    status: 'Closed',
    totalQuantity: '2',
    foced: '1',
    rejected: '2',
    activated: '3',
    createdBy: 'Shane',
  },
  {
    key: '5',
    orderNumber: 'EPK2501713',
    pon: '04529180_1',
    submittedDate: '09/04/2020',
    status: 'Pending',
    totalQuantity: '2',
    foced: '1',
    rejected: '2',
    activated: '3',
    createdBy: 'Shane',
  },
  {
    key: '6',
    orderNumber: 'EPK2501713',
    pon: '04529180_1',
    submittedDate: '09/04/2020',
    status: 'Incomplete',
    totalQuantity: '2',
    foced: '1',
    rejected: '2',
    activated: '3',
    createdBy: 'Shane',
  },
  {
    key: '7',
    orderNumber: 'EPK2501713',
    pon: '04529180_1',
    submittedDate: '09/04/2020',
    status: 'Incomplete',
    totalQuantity: '2',
    foced: '1',
    rejected: '2',
    activated: '3',
    createdBy: 'Shane',
  },
  {
    key: '8',
    orderNumber: 'EPK2501713',
    pon: '04529180_1',
    submittedDate: '09/04/2020',
    status: 'Incomplete',
    totalQuantity: '2',
    foced: '1',
    rejected: '2',
    activated: '3',
    createdBy: 'Shane',
  },
  {
    key: '9',
    orderNumber: 'EPK2501713',
    pon: '04529180_1',
    submittedDate: '09/04/2020',
    status: 'Incomplete',
    totalQuantity: '2',
    foced: '1',
    rejected: '2',
    activated: '3',
    createdBy: 'Shane',
  },
  {
    key: '10',
    orderNumber: 'EPK2501713',
    pon: '04529180_1',
    submittedDate: '09/04/2020',
    status: 'Incomplete',
    totalQuantity: '2',
    foced: '1',
    rejected: '2',
    activated: '3',
    createdBy: 'Shane',
  },
  {
    key: '11',
    orderNumber: 'EPK2501713',
    pon: '04529180_1',
    submittedDate: '09/04/2020',
    status: 'Incomplete',
    totalQuantity: '2',
    foced: '1',
    rejected: '2',
    activated: '3',
    createdBy: 'Shane',
  },
  {
    key: '12',
    orderNumber: 'EPK2501713',
    pon: '04529180_1',
    submittedDate: '09/04/2020',
    status: 'Incomplete',
    totalQuantity: '2',
    foced: '1',
    rejected: '2',
    activated: '3',
    createdBy: 'Shane',
  },
  {
    key: '13',
    orderNumber: 'EPK2501713',
    pon: '04529180_1',
    submittedDate: '09/04/2020',
    status: 'Incomplete',
    totalQuantity: '2',
    foced: '1',
    rejected: '2',
    activated: '3',
    createdBy: 'Shane',
  },
  {
    key: '14',
    orderNumber: 'EPK2501713',
    pon: '04529180_1',
    submittedDate: '09/04/2020',
    status: 'Incomplete',
    totalQuantity: '2',
    foced: '1',
    rejected: '2',
    activated: '3',
    createdBy: 'Shane',
  },
  {
    key: '15',
    orderNumber: 'EPK2501713',
    pon: '04529180_1',
    submittedDate: '09/04/2020',
    status: 'Incomplete',
    totalQuantity: '2',
    foced: '1',
    rejected: '2',
    activated: '3',
    createdBy: 'Shane',
  },
  {
    key: '16',
    orderNumber: 'EPK2501713',
    pon: '04529180_1',
    submittedDate: '09/04/2020',
    status: 'Incomplete',
    totalQuantity: '2',
    foced: '1',
    rejected: '2',
    activated: '3',
    createdBy: 'Shane',
  },
  {
    key: '17',
    orderNumber: 'EPK2501713',
    pon: '04529180_1',
    submittedDate: '09/04/2020',
    status: 'Incomplete',
    totalQuantity: '2',
    foced: '1',
    rejected: '2',
    activated: '3',
    createdBy: 'Shane',
  },
  {
    key: '18',
    orderNumber: 'EPK2501713',
    pon: '04529180_1',
    submittedDate: '09/04/2020',
    status: 'Incomplete',
    totalQuantity: '2',
    foced: '1',
    rejected: '2',
    activated: '3',
    createdBy: 'Shane',
  },
  {
    key: '19',
    orderNumber: 'EPK2501713',
    pon: '04529180_1',
    submittedDate: '09/04/2020',
    status: 'Incomplete',
    totalQuantity: '2',
    foced: '1',
    rejected: '2',
    activated: '3',
    createdBy: 'Shane',
  },
  {
    key: '20',
    orderNumber: 'EPK2501713',
    pon: '04529180_1',
    submittedDate: '09/04/2020',
    status: 'Incomplete',
    totalQuantity: '2',
    foced: '1',
    rejected: '2',
    activated: '3',
    createdBy: 'Shane',
  },
  {
    key: '21',
    orderNumber: 'EPK2501713',
    pon: '04529180_1',
    submittedDate: '09/04/2020',
    status: 'Incomplete',
    totalQuantity: '2',
    foced: '1',
    rejected: '2',
    activated: '3',
    createdBy: 'Shane',
  },
];

export const portingDetailsTableDataSource = [
  {
    key: '1',
    telephoneNumber: 'Epik Company Name',
    status: 'Closed',
    portDateTime: '09/04/2020 10:30 AM',
    trunkGroup: 'LSANCARCGR6_3199',
    rateCenter: 'BOLIVAR',
    state: 'MO',
    cnameDip: '#D92D20',
    cname: '#D92D20',
    directListing: '#D92D20',
    e911: '#D92D20',
    messaging: '#D92D20',
  },
  {
    key: '2',
    telephoneNumber: 'Epik Company Name',
    status: 'Pennding',
    portDateTime: '09/04/2020 10:30 AM',
    trunkGroup: 'LSANCARCGR6_3199',
    rateCenter: 'BOLIVAR',
    state: 'MO',
    cnameDip: '#D92D20',
    cname: '#D92D20',
    directListing: '#D92D20',
    e911: '#D92D20',
    messaging: '#D92D20',
  },
  {
    key: '3',
    telephoneNumber: 'Epik Company Name',
    status: 'Incomplete',
    portDateTime: '09/04/2020 10:30 AM',
    trunkGroup: 'LSANCARCGR6_3199',
    rateCenter: 'BOLIVAR',
    state: 'MO',
    cnameDip: '#D92D20',
    cname: '#D92D20',
    directListing: '#D92D20',
    e911: '#D92D20',
    messaging: '#D92D20',
  },
  {
    key: '4',
    telephoneNumber: 'Epik Company Name',
    status: 'Incomplete',
    portDateTime: '09/04/2020 10:30 AM',
    trunkGroup: 'LSANCARCGR6_3199',
    rateCenter: 'BOLIVAR',
    state: 'MO',
    cnameDip: '#D92D20',
    cname: '#D92D20',
    directListing: '#D92D20',
    e911: '#D92D20',
    messaging: '#D92D20',
  },
  {
    key: '5',
    telephoneNumber: 'Epik Company Name',
    status: 'Incomplete',
    portDateTime: '09/04/2020 10:30 AM',
    trunkGroup: 'LSANCARCGR6_3199',
    rateCenter: 'BOLIVAR',
    state: 'MO',
    cnameDip: '#D92D20',
    cname: '#D92D20',
    directListing: '#D92D20',
    e911: '#D92D20',
    messaging: '#D92D20',
  },
  {
    key: '6',
    telephoneNumber: 'Epik Company Name',
    status: 'Incomplete',
    portDateTime: '09/04/2020 10:30 AM',
    trunkGroup: 'LSANCARCGR6_3199',
    rateCenter: 'BOLIVAR',
    state: 'MO',
    cnameDip: '#D92D20',
    cname: '#D92D20',
    directListing: '#D92D20',
    e911: '#D92D20',
    messaging: '#D92D20',
  },
  {
    key: '7',
    telephoneNumber: 'Epik Company Name',
    status: 'Incomplete',
    portDateTime: '09/04/2020 10:30 AM',
    trunkGroup: 'LSANCARCGR6_3199',
    rateCenter: 'BOLIVAR',
    state: 'MO',
    cnameDip: '#D92D20',
    cname: '#D92D20',
    directListing: '#D92D20',
    e911: '#D92D20',
    messaging: '#D92D20',
  },
  {
    key: '8',
    telephoneNumber: 'Epik Company Name',
    status: 'Incomplete',
    portDateTime: '09/04/2020 10:30 AM',
    trunkGroup: 'LSANCARCGR6_3199',
    rateCenter: 'BOLIVAR',
    state: 'MO',
    cnameDip: '#D92D20',
    cname: '#D92D20',
    directListing: '#D92D20',
    e911: '#D92D20',
    messaging: '#D92D20',
  },
  {
    key: '9',
    telephoneNumber: 'Epik Company Name',
    status: 'Incomplete',
    portDateTime: '09/04/2020 10:30 AM',
    trunkGroup: 'LSANCARCGR6_3199',
    rateCenter: 'BOLIVAR',
    state: 'MO',
    cnameDip: '#D92D20',
    cname: '#D92D20',
    directListing: '#D92D20',
    e911: '#D92D20',
    messaging: '#D92D20',
  },
  {
    key: '10',
    telephoneNumber: 'Epik Company Name',
    status: 'Incomplete',
    portDateTime: '09/04/2020 10:30 AM',
    trunkGroup: 'LSANCARCGR6_3199',
    rateCenter: 'BOLIVAR',
    state: 'MO',
    cnameDip: '#D92D20',
    cname: '#D92D20',
    directListing: '#D92D20',
    e911: '#D92D20',
    messaging: '#D92D20',
  },
  {
    key: '11',
    telephoneNumber: 'Epik Company Name',
    status: 'Incomplete',
    portDateTime: '09/04/2020 10:30 AM',
    trunkGroup: 'LSANCARCGR6_3199',
    rateCenter: 'BOLIVAR',
    state: 'MO',
    cnameDip: '#D92D20',
    cname: '#D92D20',
    directListing: '#D92D20',
    e911: '#D92D20',
    messaging: '#D92D20',
  },
  {
    key: '12',
    telephoneNumber: 'Epik Company Name',
    status: 'Incomplete',
    portDateTime: '09/04/2020 10:30 AM',
    trunkGroup: 'LSANCARCGR6_3199',
    rateCenter: 'BOLIVAR',
    state: 'MO',
    cnameDip: '#D92D20',
    cname: '#D92D20',
    directListing: '#D92D20',
    e911: '#D92D20',
    messaging: '#D92D20',
  },
  {
    key: '13',
    telephoneNumber: 'Epik Company Name',
    status: 'Incomplete',
    portDateTime: '09/04/2020 10:30 AM',
    trunkGroup: 'LSANCARCGR6_3199',
    rateCenter: 'BOLIVAR',
    state: 'MO',
    cnameDip: '#D92D20',
    cname: '#D92D20',
    directListing: '#D92D20',
    e911: '#D92D20',
    messaging: '#D92D20',
  },
  {
    key: '14',
    telephoneNumber: 'Epik Company Name',
    status: 'Incomplete',
    portDateTime: '09/04/2020 10:30 AM',
    trunkGroup: 'LSANCARCGR6_3199',
    rateCenter: 'BOLIVAR',
    state: 'MO',
    cnameDip: '#D92D20',
    cname: '#D92D20',
    directListing: '#D92D20',
    e911: '#D92D20',
    messaging: '#D92D20',
  },
  {
    key: '15',
    telephoneNumber: 'Epik Company Name',
    status: 'Incomplete',
    portDateTime: '09/04/2020 10:30 AM',
    trunkGroup: 'LSANCARCGR6_3199',
    rateCenter: 'BOLIVAR',
    state: 'MO',
    cnameDip: '#D92D20',
    cname: '#D92D20',
    directListing: '#D92D20',
    e911: '#D92D20',
    messaging: '#D92D20',
  },
  {
    key: '16',
    telephoneNumber: 'Epik Company Name',
    status: 'Incomplete',
    portDateTime: '09/04/2020 10:30 AM',
    trunkGroup: 'LSANCARCGR6_3199',
    rateCenter: 'BOLIVAR',
    state: 'MO',
    cnameDip: '#D92D20',
    cname: '#D92D20',
    directListing: '#D92D20',
    e911: '#D92D20',
    messaging: '#D92D20',
  },
  {
    key: '17',
    telephoneNumber: 'Epik Company Name',
    status: 'Incomplete',
    portDateTime: '09/04/2020 10:30 AM',
    trunkGroup: 'LSANCARCGR6_3199',
    rateCenter: 'BOLIVAR',
    state: 'MO',
    cnameDip: '#D92D20',
    cname: '#D92D20',
    directListing: '#D92D20',
    e911: '#D92D20',
    messaging: '#D92D20',
  },
  {
    key: '18',
    telephoneNumber: 'Epik Company Name',
    status: 'Incomplete',
    portDateTime: '09/04/2020 10:30 AM',
    trunkGroup: 'LSANCARCGR6_3199',
    rateCenter: 'BOLIVAR',
    state: 'MO',
    cnameDip: '#D92D20',
    cname: '#D92D20',
    directListing: '#D92D20',
    e911: '#D92D20',
    messaging: '#D92D20',
  },
  {
    key: '19',
    telephoneNumber: 'Epik Company Name',
    status: 'Incomplete',
    portDateTime: '09/04/2020 10:30 AM',
    trunkGroup: 'LSANCARCGR6_3199',
    rateCenter: 'BOLIVAR',
    state: 'MO',
    cnameDip: '#D92D20',
    cname: '#D92D20',
    directListing: '#D92D20',
    e911: '#D92D20',
    messaging: '#D92D20',
  },
  {
    key: '20',
    telephoneNumber: 'Epik Company Name',
    status: 'Incomplete',
    portDateTime: '09/04/2020 10:30 AM',
    trunkGroup: 'LSANCARCGR6_3199',
    rateCenter: 'BOLIVAR',
    state: 'MO',
    cnameDip: '#D92D20',
    cname: '#D92D20',
    directListing: '#D92D20',
    e911: '#D92D20',
    messaging: '#D92D20',
  },
  {
    key: '21',
    telephoneNUmber: 'Epik Company Name',
    status: 'Incomplete',
    portDateTime: '09/04/2020 10:30 AM',
    trunkGroup: 'LSANCARCGR6_3199',
    rateCenter: 'BOLIVAR',
    state: 'MO',
    cnameDip: '#D92D20',
    cname: '#D92D20',
    directListing: '#D92D20',
    e911: '#D92D20',
    messaging: '#D92D20',
  },
];
