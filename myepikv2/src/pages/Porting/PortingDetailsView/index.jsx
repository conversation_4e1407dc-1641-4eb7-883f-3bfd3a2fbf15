import { AnimatedTab, ArrowLeftCircleIcon, Button, Flex } from '@/components';
import PropTypes from 'prop-types';
import { useState } from 'react';
import PortInorderDetail from '../PortInorderDetail';
import EditOrderInfo from '../EditOrderInfo';
import EditNumberInfo from '../EditNumberInfo';

const PortingDetailsView = ({ onBack }) => {
  const [portingDetailsTab, setPortingDetailsTab] = useState(
    'Port In Order Detail',
  );

  const renderPortingDetailsTabs = (selectedTab) => {
    switch (selectedTab) {
      case 'Port In Order Detail':
        return <PortInorderDetail />;
      case 'Edit Order Info':
        return <EditOrderInfo />;
      case 'Edit Numbers Info':
        return <EditNumberInfo />;
      default:
        break;
    }
  };

  return (
    <Flex vertical gap={16} style={{ height: '100%' }}>
      <Flex
        align="center"
        justify="space-between"
        style={{ padding: '0 12px' }}
      >
        <Flex align="center">
          <ArrowLeftCircleIcon
            className="edge-device-detail__back-icon"
            onClick={onBack}
          />
          <AnimatedTab
            value={portingDetailsTab}
            onChange={(key) => setPortingDetailsTab(key)}
            options={[
              'Port In Order Detail',
              'Edit Order Info',
              'Edit Numbers Info',
            ]}
            size="default"
          />
        </Flex>
        <Flex gap={4}>
          <Button>Activate Numbers</Button>
          <Button color="danger">Cancel Order</Button>
        </Flex>
      </Flex>
      {renderPortingDetailsTabs(portingDetailsTab)}
    </Flex>
  );
};

PortingDetailsView.propTypes = {
  onBack: PropTypes.func.isRequired,
};

export default PortingDetailsView;
