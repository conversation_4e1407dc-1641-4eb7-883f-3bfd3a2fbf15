import { useEffect, useState } from 'react';
import PropTypes from 'prop-types'; // Import PropTypes for validation
import { DataTable } from '@/components';
import {
  getPortingDetailsTableColumns,
  portingDetailsTableDataSource,
} from '../constant';

const PortingDetailsTable = ({ handleAction }) => {
  const [loading, setLoading] = useState(true);
  const [paginationData, setPaginationData] = useState({
    page: 1,
    totalRecord: portingDetailsTableDataSource.length,
    pageSize: 10,
  });
  const [data, setData] = useState([]);

  const onPaginationChange = (page, pageSize) => {
    setPaginationData((prev) => ({
      ...prev,
      page,
      pageSize,
    }));
  };

  const handleToggleChange = (checked, record) => {
    console.log(
      `Monitor toggled to ${checked} for device:`,
      record.serialNumber,
    );
  };

  const getPaginatedData = () => {
    const { page, pageSize } = paginationData;
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return portingDetailsTableDataSource.slice(startIndex, endIndex);
  };

  useEffect(() => {
    setLoading(true);
    const paginatedData = getPaginatedData();
    setTimeout(() => {
      setLoading(false);
      setData(paginatedData);
    }, 0);
  }, [paginationData]);

  const columns = getPortingDetailsTableColumns({
    onNameClick: (record, index) => console.log('Row clicked:', record, index),
    onMenuClick: (key) => handleAction(key),
    onToggleChange: handleToggleChange,
  });

  return (
    <DataTable
      columns={columns}
      data={data}
      loading={loading}
      rowSelection={false}
      onRowsSelectionChange={(rowsKeys, rowsData) => {
        console.log('Rows selected:', rowsKeys, rowsData);
      }}
      onPaginationChange={onPaginationChange}
      paginationData={paginationData}
      pageSizeOptions={['10', '25', '50', '100']}
      paginationBtnOutside={true}
      paginationBtnText={['Previous']}
      customPaginationClass={'custom-pagination'}
      paginationAlign="end"
      headerClass="device-table-header-row"
      isPagination={false}
      size="small"
      rowClassName="device-table-content-row"
    />
  );
};

// Add PropTypes validation
PortingDetailsTable.propTypes = {
  handleAction: PropTypes.func.isRequired, // `handleAction` is required and must be a function
};

export default PortingDetailsTable;
