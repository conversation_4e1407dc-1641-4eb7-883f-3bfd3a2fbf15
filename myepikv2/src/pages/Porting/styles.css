.sliding-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  min-height: 100%;
  z-index: 10;
  background-color: #f5f5f5;
  overflow: hidden;
  pointer-events: auto;
}

/* You are not allowed to use any background here */
.slide-in {
  animation: slideInFromRight 0.4s ease forwards;
}

.slide-out {
  animation: slideOutToRight 0.4s ease forwards;
}

@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 1;
  }
  to {
    transform: translateX(0%);
    opacity: 1;
  }
}

@keyframes slideOutToRight {
  from {
    transform: translateX(0%);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 1;
  }
}
