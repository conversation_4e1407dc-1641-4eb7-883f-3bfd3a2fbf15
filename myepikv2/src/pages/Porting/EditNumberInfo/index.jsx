import { useState } from 'react';
import PropTypes from 'prop-types';
import {
  Input,
  Flex,
  Button,
  Typography,
  Select,
  Checkbox,
} from '@/components';
import { Divider, Row, Col, DatePicker } from 'antd';
import { useStore } from '@/store';

const Field = ({ label, component, bottomLabel }) => (
  <Flex vertical gap={8}>
    <Typography className="small-text" style={{ color: 'var(--primary-gray)' }}>
      {label}
    </Typography>
    {component}
    {bottomLabel && (
      <Typography className="extra-small-text">{bottomLabel}</Typography>
    )}
  </Flex>
);

// Add PropTypes for Field Component
Field.propTypes = {
  label: PropTypes.string.isRequired, // `label` is a required string
  component: PropTypes.node.isRequired, // `component` is a required React node
  bottomLabel: PropTypes.string.isRequired,
};

const EditNumberInfo = ({ mode }) => {
  const [formValues, setFormValues] = useState({});

  const { closeDrawer } = useStore((state) => state.drawer);

  const handleChange = (field, value) => {
    setFormValues((prev) => ({ ...prev, [field]: value }));
  };

  const handleKeyDown = (field, value, event) => {
    if (event.key === 'Enter' && value.trim()) {
      if (field === 'alertEmail') {
        handleChange(field, '');
      }
    }
  };

  const sections = [
    {
      title: '',
      fields: [
        {
          name: 'oderFeature',
          label:
            'Would you like to add features ti this order? (Fearutes will be added in next page)',
          component: (
            <Flex gap={12}>
              <Flex align="center" gap={4}>
                <Checkbox />
                <Typography className="small-text-bold">Yes</Typography>
              </Flex>
              <Flex align="center" gap={4}>
                <Checkbox />
                <Typography className="small-text-bold">No</Typography>
              </Flex>
            </Flex>
          ),
        },
      ],
    },
    {
      title: 'Order Contact',
      fields: [
        {
          name: 'authenticationName',
          label: 'Authentication Name *',
          placeholder: '--',
          span: 8,
        },
        {
          name: 'authenticationDate',
          label: 'Authentication Date *',
          placeholder: '',
          component: <DatePicker type="date" />,
          span: 8,
        },
        {
          name: 'accountNumber',
          label: 'Account Number *',
          placeholder: '--',
          span: 8,
        },
        {
          name: 'billingTelephoneNumber',
          label: 'Billing Telephone Number',
          placeholder: '--',
          span: 8,
        },
        {
          name: 'endUserName',
          label: 'End User Name *',
          placeholder: '--',
          span: 8,
        },
        {
          name: 'typeService',
          label: 'Type of Service *',
          placeholder: '',
          component: <Select />,
          span: 8,
        },
      ],
    },
    {
      title: 'Service Address',
      fields: [
        {
          name: 'serviceNumber',
          label: 'Service Number *',
          placeholder: '--',
          span: 8,
        },
        {
          name: 'streetPreDirection',
          label: 'Street Pre-Direction',
          placeholder: '',
          component: <Select />,
          span: 8,
        },
        {
          name: 'streetName',
          label: 'Street Name *',
          placeholder: '--',
          span: 8,
        },
      ],
    },
    {
      title: '',
      fields: [
        {
          name: 'streetType',
          label: 'Street Type',
          placeholder: '--',
          span: 4,
        },
        {
          name: 'streetPostDirection',
          label: 'streetPostDirection',
          placeholder: '',
          component: <Select />,
          span: 4,
        },
      ],
    },
    {
      title: '',
      fields: [
        {
          name: 'locationType1',
          label: 'Location Type 1',
          placeholder: '--',
          span: 4,
        },
        {
          name: 'locationValue1',
          label: 'Location Value 1',
          placeholder: '--',
          span: 4,
        },
        {
          name: 'locationValue1',
          label: '',
          placeholder: '',
          component: <Button>Delete</Button>,
          span: 2,
        },
      ],
    },
    {
      title: '',
      fields: [
        {
          name: 'locationType2',
          label: 'Location Type 2',
          placeholder: '--',
          span: 4,
        },
        {
          name: 'locationValue2',
          label: 'Location Value 2',
          placeholder: '--',
          span: 4,
        },
        {
          name: 'delete2',
          label: '',
          placeholder: '',
          component: <Button>Delete</Button>,
          span: 2,
        },
      ],
    },
    {
      title: '',
      fields: [
        {
          name: 'locationType3',
          label: 'Location Type 3',
          placeholder: '--',
          span: 4,
        },
        {
          name: 'locationValue3',
          label: 'Location Value 3',
          placeholder: '--',
          span: 4,
        },
        {
          name: 'delete3',
          label: '',
          placeholder: '',
          component: <Button>Delete</Button>,
          span: 2,
        },
        {
          name: 'addLocation',
          label: '',
          placeholder: '',
          component: <Button>Add Location</Button>,
          span: 3,
        },
      ],
    },
    {
      title: 'Order Contact',
      fields: [
        {
          name: 'country',
          label: 'Country *',
          placeholder: '',
          component: <Select />,
          span: 4,
        },
        {
          name: 'city',
          label: 'City *',
          placeholder: '--',
          span: 8,
        },
        {
          name: 'state',
          label: 'State *',
          placeholder: '',
          component: <Select />,
          span: 4,
        },
        {
          name: 'email',
          label: 'Email',
          placeholder: '--',
          span: 8,
        },
      ],
    },
  ];

  return (
    <Flex
      vertical
      style={{
        background: 'white',
        borderRadius: '8px',
        marginBottom: '12px',
      }}
    >
      <Flex vertical style={{ margin: '12px' }}>
        <Flex justify="space-between" align="center">
          <Typography className="heading-four-bold">
            Edit Number Info
          </Typography>
        </Flex>
        <Divider className="divider-sm" />
      </Flex>
      {sections.map((section, sectionIndex) => (
        <Flex
          vertical
          key={`section-${sectionIndex}`}
          style={{ margin: '6px 12px' }}
        >
          {(section?.title || section?.showDivider) && (
            <Flex vertical>
              <Typography className="heading-five">{section.title}</Typography>
            </Flex>
          )}
          <Row gutter={[8, 16]} align="bottom">
            {section.fields.map((field, fieldIndex) => (
              <Col
                key={`field-${sectionIndex}-${fieldIndex}`}
                span={field.span}
                xs={24}
                sm={12}
                md={12}
                lg={field.span}
                xl={field.span}
                xxl={field.span}
              >
                {Field({
                  label: field.label,
                  component: field.component || (
                    <Input
                      placeholder={field.placeholder}
                      value={formValues[field.name]}
                      onChange={(e) => handleChange(field.name, e.target.value)}
                      onKeyDown={(e) =>
                        field.list &&
                        e.key === 'Enter' &&
                        handleKeyDown(field.name, formValues[field.name], e)
                      }
                      size="middle"
                      className="text-medium-regular"
                    />
                  ),
                  bottomLabel: field?.bottomLabel,
                })}
              </Col>
            ))}
            {mode === 'edit' &&
              section.editModeFields.map((field, fieldIndex) => (
                <Col
                  key={`field-${sectionIndex}-${fieldIndex}`}
                  span={field.span}
                  xs={24}
                  sm={12}
                  md={12}
                  lg={field.span}
                  xl={field.span}
                  xxl={field.span}
                >
                  {Field({
                    label: field.label,
                    component: field.component || (
                      <Input
                        placeholder={field.placeholder}
                        value={formValues[field.name]}
                        onChange={(e) =>
                          handleChange(field.name, e.target.value)
                        }
                        onKeyDown={(e) =>
                          field.list &&
                          e.key === 'Enter' &&
                          handleKeyDown(field.name, formValues[field.name], e)
                        }
                        size="middle"
                        className="text-medium-regular"
                      />
                    ),
                    bottomLabel: field?.bottomLabel,
                  })}
                </Col>
              ))}
          </Row>
          {mode === 'edit' && (
            <Button style={{ width: '10%', marginTop: 16 }}>Edit CNAM</Button>
          )}
        </Flex>
      ))}
      <Flex vertical gap={4} justify="end" style={{ marginTop: '12px' }}>
        <Divider className="divider-sm" />
        <Flex gap={8} justify="end">
          <Button
            variant="outlined"
            onClick={closeDrawer}
            style={{ width: '10%' }}
          >
            Cancel
          </Button>
          <Button
            onClick={() => console.log('Save Form:', formValues)}
            style={{ width: '10%' }}
          >
            Update
          </Button>
        </Flex>
      </Flex>
    </Flex>
  );
};

EditNumberInfo.propTypes = {
  mode: PropTypes.bool.isRequired,
};

export default EditNumberInfo;
