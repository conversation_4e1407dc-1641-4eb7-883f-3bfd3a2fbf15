import { useState, useEffect } from 'react';
import PortingMainView from './PortingMainView';
import PortingDetailsView from './PortingDetailsView';
import './styles.css';

const Porting = () => {
  const [showDetails, setShowDetails] = useState(false);
  const [renderDetails, setRenderDetails] = useState(false);

  useEffect(() => {
    if (showDetails) {
      setRenderDetails(true);
    }
  }, [showDetails]);

  const handleBack = () => setShowDetails(false);

  const handleAnimationEnd = () => {
    if (!showDetails) setRenderDetails(false);
  };

  return (
    <div
      style={{
        position: 'relative',
        width: '100%',
        height: '100%',
      }}
    >
      <PortingMainView onNext={() => setShowDetails(true)} />

      {renderDetails && (
        <div
          className={`sliding-overlay ${showDetails ? 'slide-in' : 'slide-out'}`}
          onAnimationEnd={handleAnimationEnd}
          style={{ height: '100%' }}
        >
          <PortingDetailsView onBack={handleBack} />
        </div>
      )}
    </div>
  );
};

export default Porting;
