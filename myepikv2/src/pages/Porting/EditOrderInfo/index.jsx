import { useState } from 'react';
import PropTypes from 'prop-types';
import {
  Input,
  Flex,
  Button,
  Typography,
  Select,
  Upload,
  DatePicker,
  CustomTimePicker,
} from '@/components';
import { Divider, Row, Col } from 'antd';
import { useStore } from '@/store';
import TextArea from 'antd/es/input/TextArea';

const Field = ({ label, component, bottomLabel }) => (
  <Flex vertical gap={8}>
    <Typography className="small-text" style={{ color: 'var(--primary-gray)' }}>
      {label}
    </Typography>
    {component}
    {bottomLabel && (
      <Typography className="extra-small-text">{bottomLabel}</Typography>
    )}
  </Flex>
);

// Add PropTypes for Field Component
Field.propTypes = {
  label: PropTypes.string.isRequired, // `label` is a required string
  component: PropTypes.node.isRequired, // `component` is a required React node
  bottomLabel: PropTypes.string.isRequired,
};

const EditOrderInfo = ({ mode }) => {
  const [formValues, setFormValues] = useState({});

  const { closeDrawer } = useStore((state) => state.drawer);

  const handleChange = (field, value) => {
    setFormValues((prev) => ({ ...prev, [field]: value }));
  };

  const handleKeyDown = (field, value, event) => {
    if (event.key === 'Enter' && value.trim()) {
      if (field === 'alertEmail') {
        handleChange(field, '');
      }
    }
  };

  const sections = [
    {
      title: '',
      fields: [
        {
          name: 'pon',
          label: 'PON*',
          placeholder: '',
          span: 12,
        },
        {
          name: 'resellerName',
          label: 'Reseller Name',
          placeholder: '--',
          span: 12,
        },
      ],
    },
    {
      title: 'Desired Port Time',
      fields: [
        {
          name: 'desiredDueDate',
          label: 'Desired Due Date',
          placeholder: '',
          component: <DatePicker type="date" />,
          span: 8,
        },
        {
          name: 'desiredPortTime',
          label: 'Desired Port Time',
          placeholder: '',
          component: <CustomTimePicker />,
          span: 8,
        },
        {
          name: 'desiredTimeZone',
          label: 'Desired Time Zone',
          placeholder: '',
          component: (
            <Select
              options={[{ value: 'all', label: 'All' }]}
              default="All"
              size="middle"
            />
          ),
          span: 8,
        },
      ],
    },
    {
      title: 'Order Contact',
      fields: [
        {
          name: 'name',
          label: 'Name',
          placeholder: '--',
          span: 6,
        },
        {
          name: 'workPhone',
          label: 'Work Phone',
          placeholder: '--',
          span: 6,
        },
        {
          name: 'extension',
          label: 'Extension',
          placeholder: '--',
          span: 6,
        },
        {
          name: 'email',
          label: 'Email',
          placeholder: '',
          span: 6,
        },
      ],
    },
    {
      title: 'Notes',
      fields: [
        {
          name: 'name',
          label: '',
          placeholder: '',
          component: <TextArea />,
          span: 24,
        },
      ],
    },
    {
      title: 'Attachments',
      fields: [
        {
          name: 'name',
          label: '',
          placeholder: '',
          component: <Upload />,
          span: 24,
        },
      ],
    },
  ];

  return (
    <Flex
      vertical
      style={{
        background: 'white',
        borderRadius: '8px',
        marginBottom: '12px',
      }}
    >
      <Flex vertical style={{ margin: '12px' }}>
        <Flex justify="space-between" align="center">
          <Typography className="heading-four-bold">Edit Order Info</Typography>
        </Flex>
        <Divider className="divider-sm" />
      </Flex>
      {sections.map((section, sectionIndex) => (
        <Flex
          vertical
          key={`section-${sectionIndex}`}
          style={{ margin: '6px 12px' }}
        >
          {(section?.title || section?.showDivider) && (
            <Flex vertical>
              <Typography className="heading-five">{section.title}</Typography>
            </Flex>
          )}
          <Row gutter={[8, 16]}>
            {section.fields.map((field, fieldIndex) => (
              <Col
                key={`field-${sectionIndex}-${fieldIndex}`}
                span={field.span}
                xs={24}
                sm={12}
                md={12}
                lg={field.span}
                xl={field.span}
                xxl={field.span}
              >
                {Field({
                  label: field.label,
                  component: field.component || (
                    <Input
                      placeholder={field.placeholder}
                      value={formValues[field.name]}
                      onChange={(e) => handleChange(field.name, e.target.value)}
                      onKeyDown={(e) =>
                        field.list &&
                        e.key === 'Enter' &&
                        handleKeyDown(field.name, formValues[field.name], e)
                      }
                      size="middle"
                      className="text-medium-regular"
                    />
                  ),
                  bottomLabel: field?.bottomLabel,
                })}
              </Col>
            ))}
            {mode === 'edit' &&
              section.editModeFields.map((field, fieldIndex) => (
                <Col
                  key={`field-${sectionIndex}-${fieldIndex}`}
                  span={field.span}
                  xs={24}
                  sm={12}
                  md={12}
                  lg={field.span}
                  xl={field.span}
                  xxl={field.span}
                >
                  {Field({
                    label: field.label,
                    component: field.component || (
                      <Input
                        placeholder={field.placeholder}
                        value={formValues[field.name]}
                        onChange={(e) =>
                          handleChange(field.name, e.target.value)
                        }
                        onKeyDown={(e) =>
                          field.list &&
                          e.key === 'Enter' &&
                          handleKeyDown(field.name, formValues[field.name], e)
                        }
                        size="middle"
                        className="text-medium-regular"
                      />
                    ),
                    bottomLabel: field?.bottomLabel,
                  })}
                </Col>
              ))}
          </Row>
          {mode === 'edit' && (
            <Button style={{ width: '10%', marginTop: 16 }}>Edit CNAM</Button>
          )}
        </Flex>
      ))}
      <Flex vertical gap={4} justify="end" style={{ marginTop: '12px' }}>
        <Divider className="divider-sm" />
        <Flex gap={8} justify="end">
          <Button
            variant="outlined"
            onClick={closeDrawer}
            style={{ width: '10%' }}
          >
            Cancel
          </Button>
          <Button
            onClick={() => console.log('Save Form:', formValues)}
            style={{ width: '10%' }}
          >
            Update
          </Button>
        </Flex>
      </Flex>
    </Flex>
  );
};

EditOrderInfo.propTypes = {
  mode: PropTypes.bool.isRequired,
};

export default EditOrderInfo;
