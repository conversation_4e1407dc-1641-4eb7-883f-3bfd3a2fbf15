import { Row } from 'antd';
import PropTypes from 'prop-types';
import {
  Flex,
  Select,
  Typography,
  Button,
  Input,
  ResetLineIcon,
  DatePicker,
  PageTitle,
} from '@/components';
import PortingTable from '../PortingTable';

const Field = ({ label, component }) => (
  <Flex vertical gap={8}>
    <Typography className="small-text">{label}</Typography>
    {component}
  </Flex>
);

Field.propTypes = {
  label: PropTypes.string.isRequired,
  component: PropTypes.node.isRequired,
};

const sections = [
  {
    fields: [
      { label: 'Quantity', component: <Input /> },
      {
        label: 'Submit Date Begin',
        component: <DatePicker type="date" variant="filled" />,
      },
      {
        label: 'Submit Date End',
        component: <DatePicker type="date" variant="filled" />,
      },
      {
        label: 'State',
        component: (
          <Select options={[{ label: 'All', value: 'all' }]} variant="filled" />
        ),
      },
      { label: 'Number Mask', component: <Input /> },
    ],
  },
];

const PortingMainView = ({ onNext }) => (
  <Flex vertical gap={8} style={{ height: '100%' }}>
    <Flex
      align="center"
      wrap
      justify="space-between"
      style={{ padding: '0 12px' }}
    >
      <PageTitle pageTitle={'Porting'} />
    </Flex>
    <Row gutter={[0, 16]}>
      {sections.map((section, index) => (
        <Flex
          key={index}
          gap={16}
          align="flex-end"
          justify="space-between"
          style={{ padding: '0 12px' }}
        >
          <Flex gap={4}>
            {section.fields.map((field, i) => (
              <div style={{ flex: 2 }} key={i}>
                <Field label={field.label} component={field.component} />
              </div>
            ))}
          </Flex>
          <Flex gap={4}>
            <Button icon={<ResetLineIcon />} variant="outlined" />
            <Button>Search</Button>
          </Flex>
        </Flex>
      ))}
    </Row>
    <div style={{ padding: '0 12px' }} className="custom-scrollbar">
      <Flex
        vertical
        flex={1}
        style={{
          background: 'white',
          boxShadow: '0px 1px 2px 0px #1018280F, 0px 1px 3px 0px #1018281A',
          borderRadius: '12px',
        }}
      >
        <div style={{ marginTop: '16px' }}>
          <PortingTable handleAction={onNext} />
        </div>
      </Flex>
    </div>
  </Flex>
);

PortingMainView.propTypes = {
  onNext: PropTypes.func.isRequired,
};

export default PortingMainView;
