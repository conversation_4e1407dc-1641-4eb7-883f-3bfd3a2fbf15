import {
  Flex,
  Typography,
  Checkbox,
  StatusCircle,
  PdfIcon,
  TrashIcon,
} from '@/components';
import PortingDetailsTable from '../PortingDetailsTable';

const PortInorderDetail = () => {
  return (
    <div
      style={{ padding: '0 12px', height: '100%' }}
      className="custom-scrollbar"
    >
      <Flex
        vertical
        style={{
          background: 'white',
          borderRadius: '8px',
          marginBottom: '12px',
        }}
      >
        <Flex gap={8} vertical style={{ margin: '16px 16px 12px 16px' }}>
          <Flex
            style={{
              background: 'var(--bg-blue)',
              borderRadius: '8px',
              padding: '12px',
            }}
            vertical
            gap={4}
          >
            <Flex gap={24}>
              <Flex vertical>
                <Typography className="small-text">PON</Typography>
                <Typography className="small-text">Order ID</Typography>
              </Flex>
              <Flex vertical>
                <Typography className="small-text-bold">03316086_1</Typography>
                <Typography className="small-text-bold">148111821</Typography>
              </Flex>
              <Flex vertical>
                <Typography className="small-text">Order Status</Typography>
                <Typography className="small-text">Order Created By</Typography>
              </Flex>
              <Flex vertical>
                <Typography className="small-text-bold">Pending</Typography>
                <Typography className="small-text-bold">User</Typography>
              </Flex>
              <Flex vertical>
                <Typography className="small-text">Desired Due Date</Typography>
                <Typography className="small-text">Reseller Name</Typography>
              </Flex>
              <Flex vertical>
                <Typography className="small-text-bold">28-Mar-2025</Typography>
                <Typography className="small-text-bold">+1834930452</Typography>
              </Flex>
            </Flex>
          </Flex>
          <Typography className="heading-four-bold">Instruction</Typography>
          <Flex
            style={{
              background: 'var(--bg-blue)',
              borderRadius: '8px',
              padding: '12px',
            }}
            vertical
            gap={8}
          >
            <Flex gap={16}>
              <Flex gap={4} align="center">
                <StatusCircle color="#039855" />
                <Typography className="small-text">
                  Accepted or Complete
                </Typography>
              </Flex>
              <Flex gap={4} align="center">
                <StatusCircle color="#FEC84B" />
                <Typography className="small-text">
                  Received and processing
                </Typography>
              </Flex>
              <Flex gap={4} align="center">
                <StatusCircle color="#D92D20" />
                <Typography className="small-text">
                  Rejected or Failed
                </Typography>
              </Flex>
              <Flex gap={4} align="center">
                <StatusCircle color="#667085" />
                <Typography className="small-text">Not Applicable</Typography>
              </Flex>
            </Flex>
            <Typography className="small-text">
              More precise information can be obtained by hovering over the dot
            </Typography>
          </Flex>
          <Flex align="center" gap={6} style={{ marginTop: '4px' }}>
            <Checkbox />
            <Typography className="heading-four-bold">
              Current Carrier # Group ID 120724361 - Frontier
              Communications:5200
            </Typography>
          </Flex>
        </Flex>

        <PortingDetailsTable />
        <Flex gap={8} vertical style={{ margin: '16px 16px 12px 16px' }}>
          <Typography className="heading-four-bold">Notes</Typography>
          <Flex
            style={{
              background: 'var(--bg-blue)',
              borderRadius: '8px',
              padding: '12px',
            }}
            vertical
            gap={8}
          >
            <Flex gap={24}>
              <Flex gap={2} justify="center" vertical>
                <Typography className="small-text">Note</Typography>
                <Typography className="small-text-bold">
                  02/24/2025 Port Date set for Group ID 120722583 - Frontier
                  Rochester:0121 - NSR
                </Typography>
              </Flex>
              <Flex gap={2} justify="center" vertical>
                <Typography className="small-text">Created Date</Typography>
                <Typography className="small-text-bold">
                  2025-02-13 09:28:11.0
                </Typography>
              </Flex>
              <Flex gap={2} justify="center" vertical>
                <Typography className="small-text">Entered By</Typography>
                <Typography className="small-text-bold">Inteliquent</Typography>
              </Flex>
            </Flex>
          </Flex>
          <Typography className="heading-four-bold">Attachments</Typography>
          <Flex
            style={{
              background: '#EDF5FB',
              borderRadius: '8px',
              padding: '12px',
            }}
            gap={16}
          >
            <Flex
              justify="space-between"
              gap={60}
              align="center"
              style={{
                background: 'white',
                padding: '12px 16px',
                borderRadius: '8px',
              }}
            >
              <Flex gap={6} align="center">
                <PdfIcon width={21} height={20} />
                <Flex vertical gap={0}>
                  <Typography
                    className="small-text-bold"
                    style={{ lineHeight: '12px' }}
                  >
                    Sample file.pdf
                  </Typography>
                  <Typography
                    className="extra-small-text"
                    style={{ lineHeight: '12px' }}
                  >
                    223 KB
                  </Typography>
                </Flex>
              </Flex>
              <TrashIcon />
            </Flex>
            <Flex
              justify="space-between"
              gap={60}
              align="center"
              style={{
                background: 'white',
                padding: '12px 16px',
                borderRadius: '8px',
              }}
            >
              <Flex gap={6} align="center">
                <PdfIcon width={21} height={20} />
                <Flex vertical gap={0}>
                  <Typography
                    className="small-text-bold"
                    style={{ lineHeight: '12px' }}
                  >
                    Sample file.pdf
                  </Typography>
                  <Typography
                    className="extra-small-text"
                    style={{ lineHeight: '12px' }}
                  >
                    223 KB
                  </Typography>
                </Flex>
              </Flex>
              <TrashIcon />
            </Flex>
            <Flex
              justify="space-between"
              gap={60}
              align="center"
              style={{
                background: 'white',
                padding: '12px 16px',
                borderRadius: '8px',
              }}
            >
              <Flex gap={6} align="center">
                <PdfIcon width={21} height={20} />
                <Flex vertical gap={0}>
                  <Typography
                    className="small-text-bold"
                    style={{ lineHeight: '12px' }}
                  >
                    Sample file.pdf
                  </Typography>
                  <Typography
                    className="extra-small-text"
                    style={{ lineHeight: '12px' }}
                  >
                    223 KB
                  </Typography>
                </Flex>
              </Flex>
              <TrashIcon />
            </Flex>
          </Flex>
        </Flex>
      </Flex>
    </div>
  );
};

export default PortInorderDetail;
