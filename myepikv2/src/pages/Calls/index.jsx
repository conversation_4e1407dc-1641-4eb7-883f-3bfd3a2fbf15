import { useState } from 'react';
import {
  AdvanceSearch,
  AnimatedTab,
  Button,
  CSVExportIcon,
  FilterResetIcon,
  Flex,
  Input,
  PageTitle,
  SearchIcon,
} from '@/components';
import AllCallsTable from './AllCallsTable';
import IncommingCallsTable from './IncommingCallsTable';
import OutgoingCallsTable from './OutgoingCallsTable';

const Calls = () => {
  const [callsTab, setSelectedCallsTab] = useState('All Calls');
  const [showAdvanceSearch, setShowAdvanceSearch] = useState(false);

  const modalConfig = {
    advanceSearch: {
      component: AdvanceSearch,
      props: {
        onCancel: () => setShowAdvanceSearch(false),
        handleOk: () => setShowAdvanceSearch(false),
      },
      condition: showAdvanceSearch,
    },
  };

  const renderContent = () => {
    switch (callsTab) {
      case 'All Calls':
        return <AllCallsTable />;
      case 'Outgoing Calls':
        return <OutgoingCallsTable />;
      case 'Incomming Calls':
        return <IncommingCallsTable />;
      default:
        return null;
    }
  };

  const renderModals = () => {
    return Object.entries(modalConfig).map(([key, modal]) => {
      const { component: ModalComponent, props, condition } = modal;
      return condition ? <ModalComponent key={key} {...props} /> : null;
    });
  };

  return (
    <Flex vertical gap={8} style={{ height: '100%' }}>
      <Flex
        align="center"
        wrap
        justify="space-between"
        style={{ padding: '0 12px' }}
      >
        <PageTitle pageTitle={callsTab} />
        <Flex align="center" wrap gap={6}>
          <Button icon={<CSVExportIcon />} />
          <Button icon={<FilterResetIcon />} />
          <Button
            variant="outlined"
            variantColorBgContainer
            onClick={() => setShowAdvanceSearch(true)}
          >
            Advance Search
          </Button>
          <Input
            prefix={<SearchIcon />}
            placeholder="Search"
            style={{
              maxWidth: '165px',
              paddingTop: '5px',
              paddingBottom: '5px',
            }}
          />
        </Flex>
      </Flex>
      <div style={{ padding: '0 12px' }} className="custom-scrollbar">
        <Flex
          vertical
          flex={1}
          style={{
            background: 'white',
            borderRadius: '12px',
            boxShadow: '0px 1px 2px 0px #1018280F, 0px 1px 3px 0px #1018281A',
          }}
        >
          <Flex
            align="center"
            justify="space-between"
            style={{ padding: '12px' }}
          >
            <Flex align="center">
              <AnimatedTab
                options={['All Calls', 'Outgoing Calls', 'Incomming Calls']}
                value={callsTab}
                onChange={(value) => {
                  setSelectedCallsTab(value);
                }}
                size="default"
              />
            </Flex>
            <Flex align="center"></Flex>
          </Flex>
          {renderContent()}
          {renderModals()}
        </Flex>
      </div>
    </Flex>
  );
};

export default Calls;
