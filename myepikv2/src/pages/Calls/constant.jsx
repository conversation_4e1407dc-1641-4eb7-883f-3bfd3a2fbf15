import { Typography, Tooltip } from '@/components';

export function getColumns() {
  return [
    {
      title: <Typography className="small-text">Company</Typography>,
      dataIndex: 'company',
      key: 'company',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Serial Number</Typography>,
      dataIndex: 'serialNumber',
      key: 'serialNumber',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Caller</Typography>,
      dataIndex: 'caller',
      key: 'caller',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Destination</Typography>,
      dataIndex: 'destination',
      key: 'destination',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Call Type</Typography>,
      dataIndex: 'callType',
      key: 'callType',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Direction</Typography>,
      dataIndex: 'direction',
      key: 'direction',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Date</Typography>,
      dataIndex: 'date',
      key: 'date',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Time</Typography>,
      dataIndex: 'time',
      key: 'time',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Duration</Typography>,
      dataIndex: 'duration',
      key: 'duration',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
  ];
}

export const dataSource = [
  {
    key: '1',
    company: 'Epik Company Dummy',
    serialNumber: '000251280001',
    caller: '+18506820001',
    destination: '+18504332001',
    callType: 'Voice',
    direction: 'Inbound',
    date: 'Aug 1, 2013',
    time: '09:00AM',
    duration: '2:15',
  },
  {
    key: '2',
    company: 'Epik Tech',
    serialNumber: '000251280002',
    caller: '+18506820002',
    destination: '+18504332002',
    callType: 'Fax',
    direction: 'Outbound',
    date: 'Aug 2, 2013',
    time: '10:30AM',
    duration: '3:05',
  },
  {
    key: '3',
    company: 'Epik Voice',
    serialNumber: '000251280003',
    caller: '+18506820003',
    destination: '+18504332003',
    callType: 'Voice',
    direction: 'Inbound',
    date: 'Aug 3, 2013',
    time: '11:45AM',
    duration: '0:55',
  },
  {
    key: '4',
    company: 'Epik Network',
    serialNumber: '000251280004',
    caller: '+18506820004',
    destination: '+18504332004',
    callType: 'Fax',
    direction: 'Outbound',
    date: 'Aug 4, 2013',
    time: '01:15PM',
    duration: '1:25',
  },
  {
    key: '5',
    company: 'Epik Telecom',
    serialNumber: '000251280005',
    caller: '+18506820005',
    destination: '+18504332005',
    callType: 'Voice',
    direction: 'Inbound',
    date: 'Aug 5, 2013',
    time: '02:30PM',
    duration: '4:45',
  },
  {
    key: '6',
    company: 'Epik Systems',
    serialNumber: '000251280006',
    caller: '+18506820006',
    destination: '+18504332006',
    callType: 'Voice',
    direction: 'Outbound',
    date: 'Aug 6, 2013',
    time: '03:50PM',
    duration: '0:35',
  },
  {
    key: '7',
    company: 'Epik Digital',
    serialNumber: '000251280007',
    caller: '+18506820007',
    destination: '+18504332007',
    callType: 'Fax',
    direction: 'Inbound',
    date: 'Aug 7, 2013',
    time: '05:00PM',
    duration: '2:20',
  },
  {
    key: '8',
    company: 'Epik Company Dummy',
    serialNumber: '000251280008',
    caller: '+18506820008',
    destination: '+18504332008',
    callType: 'Voice',
    direction: 'Inbound',
    date: 'Aug 8, 2013',
    time: '06:15PM',
    duration: '1:35',
  },
  {
    key: '9',
    company: 'Epik Group',
    serialNumber: '000251280009',
    caller: '+18506820009',
    destination: '+18504332009',
    callType: 'Fax',
    direction: 'Outbound',
    date: 'Aug 9, 2013',
    time: '07:30PM',
    duration: '2:50',
  },
  {
    key: '10',
    company: 'Epik Communications',
    serialNumber: '000251280010',
    caller: '+18506820010',
    destination: '+18504332010',
    callType: 'Voice',
    direction: 'Inbound',
    date: 'Aug 10, 2013',
    time: '08:45PM',
    duration: '3:15',
  },
  {
    key: '11',
    company: 'Epik Solutions',
    serialNumber: '000251280011',
    caller: '+18506820011',
    destination: '+18504332011',
    callType: 'Voice',
    direction: 'Outbound',
    date: 'Aug 11, 2013',
    time: '09:55PM',
    duration: '1:40',
  },
  {
    key: '12',
    company: 'Epik Telecom',
    serialNumber: '000251280012',
    caller: '+18506820012',
    destination: '+18504332012',
    callType: 'Fax',
    direction: 'Inbound',
    date: 'Aug 12, 2013',
    time: '10:20AM',
    duration: '3:00',
  },
  {
    key: '13',
    company: 'Epik Labs',
    serialNumber: '000251280013',
    caller: '+18506820013',
    destination: '+18504332013',
    callType: 'Voice',
    direction: 'Inbound',
    date: 'Aug 13, 2013',
    time: '11:35AM',
    duration: '0:45',
  },
  {
    key: '14',
    company: 'Epik Flow',
    serialNumber: '000251280014',
    caller: '+18506820014',
    destination: '+18504332014',
    callType: 'Fax',
    direction: 'Outbound',
    date: 'Aug 14, 2013',
    time: '01:10PM',
    duration: '2:30',
  },
  {
    key: '15',
    company: 'Epik Edge',
    serialNumber: '000251280015',
    caller: '+18506820015',
    destination: '+18504332015',
    callType: 'Voice',
    direction: 'Inbound',
    date: 'Aug 15, 2013',
    time: '02:25PM',
    duration: '1:10',
  },
  {
    key: '16',
    company: 'Epik Cloud',
    serialNumber: '000251280016',
    caller: '+18506820016',
    destination: '+18504332016',
    callType: 'Fax',
    direction: 'Inbound',
    date: 'Aug 16, 2013',
    time: '03:45PM',
    duration: '2:40',
  },
  {
    key: '17',
    company: 'Epik Wave',
    serialNumber: '000251280017',
    caller: '+18506820017',
    destination: '+18504332017',
    callType: 'Voice',
    direction: 'Outbound',
    date: 'Aug 17, 2013',
    time: '04:55PM',
    duration: '4:05',
  },
  {
    key: '18',
    company: 'Epik Tel',
    serialNumber: '000251280018',
    caller: '+18506820018',
    destination: '+18504332018',
    callType: 'Fax',
    direction: 'Inbound',
    date: 'Aug 18, 2013',
    time: '06:00PM',
    duration: '0:30',
  },
  {
    key: '19',
    company: 'Epik Team',
    serialNumber: '000251280019',
    caller: '+18506820019',
    destination: '+18504332019',
    callType: 'Voice',
    direction: 'Inbound',
    date: 'Aug 19, 2013',
    time: '07:10PM',
    duration: '3:50',
  },
  {
    key: '20',
    company: 'Epik Cloud',
    serialNumber: '000251280020',
    caller: '+18506820020',
    destination: '+18504332020',
    callType: 'Fax',
    direction: 'Outbound',
    date: 'Aug 20, 2013',
    time: '08:20PM',
    duration: '1:55',
  },
  {
    key: '21',
    company: 'Epik Logic',
    serialNumber: '000251280021',
    caller: '+18506820021',
    destination: '+18504332021',
    callType: 'Voice',
    direction: 'Inbound',
    date: 'Aug 21, 2013',
    time: '09:30PM',
    duration: '2:10',
  },
  {
    key: '22',
    company: 'Epik Connect',
    serialNumber: '000251280022',
    caller: '+18506820022',
    destination: '+18504332022',
    callType: 'Fax',
    direction: 'Inbound',
    date: 'Aug 22, 2013',
    time: '10:45PM',
    duration: '3:25',
  },
  {
    key: '23',
    company: 'Epik Secure',
    serialNumber: '000251280023',
    caller: '+18506820023',
    destination: '+18504332023',
    callType: 'Voice',
    direction: 'Outbound',
    date: 'Aug 23, 2013',
    time: '11:55PM',
    duration: '0:50',
  },
  {
    key: '24',
    company: 'Epik Switch',
    serialNumber: '000251280024',
    caller: '+18506820024',
    destination: '+18504332024',
    callType: 'Fax',
    direction: 'Inbound',
    date: 'Aug 24, 2013',
    time: '01:05AM',
    duration: '2:45',
  },
  {
    key: '25',
    company: 'Epik Voice',
    serialNumber: '000251280025',
    caller: '+18506820025',
    destination: '+18504332025',
    callType: 'Voice',
    direction: 'Inbound',
    date: 'Aug 25, 2013',
    time: '02:15AM',
    duration: '1:20',
  },
  {
    key: '26',
    company: 'Epik Digital',
    serialNumber: '000251280026',
    caller: '+18506820026',
    destination: '+18504332026',
    callType: 'Fax',
    direction: 'Outbound',
    date: 'Aug 26, 2013',
    time: '03:25AM',
    duration: '2:10',
  },
  {
    key: '27',
    company: 'Epik Systems',
    serialNumber: '000251280027',
    caller: '+18506820027',
    destination: '+18504332027',
    callType: 'Voice',
    direction: 'Inbound',
    date: 'Aug 27, 2013',
    time: '04:35AM',
    duration: '4:15',
  },
  {
    key: '28',
    company: 'Epik Com',
    serialNumber: '000251280028',
    caller: '+18506820028',
    destination: '+18504332028',
    callType: 'Fax',
    direction: 'Outbound',
    date: 'Aug 28, 2013',
    time: '05:45AM',
    duration: '3:30',
  },
  {
    key: '29',
    company: 'Epik Networks',
    serialNumber: '000251280029',
    caller: '+18506820029',
    destination: '+18504332029',
    callType: 'Voice',
    direction: 'Inbound',
    date: 'Aug 29, 2013',
    time: '06:55AM',
    duration: '0:40',
  },
  {
    key: '30',
    company: 'Epik Company Dummy',
    serialNumber: '000251280030',
    caller: '+18506820030',
    destination: '+18504332030',
    callType: 'Fax',
    direction: 'Outbound',
    date: 'Aug 30, 2013',
    time: '08:05AM',
    duration: '1:15',
  },
];
