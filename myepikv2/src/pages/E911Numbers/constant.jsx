import { Button, <PERSON>lex, Tooltip, Typography } from '@/components';

export function getColumns({ onDetailsClick }) {
  const renderActionButtons = () => (
    <Flex
      align="center"
      style={{ height: '16px' }}
      onClick={(e) => e?.stopPropagation()}
    >
      <Button size="small" onClick={onDetailsClick}>
        <Typography className="small-text" style={{ color: 'white' }}>
          Query
        </Typography>
      </Button>
    </Flex>
  );

  return [
    {
      title: <Typography className="small-text">Number</Typography>,
      dataIndex: 'number',
      key: 'number',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Company</Typography>,
      dataIndex: 'company',
      key: 'company',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Type</Typography>,
      dataIndex: 'type',
      key: 'type',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">e911 Address</Typography>,
      key: 'e911Address',
      render: renderActionButtons,
    },
  ];
}

export const dataSource = [
  {
    key: '1',
    number: '*********',
    company: 'Epik Company Name',
    type: 'Alarm',
  },
  {
    key: '2',
    number: '*********',
    company: 'Epik Company Name',
    type: 'Alarm',
  },
  {
    key: '3',
    number: '*********',
    company: 'Epik Company Name',
    type: 'Alarm',
  },
  {
    key: '4',
    number: '*********',
    company: 'Epik Company Name',
    type: 'Alarm',
  },
  {
    key: '5',
    number: '*********',
    company: 'Epik Company Name',
    type: 'Alarm',
  },
  {
    key: '6',
    number: '*********',
    company: 'Epik Company Name',
    type: 'Alarm',
  },
  {
    key: '7',
    number: '*********',
    company: 'Epik Company Name',
    type: 'Alarm',
  },
  {
    key: '8',
    number: '*********',
    company: 'Epik Company Name',
    type: 'Alarm',
  },
  {
    key: '9',
    number: '*********',
    company: 'Epik Company Name',
    type: 'Alarm',
  },
  {
    key: '10',
    number: '*********',
    company: 'Epik Company Name',
    type: 'Alarm',
  },
  {
    key: '11',
    number: '*********',
    company: 'Epik Company Name',
    type: 'Alarm',
  },
  {
    key: '12',
    number: '*********',
    company: 'Epik Company Name',
    type: 'Alarm',
  },
  {
    key: '13',
    number: '*********',
    company: 'Epik Company Name',
    type: 'Alarm',
  },
  {
    key: '14',
    number: '*********',
    company: 'Epik Company Name',
    type: 'Alarm',
  },
  {
    key: '15',
    number: '*********',
    company: 'Epik Company Name',
    type: 'Alarm',
  },
  {
    key: '16',
    number: '*********',
    company: 'Epik Company Name',
    type: 'Alarm',
  },
  {
    key: '17',
    number: '*********',
    company: 'Epik Company Name',
    type: 'Alarm',
  },
  {
    key: '18',
    number: '*********',
    company: 'Epik Company Name',
    type: 'Alarm',
  },
  {
    key: '19',
    number: '*********',
    company: 'Epik Company Name',
    type: 'Alarm',
  },
  {
    key: '20',
    number: '*********',
    company: 'Epik Company Name',
    type: 'Alarm',
  },
  {
    key: '21',
    number: '*********',
    company: 'Epik Company Name',
    type: 'Alarm',
  },
];
