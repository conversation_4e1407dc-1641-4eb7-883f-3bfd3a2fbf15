import { Flex } from 'antd';
import { SearchIcon, Input, CustomDropDown, PageTitle } from '@/components';
import E911NumbersTable from './E911NumbersTable';

const menuItems = [];

const E911Numbers = () => {
  const handleMenuClick = (e) => {
    if (e.key) {
      console.log(`Click on menu item ${e.key}`);
    }
  };

  return (
    <Flex vertical gap={8} style={{ height: '100%' }}>
      <Flex
        align="center"
        wrap
        justify="space-between"
        style={{ padding: '0 12px' }}
      >
        <PageTitle pageTitle={'E911'} />
        <Flex align="center" wrap gap={6}>
          <CustomDropDown
            filterLabel="Company"
            menuItems={menuItems}
            handleMenuClick={handleMenuClick}
          />
          <Input
            prefix={<SearchIcon />}
            placeholder="Search"
            style={{ maxWidth: '165px' }}
          />
        </Flex>
      </Flex>
      <div style={{ padding: '0 12px' }} className="custom-scrollbar">
        <Flex
          vertical
          flex={1}
          style={{
            background: 'white',
            borderRadius: '12px',
            boxShadow: '0px 1px 2px 0px #1018280F, 0px 1px 3px 0px #1018281A',
          }}
        >
          <Flex vertical style={{ marginTop: '20px', height: '100%' }}>
            <E911NumbersTable />
          </Flex>
        </Flex>
      </div>
    </Flex>
  );
};

export default E911Numbers;
