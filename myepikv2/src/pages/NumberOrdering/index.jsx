import {
  AnimatedTab,
  Button,
  CustomDropDown,
  Flex,
  PageTitle,
  SearchIcon,
  Typography,
} from '@/components';
import { useStore } from '@/store';
import { Col, Input, Row } from 'antd';
import PropTypes from 'prop-types';
import React, { useState } from 'react';
import NumberOrderSearch from './NumberOrderSearch';
import ProvisionTable from './ProvisionTable';
import OrderHistoryTable from './OrderHistoryTable';
import OrderHistoryDetails from './OrderHistoryDetails';

const Field = ({ label, component, bottomLabel }) => (
  <Flex vertical gap={8}>
    <Typography className="small-text" style={{ color: 'var(--primary-gray)' }}>
      {label}
    </Typography>
    {component}
    {bottomLabel && (
      <Typography className="extra-small-text">{bottomLabel}</Typography>
    )}
  </Flex>
);

// Add PropTypes for Field Component
Field.propTypes = {
  label: PropTypes.string.isRequired,
  component: PropTypes.node.isRequired,
  bottomLabel: PropTypes.string.isRequired,
};

const NumberOrdering = () => {
  const [formValues, setFormValues] = useState({});
  const [numberOrderingTab, setNumberOrderingTab] = useState('Search');
  const { openDrawer } = useStore((state) => state.drawer);

  const handleChange = (field, value) => {
    setFormValues((prev) => ({ ...prev, [field]: value }));
  };

  const renderContent = () => {
    switch (numberOrderingTab) {
      case 'Search':
        return <NumberOrderSearch />;
      case 'Provision':
        return <ProvisionTable />;
      case 'Order History':
        return (
          <OrderHistoryTable
            handleAction={() => drawerConfig.orderHistoryDetails()}
          />
        );
      default:
        return null;
    }
  };

  const drawerConfig = {
    orderHistoryDetails: () => openDrawer(OrderHistoryDetails, {}),
  };

  const sections = [
    {
      title: '',
      fields: [
        {
          name: 'quantity',
          label: 'Quantity',
          placeholder: 'Please Specify',
          span: 12,
        },
        {
          name: 'country',
          label: 'Country',
          placeholder: 'Please Specify',
          span: 12,
        },
        {
          name: 'state',
          label: 'State',
          placeholder: 'Please Specify',
          span: 12,
        },
        {
          name: 'numberMask',
          label: 'Number Mask',
          placeholder: '-',
          component: (
            <Flex>
              <Input.OTP mask="-" length={10} size="middle" />
            </Flex>
          ),
          span: 12,
        },
      ],
    },
  ];
  return (
    <Flex vertical gap={8} style={{ height: '100%' }}>
      <Flex
        align="center"
        wrap
        justify="space-between"
        style={{ padding: '0 12px' }}
      >
        <PageTitle pageTitle={'Number Ordering'} />
        {numberOrderingTab === 'Order History' && (
          <Flex gap={6}>
            <CustomDropDown filterLabel="User" />
            <Input
              prefix={<SearchIcon />}
              placeholder="Search"
              style={{
                maxWidth: '165px',
                paddingTop: '5px',
                paddingBottom: '5px',
              }}
            />
            <Button>Search</Button>
            <Button>Clear</Button>
          </Flex>
        )}
      </Flex>
      <Row
        gutter={[8, 16]}
        style={{ height: '100%', padding: '0 12px' }}
        className="custom-scrollbar"
      >
        {numberOrderingTab === 'Search' && (
          <Col xs={24} sm={8} lg={8} align="center" justifyContent="center">
            <Flex
              vertical
              flex="1"
              style={{
                border: '1px solid var(--border-grey)',
                boxShadow: '0px 1px 3px 0px var(--box-shadow)',
                background: 'white',
                borderRadius: '8px',
                padding: '16px',
                height: '100%',
              }}
              gap={4}
            >
              <Typography
                className="heading-four-bold"
                style={{ marginBottom: '8px' }}
              >
                Number Search
              </Typography>
              <Flex vertical gap={8}>
                {sections.map((section, sectionIndex) => (
                  <React.Fragment key={`section-${sectionIndex}`}>
                    {section.fields.map((field) => (
                      <>
                        {Field({
                          label: field.label,
                          component: field.component || (
                            <Input
                              placeholder={field.placeholder}
                              value={formValues[field.name]}
                              onChange={(e) =>
                                handleChange(field.name, e.target.value)
                              }
                              className="text-medium-regular"
                            />
                          ),
                          bottomLabel: field?.bottomLabel,
                        })}
                      </>
                    ))}
                  </React.Fragment>
                ))}
                <Flex gap={8} justify="center" style={{ marginTop: '12px' }}>
                  <Button variant="outlined" style={{ width: '50%' }}>
                    Clear
                  </Button>
                  <Button style={{ width: '50%' }}>Search</Button>
                </Flex>
              </Flex>
            </Flex>
          </Col>
        )}
        <Col
          xs={24}
          sm={numberOrderingTab === 'Search' ? 16 : 24}
          lg={numberOrderingTab === 'Search' ? 16 : 24}
          align="center"
          justifyContent="center"
        >
          <Flex
            flex="2"
            vertical
            style={{
              border: '1px solid var(--border-grey)',
              boxShadow: '0px 1px 3px 0px var(--box-shadow)',
              background: 'white',
              borderRadius: '8px',
              marginBottom: 16,
              height: '100%',
            }}
          >
            <Flex justify="space-between" style={{ margin: '12px 8px' }}>
              <AnimatedTab
                options={['Search', 'Provision', 'Order History']}
                value={numberOrderingTab}
                onChange={(value) => {
                  setNumberOrderingTab(value);
                }}
                size="default"
              />
            </Flex>
            {renderContent()}
          </Flex>
        </Col>
      </Row>
    </Flex>
  );
};

export default NumberOrdering;
