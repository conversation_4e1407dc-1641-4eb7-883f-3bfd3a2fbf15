import {
  But<PERSON>,
  <PERSON>box,
  Flex,
  <PERSON>,
  Toggle,
  Tooltip,
  Typography,
} from '@/components';

const options = [
  {
    value: 'LSNCARCGR6_3191',
    label: (
      <Typography className="small-text" style={{ marginLeft: '8px' }}>
        LSNCARCGR6_3191
      </Typography>
    ),
  },
  {
    value: 'LSNCARCGR6_3192',
    label: (
      <Typography className="small-text" style={{ marginLeft: '8px' }}>
        LSNCARCGR6_3192
      </Typography>
    ),
  },
  {
    value: 'LSNCARCGR6_3193',
    label: (
      <Typography className="small-text" style={{ marginLeft: '8px' }}>
        LSNCARCGR6_3193
      </Typography>
    ),
  },
  {
    value: 'LSNCARCGR6_3194',
    label: (
      <Typography className="small-text" style={{ marginLeft: '8px' }}>
        LSNCARCGR6_3194
      </Typography>
    ),
  },
  {
    value: 'LSNCARCGR6_3195',
    label: (
      <Typography className="small-text" style={{ marginLeft: '8px' }}>
        LSNCARCGR6_3195
      </Typography>
    ),
  },
];

export function getNumberOrderingSearchColumns() {
  return [
    {
      title: (
        <Typography className="small-text" style={{ marginLeft: '8px' }}>
          Number
        </Typography>
      ),
      dataIndex: 'number',
      key: 'number',
      render: (text) => <Typography className="small-text">{text}</Typography>,
    },
  ];
}

export function getOrderHistoryDetailsColumns() {
  const renderActionButtons = () => (
    <Flex
      align="center"
      style={{ height: '16px' }}
      onClick={(e) => e?.stopPropagation()}
    >
      <Checkbox />
    </Flex>
  );

  return [
    {
      title: (
        <Typography className="small-text" style={{ marginLeft: '8px' }}>
          Number
        </Typography>
      ),
      dataIndex: 'number',
      key: 'number',
      render: (text) => <Typography className="small-text">{text}</Typography>,
      width: 110,
    },
    {
      title: <Typography className="small-text">Destination</Typography>,
      dataIndex: 'destination',
      key: 'destination',
      render: () => <Toggle size="small" />,
      width: 100,
    },
    {
      title: <Typography className="small-text">Route</Typography>,
      dataIndex: 'route',
      key: 'route',
      render: (text) => <Typography className="small-text">{text}</Typography>,
      width: 120,
    },
    {
      title: <Typography className="small-text">Receiver Company</Typography>,
      dataIndex: 'receiverCompany',
      key: 'receiverCompany',
      render: (text) => <Typography className="small-text">{text}</Typography>,
      width: 150,
    },
    {
      title: <Typography className="small-text">Ordered By</Typography>,
      dataIndex: 'orderedBy',
      key: 'orderedBy',
      render: (text) => <Typography className="small-text">{text}</Typography>,
      width: 150,
    },
    {
      title: <Typography className="small-text">Temp Date</Typography>,
      dataIndex: 'tempDate',
      key: 'tempDate',
      render: () => renderActionButtons(),
      width: 100,
    },
  ];
}

export function orderHistoryGetColumns({ onDetailsClick }) {
  const renderActionButtons = () => (
    <Flex
      align="center"
      style={{ height: '16px' }}
      onClick={(e) => e?.stopPropagation()}
    >
      <Button size="small" onClick={onDetailsClick}>
        <Typography className="small-text" style={{ color: 'white' }}>
          Details
        </Typography>
      </Button>
    </Flex>
  );

  return [
    {
      title: (
        <Typography className="small-text" style={{ marginLeft: '8px' }}>
          Order Number
        </Typography>
      ),
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">By User</Typography>,
      dataIndex: 'byUser',
      key: 'byUser',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: (
        <Typography className="small-text"># of DID&apos;s Ordered</Typography>
      ),
      dataIndex: 'numberDIDOrdered',
      key: 'numberDIDOrdered',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Date</Typography>,
      dataIndex: 'date',
      key: 'date',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: '',
      key: 'order',
      render: renderActionButtons,
      width: 60,
    },
  ];
}

export function provisionGetColumns() {
  const renderActionButtons = () => (
    <Flex
      align="center"
      style={{ height: '16px' }}
      onClick={(e) => e?.stopPropagation()}
    >
      <Button size="small">
        <Typography className="small-text" style={{ color: 'white' }}>
          Order
        </Typography>
      </Button>
    </Flex>
  );

  const renderRoute = (options) => {
    console.log('renderRoute', options);
    return (
      <Flex style={{ width: 'fit-content' }}>
        <Select defaultValue="LSNCARCGR6_3191" options={options} size="small" />
      </Flex>
    );
  };

  return [
    {
      title: <Typography className="small-text">Number</Typography>,
      dataIndex: 'number',
      key: 'number',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: <Typography className="small-text">Call Type</Typography>,
      dataIndex: 'callType',
      key: 'callType',
      render: (options) => renderRoute(options),
    },
    {
      title: <Typography className="small-text">Call Type</Typography>,
      dataIndex: 'callType1',
      key: 'callType1',
      render: (options) => renderRoute(options),
    },
    {
      title: <Typography className="small-text">Call Type</Typography>,
      dataIndex: 'callType2',
      key: 'callType2',
      render: (options) => renderRoute(options),
    },
    {
      title: <Typography className="small-text">Date</Typography>,
      dataIndex: 'date',
      key: 'date',
      render: (text) => (
        <Typography
          ellipsis={{
            tooltip: <Tooltip>{text}</Tooltip>,
          }}
          className="small-text"
        >
          {text}
        </Typography>
      ),
    },
    {
      title: '',
      key: 'order',
      render: renderActionButtons,
      width: 70,
    },
  ];
}

export const NumberOrderingDataSource = [
  {
    key: '1',
    number: '+18506820022',
  },
  {
    key: '2',
    number: '+18506820022',
  },
  {
    number: '+18506820022',
  },
  {
    key: '4',
    number: '+18506820022',
  },
  {
    key: '5',
    number: '+18506820022',
  },
  {
    key: '6',
    number: '+18506820022',
  },
  {
    key: '7',
    number: '+18506820022',
  },
  {
    key: '8',
    number: '+18506820022',
  },
  {
    key: '9',
    number: '+18506820022',
  },
  {
    key: '10',
    number: '+18506820022',
  },
  {
    key: '11',
    number: '+18506820022',
  },
  {
    key: '12',
    number: '+18506820022',
  },
  {
    key: '13',
    number: '+18506820022',
  },
  {
    key: '14',
    number: '+18506820022',
  },
  {
    key: '15',
    number: '+18506820022',
  },
  {
    key: '16',
    number: '+18506820022',
  },
  {
    key: '17',
    number: '+18506820022',
  },
  {
    key: '18',
    number: '+18506820022',
  },
  {
    key: '19',
    number: '+18506820022',
  },
  {
    key: '20',
    number: '+18506820022',
  },
  {
    key: '21',
    number: '+18506820022',
  },
];

export const orderHistoryDetailsDataSource = [
  {
    key: '1',
    number: '+18506820022',
    route: 'NYCMNYBXGR1_4402',
    receiverCompany: 'Epik Company Name',
    orderedBy: '<EMAIL>',
  },
  {
    key: '2',
    number: '+18506820022',
    route: 'NYCMNYBXGR1_4402',
    receiverCompany: 'Epik Company Name',
    orderedBy: '<EMAIL>',
  },
  {
    key: '3',
    number: '+18506820022',
    route: 'NYCMNYBXGR1_4402',
    receiverCompany: 'Epik Company Name',
    orderedBy: '<EMAIL>',
  },
  {
    key: '4',
    number: '+18506820022',
    route: 'NYCMNYBXGR1_4402',
    receiverCompany: 'Epik Company Name',
    orderedBy: '<EMAIL>',
  },
  {
    key: '5',
    number: '+18506820022',
    route: 'NYCMNYBXGR1_4402',
    receiverCompany: 'Epik Company Name',
    orderedBy: '<EMAIL>',
  },
  {
    key: '6',
    number: '+18506820022',
    route: 'NYCMNYBXGR1_4402',
    receiverCompany: 'Epik Company Name',
    orderedBy: '<EMAIL>',
  },
  {
    key: '7',
    number: '+18506820022',
    route: 'NYCMNYBXGR1_4402',
    receiverCompany: 'Epik Company Name',
    orderedBy: '<EMAIL>',
  },
  {
    key: '8',
    number: '+18506820022',
    route: 'NYCMNYBXGR1_4402',
    receiverCompany: 'Epik Company Name',
    orderedBy: '<EMAIL>',
  },
  {
    key: '9',
    number: '+18506820022',
    route: 'NYCMNYBXGR1_4402',
    receiverCompany: 'Epik Company Name',
    orderedBy: '<EMAIL>',
  },
  {
    key: '10',
    number: '+18506820022',
    route: 'NYCMNYBXGR1_4402',
    receiverCompany: 'Epik Company Name',
    orderedBy: '<EMAIL>',
  },
  {
    key: '11',
    number: '+18506820022',
    route: 'NYCMNYBXGR1_4402',
    receiverCompany: 'Epik Company Name',
    orderedBy: '<EMAIL>',
  },
  {
    key: '12',
    number: '+18506820022',
    route: 'NYCMNYBXGR1_4402',
    receiverCompany: 'Epik Company Name',
    orderedBy: '<EMAIL>',
  },
  {
    key: '13',
    number: '+18506820022',
    route: 'NYCMNYBXGR1_4402',
    receiverCompany: 'Epik Company Name',
    orderedBy: '<EMAIL>',
  },
  {
    key: '14',
    number: '+18506820022',
    route: 'NYCMNYBXGR1_4402',
    receiverCompany: 'Epik Company Name',
    orderedBy: '<EMAIL>',
  },
  {
    key: '15',
    number: '+18506820022',
    route: 'NYCMNYBXGR1_4402',
    receiverCompany: 'Epik Company Name',
    orderedBy: '<EMAIL>',
  },
  {
    key: '16',
    number: '+18506820022',
    route: 'NYCMNYBXGR1_4402',
    receiverCompany: 'Epik Company Name',
    orderedBy: '<EMAIL>',
  },
  {
    key: '17',
    number: '+18506820022',
    route: 'NYCMNYBXGR1_4402',
    receiverCompany: 'Epik Company Name',
    orderedBy: '<EMAIL>',
  },
  {
    key: '18',
    number: '+18506820022',
    route: 'NYCMNYBXGR1_4402',
    receiverCompany: 'Epik Company Name',
    orderedBy: '<EMAIL>',
  },
  {
    key: '19',
    number: '+18506820022',
    route: 'NYCMNYBXGR1_4402',
    receiverCompany: 'Epik Company Name',
    orderedBy: '<EMAIL>',
  },
  {
    key: '20',
    number: '+18506820022',
    route: 'NYCMNYBXGR1_4402',
    receiverCompany: 'Epik Company Name',
    orderedBy: '<EMAIL>',
  },
  {
    key: '21',
    number: '+18506820022',
    route: 'NYCMNYBXGR1_4402',
    receiverCompany: 'Epik Company Name',
    orderedBy: '<EMAIL>',
  },
];

export const orderHistoryDataSource = [
  {
    key: '1',
    orderNumber: 'EPK2501713',
    byUser: 'Shane',
    numberDIDOrdered: '5',
    date: 'Aug 2, 2013',
  },
  {
    key: '2',
    orderNumber: 'EPK2501713',
    byUser: 'Shane',
    numberDIDOrdered: '5',
    date: 'Aug 2, 2013',
  },
  {
    key: '3',
    orderNumber: 'EPK2501713',
    byUser: 'Shane',
    numberDIDOrdered: '5',
    date: 'Aug 2, 2013',
  },
  {
    key: '4',
    orderNumber: 'EPK2501713',
    byUser: 'Shane',
    numberDIDOrdered: '5',
    date: 'Aug 2, 2013',
  },
  {
    key: '5',
    orderNumber: 'EPK2501713',
    byUser: 'Shane',
    numberDIDOrdered: '5',
    date: 'Aug 2, 2013',
  },
  {
    key: '6',
    orderNumber: 'EPK2501713',
    byUser: 'Shane',
    numberDIDOrdered: '5',
    date: 'Aug 2, 2013',
  },
  {
    key: '7',
    orderNumber: 'EPK2501713',
    byUser: 'Shane',
    numberDIDOrdered: '5',
    date: 'Aug 2, 2013',
  },
  {
    key: '8',
    orderNumber: 'EPK2501713',
    byUser: 'Shane',
    numberDIDOrdered: '5',
    date: 'Aug 2, 2013',
  },
  {
    key: '9',
    orderNumber: 'EPK2501713',
    byUser: 'Shane',
    numberDIDOrdered: '5',
    date: 'Aug 2, 2013',
  },
  {
    key: '10',
    orderNumber: 'EPK2501713',
    byUser: 'Shane',
    numberDIDOrdered: '5',
    date: 'Aug 2, 2013',
  },
  {
    key: '11',
    orderNumber: 'EPK2501713',
    byUser: 'Shane',
    numberDIDOrdered: '5',
    date: 'Aug 2, 2013',
  },
  {
    key: '12',
    orderNumber: 'EPK2501713',
    byUser: 'Shane',
    numberDIDOrdered: '5',
    date: 'Aug 2, 2013',
  },
  {
    key: '13',
    orderNumber: 'EPK2501713',
    byUser: 'Shane',
    numberDIDOrdered: '5',
    date: 'Aug 2, 2013',
  },
  {
    key: '14',
    orderNumber: 'EPK2501713',
    byUser: 'Shane',
    numberDIDOrdered: '5',
    date: 'Aug 2, 2013',
  },
  {
    key: '15',
    orderNumber: 'EPK2501713',
    byUser: 'Shane',
    numberDIDOrdered: '5',
    date: 'Aug 2, 2013',
  },
  {
    key: '16',
    orderNumber: 'EPK2501713',
    byUser: 'Shane',
    numberDIDOrdered: '5',
    date: 'Aug 2, 2013',
  },
  {
    key: '17',
    orderNumber: 'EPK2501713',
    byUser: 'Shane',
    numberDIDOrdered: '5',
    date: 'Aug 2, 2013',
  },
  {
    key: '18',
    orderNumber: 'EPK2501713',
    byUser: 'Shane',
    numberDIDOrdered: '5',
    date: 'Aug 2, 2013',
  },
  {
    key: '19',
    orderNumber: 'EPK2501713',
    byUser: 'Shane',
    numberDIDOrdered: '5',
    date: 'Aug 2, 2013',
  },
  {
    key: '20',
    orderNumber: 'EPK2501713',
    byUser: 'Shane',
    numberDIDOrdered: '5',
    date: 'Aug 2, 2013',
  },
  {
    key: '21',
    orderNumber: 'EPK2501713',
    byUser: 'Shane',
    numberDIDOrdered: '5',
    date: 'Aug 2, 2013',
  },
];

export const provisionDataSource = [
  {
    key: '1',
    number: '+18506820022',
    callType: options,
    callType1: options,
    callType2: options,
    date: 'Aug 2, 2013',
  },
  {
    key: '2',
    number: '+18506820022',
    callType: options,
    callType1: options,
    callType2: options,
    date: 'Aug 2, 2013',
  },
  {
    key: '3',
    number: '+18506820022',
    callType: options,
    callType1: options,
    callType2: options,
    date: 'Aug 2, 2013',
  },
  {
    key: '4',
    number: '+18506820022',
    callType: options,
    callType1: options,
    callType2: options,
    date: 'Aug 2, 2013',
  },
  {
    key: '5',
    number: '+18506820022',
    callType: options,
    callType1: options,
    callType2: options,
    date: 'Aug 2, 2013',
  },
  {
    key: '6',
    number: '+18506820022',
    callType: options,
    callType1: options,
    callType2: options,
    date: 'Aug 2, 2013',
  },
  {
    key: '7',
    number: '+18506820022',
    callType: options,
    callType1: options,
    callType2: options,
    date: 'Aug 2, 2013',
  },
  {
    key: '8',
    number: '+18506820022',
    callType: options,
    callType1: options,
    callType2: options,
    date: 'Aug 2, 2013',
  },
  {
    key: '9',
    number: '+18506820022',
    callType: options,
    callType1: options,
    callType2: options,
    date: 'Aug 2, 2013',
  },
  {
    key: '10',
    number: '+18506820022',
    callType: options,
    callType1: options,
    callType2: options,
    date: 'Aug 2, 2013',
  },
  {
    key: '11',
    number: '+18506820022',
    callType: options,
    callType1: options,
    callType2: options,
    date: 'Aug 2, 2013',
  },
  {
    key: '12',
    number: '+18506820022',
    callType: options,
    callType1: options,
    callType2: options,
    date: 'Aug 2, 2013',
  },
  {
    key: '13',
    number: '+18506820022',
    callType: options,
    callType1: options,
    callType2: options,
    date: 'Aug 2, 2013',
  },
  {
    key: '14',
    number: '+18506820022',
    callType: options,
    callType1: options,
    callType2: options,
    date: 'Aug 2, 2013',
  },
  {
    key: '15',
    number: '+18506820022',
    callType: options,
    callType1: options,
    callType2: options,
    date: 'Aug 2, 2013',
  },
  {
    key: '16',
    number: '+18506820022',
    callType: options,
    callType1: options,
    callType2: options,
    date: 'Aug 2, 2013',
  },
  {
    key: '17',
    number: '+18506820022',
    callType: options,
    callType1: options,
    callType2: options,
    date: 'Aug 2, 2013',
  },
  {
    key: '18',
    number: '+18506820022',
    callType: options,
    callType1: options,
    callType2: options,
    date: 'Aug 2, 2013',
  },
  {
    key: '19',
    number: '+18506820022',
    callType: options,
    callType1: options,
    callType2: options,
    date: 'Aug 2, 2013',
  },
  {
    key: '20',
    number: '+18506820022',
    callType: options,
    callType1: options,
    callType2: options,
    date: 'Aug 2, 2013',
  },
  {
    key: '21',
    number: '+18506820022',
    callType: options,
    callType1: options,
    callType2: options,
    date: 'Aug 2, 2013',
  },
];
