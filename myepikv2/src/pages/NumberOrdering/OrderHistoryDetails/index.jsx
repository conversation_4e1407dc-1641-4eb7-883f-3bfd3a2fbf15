import { useState, useEffect } from 'react';
import { Flex, Typography, CloseIcon, DataTable } from '@/components';
import { Divider } from 'antd';
import { useStore } from '@/store';
import {
  getOrderHistoryDetailsColumns,
  orderHistoryDetailsDataSource,
} from '../constant';

const OrderHistoryDetails = () => {
  const { closeDrawer } = useStore((state) => state.drawer);

  const [loading, setLoading] = useState(true);
  const [paginationData, setPaginationData] = useState({
    page: 1,
    totalRecord: orderHistoryDetailsDataSource.length,
    pageSize: 10,
  });
  const [data, setData] = useState([]);

  const onRowClick = () => {
    // navigate(`/edge-devices/${row?.serialNumber}`);
  };

  const onPaginationChange = (page, pageSize) => {
    setPaginationData((prev) => ({
      ...prev,
      page,
      pageSize,
    }));
  };

  const getPaginatedData = () => {
    const { page, pageSize } = paginationData;
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return orderHistoryDetailsDataSource.slice(startIndex, endIndex);
  };

  useEffect(() => {
    setLoading(true);
    const paginatedData = getPaginatedData();
    setTimeout(() => {
      setLoading(false);
      setData(paginatedData);
    }, 0);
  }, [paginationData]);

  const columns = getOrderHistoryDetailsColumns();

  return (
    <Flex vertical>
      <Flex vertical>
        <Flex justify="space-between" align="center">
          <Flex
            gap={8}
            style={{
              padding: '8px',
              background: '#EDF5FB',
              borderRadius: '8px',
            }}
          >
            <Typography className="text-medium-semibold">
              Order Number: EPK2501713
            </Typography>
            <Typography className="text-medium-semibold">
              By User: Patrick Blinn
            </Typography>
            <Typography className="text-medium-semibold">
              Date: 2/10/2025
            </Typography>
          </Flex>
          <Flex align="center" gap={8}>
            <CloseIcon onClick={closeDrawer} style={{ cursor: 'pointer' }} />
          </Flex>
        </Flex>
        <Divider className="divider-sm" />
      </Flex>
      <Flex vertical gap={4} justify="end" style={{ marginTop: '8px' }}>
        <DataTable
          columns={columns}
          data={data}
          onRowClick={onRowClick}
          loading={loading}
          rowSelection={false}
          onRowsSelectionChange={(rowsKeys, rowsData) => {
            console.log('Rows selected:', rowsKeys, rowsData);
          }}
          onPaginationChange={onPaginationChange}
          paginationData={paginationData}
          pageSizeOptions={['10', '25', '50', '100']}
          paginationBtnOutside={true}
          paginationBtnText={['Previous']}
          customPaginationClass={'custom-pagination'}
          paginationAlign="end"
          headerClass="device-table-header-row"
          isPagination={false}
          size="small"
          rowClassName="device-table-content-row"
        />
      </Flex>
    </Flex>
  );
};

export default OrderHistoryDetails;
