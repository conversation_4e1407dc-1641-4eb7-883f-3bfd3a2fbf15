import { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { DataTable } from '@/components';
import {
  getNumberOrderingSearchColumns,
  NumberOrderingDataSource,
} from '../constant';

const NumberOrderSearch = ({ handleAction }) => {
  const [loading, setLoading] = useState(true);
  const [paginationData, setPaginationData] = useState({
    page: 1,
    totalRecord: NumberOrderingDataSource.length,
    pageSize: 10,
  });
  const [data, setData] = useState([]);

  const onPaginationChange = (page, pageSize) => {
    setPaginationData((prev) => ({
      ...prev,
      page,
      pageSize,
    }));
  };

  const handleToggleChange = (checked, record) => {
    console.log(
      `Monitor toggled to ${checked} for device:`,
      record.serialNumber,
    );
  };

  const getPaginatedData = () => {
    const { page, pageSize } = paginationData;
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return NumberOrderingDataSource.slice(startIndex, endIndex);
  };

  useEffect(() => {
    setLoading(true);
    const paginatedData = getPaginatedData();
    setTimeout(() => {
      setLoading(false);
      setData(paginatedData);
    }, 0);
  }, [paginationData]);

  const columns = getNumberOrderingSearchColumns({
    onNameClick: (record, index) => console.log('Row clicked:', record, index),
    onMenuClick: (key) => handleAction(key),
    onToggleChange: handleToggleChange,
  });

  return (
    <DataTable
      columns={columns}
      data={data}
      loading={loading}
      rowSelection={true}
      onRowsSelectionChange={(rowsKeys, rowsData) => {
        console.log('Rows selected:', rowsKeys, rowsData);
      }}
      onPaginationChange={onPaginationChange}
      paginationData={paginationData}
      pageSizeOptions={['10', '25', '50', '100']}
      paginationBtnOutside={true}
      paginationBtnText={['Previous']}
      customPaginationClass={'custom-pagination'}
      paginationAlign="end"
      headerClass="device-table-header-row"
      isPagination={false}
      rowClassName="device-table-content-row"
    />
  );
};

// Add PropTypes validation
NumberOrderSearch.propTypes = {
  handleAction: PropTypes.func.isRequired, // `handleAction` is required and must be a function
};

export default NumberOrderSearch;
