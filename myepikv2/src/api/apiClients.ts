import logger from '@utils/logger';
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { APIS, API_BASE_URLS, API_URL_TOKEN_PREF } from '../constants';
import { ApiClientMap, ApiClientOptions, ApiRequestOptions } from '@/types/apiClient';

export const apiClients: ApiClientMap = {};
const DEFAULT_TIMEOUT = 30000; // 30 seconds

/**
 * Gets the appropriate token for the given API
 * @param {string} api - API identifier
 * @returns {string|null} Authentication token
 */
const getToken = (api: string): string | null => {
  try {
    if (API_URL_TOKEN_PREF[api] === 'myepik') {
      const { VITE_TEST_TOKEN } = import.meta.env;
      const myEpikToken = localStorage.getItem('jwtToken') || VITE_TEST_TOKEN;
      return myEpikToken;
    }
    return null;
  } catch (error) {
    logger.error(`<PERSON>rror getting token for API ${api}:`, error);
    return null;
  }
};

/**
 * Creates an axios instance for the given API
 * @param {string} api - API identifier
 * @param {ApiClientOptions} options - Custom client options
 * @returns {AxiosInstance} Axios instance
 */
const createApiClient = (
  api: string,
  options: ApiClientOptions = {},
): AxiosInstance => {
  if (!API_BASE_URLS[api]) {
    throw new Error(`Invalid API: ${api}. Base URL not found.`);
  }

  const {
    timeout = DEFAULT_TIMEOUT,
    headers: customHeaders = {},
    withCredentials = true,
  } = options;

  const token = getToken(api);
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...customHeaders,
  };

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  const client = axios.create({
    baseURL: API_BASE_URLS[api],
    timeout,
    headers,
    withCredentials,
  });

  client.interceptors.response.use(
    (response: AxiosResponse) => {
      return response.data;
    },
    (error) => {
      if (!error.response) {
        logger.error('Network error:', error);
        return Promise.reject(
          new Error('Network error. Please check your connection.'),
        );
      }

      const statusCode = error.response.status;
      const errorData = error.response.data;

      logger.error(`API Error (${statusCode}):`, {
        url: error.config.url,
        method: error.config.method,
        data: error.config.data,
        response: errorData,
      });

      switch (statusCode) {
        case 401:
          return Promise.reject(
            new Error('Session expired. Please log in again.'),
          );

        case 403:
          return Promise.reject(
            new Error('You do not have permission to perform this action.'),
          );

        case 404:
          return Promise.reject(
            new Error('The requested resource was not found.'),
          );

        case 429:
          return Promise.reject(
            new Error('Too many requests. Please try again later.'),
          );

        case 500:
        case 502:
        case 503:
        case 504:
          return Promise.reject(
            new Error('Server error. Please try again later.'),
          );

        default:
          return Promise.reject(
            new Error(
              errorData?.error ||
                errorData?.message ||
                errorData?.errorMessage ||
                error.message ||
                'An unknown error occurred',
            ),
          );
      }
    },
  );

  return client;
};

/**
 * Generate all API clients
 * @param {Record<string, ApiClientOptions>} options - Custom options for client creation
 * @returns {void}
 */
export const generateAllApiClients = (
  options: Record<string, ApiClientOptions> = {},
): void => {
  try {
    Object.values(APIS).forEach((api) => {
      apiClients[api] = createApiClient(api, options[api] || {});
    });
    logger.info('All API clients configured successfully');
  } catch (error) {
    logger.error('Failed to generate API clients:', error);
    throw error;
  }
};

/**
 * Get a specific API client
 * @param {string} api - API identifier
 * @returns {AxiosInstance} API client instance
 */
export const getApiClient = (api: string = APIS.AUTH): AxiosInstance => {
  if (!apiClients[api]) {
    apiClients[api] = createApiClient(api);
  }
  return apiClients[api];
};

/**
 * Wrapper function for API calls
 * @param {ApiRequestOptions} options - Request options
 * @returns {Promise<any>} API response
 */
export async function apiClient<T = any>(options: ApiRequestOptions): Promise<T> {
  const {
    api = APIS.AUTH,
    endpoint,
    method = 'GET',
    body,
    headers,
    timeout,
    withCredentials,
    onUploadProgress,
    params = {},
    raw = false,
    responseType,
  } = options;

  if (!endpoint) {
    throw new Error('Endpoint is required');
  }

  if (raw) {
    const baseUrl = API_BASE_URLS[api].replace(/\/+$/, '');
    const path = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    const queryString = new URLSearchParams(params).toString();
    const url = `${baseUrl}${path}${queryString ? `?${queryString}` : ''}`;

    const token = getToken(api);
    const isForm = typeof FormData !== 'undefined' && body instanceof FormData;

    const updatedHeaders: Record<string, string> = {
      ...(headers || {}),
      ...(token ? { Authorization: `Bearer ${token}` } : {}),
    };

    let fetchBody: BodyInit | undefined = undefined;
    if (isForm) {
      fetchBody = body as FormData;
    } else if (body !== undefined) {
      updatedHeaders['Content-Type'] = 'application/json';
      fetchBody = JSON.stringify(body);
    }

    const creds: RequestCredentials =
      withCredentials === false ? 'same-origin' : 'include';

    const response = await fetch(url, {
      method,
      headers: updatedHeaders,
      signal: (options as any)?.signal,
      body: fetchBody,
      credentials: creds,
    });

    return response as unknown as T;
  }
  try {
    const client = getApiClient(api);
    const url = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    const isFormData = body instanceof FormData;

    const config: AxiosRequestConfig = {
      method,
      url,
      ...(timeout && { timeout }),
      ...(withCredentials && { withCredentials }),
      ...(onUploadProgress && { onUploadProgress }),
      ...(headers && { headers }),
      params,
      ...(responseType && { responseType }),
    };

    if (body) {
      config.data = body;

      if (isFormData) {
        config.headers = {
          ...config.headers,
          'Content-Type': 'multipart/form-data',
        };
      }
    }

    return client(config)
  } catch (error) {
    logger.error(`API Client Error (${api}/${endpoint}):`, error);
    throw error;
  }
}
