import { API_BASE_URLS } from '@/constants';
import { createClient } from 'graphql-ws'; // Example for graphql-ws

export const createSubscriptionClient = (
  api: string,
  endpoint: string = 'query',
) => {
  return createClient({
    url: `${API_BASE_URLS[api]}${endpoint}`,
    shouldRetry: () => true,
    on: {
      closed: (event) => {
        console.warn('WebSocket closed', event);
      },
    },
    lazy: false,
    keepAlive: 12000,
    connectionParams: {},
  });
};
