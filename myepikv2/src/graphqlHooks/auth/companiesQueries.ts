import { graphql } from "@/graphql/generated/auth-api";

export const ListCompaniesQuery = graphql(`
  query ListCompanies($input: ListCompanyInput!) {
    ListCompanies(input: $input) {
      pagination {
        totalPages
        count
      }
      docs {
        _id
        name
        epikCustomerId
        companyState 
        companyType
        managedDevices
        monitoredDevices
        enterpriseNames
        createdAt
      }
    }
  }
`);

export const ListCompaniesWithIdNameQuery = graphql(`
    query ListCompaniesWithIdName($input: ListCompanyInput!) {
      ListCompanies(input: $input) {
        pagination {
          totalPages
          count
        }
        docs {
          _id
          name
        }
      }
    }
  `);