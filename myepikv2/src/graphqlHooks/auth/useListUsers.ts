import { APIS } from '@/constants';
import { graphql } from '@/graphql/generated/auth-api';
import { ListUserInput, ListUserResponse, QueryListUsersArgs } from '@/graphql/generated/auth-api/graphql';
import { useGraphQLQuery } from '@/hooks';
import { GraphQLQueryOptions } from '@/types/apiClient';
import { UseQueryResult } from '@tanstack/react-query';

const ListUserQuery = graphql(`
  query ListUsers($input: ListUserInput!) {
    ListUsers(input: $input) {
      pagination {
        totalPages
        count
      }
      docs {
        _id
        name
        email
        twoFactor
        registerDate
        enabled
        companyDoc {
            _id
            name
          }
      }
    }
  }
`);

export const listUserQueryKey = 'listUsers1';

export const useListUser = (input: QueryListUsersArgs, keepPreviousData: boolean = true) => {
  let options: GraphQLQueryOptions = {
    api: APIS.AUTH,
    query: ListUserQuery,
    queryKey: [listUserQueryKey, input],
    variables: input,
    keepPreviousData: keepPreviousData,
  };
  options.api = APIS.AUTH;
  options.query = ListUserQuery;

  return useGraphQLQuery(options) as UseQueryResult<ListUserResponse, Error>;
};
