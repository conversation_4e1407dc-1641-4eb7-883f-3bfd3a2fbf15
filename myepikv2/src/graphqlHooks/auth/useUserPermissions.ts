import { APIS } from '@/constants';
import { graphql } from '@/graphql/generated/auth-api';
import {
  ListUserPermissionQuery,
} from '@/graphql/generated/auth-api/graphql';
import { useGraphQLQuery } from '@/hooks';
import { GraphQLQueryOptions } from '@/types/apiClient';
import { UseQueryResult } from '@tanstack/react-query';

const ListUserPermission = graphql(`
  query ListUserPermission {
    ListUserPermission {
      menuItems {
        title
        resourceKey
        childs {
          title
          resourceKey
        }
      }
      uiSchema {
        resources {
          resourceKey
          features {
            feature
            meta {
              read
              write
              resourceKey
            }
          }
        }
      }
    }
  }
`);

export const listUserPermissionQueryKey = 'user-permissions';

export const useUserPermissions = (keepPreviousData: boolean = false) => {
  let options: GraphQLQueryOptions = {
    api: APIS.AUTH,
    query: ListUserPermission,
    queryKey: [listUserPermissionQueryKey],
    keepPreviousData: keepPreviousData,
  };
  options.api = APIS.AUTH;
  options.query = ListUserPermission;

  return useGraphQLQuery(options) as UseQueryResult<ListUserPermissionQuery, Error>;
};
