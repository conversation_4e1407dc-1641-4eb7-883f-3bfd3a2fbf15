import { APIS } from '@/constants';
import { ListCompanyResponse, QueryListCompaniesArgs } from '@/graphql/generated/auth-api/graphql';
import { useGraphQLQuery } from '@/hooks';
import { GraphQLQueryOptions } from '@/types/apiClient';
import { UseQueryResult } from '@tanstack/react-query';
import { ListCompaniesQuery, ListCompaniesWithIdNameQuery } from './companiesQueries';


export const queryMap = {
  ListCompaniesQuery,
  ListCompaniesWithIdNameQuery,
} as const;

type QueryKey = keyof typeof queryMap;

export const listCompaniesQueryKey = 'list-companies-query';

export const useListCompanies = (input: QueryListCompaniesArgs, queryRun: QueryKey = 'ListCompaniesQuery', keepPreviousData: boolean = true) => {
  const  currentQuery = queryMap[queryRun]
  let options: GraphQLQueryOptions = {
    api: APIS.AUTH,
    query: currentQuery,
    queryKey: [listCompaniesQueryKey, input],
    variables: input,
    keepPreviousData: keepPreviousData,
  };
  options.api = APIS.AUTH;
  options.query = currentQuery;

  return useGraphQLQuery(options) as UseQueryResult<ListCompanyResponse, Error>;
};
