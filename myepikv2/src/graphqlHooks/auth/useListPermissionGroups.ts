import { APIS } from '@/constants';
import { graphql } from '@/graphql/generated/auth-api';
import {
  ListPermissionGroupsQuery,
} from '@/graphql/generated/auth-api/graphql';
import { useGraphQLQuery } from '@/hooks';
import { GraphQLQueryOptions } from '@/types/apiClient';
import { UseQueryResult } from '@tanstack/react-query';

const ListPermissionGroups = graphql(`
  query ListPermissionGroups {
    ListPermissionGroups {
      options{
        label
        value
    }
    docs{
      title
      color
      permissions{
        permission
        schema{
          read
          write
        }
      }
    }
    
    }
  }
`);

export const ListPermissionGroupsQueryKey = 'use-permission-groups';

export const useListPermissionGroups = (keepPreviousData: boolean = false) => {
  let options: GraphQLQueryOptions = {
    api: APIS.AUTH,
    query: ListPermissionGroups,
    queryKey: [ListPermissionGroupsQueryKey],
    keepPreviousData: keepPreviousData,
  };
  options.api = APIS.AUTH;
  options.query = ListPermissionGroups;

  return useGraphQLQuery(options) as UseQueryResult<ListPermissionGroupsQuery, Error>;
};
