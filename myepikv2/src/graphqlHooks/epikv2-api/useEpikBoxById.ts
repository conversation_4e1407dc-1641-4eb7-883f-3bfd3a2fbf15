import { APIS } from '@/constants';
import { graphql } from '@/graphql/generated/epikv2-api';
import { EpikBoxByIdQuery } from '@/graphql/generated/epikv2-api/graphql';
import { useGraphQLQuery } from '@/hooks';
import { GraphQLQueryOptions } from '@/types/apiClient';
import { epikBoxByIdKey } from '@/utils/helpers';
import { UseQueryResult } from '@tanstack/react-query';

const epikBoxByIdQuery = /* GraphQL */ graphql(`
  query EpikBoxById($id: String!) {
    EpikBoxById(id: $id) {
      _id
      displayName
      serialNumber
      vpnAddress
      powerState
      livePowerState
      activeInterface
      signalStrength
      liveSignalStrength
      liveSignalStrength
      liveActiveInterface
      monitor
      publicIp
      livePublicIp
      simStatus
      liveSimStatus
      deviceOnline
      liveDeviceOnline
      lanIp
      liveLanIp
      lteIp
      lteIp2
      registered
      activeCarrier
      datacenter
      lteAnalyzer,
      speedTestData {
        latency
        jitter
        uploadSpeed
        downloadSpeed
      }
      speedTestVoice {
        latency
        jitter
        uploadSpeed
        downloadSpeed
      }
      preferredProviderTest {
        SimsInfo {
          Error
          Jitter
          PacketLoss
          PingAvg
          SIM
        }
        TimeStamp
        initiatedTimeStamp
      }

      liveSpeedTestData {
        latency
        jitter
        uploadSpeed
        downloadSpeed
      }
      liveSpeedTestVoice {
        latency
        jitter
        uploadSpeed
        downloadSpeed
      }
      livePreferredProviderTest{
        SimsInfo {
          Error
          Jitter
          PacketLoss
          PingAvg
          SIM
        }
        TimeStamp
        initiatedTimeStamp
      }
      sensorData {
        power
        temp
      } 
      liveSensorData {
        power
        temp
      } 
      myEpik
      model
      apuType
      powerSaveOption
      starCodes
      boxOfflineCallForward
      recording
      OBEnable
      eth3Disable
      nightlyUpdateTime
      ext_9override
      boxRegistrar
      e911Number
      primarySim
      priorityInterface
      portAlerts
      currentApn
      epiTimezone
      features {
        showPriTab
        pri
        sip
        twoModems
        daisyChain
        dcAutoUpdate
      }
      wifiStatus {
        Error_Msg
        Gateway
        IP
        Mode
        Password
        SSID
        Sec_Mode
        Status
        Subnet
      }
      liveWifiStatus {
        Error_Msg
        Gateway
        IP
        Mode
        Password
        SSID
        Sec_Mode
        Status
        Subnet
      }
      networkInfo {
        dns
        interfaces {
          interface
          internet
          icmp
          wg
        }
        timestamp
        error
      }
      liveNetworkInfo {
        dns
        interfaces {
          interface
          internet
          icmp
          wg
        }
        timestamp
        error
      }
      
      priSettings {
        isSynced
        pbx
        priFailovers
        echoChannels
        channels
        framing
        lineCode
        timeAndSource
        dChannel
        bChannels
        switchType
        echoCancellation
        echoCancellationType
        numberSettings {
          dtmfType
          dnis
          callerIdName
          callerIdNumber
          numbers {
            _id
          }
        }
        e911Number
        shippedAddress {
          attn
          street1
          street2
          city
          state
          zip
        }
      }
      sipSettings {
        casFailovers
        channels
        framing
        lineCode
        timeAndSource
        bChannel
        switchType
        echoCancellation
        echoCancellationType
        channelRange
        authType
        ip
        username
        password
        numberSettings {
          dtmfType
          dnis
          callerIdName
          callerIdNumber
          numbers {
            _id
          }
        }
        e911Number
        shippedAddress {
          attn
          street1
          street2
          city
          state
          zip
        }
      }
      dcAvgPing {
          chPingAvg
          laPingAvg
          nyPingAvg
          atPingAvg
          dlPingAvg
          bestDC
          bestLatency
          timeUpdated
          error
       }
      liveDcAvgPing {
        chPingAvg
          laPingAvg
          nyPingAvg
          atPingAvg
          dlPingAvg
          bestDC
          bestLatency
          timeUpdated
          error
      }
      modemInfo {
        carrier
        carrier_placeholder
        imei
        imei_placeholder
        ipAddress
        ipAddress_placeholder
        manufacturer
        manufacturer_placeholder
        model
        model_placeholder
        sim
        sim_placeholder
      }

      liveModemInfo {
        carrier
        carrier_placeholder
        imei
        imei_placeholder
        ipAddress
        ipAddress_placeholder
        manufacturer
        manufacturer_placeholder
        model
        model_placeholder
        sim
        sim_placeholder
      }
      companyDoc {
        _id
        name
      }
      obiDocs {
        _id
        macAddress
        obiNumber
        deviceId        
        port1 {
          boxPortNumber
          registered
          disabled
          provisionDate
          enableDisableLastUpdatedTime
          updatedOn
          serviceName
          monitored
          callerIdMasking
          callerIdMaskingNumber
          SilenceDetectSensitivity
          OnHookTipRingVoltage
          OffHookCurrentMax
          DTMFDetectMinLength
          DTMFDetectMinGap
          ChannelTxGain
          ChannelRxGain
          DTMFMethod
          DTMFPlaybackLevel
          RingVoltage
          DigitMapShortTimer
          CPCDelayTime
          CPCDuration
          showVadToggle
          showModemModeToggle
          modemMode
          showT38EnableToggle
          t38Enabled
          showFaxEnabledToggle
          faxEnabled
          showJitterDelay
          JitterMinDelay
          JitterMaxDelay
          showModemOnlyToggle
          modemOnly
          forceForward
          forceForwardNumber
          configOverride
          e911Number
          e911Enabled
          voip
          status
          connected
          remoteHost
          ipAddress
          remotePort
          authName
          assignedNumber
          vmNumber
          advancedModemConfiguration
          portActivated
          portActivatedDate
          startedBilling
          startedBillingDate
          callerIdOverride
          provisionUrl
          blockCallerId
          callWaiting
          configPassword
          assignedNumberRef
          appliedConfigTemplate
          vmBox
          vmailBoxDoc {
            _id
            pin
            totalMessages
            limit
            greeting
            notificationEmails
            notificationNumbers
            number
            numberDoc {
              _id
              number
              linkedBox
              assignedTo
              company
              type
              callerIdName
              isForwarded
              huntPorts
              huntType
              portLabel
              forwardUris
              linkedToObi
              faxDeliveryConfirmation
              savefaxes
              confirmationEmail
              dateCreated
              deleted
              enabled
              cid
              emails
              allowedUsers
              allowedNumbers
              groupMembers
              forwardNumbers
              fallbackEmails
              destination
              cidOverride
              port
              temporary
              greetingFilePath
              agreementFilePath
              carrier
              e911Carrier
              tdmRoute
              trunk
              previousTrunk
              linkedToExternalAta
              alarmRelayInfo
              inBoundCallBlocked
              inbandRouting
              isNumberLocked
              usbPort
              onlyEfax
              isTollFree
              tollFreeNumbers
              provisionDate
              spoofType
              spoofIncomingCid
              trunkSelection
              numDigits
            }
            deleted
            createdAt
            updatedAt
          }
          assignedNumberDoc {
            _id
            number
            linkedBox
            assignedTo
            company
            type
            callerIdName
            isForwarded
            huntPorts
            huntType
            portLabel
            forwardUris
            linkedToObi
            faxDeliveryConfirmation
            savefaxes
            confirmationEmail
            dateCreated
            deleted
            enabled
            cid
            emails
            allowedUsers
            allowedNumbers
            groupMembers
            forwardNumbers
            fallbackEmails
            routeInfo {
              route
              gateway
            }
            previousRouteInfo {
              route
              gateway
            }
            destination
            cidOverride
            port
            temporary
            greetingFilePath
            agreementFilePath
            carrier
            e911Carrier
            tdmRoute
            trunk
            previousTrunk
            linkedToExternalAta
            alarmRelayInfo
            inBoundCallBlocked
            inbandRouting
            isNumberLocked
            usbPort
            onlyEfax
            isTollFree
            tollFreeNumbers
            provisionDate
            spoofType
            spoofIncomingCid
            trunkSelection
            numDigits
            e911Info {
              address1
              address2
              city
              state
              zip
              country
              locationid
              callername
              vendor
              createdAt
              updatedAt
            }
          }
          appliedConfigTemplateDoc {
            _id
            title
            name
            default
            SilenceDetectSensitivity
            OnHookTipRingVoltage
            OffHookCurrentMax
            DTMFDetectMinLength
            DTMFDetectMinGap
            ChannelTxGain
            ChannelRxGain
            DTMFMethod
            DTMFPlaybackLevel
            RingVoltage
            DigitMapShortTimer
            CPCDelayTime
            CPCDuration
            showModemModeToggle
            modemMode
            showT38EnableToggle
            t38Enabled
            showFaxEnabledToggle
            faxEnabled
            showJitterDelay
            JitterMinDelay
            JitterMaxDelay
            inbandRoute
            showInbandRouteToggle
            trunkSelection
            showTrunkSelectionToggle
            vadEnable
            showVadToggle
            showModemOnlyToggle
            modemOnly
            ataType
          }
        }
        port2 {
          boxPortNumber
          registered
          disabled
          provisionDate
          enableDisableLastUpdatedTime
          updatedOn
          serviceName
          monitored
          callerIdMasking
          callerIdMaskingNumber
          SilenceDetectSensitivity
          OnHookTipRingVoltage
          OffHookCurrentMax
          DTMFDetectMinLength
          DTMFDetectMinGap
          ChannelTxGain
          ChannelRxGain
          DTMFMethod
          DTMFPlaybackLevel
          RingVoltage
          DigitMapShortTimer
          CPCDelayTime
          CPCDuration
          showVadToggle
          showModemModeToggle
          modemMode
          showT38EnableToggle
          t38Enabled
          showFaxEnabledToggle
          faxEnabled
          showJitterDelay
          JitterMinDelay
          JitterMaxDelay
          showModemOnlyToggle
          modemOnly
          forceForward
          forceForwardNumber
          configOverride
          e911Number
          e911Enabled
          voip
          status
          connected
          remoteHost
          ipAddress
          remotePort
          authName
          assignedNumber
          vmNumber
          advancedModemConfiguration
          portActivated
          portActivatedDate
          startedBilling
          startedBillingDate
          callerIdOverride
          provisionUrl
          blockCallerId
          callWaiting
          configPassword
          assignedNumberRef
           vmBox
          vmailBoxDoc {
            _id
            pin
            totalMessages
            limit
            greeting
            notificationEmails
            notificationNumbers
            number
            numberDoc {
              _id
              number
              linkedBox
              assignedTo
              company
              type
              callerIdName
              isForwarded
              huntPorts
              huntType
              portLabel
              forwardUris
              linkedToObi
              faxDeliveryConfirmation
              savefaxes
              confirmationEmail
              dateCreated
              deleted
              enabled
              cid
              emails
              allowedUsers
              allowedNumbers
              groupMembers
              forwardNumbers
              fallbackEmails
              destination
              cidOverride
              port
              temporary
              greetingFilePath
              agreementFilePath
              carrier
              e911Carrier
              tdmRoute
              trunk
              previousTrunk
              linkedToExternalAta
              alarmRelayInfo
              inBoundCallBlocked
              inbandRouting
              isNumberLocked
              usbPort
              onlyEfax
              isTollFree
              tollFreeNumbers
              provisionDate
              spoofType
              spoofIncomingCid
              trunkSelection
              numDigits
            }
            deleted
            createdAt
            updatedAt
          }
          assignedNumberDoc {
            _id
            number
            linkedBox
            assignedTo
            company
            type
            callerIdName
            isForwarded
            huntPorts
            huntType
            portLabel
            forwardUris
            linkedToObi
            faxDeliveryConfirmation
            savefaxes
            confirmationEmail
            dateCreated
            deleted
            enabled
            cid
            emails
            allowedUsers
            allowedNumbers
            groupMembers
            forwardNumbers
            fallbackEmails
            routeInfo {
              route
              gateway
            }
            previousRouteInfo {
              route
              gateway
            }
            destination
            cidOverride
            port
            temporary
            greetingFilePath
            agreementFilePath
            carrier
            e911Carrier
            tdmRoute
            trunk
            previousTrunk
            linkedToExternalAta
            alarmRelayInfo
            inBoundCallBlocked
            inbandRouting
            isNumberLocked
            usbPort
            onlyEfax
            isTollFree
            tollFreeNumbers
            provisionDate
            spoofType
            spoofIncomingCid
            trunkSelection
            numDigits
            e911Info {
                address1
                address2
                city
                state
                zip
                country
                locationid
                callername
                vendor
                createdAt
                updatedAt
            }
          }
          appliedConfigTemplate
          appliedConfigTemplateDoc {
            _id
            title
            name
            default
            SilenceDetectSensitivity
            OnHookTipRingVoltage
            OffHookCurrentMax
            DTMFDetectMinLength
            DTMFDetectMinGap
            ChannelTxGain
            ChannelRxGain
            DTMFMethod
            DTMFPlaybackLevel
            RingVoltage
            DigitMapShortTimer
            CPCDelayTime
            CPCDuration
            showModemModeToggle
            modemMode
            showT38EnableToggle
            t38Enabled
            showFaxEnabledToggle
            faxEnabled
            showJitterDelay
            JitterMinDelay
            JitterMaxDelay
            inbandRoute
            showInbandRouteToggle
            trunkSelection
            showTrunkSelectionToggle
            vadEnable
            showVadToggle
            showModemOnlyToggle
            modemOnly
            ataType
          }
        }
      }
      phoneDocs {
        _id
        displayName
        extension
        model
        number
        numberDoc {
          _id
          number
        }
      }
      locationDoc {
        locationName
        _id
      }
      
      sysInfo {
    _id
    boxId
    cdmStatus
    deviceFw
    error
    failoverStatus
    failoverUpdateStatus
    restbinStatus
    diskIo {
      Name
      ReadsCompleted
      WritesCompleted
    }
    diskUsage {
      all
      avail
      error
      free
      size
      used
    }
    loadavg {
      Loadavg1
      Loadavg5
      Loadavg15
      error
    }
    restbinVersion
    time
    uptime {
      uptime
    }
    watchGuardStatus
    wifiInfo {
      ap
      error
      mode
      ssid
    }
    boxFirmware {
      version
    }
    lteSignalStrength {
      signalStrength
    }
    networkInfoDetail {
    activeInterface
    arp {
      keys
      values
    }
    eth0Config {
      nameserver1
      nameserver2
      nameserver3
      nameserver4
    }
    eth0Stats {
      eth0IP
      eth0MAC
      rxErrors
      rxPackets
      txErrors
      txPackets
    }
    fwVersion
    icmp {
      eth0
      wwan0
    }
    linkStatus {
      epiLink
      eth02
    }
    modemCount
    edgeIP
    ip4G
    tunnelAccess {
      eth0
      wwan0
    }
    registrationStatus
    tunnelIP
    powerSource
    lteSgnalStrength
    priCardStatus {
      priCardStatus
    }
    deviceFirmwareVersion {
      version
    }
    epiInfo {
      fwVersion
      ip
      mac
      provUrl
      uptime
    }
    dcAvgPing {
      chPingAvg
      laPingAvg
      nyPingAvg
      atPingAvg
      dlPingAvg
      bestDC
      bestLatency
      timeUpdated
      error
    }
    cdmBoxStatus {
      Status
      LastCdmCheck
      Duration
    }
  }
    cpu {
      CPUCount
      Guest
      GuestNice
      Idle
      Iowait
      irq
      Nice
      Softirq
      StatCount
      Steal
      System
      Total
      User
    }
    memory {
      Active
      Available
      Buffers
      Cached
      Free
      Inactive
      MemAvailableEnabled
      SwapCached
      SwapFree
      SwapTotal
      SwapUsed
      Total
      Used
    }
    network {
      Name
      RxBytes
      TxBytes
    }
    portStatuses {
      eth0
      eth1
      eth2
      wg0
      wg7
      wlan0
      wwan0
    }
    lastUpdated
  }

  liveSysInfo {
    _id
    boxId
    cdmStatus
    deviceFw
    error
    failoverStatus
    failoverUpdateStatus
    restbinStatus
    diskIo {
      Name
      ReadsCompleted
      WritesCompleted
    }
    diskUsage {
      all
      avail
      error
      free
      size
      used
    }
    loadavg {
      Loadavg1
      Loadavg5
      Loadavg15
      error
    }
    restbinVersion
    time
    uptime {
      uptime
    }
    watchGuardStatus
    wifiInfo {
      ap
      error
      mode
      ssid
    }
    boxFirmware {
      version
    }
    lteSignalStrength {
      signalStrength
    }
    networkInfoDetail {
    activeInterface
    arp {
      keys
      values
    }
    eth0Config {
      nameserver1
      nameserver2
      nameserver3
      nameserver4
    }
    eth0Stats {
      eth0IP
      eth0MAC
      rxErrors
      rxPackets
      txErrors
      txPackets
    }
    fwVersion
    icmp {
      eth0
      wwan0
    }
    linkStatus {
      epiLink
      eth02
    }
    modemCount
    edgeIP
    ip4G
    tunnelAccess {
      eth0
      wwan0
    }
    registrationStatus
    tunnelIP
    powerSource
    lteSgnalStrength
    priCardStatus {
      priCardStatus
    }
    deviceFirmwareVersion {
      version
    }
    epiInfo {
      fwVersion
      ip
      mac
      provUrl
      uptime
    }
    dcAvgPing {
      chPingAvg
      laPingAvg
      nyPingAvg
      atPingAvg
      dlPingAvg
      bestDC
      bestLatency
      timeUpdated
      error
    }
    cdmBoxStatus {
      Status
      LastCdmCheck
      Duration
    }
  }
    cpu {
      CPUCount
      Guest
      GuestNice
      Idle
      Iowait
      irq
      Nice
      Softirq
      StatCount
      Steal
      System
      Total
      User
    }
    memory {
      Active
      Available
      Buffers
      Cached
      Free
      Inactive
      MemAvailableEnabled
      SwapCached
      SwapFree
      SwapTotal
      SwapUsed
      Total
      Used
    }
    network {
      Name
      RxBytes
      TxBytes
    }
    portStatuses {
      eth0
      eth1
      eth2
      wg0
      wg7
      wlan0
      wwan0
    }
    lastUpdated
  }

   vSwitchTab {
      registered
      registerationConfigCreated
      portsConfigCreated
      portsInfo {
        port
        calledId
        recording
        trunkType
      }
    }
    liveVSwitchTab {
    registered
    registerationConfigCreated
    portsConfigCreated
    portsInfo {
      port
      calledId
      recording
      trunkType
    }
  }

    }
  }
`);

export const epikBoxDeviceViewQuery = /* GraphQL */ graphql(`
  query EpikBoxDeviceView($id: String!) {
    EpikBoxById(id: $id) {
      _id
      displayName
      serialNumber
      fwVersion
      creationDate
      vpnAddress
      apuType
      model
      lteIp,
      lteIp2,
      primarySim,
      priorityInterface,
      simStatus,
      liveSimStatus
      advancedRouting
      myEpik
      boxRegistrar
      customerProvidedIp
      activeLTE {
        sim
        imei
        mtn
        ip
        iccid
        timeStamp
      }
      modems {
        imeis
        label
        phones
        sims
        type
      }
      modemInfo {
        model
        imei
        sim
        ipAddress
      }
    }
  }
`);


export const queryMap = {
  base: epikBoxByIdQuery,
  deviceView: epikBoxDeviceViewQuery,
} as const;

type QueryKey = keyof typeof queryMap;

type UseEpikBoxByIdType = { 
  input: { id: string }, 
  keepPreviousData: boolean, 
  enabled: boolean 
  queryRun?: QueryKey
}

export const useEpikBoxById = ({
  input,
  keepPreviousData = true,
  enabled = true,
  queryRun = 'base',

}: UseEpikBoxByIdType) => {
  const selectedQuery = queryMap[queryRun];

  let options: GraphQLQueryOptions = {
    api: APIS.EPIKV2,
    query: selectedQuery,
    queryKey: epikBoxByIdKey(input.id),
    variables: input,
    keepPreviousData: keepPreviousData,
    enabled,
  };
  (options.api = APIS.EPIKV2), (options.query = selectedQuery);
  return useGraphQLQuery(options) as UseQueryResult<EpikBoxByIdQuery, Error>;
};
