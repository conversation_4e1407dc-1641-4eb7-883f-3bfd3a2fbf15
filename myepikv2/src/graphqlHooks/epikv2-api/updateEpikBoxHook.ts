import { useQueryClient } from '@tanstack/react-query';
import { graphql } from '@/graphql/generated/epikv2-api';
import {
  EpikBoxUpdateHookSubscription,
  EpikBoxUpdateHookSubscriptionVariables,
} from '@/graphql/generated/epikv2-api/graphql';
import { SubscriptionOptions, useGraphQLSubscription } from '@/hooks';
import { useEdgeDeviceStore } from '@/store/edgeDeviceSlice';
import { useStore } from '@/store';
import logger from '@/utils/logger';

const EpikBoxUpdateHook = graphql(`
  subscription EpikBoxUpdateHook($fields: [String!]!, $id: String!) {
    EpikBoxUpdateHook(fields: $fields, id: $id) {
      fieldName
      value
      id
      error
    }
  }
`);

export const useEpikBoxUpdateHook = (
  id: string,
  fields: string[],
  queryKey: string[],
  enabled: boolean = true
) => {
  const queryClient = useQueryClient();
  const setFieldError = useStore.getState().error.setErrorState;
  const resetErrors    = useStore.getState().error.resetErrorState;

  const variables: EpikBoxUpdateHookSubscriptionVariables = { id, fields };

  const options: SubscriptionOptions<
    EpikBoxUpdateHookSubscription,
    EpikBoxUpdateHookSubscriptionVariables
  > = {
    query: EpikBoxUpdateHook,
    variables,
    enabled,
    onData: (data) => {
      const update = data?.EpikBoxUpdateHook;
      if (!update) return;

      const { fieldName, value, error, id: payloadId } = update;

      logger.debug('SUBSCRIPTION UPDATE:', fieldName, value, error, payloadId);

      const errorKey = `sub:${fieldName}`;

      if (typeof error === 'string' && error.trim()?.length > 0) {
        setFieldError(errorKey, error);
      } else {
        setFieldError(errorKey, null);
      }

      queryClient.setQueryData(queryKey, (old: any) => {
        logger.debug('SUBSCRIPTION UPDATE:', fieldName, value, id);

        if (!old?.EpikBoxById) return old;
        logger.debug('OLD VALUE:', old.EpikBoxById);
        if (old.EpikBoxById._id !== payloadId) return old;
        useEdgeDeviceStore.getState().setLoadingState(fieldName);
        return {
          ...old,
          EpikBoxById: {
            ...old.EpikBoxById,
            [fieldName]: value,
          },
        };
      });
    },
    onError: (err) => {
      const message =
        (err as any)?.message ??
        (typeof err === 'string' ? err : 'Subscription error');
      logger.error('Subscription error (EpikBoxUpdateHook):', err);
      setFieldError('sub:allFields', message);
    },
    onSuccess: () => resetErrors(['sub:allFields']),
  };

  return useGraphQLSubscription(options);
};
