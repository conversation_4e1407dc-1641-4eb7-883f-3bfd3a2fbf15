import { APIS } from '@/constants';
import { graphql } from '@/graphql/generated/epikv2-api';
import { EpiDetailByIdQuery } from '@/graphql/generated/epikv2-api/graphql';
import { useGraphQLQuery } from '@/hooks';
import { GraphQLQueryOptions } from '@/types/apiClient';
import { epiDetailByIdKey } from '@/utils/helpers';
import { UseQueryResult } from '@tanstack/react-query';

const epiDetailByIdQuery = /* GraphQL */ graphql(`
 query EpiDetailById($id: String!) {
  EpiDetailById(id: $id) {
    _id
    deviceId
    obiNumber
    macAddress
    assignedTo
    registrationMeta {
      wanInfo {
        placeHolder
        ip
        subnet
        gateway
        dns
      }
      sp1ServiceStatus {
        status
        callState
      }
      sp2ServiceStatus {
        status
        callState
      }
      obiTalkServiceStatus {
        placeHolder
        status
      }
    }
    liveRegistrationMeta {
      wanInfo {
        placeHolder
        ip
        subnet
        gateway
        dns
      }
      sp1ServiceStatus {
        status
        callState
      }
      sp2ServiceStatus {
        status
        callState
      }
      obiTalkServiceStatus {
        placeHolder
        status
      }
    }
    portPhysicalMeta {
      name
      state
      loopCurrent
      Vbat
      tipRingVoltage
      lastCallerInfo
    }
    livePortPhysicalMeta {
      name
      state
      loopCurrent
      Vbat
      tipRingVoltage
      lastCallerInfo
    }
  }
}
`);


export const useEpiDetailById = (
  input: { id: string },
  keepPreviousData: boolean = true,
  enabled: boolean = true,
) => {
  let options: GraphQLQueryOptions = {
    api: APIS.EPIKV2,
    query: epiDetailByIdQuery,
    queryKey: epiDetailByIdKey(input.id),
    variables: input,
    keepPreviousData: keepPreviousData,
    enabled,
  };
  (options.api = APIS.EPIKV2), (options.query = epiDetailByIdQuery);
  return useGraphQLQuery(options) as UseQueryResult<EpiDetailByIdQuery, Error>;
};
