import { APIS } from '@/constants';
import { graphql } from '@/graphql/generated/epikv2-api';
import {
  EpiDocumentPaginationResult,
  QueryListEpiArgs,
} from '@/graphql/generated/epikv2-api/graphql';
import { useGraphQLQuery } from '@/hooks';
import { GraphQLQueryOptions } from '@/types/apiClient';
import { UseQueryResult } from '@tanstack/react-query';

const ListObisQuery = graphql(`
  query ListEpi($input: EpiFilterInput!, $pagination: PaginationInput!) {
    ListEpi(filter: $input, pagination: $pagination) {
      pagination {
        totalPages
        count
      }
      docs {
        _id
        macAddress
        obiNumber
      }
    }
  }
`);

export const listObisQueryKey = 'list-obis';

export const useListObis = (
  input: QueryListEpiArgs,
  keepPreviousData: boolean = true,
) => {
  let options: GraphQLQueryOptions = {
    api: APIS.EPIKV2,
    query: ListObisQuery,
    queryKey: [listObisQueryKey, input],
    variables: input,
    keepPreviousData: keepPreviousData,
  };
  options.api = APIS.EPIKV2;

  return useGraphQLQuery(options) as UseQueryResult<
    EpiDocumentPaginationResult,
    Error
  >;
};
