import { APIS } from '@/constants';
import { graphql } from '@/graphql/generated/epikv2-api';
import {
  QueryListNumbersArgs,
  NumberDocumentPaginationResult,
} from '@/graphql/generated/epikv2-api/graphql';
import { useGraphQLQuery } from '@/hooks';
import { GraphQLQueryOptions } from '@/types/apiClient';
import { UseQueryResult } from '@tanstack/react-query';

const ListNumbersQuery = /* GraphQL */ graphql(`
  query ListNumbers($filter: ListNumberInput!, $pagination: PaginationInput!) {
    ListNumbers(pagination: $pagination, filter: $filter) {
      docs {
        _id
        number
        linkedBox
        company
        callerIdName
      }
      pagination {
        currentPage
        totalPages
        count
      }
    }
  }
`);

export const listNumbersQueryKey = 'list-numbers';

export const useListNumbers = (
  input: QueryListNumbersArgs,
  keepPreviousData: boolean = true,
  enabled: boolean = false,
) => {
  let options: GraphQLQueryOptions = {
    api: APIS.EPIKV2,
    query: ListNumbersQuery,
    queryKey: [listNumbersQueryKey, input],
    variables: input,
    keepPreviousData: keepPreviousData,
    enabled: enabled
  };
  options.api = APIS.EPIKV2;
  options.query = ListNumbersQuery;

  return useGraphQLQuery(options) as UseQueryResult<
    NumberDocumentPaginationResult,
    Error
  >;
};
