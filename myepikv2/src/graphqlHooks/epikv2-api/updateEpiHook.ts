import { useQueryClient } from '@tanstack/react-query';
import { graphql } from '@/graphql/generated/epikv2-api';
import {
  EpiUpdateHookSubscription,
  EpiUpdateHookSubscriptionVariables,
} from '@/graphql/generated/epikv2-api/graphql';
import { SubscriptionOptions, useGraphQLSubscription } from '@/hooks';
import logger from '@/utils/logger';
import { useEpiStore } from '@/store/epiSlice';
import { useStore } from '@/store';

const EpiUpdateHook = graphql(`
  subscription EpiUpdateHook($fields: [String!]!, $id: ID!) {
    EpiUpdateHook(fields: $fields, id: $id) {
      fieldName
      value
      id
      error
    }
  }
`);


export const useEpiUpdateHook = (
  id: string,
  fields: string[],
  queryKey: string[],
) => {
  const queryClient = useQueryClient();

  const setFieldError = useStore.getState().error.setErrorState;
  const resetErrors    = useStore.getState().error.resetErrorState;

  const variables: EpiUpdateHookSubscriptionVariables = { id, fields };

  const options: SubscriptionOptions<
    EpiUpdateHookSubscription,
    EpiUpdateHookSubscriptionVariables
  > = {
    query: EpiUpdateHook,
    variables,
    onData: (data) => {
      const update = data?.EpiUpdateHook;
      if (!update) return;

      const { fieldName, value, error, id: payloadId } = update;

      logger.debug('SUBSCRIPTION UPDATE:', fieldName, value, error, payloadId);

      const errorKey = `sub:${fieldName}`;

      if (typeof error === 'string' && error.trim()?.length > 0) {
        setFieldError(errorKey, error);
      } else {
        setFieldError(errorKey, null);
      }

      queryClient.setQueryData(queryKey, (old: any) => {
        logger.debug('SUBSCRIPTION UPDATE:', fieldName, value, id);

        if (!old?.EpiDetailById) return old;
        logger.debug('OLD VALUE:', old.EpiDetailById);
        if (old.EpiDetailById._id !== payloadId) return old;
        useEpiStore.getState().setLoadingState(fieldName);
        return {
          ...old,
          EpiDetailById: {
            ...old.EpiDetailById,
            [fieldName]: value,
          },
        };
      });
    },
    onError: (err) => {
      const message =
        (err as any)?.message ??
        (typeof err === 'string' ? err : 'Subscription error');
      logger.error('Subscription error (EpiUpdateHook):', err);
      setFieldError('sub:allFields', message);
    },
    onSuccess: () => resetErrors(['sub:allFields']),
  };

  return useGraphQLSubscription(options);
};
