import { APIS } from '@/constants';
import { graphql } from '@/graphql/generated/epikv2-api';
import {
  EpikBoxByIdQuery,
  EpikBoxDocumentPopulatedPaginationResult,
  ListEpikBoxesQuery,
  QueryListEpikBoxesArgs,
} from '@/graphql/generated/epikv2-api/graphql';
import { useGraphQLQuery } from '@/hooks';
import { GraphQLQueryOptions } from '@/types/apiClient';
import { UseQueryResult } from '@tanstack/react-query';

const listEpikBoxesQuery = graphql(`
  query ListEpikBoxes(
    $pagination: PaginationInput
    $filter: EpikBoxFilterInput!
  ) {
    ListEpikBoxes(pagination: $pagination, filter: $filter) {
      docs {
        _id
        serialNumber
        vpnAddress
        creationDate
        deleted
        monitor
        displayName
        numPorts
        companyDoc {
          _id
          name
        }
        locationDoc {
          _id
          locationName
        }
      }
      pagination {
        currentPage
        totalPages
        count
      }
    }
  }
`);

export const listEpikBoxesQueryKey = 'list-epikboxes';

export const useListEpikBoxes = (
  input: QueryListEpikBoxesArgs,
  keepPreviousData: boolean = true,
  enabled: boolean = true,
) => {
  let options: GraphQLQueryOptions = {
    api: APIS.EPIKV2,
    query: listEpikBoxesQuery,
    queryKey: [listEpikBoxesQueryKey, input],
    variables: input,
    keepPreviousData: keepPreviousData,
    enabled,
  };
  (options.api = APIS.EPIKV2), (options.query = listEpikBoxesQuery);

  return useGraphQLQuery(options) as UseQueryResult<ListEpikBoxesQuery, Error>;
};
