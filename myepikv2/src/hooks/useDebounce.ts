import { useRef } from 'react';

/**
 * A hook that returns a debounced version of the provided function
 * @param func The function to be debounced
 * @param timeout The debounce delay in milliseconds
 * @returns A debounced version of the provided function
 */
export function useDebounce<T extends (...args: any[]) => void>(
  func: T,
  timeout: number = 300,
): (...args: Parameters<T>) => void {
  const timer = useRef<NodeJS.Timeout | null>(null);

  return (...args: Parameters<T>) => {
    if (timer.current) {
      clearTimeout(timer.current);
      timer.current = null;
    }

    timer.current = setTimeout(() => {
      func(...args);
      if (timer.current) {
        clearTimeout(timer.current);
      }
      timer.current = null;
    }, timeout);
  };
}
