import { useEffect } from 'react';
import { apiClient, createSubscriptionClient } from '@/api';
import {
  ApiRequestOptions,
  GraphQLQueryOptions,
  GraphQLResponse,
} from '@/types/apiClient';
import logger from '@/utils/logger';
import {
  QueryKey,
  useMutation,
  UseMutationOptions,
  useQuery,
  useQueryClient,
  UseQueryOptions,
} from '@tanstack/react-query';
import { print } from 'graphql';
import { API_BASE_URLS, APIS } from '../constants';
import { useStore } from '@/store';

/**
 * Hook for making GraphQL queries
 * @param options - Query options
 * @returns Query result
 */
export function useGraphQLQuery(options: GraphQLQueryOptions) {
  const {
    query,
    variables = {},
    api = APIS.AUTH,
    queryKey,
    enabled = true,
    staleTime,
    cacheTime, // For backward compatibility
    gcTime, // v4 naming
    retry = 1,
    refetchOnWindowFocus = false,
    onSuccess,
    onError,
    select,
  } = options;

  // Generate a stable queryKey if not provided
  const finalQueryKey = queryKey || ['graphql', query, variables, api];

  const storeKey =
    Array.isArray(finalQueryKey) && typeof finalQueryKey[0] === 'string'
      ? finalQueryKey[0]
      : 'default';

  const setErrorState = useStore((s) => s?.error?.setErrorState);

  const queryResult = useQuery({
    queryKey: finalQueryKey,
    queryFn: async () => {
      try {
        const data = await apiClient<GraphQLResponse>({
          api,
          endpoint: '/query',
          method: 'POST',
          body: {
            query: print(query), // <-- IMPORTANT: convert to string
            variables,
          },
        });

        if (data.message) {
          logger.error('GraphQL query errors:', data.errors);
          throw new Error(data.message);
        }
        if (data.errors && data.errors.length > 0) {
          const errorMessage = data.errors.map((e) => e.message).join(', ');
          logger.error('GraphQL query errors:', data.errors);
          throw new Error(errorMessage);
        }

        return data.data;
      } catch (error: any) {
        const err = new Error(error?.message || 'Unknown error');
        (err as any).type = error?.type || 'NETWORK_ERROR';
        (err as any).raw = error;
        logger.error('GraphQL query failed:', err);
        throw err;
      }
    },
    enabled,
    staleTime,
    gcTime: gcTime || cacheTime, // Support both naming conventions
    retry,
    refetchOnWindowFocus,
    select,
  });

  useEffect(() => {
    if (queryResult.isSuccess) {
      setErrorState(storeKey, null);
      onSuccess?.(queryResult.data);
    }
  }, [queryResult.isSuccess]);

  useEffect(() => {
    if (queryResult.isError) {
      const msg = queryResult.error?.message || 'Internal error.';
      setErrorState(storeKey, msg);
      onError?.(msg);
      logger.error('GraphQL query error handler:', storeKey, msg, queryResult.error);
    }
  }, [queryResult.isError]);

  return queryResult;
}

type OptimisticUpdate<TVariables = any> = {
  queryKey: QueryKey;
  updateFn: (oldData: any, newData: TVariables) => any;
};

// REST API Types
interface RestGetOptions<TData = any, TError = Error> {
  endpoint: string;
  params?: Record<string, any>;
  api?: string;
  queryKey?: QueryKey;
  enabled?: boolean;
  staleTime?: number;
  gcTime?: number;
  retry?: number | boolean;
  refetchOnWindowFocus?: boolean;
  onSuccess?: (data: TData) => void;
  onError?: (error: TError) => void;
  select?: (data: any) => TData;
  responseType?: 'json' | 'text' | 'blob' | 'arraybuffer';
  keepPreviousData?: boolean;
}

/**
 * Hook for making REST GET requests
 * @param options - Query options
 * @returns Query result
 */
export function useGet<TData = any, TError = Error>(
  options: RestGetOptions<TData, TError>,
) {
  const {
    endpoint,
    params = {},
    api = APIS.AUTH,
    queryKey,
    enabled = true,
    staleTime,
    gcTime,
    retry = 1,
    refetchOnWindowFocus = false,
    onSuccess,
    onError,
    select,
    responseType,
    keepPreviousData = true,
  } = options;

  // Generate a stable queryKey if not provided
  const finalQueryKey = queryKey || [endpoint, params, api, responseType];

  return useQuery<TData, TError>({
    queryKey: finalQueryKey,
    queryFn: async (): Promise<TData> => {
      try {
        return await apiClient<TData>({
          api,
          endpoint,
          method: 'GET',
          params,
          responseType,
        });
      } catch (error) {
        logger.error(`GET request failed (${endpoint}):`, error);
        throw error;
      }
    },
    enabled,
    staleTime,
    gcTime,
    retry,
    refetchOnWindowFocus,
    keepPreviousData,
    onSuccess: (data: TData) => {
      logger.debug(`GET request succeeded (${endpoint}):`, data);
      if (onSuccess) onSuccess(data);
    },
    onError: (error: TError) => {
      logger.error(`GET request error handler (${endpoint}):`, error);
      if (onError) onError(error as TError);
    },
    select,
  } as UseQueryOptions<TData, TError>);
}




export interface RestMutationOptions<TData = any, TVariables = any, TError = Error> {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  endpoint: string;
  api?: string;
  onSuccess?: (data: TData, variables: TVariables) => void;
  onError?: (error: TError, variables: TVariables) => void;
  invalidateQueries?: QueryKey[];
  optimisticUpdate?: OptimisticUpdate<TVariables>;
  body?: any;
  headers?: Record<string, string>;
  onUploadProgress?: (progressEvent: any) => void;
  params?: Record<string, any>;
  timeout?: number;
  withCredentials?: boolean;
}

/**
 * A mutation hook that:
 * - Sends FormData via native fetch (no Content-Type header set manually)
 * - Sends JSON bodies via your apiClient (raw: true => returns Response)
 * - Avoids destructuring FormData (which turns it into {})
 */
export function useRestMutation<TData = Response, TVariables = any, TError = Error>(
  options: RestMutationOptions<TData, TVariables, TError>,
) {
  const {
    method,
    endpoint,
    api = APIS.AUTH,
    onSuccess,
    onError,
    invalidateQueries = [],
    optimisticUpdate,
    body,
    headers,
    onUploadProgress,
    params,
    timeout,
    withCredentials,
  } = options;

  const queryClient = useQueryClient();

  type Vars = TVariables | (TVariables & { signal?: AbortSignal }) | FormData;

  return useMutation<TData, TError, Vars>({
    mutationFn: async (variables): Promise<TData> => {
      let signal: AbortSignal | undefined;
      let payload: any = variables;

      const isFormData = typeof FormData !== 'undefined' && variables instanceof FormData;

      if (!isFormData && variables && typeof variables === 'object' && 'signal' in (variables as any)) {
        const { signal: s, ...rest } = variables as any;
        signal = s as AbortSignal | undefined;
        payload = rest;
      }

      if (typeof body !== 'undefined') {
        payload = body;
      }

      try {
        if (isFormData || payload instanceof FormData) {
          const baseUrl = API_BASE_URLS[api].replace(/\/+$/, '');
          const path = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;

          const queryString = params ? new URLSearchParams(params as Record<string, string>).toString() : '';
          const url = `${baseUrl}${path}${queryString ? `?${queryString}` : ''}`;

          const resp = await fetch(url, {
            method,
            body: payload as FormData,
            headers,
            credentials: withCredentials ? 'include' : 'same-origin',
            signal,
          });
          return resp as unknown as TData;
        }

        const resp = await apiClient<Response>({
          api,
          body: payload,
          endpoint,
          headers,
          method,
          onUploadProgress,
          params,
          timeout,
          withCredentials,
          raw: true,
          signal,
        });

        return resp as unknown as TData;
      } catch (error) {
        logger.error(`${method} request failed (${endpoint}):`, error);
        throw error;
      }
    },
    onMutate: async (newData) => {
      if (!optimisticUpdate) return {};

      const { queryKey, updateFn } = optimisticUpdate;

      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey });

      // Snapshot the previous value
      const previousData = queryClient.getQueryData(queryKey);
      queryClient.setQueryData(queryKey, (old: any) => updateFn(old, newData as TVariables));

      // Return a context with the previous data
      return { previousData, queryKey };
    },
    onSuccess: (data, variables) => {
      logger.debug(`${method} request succeeded (${endpoint}):`, data);

      // Invalidate any dependent queries
      if (invalidateQueries.length > 0) {
        invalidateQueries.forEach((key) => queryClient.invalidateQueries({ queryKey: key }));
      }

      onSuccess?.(data, variables as TVariables);
    },
    onError: (error, variables, context : any) => {
      logger.error(`${method} request error handler (${endpoint}):`, error);

      // Rollback optimistic update on error
      if (context?.previousData && context?.queryKey) {
        queryClient.setQueryData(context.queryKey, context.previousData);
      }

      onError?.(error as TError, variables as TVariables);
    },
  } as UseMutationOptions<TData, TError, Vars>);
}

export async function apiClientAsync<T>(
  options: ApiRequestOptions,
): Promise<T> {
  try {
    return await apiClient(options);
  } catch (error: any) {
    return { status: 'failed', message: error?.message || '' } as T;
  }
}
