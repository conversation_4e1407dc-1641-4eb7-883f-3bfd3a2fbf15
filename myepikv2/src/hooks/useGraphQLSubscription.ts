import { useEffect, useRef } from 'react';
import { print } from 'graphql';
import { Client } from 'graphql-ws';
import { TypedDocumentNode } from '@graphql-typed-document-node/core';
import { APIS } from '@/constants';
import { createSubscriptionClient } from '@/api';
import logger from '@/utils/logger';

export interface SubscriptionOptions<TData = any, TVariables = any> {
  api?: string;
  query: TypedDocumentNode<TData, TVariables>;
  variables?: TVariables;
  onData?: (data: TData) => void;
  onError?: (error: Error, variables?: TVariables) => void;
  onSuccess?: () => void;
  enabled?: boolean;
}

export function useGraphQLSubscription<TData = any, TVariables extends Record<string, unknown> | null | undefined = any>(
  options: SubscriptionOptions<TData, TVariables>
) {
  const {
    api = APIS.EPIKV2,
    query,
    variables,
    onData,
    onError,
    onSuccess,
    enabled = true,
  } = options;

  const clientRef = useRef<Client | null>(null);

  useEffect(() => {
    const client = createSubscriptionClient(api);
    clientRef.current = client;

    const unsubscribe = client.subscribe(
      {
        query: print(query),
        variables,
      },
      {
        next: (result) => {
          if ('errors' in result) {
            logger.error('GraphQL subscription error', result.errors);
            onError?.(new Error('GraphQL subscription error'), variables);
            return;
          }

          const data = result.data as TData;
          logger.debug('Subscription data:', data);
          onData?.(data);
        },
        error: (err) => {
          logger.error('Subscription connection error', err);
          onError?.(err as Error, variables);
        },
        complete: () => {
          logger.debug('Subscription completed');
          onSuccess?.();
        },
      }
    );

    return () => {
      logger.debug('Cleaning up subscription');
      unsubscribe();
    };
  }, [enabled, api, print(query), JSON.stringify(variables)]);

  return null;
}
