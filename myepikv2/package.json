{"name": "myepikv2", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write \"src/**/*.{js,jsx,json,css,scss,md}\"", "postinstall": "node ../hooks/install-hooks.js", "codegen": "graphql-codegen --config auth.codegen.ts && graphql-codegen --config epikv2-api.codegen.ts"}, "lint-staged": {"*.{js,jsx,json,css,scss,md}": ["eslint --fix --no-warn-ignored", "prettier --write"]}, "dependencies": {"@ant-design/charts": "^2.2.1", "@ant-design/plots": "^2.3.2", "@tanstack/react-query": "^5.75.1", "antd": "^5.21.2", "axios": "^1.9.0", "dayjs": "^1.11.13", "graphql-transport-ws": "^1.9.0", "graphql-ws": "^6.0.5", "immer": "^10.1.1", "libphonenumber-js": "^1.12.15", "rc-tween-one": "^3.0.6", "react": "^18.3.1", "react-dom": "^18.3.1", "react-media-recorder": "^1.7.2", "react-router-dom": "^6.27.0", "react-sortablejs": "^6.1.4", "sortablejs": "^1.15.6", "zustand": "^5.0.0"}, "devDependencies": {"@0no-co/graphqlsp": "^1.12.16", "@eslint/js": "^9.9.0", "@graphql-codegen/cli": "^5.0.6", "@graphql-codegen/schema-ast": "^4.1.0", "@parcel/watcher": "^2.5.1", "@rollup/plugin-graphql": "^2.0.5", "@types/node": "^22.15.3", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.3.1", "eslint": "^9.15.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "lint-staged": "^15.2.10", "typescript": "^5.8.3", "vite": "^5.4.1", "vite-plugin-svgr": "^4.2.0"}}