import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import svgr from 'vite-plugin-svgr';
import path from 'path';
import graphql from '@rollup/plugin-graphql';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [graphql(), react(), svgr()],
  server: {
    host: true,
    port: 5000,
    allowedHosts: ['wapi.epikadmin.com', 'dev.epik.io'],
  },
  base: '/apps/epikv2',
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@utils': path.resolve(__dirname, './src/utils'),
      '@EdgeDeviceDetail': path.resolve(
        __dirname,
        './src/components/specific/EdgeDeviceDetail',
      ), // Alias for EdgeDeviceDetail
    },
  },
});
