import type { CodegenConfig } from '@graphql-codegen/cli';
import 'dotenv/config';

const config: CodegenConfig = {
  generates: {
    './src/graphql/generated/auth-api/': {
      schema: [
        {
          'http://dev.epik.io/apps/auth-api/query': {},
        },
      ],
      documents: [
        'src/graphqlHooks/auth/companiesQueries.ts',
        'src/graphqlHooks/auth/useListUsers.ts',
        'src/graphqlHooks/auth/useListPermissionGroups.ts',
        'src/graphqlHooks/auth/useUserPermissions.ts',
      ],
      preset: 'client',
      plugins: [],
    },
    './src/graphql/generated/epikv2-api/': {
      schema: [
        {
          'http://dev.epik.io/apps/epikv2-api/query': {},
        },
      ],
      documents: [
        'src/graphqlHooks/epikv2-api/epikBoxQueries.ts',
        'src/graphqlHooks/epikv2-api/useListNumbers.ts',
        'src/graphqlHooks/epikv2-api/useListObis.ts',
        'src/graphqlHooks/epikv2-api/useEpiDetailById.ts',
        'src/graphqlHooks/epikv2-api/**/*.graphql',
      ],
      preset: 'client',
      plugins: [],
    },
    './schema-auth.graphql': {
      schema: [
        {
          'http://dev.epik.io/apps/auth-api/query': {
            headers: {
              Authorization: 'Bearer YOUR_AUTH_API_TOKEN',
            },
          },
        },
      ],
      plugins: ['schema-ast'],
      config: {
        includeDirectives: true,
      },
    },
    './schema-epikv2.graphql': {
      schema: [
        {
          'http://dev.epik.io/apps/epikv2-api/query': {
            headers: {
              Cookie:
                `v2-jwt=${process.env.VITE_AUTH_TOKEN}`
            },
          },
        },
      ],
      plugins: ['schema-ast'],
      config: {
        includeDirectives: true,
      },
    },
  },
};

export default config;
