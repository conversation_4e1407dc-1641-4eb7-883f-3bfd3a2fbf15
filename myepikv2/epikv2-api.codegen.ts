import 'dotenv/config';
import type { CodegenConfig } from '@graphql-codegen/cli';

const config: CodegenConfig = {
  schema: [
    {
      'http://dev.epik.io/apps/epikv2-api/query': {
        headers: {
          Cookie:
            `v2-jwt=${process.env.VITE_AUTH_TOKEN}`
        },
      },
    },
  ],
  documents: ['src/graphqlHooks/epikv2-api/**/*.ts'],
  generates: {
    './src/graphql/generated/epikv2-api/': {
      preset: 'client',
    },
  },
};

export default config;
